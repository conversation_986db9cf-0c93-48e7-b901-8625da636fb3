import { reactRouter } from '@react-router/dev/vite';
import { defineConfig } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';

const DEFAULT_PROXY_TARGET = 'https://go.t2.moego.dev';

export default defineConfig({
  base: '/web/',
  server: {
    port: 10729,
    proxy: {
      '/moego.api': DEFAULT_PROXY_TARGET,
      '/moego.client': DEFAULT_PROXY_TARGET,
    },
  },
  plugins: [reactRouter(), tsconfigPaths()],
});
