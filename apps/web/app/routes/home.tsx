import type { Route } from './+types/home';

export function meta({}: Route.MetaArgs) {
  return [{ title: 'New React Router App' }, { name: 'description', content: 'Welcome to React Router!' }];
}

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
      <div className="max-w-3xl w-full bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl p-8 space-y-6">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-800 text-center">Welcome to Business-Kit System</h1>
        <p className="text-lg text-gray-600 text-center leading-relaxed">Nice to meet you!</p>
        <div className="flex justify-center gap-4">
          <button
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors"
            onClick={() => {
              window.location.href = window.location.origin;
            }}
          >
            Story book
          </button>
          <button
            disabled
            className="px-6 py-3 bg-white hover:bg-gray-100 text-blue-600 font-semibold rounded-lg border-2 border-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Learn more
          </button>
        </div>
      </div>
    </div>
  );
}
