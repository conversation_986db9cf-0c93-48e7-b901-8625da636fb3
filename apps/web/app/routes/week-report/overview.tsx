import { useLoaderData, useParams } from 'react-router';
import { Dashboard, ReportHeader } from '../../modules/WeeklyReport/WeeklyReport';

import dayjs from 'dayjs';
import { useMemo } from 'react';
import { DetailsContainer } from '~/components/DetailsContainer';
import { SectionContainer } from '~/components/SectionContainer';
import { queryClient } from '~/libs/query-client';
import { TemplateDataAdapter } from '~/modules/WeeklyReport/adapter/adapter';
import { RepeatabilityHistory } from '~/modules/WeeklyReport/RepeatabilityHistory';
import type { ReportJsonData } from '~/modules/WeeklyReport/type';
import { generateTemplateData } from '~/modules/WeeklyReport/utils/generateTemplateData';
import { VulnerabilityHistory } from '~/modules/WeeklyReport/VulnerabilityHistory';
import {
  DuplicateFilesTable,
  LicenseDistribution,
  LicenseOverviewCard,
  LicenseTable,
  RepeatabilityCards,
  VulnerabilityDetails,
  VulnerabilityDistribution,
  VulnerabilityTable,
} from '~/modules/WeeklyReport/WeeklyReport';
import { useWeeklyReportData } from '~/query/report';
import './overview.css';

export async function clientLoader({ params }: { params: { projectId: string; reportDate: string } }) {
  const { projectId, reportDate } = params;
  // 获得上周时间(基于 Intl 新 API)
  const lastWeek = dayjs(reportDate).subtract(1, 'week');
  const lastWeekDate = lastWeek.format('YYYY-MM-DD');
  try {
    const lastWeekResponse = await queryClient.fetchQuery(
      useWeeklyReportData.getFetchOptions({
        projectId,
        weekStr: lastWeekDate,
      }),
    );

    const response = await queryClient.fetchQuery(
      useWeeklyReportData.getFetchOptions({
        projectId,
        weekStr: reportDate,
      }),
    );

    return {
      response,
      lastWeekResponse,
    };
  } catch (err) {
    throw new Error(err instanceof Error ? err.message : 'An error occurred');
  }
}

const Overview = () => {
  const data = useLoaderData<{
    response: ReportJsonData;
    lastWeekResponse: ReportJsonData;
  }>();
  const templateData = useMemo(() => {
    return generateTemplateData(data.response, data.lastWeekResponse);
  }, []);

  const { projectId, reportDate } = useParams();

  console.log('templateDatatemplateData', templateData);

  return (
    <div className="max-w-5xl mx-auto p-5 font-sans text-gray-700 overview-container">
      <ReportHeader projectName={templateData.projectName} reportDate={templateData.reportDate} />

      <SectionContainer title="总览看板">
        <Dashboard metrics={TemplateDataAdapter.dashboardCards2metrics(templateData.dashboardCards)} />
        <DetailsContainer title="漏洞分布">
          <VulnerabilityDistribution data={templateData.vulnerabilityDistribution.items} />
        </DetailsContainer>
        <DetailsContainer title="许可证问题">
          <div className="flex flex-row">
            <LicenseOverviewCard title="错误许可证" value={templateData.errorLicenses.length} />
            <LicenseOverviewCard title="未知许可证" value={templateData.unknownLicenses.length} />
          </div>
        </DetailsContainer>
        {projectId && reportDate && (
          <DetailsContainer title="安全问题趋势">
            <VulnerabilityHistory projectId={projectId} endDate={reportDate} />
          </DetailsContainer>
        )}
      </SectionContainer>

      <SectionContainer title="依赖审计报告">
        <DetailsContainer title="漏洞摘要">
          <VulnerabilityTable data={templateData.vulnerabilitySummary} />
        </DetailsContainer>
        <VulnerabilityDetails vulnerabilityDetails={templateData.vulnerabilityDetails} />
      </SectionContainer>

      <SectionContainer title="许可证安全报告">
        <DetailsContainer title="许可证分布">
          <LicenseDistribution data={templateData.licenseDistribution} />
        </DetailsContainer>
        {templateData.errorLicenses.length > 0 && (
          <DetailsContainer title="错误许可证">
            <LicenseTable data={templateData.errorLicenses} />
            <span className="text-[14px]">修复建议：移除依赖或购买使用权</span>
          </DetailsContainer>
        )}
        {templateData.unknownLicenses.length > 0 && (
          <DetailsContainer title="未知许可证">
            <LicenseTable data={templateData.unknownLicenses} />
            <span className="text-[14px]">修复建议：审查许可条款并确认合规性</span>
          </DetailsContainer>
        )}
      </SectionContainer>

      <SectionContainer title="代码重复度报告">
        <DetailsContainer title="重复度概览">
          <RepeatabilityCards data={templateData.repeatabilityCards} />
        </DetailsContainer>
        <DetailsContainer title="重复度 Top">
          <DuplicateFilesTable data={templateData.duplicateFiles} />
        </DetailsContainer>
        {projectId && reportDate && (
          <DetailsContainer title="代码重复度趋势">
            <RepeatabilityHistory projectId={projectId} endDate={reportDate} />
          </DetailsContainer>
        )}
      </SectionContainer>
    </div>
  );
};

export default Overview;
