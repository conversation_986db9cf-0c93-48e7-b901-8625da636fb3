import classNames from 'classnames';
import type React from 'react';
import type { ReactNode } from 'react';

export interface DetailsContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  children: ReactNode;
  className?: string;
}

export function DetailsContainer({ title, children, className, ...rest }: DetailsContainerProps) {
  return (
    <div className={classNames('bg-gray-50 rounded-lg p-5 mt-5', className)} {...rest}>
      <div className="text-lg font-bold mb-4 text-gray-700">{title}</div>
      {children}
    </div>
  );
}
