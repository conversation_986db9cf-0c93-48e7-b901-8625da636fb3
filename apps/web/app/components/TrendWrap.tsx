import classNames from 'classnames';

const trendColors = {
  up: 'text-red-500',
  'trend-up': 'text-red-500',
  down: 'text-green-500',
  'trend-down': 'text-green-500',
  stable: 'text-gray-500',
  'trend-indicator': 'text-gray-500',
};
const trendIcons = {
  up: '↑',
  'trend-up': '↑',
  down: '↓',
  'trend-down': '↓',
  stable: '-',
  'trend-indicator': '-',
};

export type TrendKey = keyof typeof trendColors;

interface TrendIconProps extends React.HTMLAttributes<HTMLSpanElement> {
  trend: TrendKey;
}

export function TrendWrap({ className, trend, children, ...rest }: TrendIconProps) {
  return (
    <span className={classNames(`text-sm ml-2 ${trendColors[trend]}`, className)} {...rest}>
      {trendIcons[trend]}
      {children}
    </span>
  );
}
