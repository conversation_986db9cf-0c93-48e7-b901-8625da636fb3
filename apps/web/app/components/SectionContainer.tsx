import classNames from 'classnames';
import type { ReactNode } from 'react';
import React from 'react';

export interface SectionContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  children: ReactNode;
  className?: string;
}

export function SectionContainer({ title, children, className, ...rest }: SectionContainerProps) {
  const sectionId = `tag-${title}`;

  return (
    <div className={classNames('mb-10 bg-white rounded-lg p-5', className)} {...rest}>
      <a id={sectionId} href={`#${sectionId}`}>
        <h2 className="text-xl text-gray-800 font-bold border-b border-gray-200 pb-2.5 mb-5">{title}</h2>
      </a>
      {children}
    </div>
  );
}
