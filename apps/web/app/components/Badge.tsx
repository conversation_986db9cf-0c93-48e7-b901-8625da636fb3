import React from 'react';

import classNames from 'classnames';
import type { ReactNode } from 'react';

export type BadgeType = 'critical' | 'high' | 'moderate' | 'low' | 'error' | 'warning' | 'success' | 'info';

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  type: BadgeType;
  children: ReactNode;
}

export function Badge({ type, children, className, ...rest }: BadgeProps) {
  const colorMap = {
    critical: 'bg-red-500',
    high: 'bg-orange-500',
    moderate: 'bg-[#da8936]',
    low: 'bg-[#e49f36]',
    error: 'bg-red-500',
    warning: 'bg-[#da8936]',
    success: 'bg-blue-500',
    info: 'bg-blue-500',
  };

  return (
    <span
      className={classNames(
        `inline-block px-2 py-1 rounded text-xs font-bold text-white whitespace-nowrap ${colorMap[type]}`,
        className,
      )}
      {...rest}
    >
      {children}
    </span>
  );
}
