import { useSuspenseQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { createQuery } from 'react-query-kit';
import { CDN_PREFIX, HISTORY_REPORT_KEY, WEEKLY_REPORT_KEY } from '~/config/const';
import { queryClient } from '~/libs/query-client';
import type { ReportJsonData } from '~/modules/WeeklyReport/type';

export const useWeeklyReportData = createQuery<
  ReportJsonData,
  {
    projectId: string;
    weekStr: string;
  }
>({
  queryKey: WEEKLY_REPORT_KEY,
  fetcher: async ({ projectId, weekStr }) => {
    return fetch(`${CDN_PREFIX}${projectId}/${weekStr}-report.json`)
      .then((res) => res.json())
      .catch(() => null);
  },
});

export const useHistoryReportData = ({ projectId, endDate }: { projectId: string; endDate: string }) => {
  const dateList = useMemo(() => {
    const date = dayjs(endDate);
    const dateList = [];
    for (let i = 0; i < 6; i++) {
      dateList.push(date.subtract(i, 'week').format('YYYY-MM-DD'));
    }
    return dateList;
  }, [endDate]);

  return useSuspenseQuery({
    queryKey: HISTORY_REPORT_KEY,
    queryFn: () => {
      return Promise.all(
        dateList.map((date) => {
          return queryClient
            .fetchQuery(
              useWeeklyReportData.getFetchOptions({
                projectId,
                weekStr: date,
              }),
            )
            .then((res) => {
              return {
                date,
                data: res ?? null,
              };
            });
        }),
      );
    },
  });
};
