import { Badge } from '~/components/Badge';
import type { LicenseData } from './type';

export interface LicenseTableProps {
  data: LicenseData[];
}

export function LicenseTable({ data }: LicenseTableProps) {
  return (
    <div className="overflow-x-auto -webkit-overflow-scrolling-touch mb-4 rounded-lg">
      <table className="w-full min-w-[600px] border-collapse mt-5">
        <thead>
          <tr>
            <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10">许可证</th>
            <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10">发布者</th>
            <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10">邮箱</th>
            <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10">状态</th>
            <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10">包路径</th>
          </tr>
        </thead>
        <tbody>
          {data.map((license, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="p-3 border-b border-gray-200 whitespace-nowrap">{license.name}</td>
              <td className="p-3 border-b border-gray-200 whitespace-nowrap">{license.publisher}</td>
              <td className="p-3 border-b border-gray-200 whitespace-nowrap">{license.email || '-'}</td>
              <td className="p-3 border-b border-gray-200 whitespace-nowrap">
                <Badge type={license.status.class}>{license.status.text}</Badge>
              </td>
              <td className="p-3 border-b border-gray-200 break-all whitespace-nowrap">{license.path}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
