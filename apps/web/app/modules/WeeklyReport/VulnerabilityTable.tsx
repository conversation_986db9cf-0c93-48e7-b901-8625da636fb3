import { Badge } from '../../components/Badge';

export interface VulnerabilitySummaryItem {
  level: 'critical' | 'high' | 'moderate' | 'low' | 'info';
  severity: string;
  count: number;
  description: string;
}

export interface VulnerabilityTableProps {
  data: VulnerabilitySummaryItem[];
}

export function VulnerabilityTable({ data }: VulnerabilityTableProps) {
  return (
    <div className="overflow-x-auto -webkit-overflow-scrolling-touch mb-4 rounded-lg">
      <table className="w-full min-w-[600px] border-collapse mt-5">
        <thead>
          <tr>
            <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10">严重程度</th>
            <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10">数量</th>
            <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10">描述</th>
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="p-3 border-b border-gray-200">
                <Badge type={item.level}>{item.severity}</Badge>
              </td>
              <td className="p-3 border-b border-gray-200">{item.count}</td>
              <td className="p-3 border-b border-gray-200">{item.description}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
