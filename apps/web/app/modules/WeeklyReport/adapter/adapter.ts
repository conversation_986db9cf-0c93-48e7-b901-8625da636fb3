import type { Metric } from '../Dashboard';
import type { DashboardCard } from '../type';

export class TemplateDataAdapter {
  static dashboardCards2metrics(dashboard: DashboardCard[]): Metric[] {
    return dashboard.map((card) => ({
      title: card.title,
      value: card.value,
      trend: card.trend,
      status: card.trend
        ? {
            type:
              card.trend.direction === 'trend-up'
                ? 'increase'
                : card.trend.direction === 'trend-down'
                  ? 'decrease'
                  : 'stable',
            value: card.trend.value,
          }
        : undefined,
    }));
  }
}
