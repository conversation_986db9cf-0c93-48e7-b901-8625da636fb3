export interface TimelineEvent {
  date: string;
  title: string;
  description: string;
}

export const Timeline = ({ events }: { events: TimelineEvent[] }) => (
  <div className="bg-white rounded-lg shadow-sm p-6">
    <h2 className="text-xl font-semibold text-gray-800 mb-4">时间轴</h2>

    <div className="relative pl-8">
      <div className="absolute left-0 top-0 w-0.5 h-full bg-gray-200"></div>

      {events.map((event, index) => (
        <div key={index} className="relative mb-6 pl-4">
          <div className="absolute left-[-17px] top-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white"></div>
          <div className="text-sm font-medium text-blue-500">{event.date}</div>
          <h3 className="mt-1 font-medium">{event.title}</h3>
          <p className="text-gray-600 text-sm mt-1">{event.description}</p>
        </div>
      ))}
    </div>
  </div>
);
