export interface Recommendation {
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
}

export const Recommendations = ({ items }: { items: Recommendation[] }) => (
  <div className="bg-white rounded-lg shadow-sm p-6">
    <h2 className="text-xl font-semibold text-gray-800 mb-4">建议措施</h2>

    {items.map((item) => (
      <div key={item.title} className="flex items-start mb-4 p-3 bg-blue-50 rounded">
        <div
          className={`w-8 h-8 flex items-center justify-center rounded-full ${
            item.priority === 'high' ? 'bg-red-500' : item.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
          }`}
        >
          <span className="text-white text-sm">{item.priority[0].toUpperCase()}</span>
        </div>
        <div className="ml-4">
          <h3 className="font-medium">{item.title}</h3>
          <p className="text-gray-600 text-sm mt-1">{item.description}</p>
        </div>
      </div>
    ))}
  </div>
);
