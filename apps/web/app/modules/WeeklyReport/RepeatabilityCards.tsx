import { TrendWrap, type Trend<PERSON><PERSON> } from '~/components/TrendWrap';

export interface RepeatabilityCard {
  title: string;
  value: string | number;
  valueClass?: string;
  trend?: {
    direction: TrendKey;
    value: string | number;
  };
}

export interface RepeatabilityCardsProps {
  data: RepeatabilityCard[];
}

export function RepeatabilityCards({ data }: RepeatabilityCardsProps) {
  return (
    <div className="flex flex-wrap gap-5">
      {data.map((card, index) => (
        <div key={index} className="flex-1 min-w-[150px] bg-gray-50 rounded-lg p-4 pl-0 duration-200">
          <div className="text-base font-bold mb-2.5 text-gray-600">{card.title}</div>
          <div className={`text-2xl font-bold flex items-center whitespace-nowrap ${card.valueClass}`}>
            {card.value}
            {card.trend && (
              <TrendWrap className={`ml-2.5 text-sm flex items-center`} trend={card.trend.direction}>
                {card.trend.value}
              </TrendWrap>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
