export interface LicenseDistributionItem {
  name: string;
  count: number;
}

export interface LicenseDistributionProps {
  data: LicenseDistributionItem[];
}

export function LicenseDistribution({ data }: LicenseDistributionProps) {
  return (
    <div className="flex flex-wrap gap-2.5 mt-4">
      {data.map((license, index) => (
        <div key={index} className="flex-1 min-w-[120px] bg-blue-50 p-4 rounded-lg">
          <div className="font-bold">{license.name}</div>
          <div className="text-xl mt-1.5">{license.count}</div>
        </div>
      ))}
    </div>
  );
}
