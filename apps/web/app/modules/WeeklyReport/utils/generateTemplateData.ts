import type { ReportJsonData } from '../type';

const getTrendData = (currentValue: number | string, previousValue?: number | string) => {
  if (!previousValue) {
    return {
      direction: 'trend-indicator' as const,
      icon: '-',
      value: '0',
    };
  }

  const current = parseFloat(String(currentValue));
  const previous = parseFloat(String(previousValue));
  if (current === previous) {
    return {
      direction: 'trend-indicator' as const,
      icon: '-',
      value: '0',
    };
  }
  const isPercent = String(currentValue).includes('%');
  const direction = current > previous ? 'up' : 'down';
  const icon = current > previous ? '↑' : '↓';

  const diff = current - previous;
  if (isPercent) {
    const value = current > previous ? `+${diff.toFixed(2)}%` : `${diff.toFixed(2)}%`;
    return {
      direction: `trend-${direction}` as const,
      icon,
      value,
    };
  }
  const value = current > previous ? `+${current - previous}` : `${current - previous}`;

  return {
    direction: `trend-${direction}` as const,
    icon,
    value,
  };
};

interface Card {
  title: string;
  value: number | string;
  trend?: {
    direction: string;
    icon: string;
    value: string;
  };
}

const updateCardsWithTrend = <T extends Card>(currentCards: T[], previousCards: T[]): T[] => {
  for (let i = 0; i < currentCards.length; i++) {
    const card = currentCards[i];
    const previousCard = previousCards.find((item) => item.title === card.title);
    if (!previousCard) continue;
    card.trend = getTrendData(card.value, previousCard.value);
  }
  return currentCards;
};

export const generateTemplateData = (currentData: ReportJsonData, previousData?: ReportJsonData | null) => {
  if (!previousData) {
    return currentData.templateData;
  }

  const { templateData: currentTemplateData } = currentData;
  const { templateData: previousTemplateData } = previousData;

  updateCardsWithTrend(currentTemplateData.dashboardCards, previousTemplateData.dashboardCards);
  updateCardsWithTrend(currentTemplateData.repeatabilityCards, previousTemplateData.repeatabilityCards);

  return currentTemplateData;
};
