import { Badge, type BadgeType } from '~/components/Badge';
import type { VulnerabilityDistributionItem } from './type';

export interface VulnerabilityDistributionProps {
  data: VulnerabilityDistributionItem[];
}

export function VulnerabilityDistribution({ data }: VulnerabilityDistributionProps) {
  return (
    <div>
      <div className="h-5 rounded-lg overflow-hidden bg-gray-200">
        <div
          className="h-full transition-all duration-500"
          style={{
            width: '100%',
            background: `linear-gradient(to right, #e74c3c 6.7%, #e67e22 40%, #f39c12 86.7%, #3498db 100%)`,
          }}
        />
      </div>
      <div className="flex justify-between flex-wrap mt-1.5">
        {data.map((item) => (
          <span key={item.level} className="flex items-center gap-1">
            <Badge type={item.level as BadgeType}>{item.count}</Badge>
            {item.label}
          </span>
        ))}
      </div>
    </div>
  );
}
