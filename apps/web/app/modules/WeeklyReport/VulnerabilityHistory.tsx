import { Suspense, useEffect, useRef } from 'react';
import { VChart } from '~/libs/vchart';
import { useHistoryReportData } from '~/query/report';

export interface VulnerabilityHistoryProps {
  projectId: string;
  endDate: string;
}

const VulnerabilityHistoryCard = ({ projectId, endDate }: VulnerabilityHistoryProps) => {
  // 往前 6 周 的 date
  const { data } = useHistoryReportData({ projectId, endDate });
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!data || !ref.current) {
      return;
    }
    const values = data.map((item) => {
      if (!item.data)
        return {
          week: item.date,
          value: 0,
        };
      return {
        week: item.data.templateData.reportDate,
        value: item.data.templateData.dashboardCards.find((item) => item.title === '安全问题')?.value,
      };
    });
    values[0].week = '本周';
    values.reverse();
    const spec = {
      type: 'bar',
      data: [
        {
          id: 'barData',
          values: values,
        },
      ],
      xField: 'week',
      yField: 'value',
      label: {
        visible: true,
      },
      color: ['#e74c3c'],
    };
    const vchart = new VChart(spec, { dom: ref.current });
    vchart.renderSync();

    return () => {
      vchart.release();
    };
  }, [data]);

  return <div className="w-full h-[300px]" ref={ref}></div>;
};

export const VulnerabilityHistory = ({ projectId, endDate }: VulnerabilityHistoryProps) => {
  return (
    <Suspense>
      <VulnerabilityHistoryCard projectId={projectId} endDate={endDate} />
    </Suspense>
  );
};
