import { useMemo } from 'react';
import type { VulnerabilityDetail } from './type';
import { VulnerabilityAccordion } from './VulnerabilityAccordion';

export interface VulnerabilityDetailsProps {
  vulnerabilityDetails: VulnerabilityDetail[];
}

export const VulnerabilityDetails = ({ vulnerabilityDetails }: VulnerabilityDetailsProps) => {
  const criticalVulnerabilities = useMemo(
    () => vulnerabilityDetails.filter((item) => item.level === 'critical'),
    [vulnerabilityDetails],
  );
  const highVulnerabilities = useMemo(
    () => vulnerabilityDetails.filter((item) => item.level === 'high'),
    [vulnerabilityDetails],
  );
  const moderateVulnerabilities = useMemo(
    () => vulnerabilityDetails.filter((item) => item.level === 'moderate'),
    [vulnerabilityDetails],
  );
  const lowVulnerabilities = useMemo(
    () => vulnerabilityDetails.filter((item) => item.level === 'low'),
    [vulnerabilityDetails],
  );
  const infoVulnerabilities = useMemo(
    () => vulnerabilityDetails.filter((item) => item.level === 'info'),
    [vulnerabilityDetails],
  );

  return (
    <div className="py-2">
      {criticalVulnerabilities.length > 0 && (
        <VulnerabilityAccordion
          label="严重漏洞"
          level="critical"
          count={criticalVulnerabilities.length}
          title="严重级别漏洞"
          data={criticalVulnerabilities}
        />
      )}
      {highVulnerabilities.length > 0 && (
        <VulnerabilityAccordion
          label="高危漏洞"
          level="high"
          count={highVulnerabilities.length}
          title="高危级别漏洞"
          data={highVulnerabilities}
        />
      )}
      {moderateVulnerabilities.length > 0 && (
        <VulnerabilityAccordion
          label="中危漏洞"
          level="moderate"
          count={moderateVulnerabilities.length}
          title="中危级别漏洞"
          data={moderateVulnerabilities}
        />
      )}
      {lowVulnerabilities.length > 0 && (
        <VulnerabilityAccordion
          label="低危漏洞"
          level="low"
          count={lowVulnerabilities.length}
          title="低危级别漏洞"
          data={lowVulnerabilities}
        />
      )}
      {infoVulnerabilities.length > 0 && (
        <VulnerabilityAccordion
          label="信息漏洞"
          level="info"
          count={infoVulnerabilities.length}
          title="信息级别漏洞"
          data={infoVulnerabilities}
        />
      )}
    </div>
  );
};
