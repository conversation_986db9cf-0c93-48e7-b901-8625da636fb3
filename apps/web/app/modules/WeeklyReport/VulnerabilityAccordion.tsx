import { useState } from 'react';
import { Badge } from '~/components/Badge';
import { VulnerabilityItem } from './VulnerabilityItem';

interface VulnerabilityItem {
  title: string;
  description: string;
  recommendation: string;
  paths: string[];
  module: string;
  cve: string;
  cvss: number;
}

interface VulnerabilityAccordionProps {
  data: VulnerabilityItem[];
  title: string;
  level: 'critical' | 'high' | 'moderate' | 'low' | 'info';
  label: string;
  count: number;
}

export function VulnerabilityAccordion({ data, title, level, label, count }: VulnerabilityAccordionProps) {
  const [isOpen, setIsOpen] = useState(false);

  const levelColorMap = {
    critical: 'text-red-600',
    high: 'text-orange-600',
    moderate: 'text-yellow-600',
    low: 'text-blue-600',
    info: 'text-green-600',
  };

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="border rounded-lg overflow-hidden mb-4">
      <div
        className="flex items-center justify-between p-4 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
        onClick={handleToggle}
      >
        <div className="flex items-center gap-3">
          <Badge type={level}>{label}</Badge>
          {title}
          <span className={`font-medium ${levelColorMap[level]}`}>{count}</span>
        </div>
        <svg
          className={`w-5 h-5 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="m6 9 6 6 6-6" />
        </svg>
      </div>

      <div
        className={`transition-all duration-300 break-words w-[100%] max-w-[100%] ease-in-out ${
          isOpen ? 'max-h-[2000px] overflow-y-auto overflow-x-hidden opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="p-4 space-y-6">
          {data.map((item, index) => (
            <VulnerabilityItem key={index} {...item} showDivider={index > 0} />
          ))}
        </div>
      </div>
    </div>
  );
}
