import { Suspense, useEffect, useRef } from 'react';
import { VChart } from '~/libs/vchart';
import { useHistoryReportData } from '~/query/report';

export interface RepeatabilityHistoryProps {
  projectId: string;
  endDate: string;
}

const RepeatabilityHistoryCard = ({ projectId, endDate }: RepeatabilityHistoryProps) => {
  // 往前 6 周 的 date
  const { data } = useHistoryReportData({ projectId, endDate });
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!data || !ref.current) {
      return;
    }
    const values = data.map((item) => {
      if (!item.data)
        return {
          week: item.date,
          value: 0,
        };
      return {
        week: item.data.templateData.reportDate,
        value: parseFloat(
          String(item.data.templateData.dashboardCards.find((item) => item.title === '代码重复率')?.value),
        ),
      };
    });
    values[0].week = '本周';
    values.reverse();
    const spec = {
      type: 'bar',
      data: [
        {
          id: 'barData',
          values: values,
        },
      ],
      xField: 'week',
      yField: 'value',
      label: {
        visible: true,
        formatMethod: (value: number) => `${value}%`,
      },
      axis: {
        y: {
          label: {
            formatMethod: (value: number) => `${value}%`,
          },
        },
      },
      color: ['#f39c12'],
    };
    const vchart = new VChart(spec, { dom: ref.current });
    vchart.renderSync();

    return () => {
      vchart.release();
    };
  }, [data]);

  return <div className="w-full h-[300px]" ref={ref}></div>;
};

export const RepeatabilityHistory = ({ projectId, endDate }: RepeatabilityHistoryProps) => {
  return (
    <Suspense>
      <RepeatabilityHistoryCard projectId={projectId} endDate={endDate} />
    </Suspense>
  );
};
