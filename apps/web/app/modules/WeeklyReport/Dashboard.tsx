import { TrendWrap, type TrendKey } from '~/components/TrendWrap';

export interface Metric {
  title: string;
  value: string | number;
  trend: {
    direction: TrendKey;
    value: string;
    icon?: string;
  };
  severity?: 'critical' | 'high' | 'moderate' | 'low';
}

export const Dashboard = ({ metrics }: { metrics: Metric[] }) => (
  <div className="flex flex-wrap gap-4 mb-8">
    {metrics.map((metric) => (
      <MetricCard key={metric.title} {...metric} />
    ))}
  </div>
);

const MetricCard = ({ title, value, trend, severity }: Metric) => {
  const severityColors = {
    critical: 'text-red-500',
    high: 'text-orange-500',
    moderate: 'text-yellow-500',
    low: 'text-blue-500',
    info: 'text-green-500',
  };

  return (
    <div className="flex-1 min-w-[150px] bg-[#f9f9f9] p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow">
      <div className="text-gray-600 text-sm font-medium mb-2">{title}</div>
      <div className={`text-2xl font-bold ${severity ? severityColors[severity] : 'text-gray-800'}`}>
        {value}
        <TrendWrap trend={trend.direction}>{trend.value}</TrendWrap>
      </div>
    </div>
  );
};
