import classNames from 'classnames';
import { useMemo, useState } from 'react';
import { Badge } from '~/components/Badge';

export interface DuplicateFile {
  status: {
    class: 'error' | 'warning' | 'success';
    text: string;
  };
  lines: number;
  title: string;
  tokens: number;
}

export interface DuplicateFilesTableProps {
  data: DuplicateFile[];
}

export function DuplicateFilesTable({ data }: DuplicateFilesTableProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const INITIAL_DISPLAY_COUNT = 5;

  const displayData = useMemo(
    () => (isExpanded ? data : data.slice(0, INITIAL_DISPLAY_COUNT)),
    [isExpanded, data, INITIAL_DISPLAY_COUNT],
  );
  const hasMore = data.length > INITIAL_DISPLAY_COUNT;

  return (
    <div>
      <div className="overflow-x-auto -webkit-overflow-scrolling-touch mb-4 rounded-lg">
        <table className="w-full min-w-[600px] border-collapse mt-5">
          <thead>
            <tr>
              <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10 whitespace-nowrap">
                状态
              </th>
              <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10 whitespace-nowrap">
                重复行数
              </th>
              <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10">
                重复代码
              </th>
              <th className="bg-gray-50 font-bold p-3 text-left border-b border-gray-200 sticky top-0 z-10 whitespace-nowrap">
                重复 Token 数
              </th>
            </tr>
          </thead>
          <tbody>
            {displayData.map((file, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="p-3 border-b border-gray-200">
                  <Badge type={file.status.class}>{file.status.text}</Badge>
                </td>
                <td className="p-3 border-b border-gray-200">{file.lines}</td>
                <td className="p-3 border-b border-gray-200 whitespace-pre-wrap">{file.title}</td>
                <td className="p-3 border-b border-gray-200">{file.tokens}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {hasMore && (
        <div
          className={classNames('flex justify-center mt-4 mb-2', {
            'sticky bottom-[12px]': isExpanded,
          })}
        >
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className={classNames(
              'inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200',
            )}
          >
            {isExpanded ? (
              <>
                收起
                <svg className="w-4 h-4 ml-1.5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </>
            ) : (
              <>
                显示全部 ({data.length} 条)
                <svg className="w-4 h-4 ml-1.5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
}
