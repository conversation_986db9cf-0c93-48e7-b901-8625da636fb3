import { useState } from 'react';

interface VulnerabilityItemProps {
  title: string;
  description: string;
  recommendation: string;
  paths: string[];
  module: string;
  cve: string;
  cvss: number;
  showDivider?: boolean;
}

export function VulnerabilityItem({
  title,
  description,
  recommendation,
  paths,
  module,
  cve,
  cvss,
  showDivider,
}: VulnerabilityItemProps) {
  const [isPathsExpanded, setIsPathsExpanded] = useState(false);
  const PATHS_DISPLAY_LIMIT = 5;

  return (
    <div className="vulnerability-item">
      {showDivider && <div className="border-t my-6" />}
      <h4 className="text-lg font-semibold mb-3">{title}</h4>

      <MetaInfo module={module} cve={cve} cvss={cvss} />

      <div
        className="mb-3 text-gray-700"
        dangerouslySetInnerHTML={{
          __html: description,
        }}
      />

      <div className="mb-3">
        <strong>修复建议:</strong> {recommendation}
      </div>

      <VulnerabilityPaths
        paths={paths}
        isExpanded={isPathsExpanded}
        displayLimit={PATHS_DISPLAY_LIMIT}
        onToggle={() => setIsPathsExpanded(!isPathsExpanded)}
      />
    </div>
  );
}

function MetaInfo({ module, cve, cvss }: { module: string; cve: string; cvss: number }) {
  return (
    <div className="grid grid-cols-3 gap-4 mb-4 text-sm text-gray-600">
      <div>
        <strong>模块:</strong> {module}
      </div>
      <div>
        <strong>CVE:</strong> {cve}
      </div>
      <div>
        <strong>CVSS:</strong> {cvss}
      </div>
    </div>
  );
}

interface VulnerabilityPathsProps {
  paths: string[];
  isExpanded: boolean;
  displayLimit: number;
  onToggle: () => void;
}

function VulnerabilityPaths({ paths, isExpanded, displayLimit, onToggle }: VulnerabilityPathsProps) {
  const displayPaths = isExpanded ? paths : paths.slice(0, displayLimit);
  const hasMore = paths.length > displayLimit;

  return (
    <div className="relative">
      <strong>受影响路径:</strong>
      <div className="mt-2 bg-gray-50 p-3 rounded-md space-y-2 text-sm font-mono max-h-[300px] overflow-y-auto">
        {displayPaths.map((path, index) => (
          <div key={index} className="break-all">
            {path}
          </div>
        ))}
      </div>

      {hasMore && (
        <div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggle();
            }}
            className="mt-2 text-blue-600 hover:text-blue-800 transition-colors text-sm flex items-center gap-1"
          >
            {isExpanded ? '收起' : `显示更多 (${paths.length - displayLimit})`}
            <svg
              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
}
