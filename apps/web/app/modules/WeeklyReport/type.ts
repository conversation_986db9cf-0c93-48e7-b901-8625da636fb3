import type { TrendKey } from '~/components/TrendWrap';

export interface TemplateData {
  projectName: string;
  reportDate: string;
  vulnerabilityDistribution: {
    items: VulnerabilityDistributionItem[];
  };
  vulnerabilitySummary: VulnerabilitySummaryItem[];
  vulnerabilityDetails: VulnerabilityDetail[];
  keyFindings: any[]; // 根据实际数据结构补充类型
  dashboardCards: DashboardCard[];
  recentlyFixed: any[]; // 根据实际数据结构补充类型
  errorLicenses: LicenseData[];
  unknownLicenses: LicenseData[];
  licenseDistribution: LicenseDistribution[];
  duplicateFiles: DuplicateFile[];
  repeatabilityCards: RepeatabilityCard[];
  contactEmail: string;
  currentYear: string;
  historyReportUrl: string;
  recommendations: any[]; // 根据实际数据结构补充类型
  history: any[]; // 根据实际数据结构补充类型
}

export interface VulnerabilityDistributionItem {
  label: string;
  count: number;
  level: string;
}

export interface VulnerabilitySummaryItem {
  severity: string;
  level: 'critical' | 'high' | 'moderate' | 'low' | 'info';
  description: string;
  count: number;
}

export interface VulnerabilityDetail {
  level: string;
  severity: string;
  title: string;
  module: string;
  cve: string;
  cvss: number;
  description: string;
  recommendation: string;
  paths: string[];
}

export interface DashboardCard {
  title: string;
  value: string | number;
  trend: {
    direction: TrendKey;
    icon: string;
    value: string;
  };
}

export interface LicenseData {
  licenses: string;
  repository: string;
  publisher: string;
  email?: string;
  url: string;
  path: string;
  licenseFile: string;
  name: string;
  status: {
    class: 'warning' | 'error';
    text: string;
  };
}

export interface LicenseDistribution {
  name: string;
  count: number;
}

export interface DuplicateFile {
  lines: number;
  tokens: number;
  title: string;
  status: {
    class: 'error' | 'warning' | 'success';
    text: string;
  };
}

export interface RepeatabilityCard {
  title: string;
  value: string;
  trend: {
    direction: TrendKey;
    icon: string;
    value: string;
  };
}

export interface Resolve {
  id: number;
  path: string;
  dev: boolean;
  optional: boolean;
  bundled: boolean;
}

export interface Action {
  action: string;
  resolves: Resolve[];
  module: string;
  target: string;
  depth: number;
}

export interface Advisory {
  findings: {
    version: string;
    paths: string[];
  }[];
  found_by: null;
  deleted: null;
  references: string;
  created: string;
  id: number;
  npm_advisory_id: null;
  overview: string;
  reported_by: null;
  title: string;
  metadata: null;
  cves: string[];
  access: string;
  severity: string;
  module_name: string;
  vulnerable_versions: string;
  github_advisory_id: string;
  recommendation: string;
  patched_versions: string;
  updated: string;
  cvss: {
    score: number;
    vectorString: null;
  };
  cwe: string[];
  url: string;
}

export interface AuditData {
  actions: Action[];
  advisories: {
    [key: string]: Advisory;
  };
  muted: string[];
  metadata: {
    vulnerabilities: {
      info: number;
      low: number;
      moderate: number;
      high: number;
      critical: number;
    };
    dependencies: number;
    devDependencies: number;
    optionalDependencies: number;
    totalDependencies: number;
  };
}

export interface UnknownLicenseData {
  licenses: string;
  repository: string;
  publisher: string;
  email?: string;
  url: string;
  path: string;
  licenseFile: string;
}

export interface LicenseData {
  errorLicenses: string[];
  unknownLicenses: UnknownLicenseData[];
  top5Licenses: {
    name: string;
    count: number;
  }[];
}

export interface SourceOrTargetFile {
  filePath: string;
  start: {
    line: number;
    column: number;
  };
  end: {
    line: number;
    column: number;
  };
}

export interface Clone {
  format: string;
  lines: number;
  tokens: number;
  sourceFile: SourceOrTargetFile;
  targetFile: SourceOrTargetFile;
  sourceStr: string;
}

export interface Meta {
  data: {
    format: string;
    filesAnalyzed: string;
    totalLines: string;
    totalTokens: string;
    clonesFound: string;
    duplicatedLines: string;
    duplicatedTokens: string;
  }[];
  format: string;
  filesAnalyzed: string;
  totalLines: string;
  totalTokens: string;
  clonesFound: string;
  duplicatedLines: string;
  duplicatedTokens: string;
}

export interface CpdData {
  clone: Clone[];
  meta: Meta;
}

export interface ReportJsonData {
  templateData: TemplateData;
  auditData: AuditData;
  licenseData: LicenseData;
  cpdData: CpdData;
  timestamp: number;
}
