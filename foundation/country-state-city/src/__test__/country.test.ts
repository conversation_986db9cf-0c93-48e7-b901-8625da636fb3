/* eslint-disable no-shadow */
import { Country } from '../index';
import { ICountry } from '../interface';

const executeAllTests = (Country: any) => {
  describe('Check for Country Module', () => {
    it('Check for Country By Code', () => {
      const code = 'US';
      const country: ICountry = Country.getCountryByCode(code);

      expect(country).toEqual({
        name: 'United States',
        isoCode: 'US',
        flag: '🇺🇸',
        phonecode: '1',
        currency: 'USD',
        latitude: '38.00000000',
        longitude: '-97.00000000',
        timezones: [
          {
            zoneName: 'America/Adak',
            gmtOffset: -36000,
            gmtOffsetName: 'UTC-10:00',
            abbreviation: 'HST',
            tzName: 'Hawaii–Aleutian Standard Time',
          },
          {
            zoneName: 'America/Anchorage',
            gmtOffset: -32400,
            gmtOffsetName: 'UTC-09:00',
            abbreviation: 'AKST',
            tzName: 'Alaska Standard Time',
          },
          {
            zoneName: 'America/Boise',
            gmtOffset: -25200,
            gmtOffsetName: 'UTC-07:00',
            abbreviation: 'MST',
            tzName: 'Mountain Standard Time (North America',
          },
          {
            zoneName: 'America/Chicago',
            gmtOffset: -21600,
            gmtOffsetName: 'UTC-06:00',
            abbreviation: 'CST',
            tzName: 'Central Standard Time (North America',
          },
          {
            zoneName: 'America/Denver',
            gmtOffset: -25200,
            gmtOffsetName: 'UTC-07:00',
            abbreviation: 'MST',
            tzName: 'Mountain Standard Time (North America',
          },
          {
            zoneName: 'America/Detroit',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Indiana/Indianapolis',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Indiana/Knox',
            gmtOffset: -21600,
            gmtOffsetName: 'UTC-06:00',
            abbreviation: 'CST',
            tzName: 'Central Standard Time (North America',
          },
          {
            zoneName: 'America/Indiana/Marengo',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Indiana/Petersburg',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Indiana/Tell_City',
            gmtOffset: -21600,
            gmtOffsetName: 'UTC-06:00',
            abbreviation: 'CST',
            tzName: 'Central Standard Time (North America',
          },
          {
            zoneName: 'America/Indiana/Vevay',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Indiana/Vincennes',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Indiana/Winamac',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Juneau',
            gmtOffset: -32400,
            gmtOffsetName: 'UTC-09:00',
            abbreviation: 'AKST',
            tzName: 'Alaska Standard Time',
          },
          {
            zoneName: 'America/Kentucky/Louisville',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Kentucky/Monticello',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Los_Angeles',
            gmtOffset: -28800,
            gmtOffsetName: 'UTC-08:00',
            abbreviation: 'PST',
            tzName: 'Pacific Standard Time (North America',
          },
          {
            zoneName: 'America/Menominee',
            gmtOffset: -21600,
            gmtOffsetName: 'UTC-06:00',
            abbreviation: 'CST',
            tzName: 'Central Standard Time (North America',
          },
          {
            zoneName: 'America/Metlakatla',
            gmtOffset: -32400,
            gmtOffsetName: 'UTC-09:00',
            abbreviation: 'AKST',
            tzName: 'Alaska Standard Time',
          },
          {
            zoneName: 'America/New_York',
            gmtOffset: -18000,
            gmtOffsetName: 'UTC-05:00',
            abbreviation: 'EST',
            tzName: 'Eastern Standard Time (North America',
          },
          {
            zoneName: 'America/Nome',
            gmtOffset: -32400,
            gmtOffsetName: 'UTC-09:00',
            abbreviation: 'AKST',
            tzName: 'Alaska Standard Time',
          },
          {
            zoneName: 'America/North_Dakota/Beulah',
            gmtOffset: -21600,
            gmtOffsetName: 'UTC-06:00',
            abbreviation: 'CST',
            tzName: 'Central Standard Time (North America',
          },
          {
            zoneName: 'America/North_Dakota/Center',
            gmtOffset: -21600,
            gmtOffsetName: 'UTC-06:00',
            abbreviation: 'CST',
            tzName: 'Central Standard Time (North America',
          },
          {
            zoneName: 'America/North_Dakota/New_Salem',
            gmtOffset: -21600,
            gmtOffsetName: 'UTC-06:00',
            abbreviation: 'CST',
            tzName: 'Central Standard Time (North America',
          },
          {
            zoneName: 'America/Phoenix',
            gmtOffset: -25200,
            gmtOffsetName: 'UTC-07:00',
            abbreviation: 'MST',
            tzName: 'Mountain Standard Time (North America',
          },
          {
            zoneName: 'America/Sitka',
            gmtOffset: -32400,
            gmtOffsetName: 'UTC-09:00',
            abbreviation: 'AKST',
            tzName: 'Alaska Standard Time',
          },
          {
            zoneName: 'America/Yakutat',
            gmtOffset: -32400,
            gmtOffsetName: 'UTC-09:00',
            abbreviation: 'AKST',
            tzName: 'Alaska Standard Time',
          },
          {
            zoneName: 'Pacific/Honolulu',
            gmtOffset: -36000,
            gmtOffsetName: 'UTC-10:00',
            abbreviation: 'HST',
            tzName: 'Hawaii–Aleutian Standard Time',
          },
        ],
      });
    });

    test('Check for Country By undefined Code ', () => {
      let code;
      const country: ICountry = Country.getCountryByCode(code);
      expect(country).toEqual(code);
    });
  });
};

export default executeAllTests;
executeAllTests(Country);
