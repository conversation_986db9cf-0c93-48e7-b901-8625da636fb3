[{"name": "A<PERSON>s Green", "countryCode": "US", "stateCode": "CO", "latitude": "39.55666000", "longitude": "-104.89609000"}, {"name": "Adams County", "countryCode": "US", "stateCode": "CO", "latitude": "39.87363000", "longitude": "-104.33791000"}, {"name": "Air Force Academy", "countryCode": "US", "stateCode": "CO", "latitude": "38.99425000", "longitude": "-104.86375000"}, {"name": "Akron", "countryCode": "US", "stateCode": "CO", "latitude": "40.16054000", "longitude": "-103.21438000"}, {"name": "Alamosa", "countryCode": "US", "stateCode": "CO", "latitude": "37.46945000", "longitude": "-105.87002000"}, {"name": "Alamosa County", "countryCode": "US", "stateCode": "CO", "latitude": "37.57289000", "longitude": "-105.78829000"}, {"name": "Alamosa East", "countryCode": "US", "stateCode": "CO", "latitude": "37.47735000", "longitude": "-105.84217000"}, {"name": "Applewood", "countryCode": "US", "stateCode": "CO", "latitude": "39.75778000", "longitude": "-105.16250000"}, {"name": "Arapahoe County", "countryCode": "US", "stateCode": "CO", "latitude": "39.64977000", "longitude": "-104.33924000"}, {"name": "Archuleta County", "countryCode": "US", "stateCode": "CO", "latitude": "37.19360000", "longitude": "-107.04833000"}, {"name": "Aristocrat Ranchettes", "countryCode": "US", "stateCode": "CO", "latitude": "40.10915000", "longitude": "-104.76247000"}, {"name": "Arvada", "countryCode": "US", "stateCode": "CO", "latitude": "39.80276000", "longitude": "-105.08748000"}, {"name": "Aspen", "countryCode": "US", "stateCode": "CO", "latitude": "39.19110000", "longitude": "-106.81754000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.58248000", "longitude": "-104.73191000"}, {"name": "Aurora", "countryCode": "US", "stateCode": "CO", "latitude": "39.72943000", "longitude": "-104.83192000"}, {"name": "Avon", "countryCode": "US", "stateCode": "CO", "latitude": "39.63137000", "longitude": "-106.52225000"}, {"name": "Baca County", "countryCode": "US", "stateCode": "CO", "latitude": "37.31918000", "longitude": "-102.56047000"}, {"name": "Basalt", "countryCode": "US", "stateCode": "CO", "latitude": "39.36887000", "longitude": "-107.03282000"}, {"name": "Battlement Mesa", "countryCode": "US", "stateCode": "CO", "latitude": "39.44137000", "longitude": "-108.02507000"}, {"name": "Bayfield", "countryCode": "US", "stateCode": "CO", "latitude": "37.22556000", "longitude": "-107.59811000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.75887000", "longitude": "-104.42746000"}, {"name": "Bent County", "countryCode": "US", "stateCode": "CO", "latitude": "37.95509000", "longitude": "-103.07170000"}, {"name": "Berkley", "countryCode": "US", "stateCode": "CO", "latitude": "39.80443000", "longitude": "-105.02609000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.30832000", "longitude": "-105.08109000"}, {"name": "Black Forest", "countryCode": "US", "stateCode": "CO", "latitude": "39.01305000", "longitude": "-104.70081000"}, {"name": "Boulder", "countryCode": "US", "stateCode": "CO", "latitude": "40.01499000", "longitude": "-105.27055000"}, {"name": "Boulder County", "countryCode": "US", "stateCode": "CO", "latitude": "40.09246000", "longitude": "-105.35770000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.48165000", "longitude": "-106.03835000"}, {"name": "Brighton", "countryCode": "US", "stateCode": "CO", "latitude": "39.98526000", "longitude": "-104.82053000"}, {"name": "Broomfield", "countryCode": "US", "stateCode": "CO", "latitude": "39.92054000", "longitude": "-105.08665000"}, {"name": "Broomfield County", "countryCode": "US", "stateCode": "CO", "latitude": "39.95413000", "longitude": "-105.05266000"}, {"name": "Brush", "countryCode": "US", "stateCode": "CO", "latitude": "40.25887000", "longitude": "-103.62384000"}, {"name": "Buena Vista", "countryCode": "US", "stateCode": "CO", "latitude": "38.84222000", "longitude": "-106.13113000"}, {"name": "Burlington", "countryCode": "US", "stateCode": "CO", "latitude": "39.30611000", "longitude": "-102.26936000"}, {"name": "Byers", "countryCode": "US", "stateCode": "CO", "latitude": "39.71137000", "longitude": "-104.22774000"}, {"name": "Campion", "countryCode": "US", "stateCode": "CO", "latitude": "40.34943000", "longitude": "-105.07776000"}, {"name": "Carbondale", "countryCode": "US", "stateCode": "CO", "latitude": "39.40221000", "longitude": "-107.21116000"}, {"name": "Carriage Club", "countryCode": "US", "stateCode": "CO", "latitude": "39.53249000", "longitude": "-104.90109000"}, {"name": "Cascade-Chipita Park", "countryCode": "US", "stateCode": "CO", "latitude": "38.94354000", "longitude": "-105.00237000"}, {"name": "Castle Pines", "countryCode": "US", "stateCode": "CO", "latitude": "39.45804000", "longitude": "-104.89609000"}, {"name": "Castle Pines North", "countryCode": "US", "stateCode": "CO", "latitude": "39.47174000", "longitude": "-104.89482000"}, {"name": "Castle Rock", "countryCode": "US", "stateCode": "CO", "latitude": "39.37221000", "longitude": "-104.85609000"}, {"name": "Castlewood", "countryCode": "US", "stateCode": "CO", "latitude": "39.58471000", "longitude": "-104.90109000"}, {"name": "Cañon City", "countryCode": "US", "stateCode": "CO", "latitude": "38.44098000", "longitude": "-105.24245000"}, {"name": "Cedaredge", "countryCode": "US", "stateCode": "CO", "latitude": "38.90165000", "longitude": "-107.92645000"}, {"name": "Centennial", "countryCode": "US", "stateCode": "CO", "latitude": "39.57916000", "longitude": "-104.87692000"}, {"name": "Center", "countryCode": "US", "stateCode": "CO", "latitude": "37.75306000", "longitude": "-106.10864000"}, {"name": "Central City", "countryCode": "US", "stateCode": "CO", "latitude": "39.80193000", "longitude": "-105.51416000"}, {"name": "Chaffee County", "countryCode": "US", "stateCode": "CO", "latitude": "38.74690000", "longitude": "-106.19407000"}, {"name": "Cherry Creek", "countryCode": "US", "stateCode": "CO", "latitude": "39.63455000", "longitude": "-104.88286000"}, {"name": "Cherry Hills Village", "countryCode": "US", "stateCode": "CO", "latitude": "39.64165000", "longitude": "-104.95943000"}, {"name": "Cheyenne County", "countryCode": "US", "stateCode": "CO", "latitude": "38.82794000", "longitude": "-102.60340000"}, {"name": "Cheyenne Wells", "countryCode": "US", "stateCode": "CO", "latitude": "38.82140000", "longitude": "-102.35324000"}, {"name": "Cimarron Hills", "countryCode": "US", "stateCode": "CO", "latitude": "38.85861000", "longitude": "-104.69886000"}, {"name": "Clear Creek County", "countryCode": "US", "stateCode": "CO", "latitude": "39.68910000", "longitude": "-105.64436000"}, {"name": "Clifton", "countryCode": "US", "stateCode": "CO", "latitude": "39.09193000", "longitude": "-108.44898000"}, {"name": "Coal Creek", "countryCode": "US", "stateCode": "CO", "latitude": "39.90638000", "longitude": "-105.37749000"}, {"name": "Colorado City", "countryCode": "US", "stateCode": "CO", "latitude": "37.94529000", "longitude": "-104.83526000"}, {"name": "Colorado Springs", "countryCode": "US", "stateCode": "CO", "latitude": "38.83388000", "longitude": "-104.82136000"}, {"name": "Columbine", "countryCode": "US", "stateCode": "CO", "latitude": "39.58777000", "longitude": "-105.06943000"}, {"name": "Columbine Valley", "countryCode": "US", "stateCode": "CO", "latitude": "39.60110000", "longitude": "-105.03221000"}, {"name": "Commerce City", "countryCode": "US", "stateCode": "CO", "latitude": "39.80832000", "longitude": "-104.93387000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "37.08835000", "longitude": "-106.01974000"}, {"name": "Conejos County", "countryCode": "US", "stateCode": "CO", "latitude": "37.20070000", "longitude": "-106.19163000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "37.34888000", "longitude": "-108.58593000"}, {"name": "Costilla County", "countryCode": "US", "stateCode": "CO", "latitude": "37.27810000", "longitude": "-105.42827000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.51525000", "longitude": "-107.54645000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "37.84917000", "longitude": "-106.92643000"}, {"name": "Crested <PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.86971000", "longitude": "-106.98782000"}, {"name": "Cripple Creek", "countryCode": "US", "stateCode": "CO", "latitude": "38.74666000", "longitude": "-105.17831000"}, {"name": "Crowley County", "countryCode": "US", "stateCode": "CO", "latitude": "38.32666000", "longitude": "-103.78483000"}, {"name": "Custer County", "countryCode": "US", "stateCode": "CO", "latitude": "38.10868000", "longitude": "-105.36747000"}, {"name": "Dacono", "countryCode": "US", "stateCode": "CO", "latitude": "40.08471000", "longitude": "-104.93942000"}, {"name": "Del Norte", "countryCode": "US", "stateCode": "CO", "latitude": "37.67889000", "longitude": "-106.35337000"}, {"name": "Delta", "countryCode": "US", "stateCode": "CO", "latitude": "38.74221000", "longitude": "-108.06896000"}, {"name": "Delta County", "countryCode": "US", "stateCode": "CO", "latitude": "38.86137000", "longitude": "-107.86288000"}, {"name": "Denver", "countryCode": "US", "stateCode": "CO", "latitude": "39.73915000", "longitude": "-104.98470000"}, {"name": "Denver County", "countryCode": "US", "stateCode": "CO", "latitude": "39.76204000", "longitude": "-104.87635000"}, {"name": "Derby", "countryCode": "US", "stateCode": "CO", "latitude": "39.83943000", "longitude": "-104.91859000"}, {"name": "Dolores County", "countryCode": "US", "stateCode": "CO", "latitude": "37.75160000", "longitude": "-108.51722000"}, {"name": "Douglas County", "countryCode": "US", "stateCode": "CO", "latitude": "39.32972000", "longitude": "-104.92956000"}, {"name": "Dove Creek", "countryCode": "US", "stateCode": "CO", "latitude": "37.76610000", "longitude": "-108.90594000"}, {"name": "Dove Valley", "countryCode": "US", "stateCode": "CO", "latitude": "39.57771000", "longitude": "-104.82940000"}, {"name": "Durango", "countryCode": "US", "stateCode": "CO", "latitude": "37.27528000", "longitude": "-107.88007000"}, {"name": "Eads", "countryCode": "US", "stateCode": "CO", "latitude": "38.48056000", "longitude": "-102.78186000"}, {"name": "Eagle", "countryCode": "US", "stateCode": "CO", "latitude": "39.65526000", "longitude": "-106.82865000"}, {"name": "Eagle County", "countryCode": "US", "stateCode": "CO", "latitude": "39.62783000", "longitude": "-106.69530000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.53026000", "longitude": "-104.71135000"}, {"name": "Edgewater", "countryCode": "US", "stateCode": "CO", "latitude": "39.75304000", "longitude": "-105.06415000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.64499000", "longitude": "-106.59420000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.39498000", "longitude": "-107.09033000"}, {"name": "El Paso County", "countryCode": "US", "stateCode": "CO", "latitude": "38.83209000", "longitude": "-104.52558000"}, {"name": "Elbert County", "countryCode": "US", "stateCode": "CO", "latitude": "39.28656000", "longitude": "-104.13589000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.36027000", "longitude": "-104.59691000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.83833000", "longitude": "-104.38691000"}, {"name": "Englewood", "countryCode": "US", "stateCode": "CO", "latitude": "39.64777000", "longitude": "-104.98776000"}, {"name": "Erie", "countryCode": "US", "stateCode": "CO", "latitude": "40.05026000", "longitude": "-105.04998000"}, {"name": "Estes Park", "countryCode": "US", "stateCode": "CO", "latitude": "40.37721000", "longitude": "-105.52167000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.37637000", "longitude": "-104.69219000"}, {"name": "Evergreen", "countryCode": "US", "stateCode": "CO", "latitude": "39.63332000", "longitude": "-105.31721000"}, {"name": "Fairplay", "countryCode": "US", "stateCode": "CO", "latitude": "39.22471000", "longitude": "-106.00196000"}, {"name": "Federal Heights", "countryCode": "US", "stateCode": "CO", "latitude": "39.85137000", "longitude": "-104.99859000"}, {"name": "Firestone", "countryCode": "US", "stateCode": "CO", "latitude": "40.11248000", "longitude": "-104.93664000"}, {"name": "Florence", "countryCode": "US", "stateCode": "CO", "latitude": "38.39028000", "longitude": "-105.11860000"}, {"name": "Fort Carson", "countryCode": "US", "stateCode": "CO", "latitude": "38.73749000", "longitude": "-104.78886000"}, {"name": "Fort Collins", "countryCode": "US", "stateCode": "CO", "latitude": "40.58526000", "longitude": "-105.08442000"}, {"name": "Fort Lupton", "countryCode": "US", "stateCode": "CO", "latitude": "40.08471000", "longitude": "-104.81303000"}, {"name": "Fort Morgan", "countryCode": "US", "stateCode": "CO", "latitude": "40.25026000", "longitude": "-103.79995000"}, {"name": "Fountain", "countryCode": "US", "stateCode": "CO", "latitude": "38.68222000", "longitude": "-104.70081000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.12917000", "longitude": "-104.02329000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.94499000", "longitude": "-105.81723000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.09915000", "longitude": "-104.93720000"}, {"name": "Fremont County", "countryCode": "US", "stateCode": "CO", "latitude": "38.47297000", "longitude": "-105.43966000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.57443000", "longitude": "-106.09752000"}, {"name": "Fruita", "countryCode": "US", "stateCode": "CO", "latitude": "39.15887000", "longitude": "-108.72899000"}, {"name": "Fruitvale", "countryCode": "US", "stateCode": "CO", "latitude": "39.08165000", "longitude": "-108.49676000"}, {"name": "Garfield County", "countryCode": "US", "stateCode": "CO", "latitude": "39.59931000", "longitude": "-107.90395000"}, {"name": "Genesee", "countryCode": "US", "stateCode": "CO", "latitude": "39.68582000", "longitude": "-105.27277000"}, {"name": "Georgetown", "countryCode": "US", "stateCode": "CO", "latitude": "39.70610000", "longitude": "-105.69750000"}, {"name": "Gilcrest", "countryCode": "US", "stateCode": "CO", "latitude": "40.28193000", "longitude": "-104.77775000"}, {"name": "Gilpin County", "countryCode": "US", "stateCode": "CO", "latitude": "39.85756000", "longitude": "-105.52253000"}, {"name": "Glendale", "countryCode": "US", "stateCode": "CO", "latitude": "39.70499000", "longitude": "-104.93359000"}, {"name": "Gleneagle", "countryCode": "US", "stateCode": "CO", "latitude": "39.04527000", "longitude": "-104.82442000"}, {"name": "Glenwood Springs", "countryCode": "US", "stateCode": "CO", "latitude": "39.55054000", "longitude": "-107.32478000"}, {"name": "Golden", "countryCode": "US", "stateCode": "CO", "latitude": "39.75554000", "longitude": "-105.22110000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.08610000", "longitude": "-105.93946000"}, {"name": "Grand County", "countryCode": "US", "stateCode": "CO", "latitude": "40.10261000", "longitude": "-106.11836000"}, {"name": "Grand Junction", "countryCode": "US", "stateCode": "CO", "latitude": "39.06387000", "longitude": "-108.55065000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.42331000", "longitude": "-104.70913000"}, {"name": "Greenwood Village", "countryCode": "US", "stateCode": "CO", "latitude": "39.61721000", "longitude": "-104.95081000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.06335000", "longitude": "-105.17107000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.54582000", "longitude": "-106.92532000"}, {"name": "Gunnison County", "countryCode": "US", "stateCode": "CO", "latitude": "38.66680000", "longitude": "-107.03170000"}, {"name": "Gypsum", "countryCode": "US", "stateCode": "CO", "latitude": "39.64693000", "longitude": "-106.95171000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.49529000", "longitude": "-107.25729000"}, {"name": "Highlands Ranch", "countryCode": "US", "stateCode": "CO", "latitude": "39.55388000", "longitude": "-104.96943000"}, {"name": "Hinsdale County", "countryCode": "US", "stateCode": "CO", "latitude": "37.82134000", "longitude": "-107.30031000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.66757000", "longitude": "-104.91797000"}, {"name": "Holyoke", "countryCode": "US", "stateCode": "CO", "latitude": "40.58444000", "longitude": "-102.30241000"}, {"name": "Hot Sulphur Springs", "countryCode": "US", "stateCode": "CO", "latitude": "40.07304000", "longitude": "-106.10280000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.07359000", "longitude": "-104.64302000"}, {"name": "Huerfano County", "countryCode": "US", "stateCode": "CO", "latitude": "37.68468000", "longitude": "-104.96058000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.13610000", "longitude": "-103.46994000"}, {"name": "Idaho Springs", "countryCode": "US", "stateCode": "CO", "latitude": "39.74249000", "longitude": "-105.51361000"}, {"name": "Indian Hills", "countryCode": "US", "stateCode": "CO", "latitude": "39.61665000", "longitude": "-105.23721000"}, {"name": "Inverness", "countryCode": "US", "stateCode": "CO", "latitude": "39.57738000", "longitude": "-104.86135000"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "CO", "latitude": "40.66643000", "longitude": "-106.34279000"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "CO", "latitude": "39.58642000", "longitude": "-105.25047000"}, {"name": "Johnstown", "countryCode": "US", "stateCode": "CO", "latitude": "40.33693000", "longitude": "-104.91220000"}, {"name": "Julesburg", "countryCode": "US", "stateCode": "CO", "latitude": "40.98833000", "longitude": "-102.26435000"}, {"name": "Keenesburg", "countryCode": "US", "stateCode": "CO", "latitude": "40.10832000", "longitude": "-104.51996000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.57582000", "longitude": "-105.11221000"}, {"name": "Kersey", "countryCode": "US", "stateCode": "CO", "latitude": "40.38748000", "longitude": "-104.56163000"}, {"name": "Keystone", "countryCode": "US", "stateCode": "CO", "latitude": "39.59943000", "longitude": "-105.98724000"}, {"name": "Kiowa", "countryCode": "US", "stateCode": "CO", "latitude": "39.34721000", "longitude": "-104.46441000"}, {"name": "Kiowa County", "countryCode": "US", "stateCode": "CO", "latitude": "38.43269000", "longitude": "-102.74034000"}, {"name": "Kit Carson County", "countryCode": "US", "stateCode": "CO", "latitude": "39.30544000", "longitude": "-102.60289000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.65471000", "longitude": "-105.29971000"}, {"name": "<PERSON><PERSON><PERSON>ling", "countryCode": "US", "stateCode": "CO", "latitude": "40.05887000", "longitude": "-106.38892000"}, {"name": "La Junta", "countryCode": "US", "stateCode": "CO", "latitude": "37.98501000", "longitude": "-103.54383000"}, {"name": "La Plata County", "countryCode": "US", "stateCode": "CO", "latitude": "37.28656000", "longitude": "-107.84334000"}, {"name": "La Salle", "countryCode": "US", "stateCode": "CO", "latitude": "40.34887000", "longitude": "-104.70191000"}, {"name": "Lafayette", "countryCode": "US", "stateCode": "CO", "latitude": "39.99360000", "longitude": "-105.08971000"}, {"name": "Lake City", "countryCode": "US", "stateCode": "CO", "latitude": "38.03000000", "longitude": "-107.31533000"}, {"name": "Lake County", "countryCode": "US", "stateCode": "CO", "latitude": "39.20238000", "longitude": "-106.34484000"}, {"name": "Lakewood", "countryCode": "US", "stateCode": "CO", "latitude": "39.70471000", "longitude": "-105.08137000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.08723000", "longitude": "-102.62075000"}, {"name": "Laporte", "countryCode": "US", "stateCode": "CO", "latitude": "40.62633000", "longitude": "-105.13916000"}, {"name": "Larimer County", "countryCode": "US", "stateCode": "CO", "latitude": "40.66641000", "longitude": "-105.46116000"}, {"name": "Las Animas", "countryCode": "US", "stateCode": "CO", "latitude": "38.06667000", "longitude": "-103.22271000"}, {"name": "Las Animas County", "countryCode": "US", "stateCode": "CO", "latitude": "37.31585000", "longitude": "-104.03872000"}, {"name": "Leadville", "countryCode": "US", "stateCode": "CO", "latitude": "39.25082000", "longitude": "-106.29252000"}, {"name": "Leadville North", "countryCode": "US", "stateCode": "CO", "latitude": "39.25760000", "longitude": "-106.30138000"}, {"name": "Limon", "countryCode": "US", "stateCode": "CO", "latitude": "39.26388000", "longitude": "-103.69217000"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "CO", "latitude": "38.98807000", "longitude": "-103.51397000"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "CO", "latitude": "38.42916000", "longitude": "-105.21999000"}, {"name": "Littleton", "countryCode": "US", "stateCode": "CO", "latitude": "39.61332000", "longitude": "-105.01665000"}, {"name": "Lochbuie", "countryCode": "US", "stateCode": "CO", "latitude": "40.00721000", "longitude": "-104.71608000"}, {"name": "Logan County", "countryCode": "US", "stateCode": "CO", "latitude": "40.72468000", "longitude": "-103.11010000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.19581000", "longitude": "-108.81316000"}, {"name": "Lone Tree", "countryCode": "US", "stateCode": "CO", "latitude": "39.55171000", "longitude": "-104.88630000"}, {"name": "Longmont", "countryCode": "US", "stateCode": "CO", "latitude": "40.16721000", "longitude": "-105.10193000"}, {"name": "Louisville", "countryCode": "US", "stateCode": "CO", "latitude": "39.97776000", "longitude": "-105.13193000"}, {"name": "Loveland", "countryCode": "US", "stateCode": "CO", "latitude": "40.39776000", "longitude": "-105.07498000"}, {"name": "Lyons", "countryCode": "US", "stateCode": "CO", "latitude": "40.22471000", "longitude": "-105.27138000"}, {"name": "Mancos", "countryCode": "US", "stateCode": "CO", "latitude": "37.34500000", "longitude": "-108.28925000"}, {"name": "Manitou Springs", "countryCode": "US", "stateCode": "CO", "latitude": "38.85971000", "longitude": "-104.91720000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.23332000", "longitude": "-104.99859000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.03747000", "longitude": "-107.91313000"}, {"name": "Meridian", "countryCode": "US", "stateCode": "CO", "latitude": "39.53957000", "longitude": "-104.84528000"}, {"name": "Mesa County", "countryCode": "US", "stateCode": "CO", "latitude": "39.01828000", "longitude": "-108.46645000"}, {"name": "Milliken", "countryCode": "US", "stateCode": "CO", "latitude": "40.32943000", "longitude": "-104.85525000"}, {"name": "Mineral County", "countryCode": "US", "stateCode": "CO", "latitude": "37.66900000", "longitude": "-106.92409000"}, {"name": "Minturn", "countryCode": "US", "stateCode": "CO", "latitude": "39.58637000", "longitude": "-106.43086000"}, {"name": "Moffat County", "countryCode": "US", "stateCode": "CO", "latitude": "40.61843000", "longitude": "-108.20730000"}, {"name": "Monte Vista", "countryCode": "US", "stateCode": "CO", "latitude": "37.57917000", "longitude": "-106.14808000"}, {"name": "Montezuma County", "countryCode": "US", "stateCode": "CO", "latitude": "37.33841000", "longitude": "-108.59671000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.47832000", "longitude": "-107.87617000"}, {"name": "Montrose County", "countryCode": "US", "stateCode": "CO", "latitude": "38.40218000", "longitude": "-108.26936000"}, {"name": "Monument", "countryCode": "US", "stateCode": "CO", "latitude": "39.09166000", "longitude": "-104.87276000"}, {"name": "Morgan County", "countryCode": "US", "stateCode": "CO", "latitude": "40.26271000", "longitude": "-103.80982000"}, {"name": "Mountain Village", "countryCode": "US", "stateCode": "CO", "latitude": "37.93138000", "longitude": "-107.85645000"}, {"name": "Nederland", "countryCode": "US", "stateCode": "CO", "latitude": "39.96138000", "longitude": "-105.51083000"}, {"name": "New Castle", "countryCode": "US", "stateCode": "CO", "latitude": "39.57276000", "longitude": "-107.53644000"}, {"name": "Niwot", "countryCode": "US", "stateCode": "CO", "latitude": "40.10387000", "longitude": "-105.17082000"}, {"name": "Northglenn", "countryCode": "US", "stateCode": "CO", "latitude": "39.88554000", "longitude": "-104.98720000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.60499000", "longitude": "-107.98229000"}, {"name": "Orchard City", "countryCode": "US", "stateCode": "CO", "latitude": "38.82832000", "longitude": "-107.97090000"}, {"name": "Orchard Mesa", "countryCode": "US", "stateCode": "CO", "latitude": "39.04304000", "longitude": "-108.55232000"}, {"name": "Ordway", "countryCode": "US", "stateCode": "CO", "latitude": "38.21806000", "longitude": "-103.75606000"}, {"name": "Otero County", "countryCode": "US", "stateCode": "CO", "latitude": "37.90270000", "longitude": "-103.71645000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.02277000", "longitude": "-107.67145000"}, {"name": "Ouray County", "countryCode": "US", "stateCode": "CO", "latitude": "38.15550000", "longitude": "-107.76932000"}, {"name": "Pagosa Springs", "countryCode": "US", "stateCode": "CO", "latitude": "37.26945000", "longitude": "-107.00976000"}, {"name": "Palisade", "countryCode": "US", "stateCode": "CO", "latitude": "39.11026000", "longitude": "-108.35092000"}, {"name": "Palmer Lake", "countryCode": "US", "stateCode": "CO", "latitude": "39.12221000", "longitude": "-104.91720000"}, {"name": "Paonia", "countryCode": "US", "stateCode": "CO", "latitude": "38.86832000", "longitude": "-107.59200000"}, {"name": "Parachute", "countryCode": "US", "stateCode": "CO", "latitude": "39.45192000", "longitude": "-108.05285000"}, {"name": "Park County", "countryCode": "US", "stateCode": "CO", "latitude": "39.11930000", "longitude": "-105.71717000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.51860000", "longitude": "-104.76136000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.42500000", "longitude": "-105.02276000"}, {"name": "Perry Park", "countryCode": "US", "stateCode": "CO", "latitude": "39.25666000", "longitude": "-104.99248000"}, {"name": "Phillips County", "countryCode": "US", "stateCode": "CO", "latitude": "40.59388000", "longitude": "-102.35758000"}, {"name": "Pitkin County", "countryCode": "US", "stateCode": "CO", "latitude": "39.21711000", "longitude": "-106.91658000"}, {"name": "Platteville", "countryCode": "US", "stateCode": "CO", "latitude": "40.21498000", "longitude": "-104.82275000"}, {"name": "Ponderosa Park", "countryCode": "US", "stateCode": "CO", "latitude": "39.40832000", "longitude": "-104.65108000"}, {"name": "Prowers County", "countryCode": "US", "stateCode": "CO", "latitude": "37.95518000", "longitude": "-102.39335000"}, {"name": "Pueblo", "countryCode": "US", "stateCode": "CO", "latitude": "38.25445000", "longitude": "-104.60914000"}, {"name": "Pueblo County", "countryCode": "US", "stateCode": "CO", "latitude": "38.17342000", "longitude": "-104.51285000"}, {"name": "Pueblo West", "countryCode": "US", "stateCode": "CO", "latitude": "38.35000000", "longitude": "-104.72275000"}, {"name": "Rangely", "countryCode": "US", "stateCode": "CO", "latitude": "40.08748000", "longitude": "-108.80483000"}, {"name": "Redlands", "countryCode": "US", "stateCode": "CO", "latitude": "39.07887000", "longitude": "-108.63565000"}, {"name": "Rifle", "countryCode": "US", "stateCode": "CO", "latitude": "39.53470000", "longitude": "-107.78312000"}, {"name": "Rio Blanco County", "countryCode": "US", "stateCode": "CO", "latitude": "39.97984000", "longitude": "-108.21721000"}, {"name": "Rio Grande County", "countryCode": "US", "stateCode": "CO", "latitude": "37.58252000", "longitude": "-106.38321000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "38.05251000", "longitude": "-103.72023000"}, {"name": "Routt County", "countryCode": "US", "stateCode": "CO", "latitude": "40.48507000", "longitude": "-106.99119000"}, {"name": "Roxborough Park", "countryCode": "US", "stateCode": "CO", "latitude": "39.47388000", "longitude": "-105.08526000"}, {"name": "Saguache", "countryCode": "US", "stateCode": "CO", "latitude": "38.08750000", "longitude": "-106.14197000"}, {"name": "Saguache County", "countryCode": "US", "stateCode": "CO", "latitude": "38.08055000", "longitude": "-106.28156000"}, {"name": "Salida", "countryCode": "US", "stateCode": "CO", "latitude": "38.53472000", "longitude": "-105.99890000"}, {"name": "San Juan County", "countryCode": "US", "stateCode": "CO", "latitude": "37.76404000", "longitude": "-107.67615000"}, {"name": "San Luis", "countryCode": "US", "stateCode": "CO", "latitude": "37.20085000", "longitude": "-105.42390000"}, {"name": "San Miguel County", "countryCode": "US", "stateCode": "CO", "latitude": "38.00374000", "longitude": "-108.40583000"}, {"name": "Security-Widefield", "countryCode": "US", "stateCode": "CO", "latitude": "38.74728000", "longitude": "-104.71439000"}, {"name": "Sedgwick County", "countryCode": "US", "stateCode": "CO", "latitude": "40.87591000", "longitude": "-102.35179000"}, {"name": "Severance", "countryCode": "US", "stateCode": "CO", "latitude": "40.52415000", "longitude": "-104.85108000"}, {"name": "Shaw Heights", "countryCode": "US", "stateCode": "CO", "latitude": "39.85250000", "longitude": "-105.04306000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.64693000", "longitude": "-105.02526000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.83776000", "longitude": "-105.00137000"}, {"name": "Silt", "countryCode": "US", "stateCode": "CO", "latitude": "39.54859000", "longitude": "-107.65617000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.63214000", "longitude": "-106.07428000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "37.81194000", "longitude": "-107.66451000"}, {"name": "Snowmass Village", "countryCode": "US", "stateCode": "CO", "latitude": "39.21304000", "longitude": "-106.93782000"}, {"name": "Southglenn", "countryCode": "US", "stateCode": "CO", "latitude": "39.58721000", "longitude": "-104.95276000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "CO", "latitude": "37.40835000", "longitude": "-102.61436000"}, {"name": "Steamboat Springs", "countryCode": "US", "stateCode": "CO", "latitude": "40.48498000", "longitude": "-106.83172000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.62554000", "longitude": "-103.20771000"}, {"name": "Stonegate", "countryCode": "US", "stateCode": "CO", "latitude": "39.53082000", "longitude": "-104.80386000"}, {"name": "Strasburg", "countryCode": "US", "stateCode": "CO", "latitude": "39.73832000", "longitude": "-104.32329000"}, {"name": "Stratmoor", "countryCode": "US", "stateCode": "CO", "latitude": "38.77388000", "longitude": "-104.77970000"}, {"name": "Summit County", "countryCode": "US", "stateCode": "CO", "latitude": "39.63417000", "longitude": "-106.11638000"}, {"name": "Superior", "countryCode": "US", "stateCode": "CO", "latitude": "39.95276000", "longitude": "-105.16860000"}, {"name": "Teller County", "countryCode": "US", "stateCode": "CO", "latitude": "38.88217000", "longitude": "-105.16183000"}, {"name": "Telluride", "countryCode": "US", "stateCode": "CO", "latitude": "37.93749000", "longitude": "-107.81229000"}, {"name": "The Pinery", "countryCode": "US", "stateCode": "CO", "latitude": "39.45527000", "longitude": "-104.73442000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.86804000", "longitude": "-104.97192000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.97804000", "longitude": "-104.87331000"}, {"name": "Towaoc", "countryCode": "US", "stateCode": "CO", "latitude": "37.20444000", "longitude": "-108.72954000"}, {"name": "Trinidad", "countryCode": "US", "stateCode": "CO", "latitude": "37.16946000", "longitude": "-104.50054000"}, {"name": "Twin Lakes", "countryCode": "US", "stateCode": "CO", "latitude": "39.82499000", "longitude": "-105.00470000"}, {"name": "Upper Bear Creek", "countryCode": "US", "stateCode": "CO", "latitude": "39.62385000", "longitude": "-105.41780000"}, {"name": "Vail", "countryCode": "US", "stateCode": "CO", "latitude": "39.64026000", "longitude": "-106.37420000"}, {"name": "Walden", "countryCode": "US", "stateCode": "CO", "latitude": "40.73164000", "longitude": "-106.28364000"}, {"name": "Walsenburg", "countryCode": "US", "stateCode": "CO", "latitude": "37.62418000", "longitude": "-104.78026000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "CO", "latitude": "39.97106000", "longitude": "-103.20125000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "39.83665000", "longitude": "-104.95915000"}, {"name": "Weld County", "countryCode": "US", "stateCode": "CO", "latitude": "40.55484000", "longitude": "-104.39253000"}, {"name": "Wellington", "countryCode": "US", "stateCode": "CO", "latitude": "40.70387000", "longitude": "-105.00859000"}, {"name": "West Pleasant View", "countryCode": "US", "stateCode": "CO", "latitude": "39.73256000", "longitude": "-105.17852000"}, {"name": "Westcliffe", "countryCode": "US", "stateCode": "CO", "latitude": "38.13472000", "longitude": "-105.46584000"}, {"name": "Westminster", "countryCode": "US", "stateCode": "CO", "latitude": "39.83665000", "longitude": "-105.03720000"}, {"name": "Wheat Ridge", "countryCode": "US", "stateCode": "CO", "latitude": "39.76610000", "longitude": "-105.07721000"}, {"name": "Windsor", "countryCode": "US", "stateCode": "CO", "latitude": "40.47748000", "longitude": "-104.90136000"}, {"name": "Woodland Park", "countryCode": "US", "stateCode": "CO", "latitude": "38.99388000", "longitude": "-105.05693000"}, {"name": "Woodmoor", "countryCode": "US", "stateCode": "CO", "latitude": "39.10138000", "longitude": "-104.84748000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.07582000", "longitude": "-102.22325000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "CO", "latitude": "40.12221000", "longitude": "-102.72521000"}, {"name": "Yuma County", "countryCode": "US", "stateCode": "CO", "latitude": "40.00290000", "longitude": "-102.42423000"}]