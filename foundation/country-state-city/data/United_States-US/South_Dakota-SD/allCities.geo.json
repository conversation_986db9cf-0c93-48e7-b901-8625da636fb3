[{"name": "Aberdeen", "countryCode": "US", "stateCode": "SD", "latitude": "45.46470000", "longitude": "-98.48648000"}, {"name": "Alexandria", "countryCode": "US", "stateCode": "SD", "latitude": "43.65359000", "longitude": "-97.78285000"}, {"name": "Armour", "countryCode": "US", "stateCode": "SD", "latitude": "43.31860000", "longitude": "-98.34675000"}, {"name": "Aurora County", "countryCode": "US", "stateCode": "SD", "latitude": "43.71801000", "longitude": "-98.56155000"}, {"name": "Baltic", "countryCode": "US", "stateCode": "SD", "latitude": "43.76136000", "longitude": "-96.74033000"}, {"name": "Beadle County", "countryCode": "US", "stateCode": "SD", "latitude": "44.41448000", "longitude": "-98.27811000"}, {"name": "Belle <PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.67137000", "longitude": "-103.85215000"}, {"name": "Bennett County", "countryCode": "US", "stateCode": "SD", "latitude": "43.19492000", "longitude": "-101.66397000"}, {"name": "Beresford", "countryCode": "US", "stateCode": "SD", "latitude": "43.08054000", "longitude": "-96.77366000"}, {"name": "<PERSON>ison", "countryCode": "US", "stateCode": "SD", "latitude": "45.52026000", "longitude": "-102.46127000"}, {"name": "Blackhawk", "countryCode": "US", "stateCode": "SD", "latitude": "44.15110000", "longitude": "-103.30796000"}, {"name": "Bon Homme County", "countryCode": "US", "stateCode": "SD", "latitude": "42.98837000", "longitude": "-97.88463000"}, {"name": "Box Elder", "countryCode": "US", "stateCode": "SD", "latitude": "44.11249000", "longitude": "-103.06823000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.59470000", "longitude": "-96.57199000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "45.79162000", "longitude": "-97.75094000"}, {"name": "Brookings", "countryCode": "US", "stateCode": "SD", "latitude": "44.31136000", "longitude": "-96.79839000"}, {"name": "Brookings County", "countryCode": "US", "stateCode": "SD", "latitude": "44.36968000", "longitude": "-96.79042000"}, {"name": "Brown County", "countryCode": "US", "stateCode": "SD", "latitude": "45.58972000", "longitude": "-98.35161000"}, {"name": "Brule County", "countryCode": "US", "stateCode": "SD", "latitude": "43.71806000", "longitude": "-99.08094000"}, {"name": "Buffalo", "countryCode": "US", "stateCode": "SD", "latitude": "45.58416000", "longitude": "-103.54603000"}, {"name": "Buffalo County", "countryCode": "US", "stateCode": "SD", "latitude": "44.07635000", "longitude": "-99.20496000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.18250000", "longitude": "-99.29205000"}, {"name": "Butte County", "countryCode": "US", "stateCode": "SD", "latitude": "44.90583000", "longitude": "-103.50802000"}, {"name": "Campbell County", "countryCode": "US", "stateCode": "SD", "latitude": "45.77113000", "longitude": "-100.05163000"}, {"name": "Canton", "countryCode": "US", "stateCode": "SD", "latitude": "43.30081000", "longitude": "-96.59282000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.81083000", "longitude": "-99.33066000"}, {"name": "Charles <PERSON> County", "countryCode": "US", "stateCode": "SD", "latitude": "43.20791000", "longitude": "-98.58789000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.87774000", "longitude": "-97.73314000"}, {"name": "Clark County", "countryCode": "US", "stateCode": "SD", "latitude": "44.85825000", "longitude": "-97.72950000"}, {"name": "Clay County", "countryCode": "US", "stateCode": "SD", "latitude": "42.91465000", "longitude": "-96.97566000"}, {"name": "Clear Lake", "countryCode": "US", "stateCode": "SD", "latitude": "44.74580000", "longitude": "-96.68256000"}, {"name": "Codington County", "countryCode": "US", "stateCode": "SD", "latitude": "44.97785000", "longitude": "-97.18862000"}, {"name": "Colonial Pine Hills", "countryCode": "US", "stateCode": "SD", "latitude": "44.00777000", "longitude": "-103.31546000"}, {"name": "Corson County", "countryCode": "US", "stateCode": "SD", "latitude": "45.70860000", "longitude": "-101.19687000"}, {"name": "Crooks", "countryCode": "US", "stateCode": "SD", "latitude": "43.66470000", "longitude": "-96.81089000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.76665000", "longitude": "-103.59881000"}, {"name": "Custer County", "countryCode": "US", "stateCode": "SD", "latitude": "43.67763000", "longitude": "-103.45154000"}, {"name": "Dakota Dunes", "countryCode": "US", "stateCode": "SD", "latitude": "42.48749000", "longitude": "-96.48642000"}, {"name": "Davison County", "countryCode": "US", "stateCode": "SD", "latitude": "43.67474000", "longitude": "-98.14600000"}, {"name": "Day County", "countryCode": "US", "stateCode": "SD", "latitude": "45.36715000", "longitude": "-97.60741000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.38747000", "longitude": "-97.55035000"}, {"name": "Deadwood", "countryCode": "US", "stateCode": "SD", "latitude": "44.37665000", "longitude": "-103.72964000"}, {"name": "Dell Rapids", "countryCode": "US", "stateCode": "SD", "latitude": "43.82608000", "longitude": "-96.70616000"}, {"name": "Deuel County", "countryCode": "US", "stateCode": "SD", "latitude": "44.76006000", "longitude": "-96.66797000"}, {"name": "Dewey County", "countryCode": "US", "stateCode": "SD", "latitude": "45.15662000", "longitude": "-100.87186000"}, {"name": "Douglas County", "countryCode": "US", "stateCode": "SD", "latitude": "43.38692000", "longitude": "-98.36610000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "45.04748000", "longitude": "-101.60099000"}, {"name": "Eagle Butte", "countryCode": "US", "stateCode": "SD", "latitude": "45.00248000", "longitude": "-101.23349000"}, {"name": "Edmunds County", "countryCode": "US", "stateCode": "SD", "latitude": "45.41880000", "longitude": "-99.21533000"}, {"name": "Elk Point", "countryCode": "US", "stateCode": "SD", "latitude": "42.68333000", "longitude": "-96.68365000"}, {"name": "Fall River County", "countryCode": "US", "stateCode": "SD", "latitude": "43.23939000", "longitude": "-103.52756000"}, {"name": "Faulk County", "countryCode": "US", "stateCode": "SD", "latitude": "45.07101000", "longitude": "-99.14525000"}, {"name": "Faulkton", "countryCode": "US", "stateCode": "SD", "latitude": "45.03497000", "longitude": "-99.12400000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.04942000", "longitude": "-96.59532000"}, {"name": "Fort Pierre", "countryCode": "US", "stateCode": "SD", "latitude": "44.35359000", "longitude": "-100.37374000"}, {"name": "Fort Thompson", "countryCode": "US", "stateCode": "SD", "latitude": "44.06860000", "longitude": "-99.43788000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.35249000", "longitude": "-97.43729000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.71747000", "longitude": "-96.50282000"}, {"name": "Gettysburg", "countryCode": "US", "stateCode": "SD", "latitude": "45.01165000", "longitude": "-99.95567000"}, {"name": "Grant County", "countryCode": "US", "stateCode": "SD", "latitude": "45.17194000", "longitude": "-96.76769000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.23222000", "longitude": "-99.43038000"}, {"name": "Gregory County", "countryCode": "US", "stateCode": "SD", "latitude": "43.19238000", "longitude": "-99.18561000"}, {"name": "Groton", "countryCode": "US", "stateCode": "SD", "latitude": "45.44746000", "longitude": "-98.09871000"}, {"name": "Haakon County", "countryCode": "US", "stateCode": "SD", "latitude": "44.29443000", "longitude": "-101.53999000"}, {"name": "Hamlin County", "countryCode": "US", "stateCode": "SD", "latitude": "44.67376000", "longitude": "-97.18833000"}, {"name": "Hand County", "countryCode": "US", "stateCode": "SD", "latitude": "44.54778000", "longitude": "-99.00494000"}, {"name": "Hanson County", "countryCode": "US", "stateCode": "SD", "latitude": "43.67482000", "longitude": "-97.78734000"}, {"name": "Harding County", "countryCode": "US", "stateCode": "SD", "latitude": "45.58035000", "longitude": "-103.49577000"}, {"name": "Harrisburg", "countryCode": "US", "stateCode": "SD", "latitude": "43.43137000", "longitude": "-96.69727000"}, {"name": "Hartford", "countryCode": "US", "stateCode": "SD", "latitude": "43.62303000", "longitude": "-96.94255000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.65719000", "longitude": "-97.20507000"}, {"name": "Highmore", "countryCode": "US", "stateCode": "SD", "latitude": "44.52137000", "longitude": "-99.44150000"}, {"name": "Hot Springs", "countryCode": "US", "stateCode": "SD", "latitude": "43.43165000", "longitude": "-103.47436000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.01081000", "longitude": "-97.52674000"}, {"name": "Hughes County", "countryCode": "US", "stateCode": "SD", "latitude": "44.38903000", "longitude": "-99.99605000"}, {"name": "Huron", "countryCode": "US", "stateCode": "SD", "latitude": "44.36332000", "longitude": "-98.21426000"}, {"name": "Hutchinson County", "countryCode": "US", "stateCode": "SD", "latitude": "43.33485000", "longitude": "-97.75442000"}, {"name": "Hyde County", "countryCode": "US", "stateCode": "SD", "latitude": "44.54736000", "longitude": "-99.48711000"}, {"name": "Ipswich", "countryCode": "US", "stateCode": "SD", "latitude": "45.44442000", "longitude": "-99.02928000"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "SD", "latitude": "43.69421000", "longitude": "-101.62813000"}, {"name": "Jerauld County", "countryCode": "US", "stateCode": "SD", "latitude": "44.06635000", "longitude": "-98.62973000"}, {"name": "Jones County", "countryCode": "US", "stateCode": "SD", "latitude": "43.96061000", "longitude": "-100.68972000"}, {"name": "Kadoka", "countryCode": "US", "stateCode": "SD", "latitude": "43.83388000", "longitude": "-101.50987000"}, {"name": "Kennebec", "countryCode": "US", "stateCode": "SD", "latitude": "43.90360000", "longitude": "-99.86178000"}, {"name": "Kingsbury County", "countryCode": "US", "stateCode": "SD", "latitude": "44.36959000", "longitude": "-97.49148000"}, {"name": "Lake Andes", "countryCode": "US", "stateCode": "SD", "latitude": "43.15638000", "longitude": "-98.54147000"}, {"name": "Lake County", "countryCode": "US", "stateCode": "SD", "latitude": "44.02204000", "longitude": "-97.12938000"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "SD", "latitude": "44.35863000", "longitude": "-103.79222000"}, {"name": "Lead", "countryCode": "US", "stateCode": "SD", "latitude": "44.35221000", "longitude": "-103.76520000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "45.94083000", "longitude": "-102.15932000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.35415000", "longitude": "-96.89200000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "45.72275000", "longitude": "-98.94094000"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "SD", "latitude": "43.27892000", "longitude": "-96.72182000"}, {"name": "Lyman County", "countryCode": "US", "stateCode": "SD", "latitude": "43.89582000", "longitude": "-99.84738000"}, {"name": "Madison", "countryCode": "US", "stateCode": "SD", "latitude": "44.00608000", "longitude": "-97.11395000"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "SD", "latitude": "45.75867000", "longitude": "-97.59850000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.17250000", "longitude": "-101.73265000"}, {"name": "McCook County", "countryCode": "US", "stateCode": "SD", "latitude": "43.67431000", "longitude": "-97.36844000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "45.********", "longitude": "-101.********"}, {"name": "McPherson County", "countryCode": "US", "stateCode": "SD", "latitude": "45.********", "longitude": "-99.********"}, {"name": "Meade County", "countryCode": "US", "stateCode": "SD", "latitude": "44.********", "longitude": "-102.********"}, {"name": "Mellette County", "countryCode": "US", "stateCode": "SD", "latitude": "43.********", "longitude": "-100.********"}, {"name": "Milbank", "countryCode": "US", "stateCode": "SD", "latitude": "45.********", "longitude": "-96.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.********", "longitude": "-98.********"}, {"name": "Miner County", "countryCode": "US", "stateCode": "SD", "latitude": "44.********", "longitude": "-97.********"}, {"name": "Minnehaha County", "countryCode": "US", "stateCode": "SD", "latitude": "43.********", "longitude": "-96.********"}, {"name": "Mission", "countryCode": "US", "stateCode": "SD", "latitude": "43.********", "longitude": "-100.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.********", "longitude": "-98.********"}, {"name": "Mobridge", "countryCode": "US", "stateCode": "SD", "latitude": "45.********", "longitude": "-100.********"}, {"name": "Moody County", "countryCode": "US", "stateCode": "SD", "latitude": "44.********", "longitude": "-96.********"}, {"name": "Mound City", "countryCode": "US", "stateCode": "SD", "latitude": "45.72527000", "longitude": "-100.06845000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.88832000", "longitude": "-100.71291000"}, {"name": "North Eagle Butte", "countryCode": "US", "stateCode": "SD", "latitude": "45.00415000", "longitude": "-101.23376000"}, {"name": "North Sioux City", "countryCode": "US", "stateCode": "SD", "latitude": "42.52722000", "longitude": "-96.48309000"}, {"name": "North Spearfish", "countryCode": "US", "stateCode": "SD", "latitude": "44.50665000", "longitude": "-103.89215000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.18859000", "longitude": "-102.73962000"}, {"name": "Oglala Lakota County", "countryCode": "US", "stateCode": "SD", "latitude": "43.33559000", "longitude": "-102.55162000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.24082000", "longitude": "-97.67534000"}, {"name": "On<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.70804000", "longitude": "-100.05984000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.39748000", "longitude": "-97.13645000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.39888000", "longitude": "-97.98368000"}, {"name": "Pennington County", "countryCode": "US", "stateCode": "SD", "latitude": "44.00373000", "longitude": "-102.82383000"}, {"name": "Perkins County", "countryCode": "US", "stateCode": "SD", "latitude": "45.49051000", "longitude": "-102.47563000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.03943000", "longitude": "-101.66514000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.36832000", "longitude": "-100.35097000"}, {"name": "Pine Ridge", "countryCode": "US", "stateCode": "SD", "latitude": "43.02554000", "longitude": "-102.55627000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.71555000", "longitude": "-98.48509000"}, {"name": "Platte", "countryCode": "US", "stateCode": "SD", "latitude": "43.38694000", "longitude": "-98.84453000"}, {"name": "Po<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.23971000", "longitude": "-102.33099000"}, {"name": "Potter County", "countryCode": "US", "stateCode": "SD", "latitude": "45.06450000", "longitude": "-99.95725000"}, {"name": "Rapid City", "countryCode": "US", "stateCode": "SD", "latitude": "44.08054000", "longitude": "-103.23101000"}, {"name": "Rapid Valley", "countryCode": "US", "stateCode": "SD", "latitude": "44.06249000", "longitude": "-103.14629000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.87581000", "longitude": "-98.51871000"}, {"name": "Roberts County", "countryCode": "US", "stateCode": "SD", "latitude": "45.62965000", "longitude": "-96.94612000"}, {"name": "Rosebud", "countryCode": "US", "stateCode": "SD", "latitude": "43.23278000", "longitude": "-100.85348000"}, {"name": "Salem", "countryCode": "US", "stateCode": "SD", "latitude": "43.72415000", "longitude": "-97.38895000"}, {"name": "Sanborn County", "countryCode": "US", "stateCode": "SD", "latitude": "44.02345000", "longitude": "-98.09139000"}, {"name": "Selby", "countryCode": "US", "stateCode": "SD", "latitude": "45.50638000", "longitude": "-100.03207000"}, {"name": "Sioux Falls", "countryCode": "US", "stateCode": "SD", "latitude": "43.54997000", "longitude": "-96.70033000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "45.66468000", "longitude": "-97.04980000"}, {"name": "Spearfish", "countryCode": "US", "stateCode": "SD", "latitude": "44.49082000", "longitude": "-103.85937000"}, {"name": "Spink County", "countryCode": "US", "stateCode": "SD", "latitude": "44.93802000", "longitude": "-98.34619000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "SD", "latitude": "42.85417000", "longitude": "-97.89729000"}, {"name": "Stanley County", "countryCode": "US", "stateCode": "SD", "latitude": "44.41232000", "longitude": "-100.73594000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "44.40971000", "longitude": "-103.50908000"}, {"name": "Sully County", "countryCode": "US", "stateCode": "SD", "latitude": "44.71558000", "longitude": "-100.13220000"}, {"name": "Summerset", "countryCode": "US", "stateCode": "SD", "latitude": "44.18998000", "longitude": "-103.34384000"}, {"name": "Tea", "countryCode": "US", "stateCode": "SD", "latitude": "43.44637000", "longitude": "-96.83588000"}, {"name": "Timber Lake", "countryCode": "US", "stateCode": "SD", "latitude": "45.42915000", "longitude": "-101.07403000"}, {"name": "Todd County", "countryCode": "US", "stateCode": "SD", "latitude": "43.19337000", "longitude": "-100.71841000"}, {"name": "Tripp County", "countryCode": "US", "stateCode": "SD", "latitude": "43.34587000", "longitude": "-99.88400000"}, {"name": "Turner County", "countryCode": "US", "stateCode": "SD", "latitude": "43.31087000", "longitude": "-97.14866000"}, {"name": "Tyndall", "countryCode": "US", "stateCode": "SD", "latitude": "42.99333000", "longitude": "-97.86285000"}, {"name": "Union County", "countryCode": "US", "stateCode": "SD", "latitude": "42.83249000", "longitude": "-96.65609000"}, {"name": "Vermillion", "countryCode": "US", "stateCode": "SD", "latitude": "42.77944000", "longitude": "-96.92921000"}, {"name": "Volga", "countryCode": "US", "stateCode": "SD", "latitude": "44.32358000", "longitude": "-96.92645000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "43.07972000", "longitude": "-98.29313000"}, {"name": "Walworth County", "countryCode": "US", "stateCode": "SD", "latitude": "45.42995000", "longitude": "-100.03156000"}, {"name": "Watertown", "countryCode": "US", "stateCode": "SD", "latitude": "44.89941000", "longitude": "-97.11507000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "SD", "latitude": "45.33218000", "longitude": "-97.52009000"}, {"name": "Wessington Springs", "countryCode": "US", "stateCode": "SD", "latitude": "44.07916000", "longitude": "-98.56954000"}, {"name": "White River", "countryCode": "US", "stateCode": "SD", "latitude": "43.56805000", "longitude": "-100.74542000"}, {"name": "Winner", "countryCode": "US", "stateCode": "SD", "latitude": "43.37667000", "longitude": "-99.85901000"}, {"name": "Woonsocket", "countryCode": "US", "stateCode": "SD", "latitude": "44.05360000", "longitude": "-98.27564000"}, {"name": "Yankton", "countryCode": "US", "stateCode": "SD", "latitude": "42.87111000", "longitude": "-97.39728000"}, {"name": "Yankton County", "countryCode": "US", "stateCode": "SD", "latitude": "43.00897000", "longitude": "-97.39475000"}, {"name": "Ziebach County", "countryCode": "US", "stateCode": "SD", "latitude": "44.98041000", "longitude": "-101.66586000"}]