[{"name": "Aberdeen", "countryCode": "US", "stateCode": "MS", "latitude": "33.82511000", "longitude": "-88.54366000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.31012000", "longitude": "-89.17284000"}, {"name": "Adams County", "countryCode": "US", "stateCode": "MS", "latitude": "31.48294000", "longitude": "-91.35350000"}, {"name": "Alcorn County", "countryCode": "US", "stateCode": "MS", "latitude": "34.88077000", "longitude": "-88.58026000"}, {"name": "Amite County", "countryCode": "US", "stateCode": "MS", "latitude": "31.17440000", "longitude": "-90.80443000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.98428000", "longitude": "-88.48810000"}, {"name": "Arnold <PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.33517000", "longitude": "-89.37340000"}, {"name": "Ashland", "countryCode": "US", "stateCode": "MS", "latitude": "34.83287000", "longitude": "-89.17590000"}, {"name": "Attala County", "countryCode": "US", "stateCode": "MS", "latitude": "33.08629000", "longitude": "-89.58155000"}, {"name": "<PERSON>ld<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.50954000", "longitude": "-88.63533000"}, {"name": "Batesville", "countryCode": "US", "stateCode": "MS", "latitude": "34.31150000", "longitude": "-89.94426000"}, {"name": "Bay Saint Louis", "countryCode": "US", "stateCode": "MS", "latitude": "30.30881000", "longitude": "-89.33005000"}, {"name": "Bay Springs", "countryCode": "US", "stateCode": "MS", "latitude": "31.97904000", "longitude": "-89.28728000"}, {"name": "Beechwood", "countryCode": "US", "stateCode": "MS", "latitude": "32.32765000", "longitude": "-90.82677000"}, {"name": "Belmont", "countryCode": "US", "stateCode": "MS", "latitude": "34.50982000", "longitude": "-88.20921000"}, {"name": "Belzoni", "countryCode": "US", "stateCode": "MS", "latitude": "33.18429000", "longitude": "-90.48926000"}, {"name": "Benton County", "countryCode": "US", "stateCode": "MS", "latitude": "34.81729000", "longitude": "-89.18848000"}, {"name": "Biloxi", "countryCode": "US", "stateCode": "MS", "latitude": "30.39603000", "longitude": "-88.88531000"}, {"name": "Bolivar County", "countryCode": "US", "stateCode": "MS", "latitude": "33.79554000", "longitude": "-90.88040000"}, {"name": "Booneville", "countryCode": "US", "stateCode": "MS", "latitude": "34.65815000", "longitude": "-88.56672000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.27320000", "longitude": "-89.98592000"}, {"name": "Brookhaven", "countryCode": "US", "stateCode": "MS", "latitude": "31.57906000", "longitude": "-90.44065000"}, {"name": "Brooksville", "countryCode": "US", "stateCode": "MS", "latitude": "33.23457000", "longitude": "-88.58227000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.99206000", "longitude": "-89.34896000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.46295000", "longitude": "-90.85010000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.87232000", "longitude": "-89.69064000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.17932000", "longitude": "-90.24537000"}, {"name": "Caledonia", "countryCode": "US", "stateCode": "MS", "latitude": "33.68289000", "longitude": "-88.32448000"}, {"name": "Calhoun City", "countryCode": "US", "stateCode": "MS", "latitude": "33.85539000", "longitude": "-89.31146000"}, {"name": "Calhoun County", "countryCode": "US", "stateCode": "MS", "latitude": "33.93645000", "longitude": "-89.33645000"}, {"name": "Canton", "countryCode": "US", "stateCode": "MS", "latitude": "32.61264000", "longitude": "-90.03675000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.61686000", "longitude": "-89.65256000"}, {"name": "Carroll County", "countryCode": "US", "stateCode": "MS", "latitude": "33.44853000", "longitude": "-89.92020000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.50818000", "longitude": "-89.92036000"}, {"name": "Carthage", "countryCode": "US", "stateCode": "MS", "latitude": "32.73264000", "longitude": "-89.53618000"}, {"name": "Centreville", "countryCode": "US", "stateCode": "MS", "latitude": "31.08962000", "longitude": "-91.06844000"}, {"name": "Charleston", "countryCode": "US", "stateCode": "MS", "latitude": "34.00678000", "longitude": "-90.05676000"}, {"name": "Chickasaw County", "countryCode": "US", "stateCode": "MS", "latitude": "33.92080000", "longitude": "-88.94786000"}, {"name": "Choctaw County", "countryCode": "US", "stateCode": "MS", "latitude": "33.34731000", "longitude": "-89.24838000"}, {"name": "Claiborne County", "countryCode": "US", "stateCode": "MS", "latitude": "31.97369000", "longitude": "-90.91181000"}, {"name": "Clarke County", "countryCode": "US", "stateCode": "MS", "latitude": "32.04140000", "longitude": "-88.68940000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.20011000", "longitude": "-90.57093000"}, {"name": "Clay County", "countryCode": "US", "stateCode": "MS", "latitude": "33.65567000", "longitude": "-88.78157000"}, {"name": "Cleary", "countryCode": "US", "stateCode": "MS", "latitude": "32.16543000", "longitude": "-90.18064000"}, {"name": "Cleveland", "countryCode": "US", "stateCode": "MS", "latitude": "33.74400000", "longitude": "-90.72482000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.34153000", "longitude": "-90.32176000"}, {"name": "Coahoma County", "countryCode": "US", "stateCode": "MS", "latitude": "34.22917000", "longitude": "-90.60269000"}, {"name": "Coldwater", "countryCode": "US", "stateCode": "MS", "latitude": "34.69177000", "longitude": "-89.97731000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.64544000", "longitude": "-89.55535000"}, {"name": "Collinsville", "countryCode": "US", "stateCode": "MS", "latitude": "32.49792000", "longitude": "-88.84588000"}, {"name": "Columbia", "countryCode": "US", "stateCode": "MS", "latitude": "31.25184000", "longitude": "-89.83758000"}, {"name": "Columbus", "countryCode": "US", "stateCode": "MS", "latitude": "33.49567000", "longitude": "-88.42726000"}, {"name": "Columbus Air Force Base", "countryCode": "US", "stateCode": "MS", "latitude": "33.63239000", "longitude": "-88.45153000"}, {"name": "Como", "countryCode": "US", "stateCode": "MS", "latitude": "34.51066000", "longitude": "-89.93981000"}, {"name": "Conehatta", "countryCode": "US", "stateCode": "MS", "latitude": "32.45125000", "longitude": "-89.28534000"}, {"name": "Copiah County", "countryCode": "US", "stateCode": "MS", "latitude": "31.86924000", "longitude": "-90.44880000"}, {"name": "Corinth", "countryCode": "US", "stateCode": "MS", "latitude": "34.93425000", "longitude": "-88.52227000"}, {"name": "Covington County", "countryCode": "US", "stateCode": "MS", "latitude": "31.63322000", "longitude": "-89.55263000"}, {"name": "Crystal Springs", "countryCode": "US", "stateCode": "MS", "latitude": "31.98738000", "longitude": "-90.35704000"}, {"name": "D'Iberville", "countryCode": "US", "stateCode": "MS", "latitude": "30.42631000", "longitude": "-88.89086000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.76763000", "longitude": "-88.65088000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.37936000", "longitude": "-89.26449000"}, {"name": "De Soto County", "countryCode": "US", "stateCode": "MS", "latitude": "34.87540000", "longitude": "-89.99178000"}, {"name": "Decatur", "countryCode": "US", "stateCode": "MS", "latitude": "32.43903000", "longitude": "-89.10839000"}, {"name": "Der<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.85567000", "longitude": "-89.28452000"}, {"name": "Diamondhead", "countryCode": "US", "stateCode": "MS", "latitude": "30.39464000", "longitude": "-89.36394000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.80956000", "longitude": "-90.52648000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.63318000", "longitude": "-89.71119000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.07513000", "longitude": "-89.85453000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.33015000", "longitude": "-90.60565000"}, {"name": "Ellisville", "countryCode": "US", "stateCode": "MS", "latitude": "31.60405000", "longitude": "-89.19561000"}, {"name": "Escatawpa", "countryCode": "US", "stateCode": "MS", "latitude": "30.44048000", "longitude": "-88.54363000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.54068000", "longitude": "-89.26701000"}, {"name": "Farmington", "countryCode": "US", "stateCode": "MS", "latitude": "34.93009000", "longitude": "-88.45227000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.71155000", "longitude": "-91.06066000"}, {"name": "Flora", "countryCode": "US", "stateCode": "MS", "latitude": "32.54320000", "longitude": "-90.30926000"}, {"name": "Florence", "countryCode": "US", "stateCode": "MS", "latitude": "32.15348000", "longitude": "-90.13120000"}, {"name": "Flowood", "countryCode": "US", "stateCode": "MS", "latitude": "32.30959000", "longitude": "-90.13898000"}, {"name": "Forest", "countryCode": "US", "stateCode": "MS", "latitude": "32.36459000", "longitude": "-89.47423000"}, {"name": "Forrest County", "countryCode": "US", "stateCode": "MS", "latitude": "31.18887000", "longitude": "-89.25786000"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "MS", "latitude": "31.47715000", "longitude": "-90.89785000"}, {"name": "Friars Point", "countryCode": "US", "stateCode": "MS", "latitude": "34.37088000", "longitude": "-90.63834000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.27399000", "longitude": "-88.40921000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.38575000", "longitude": "-88.61169000"}, {"name": "George County", "countryCode": "US", "stateCode": "MS", "latitude": "30.86261000", "longitude": "-88.64403000"}, {"name": "Glendale", "countryCode": "US", "stateCode": "MS", "latitude": "31.36462000", "longitude": "-89.30617000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.97013000", "longitude": "-89.91231000"}, {"name": "Greene County", "countryCode": "US", "stateCode": "MS", "latitude": "31.21422000", "longitude": "-88.63916000"}, {"name": "Greenville", "countryCode": "US", "stateCode": "MS", "latitude": "33.40898000", "longitude": "-91.05978000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.51623000", "longitude": "-90.17953000"}, {"name": "Grenada", "countryCode": "US", "stateCode": "MS", "latitude": "33.76900000", "longitude": "-89.80842000"}, {"name": "Grenada County", "countryCode": "US", "stateCode": "MS", "latitude": "33.76995000", "longitude": "-89.80201000"}, {"name": "Gulf Hills", "countryCode": "US", "stateCode": "MS", "latitude": "30.43048000", "longitude": "-88.84225000"}, {"name": "Gulf Park Estates", "countryCode": "US", "stateCode": "MS", "latitude": "30.39187000", "longitude": "-88.76114000"}, {"name": "Gulfport", "countryCode": "US", "stateCode": "MS", "latitude": "30.36742000", "longitude": "-89.09282000"}, {"name": "Guntown", "countryCode": "US", "stateCode": "MS", "latitude": "34.44316000", "longitude": "-88.65978000"}, {"name": "Hancock County", "countryCode": "US", "stateCode": "MS", "latitude": "30.39378000", "longitude": "-89.47456000"}, {"name": "Harrison County", "countryCode": "US", "stateCode": "MS", "latitude": "30.41605000", "longitude": "-89.08164000"}, {"name": "Hattiesburg", "countryCode": "US", "stateCode": "MS", "latitude": "31.32712000", "longitude": "-89.29034000"}, {"name": "Hazlehurst", "countryCode": "US", "stateCode": "MS", "latitude": "31.86044000", "longitude": "-90.39593000"}, {"name": "Helena", "countryCode": "US", "stateCode": "MS", "latitude": "30.49464000", "longitude": "-88.49585000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.82399000", "longitude": "-89.99370000"}, {"name": "Hickory Hills", "countryCode": "US", "stateCode": "MS", "latitude": "30.45687000", "longitude": "-88.63919000"}, {"name": "Hide-A-Way Lake", "countryCode": "US", "stateCode": "MS", "latitude": "30.56492000", "longitude": "-89.64020000"}, {"name": "Hillsboro", "countryCode": "US", "stateCode": "MS", "latitude": "32.45931000", "longitude": "-89.51146000"}, {"name": "Hinds County", "countryCode": "US", "stateCode": "MS", "latitude": "32.26670000", "longitude": "-90.44282000"}, {"name": "Hollandale", "countryCode": "US", "stateCode": "MS", "latitude": "33.16901000", "longitude": "-90.85399000"}, {"name": "Holly Springs", "countryCode": "US", "stateCode": "MS", "latitude": "34.76760000", "longitude": "-89.44869000"}, {"name": "Holmes County", "countryCode": "US", "stateCode": "MS", "latitude": "33.12351000", "longitude": "-90.09205000"}, {"name": "Horn Lake", "countryCode": "US", "stateCode": "MS", "latitude": "34.95537000", "longitude": "-90.03481000"}, {"name": "Houston", "countryCode": "US", "stateCode": "MS", "latitude": "33.89845000", "longitude": "-88.99923000"}, {"name": "Humphreys County", "countryCode": "US", "stateCode": "MS", "latitude": "33.12870000", "longitude": "-90.52662000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.66103000", "longitude": "-88.49418000"}, {"name": "Indianola", "countryCode": "US", "stateCode": "MS", "latitude": "33.45095000", "longitude": "-90.65509000"}, {"name": "Issaquena County", "countryCode": "US", "stateCode": "MS", "latitude": "32.74139000", "longitude": "-90.98921000"}, {"name": "Itawamba County", "countryCode": "US", "stateCode": "MS", "latitude": "34.27999000", "longitude": "-88.36132000"}, {"name": "<PERSON><PERSON> Ben<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.49512000", "longitude": "-90.31981000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.81176000", "longitude": "-88.19004000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.29876000", "longitude": "-90.18481000"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "MS", "latitude": "30.46289000", "longitude": "-88.62284000"}, {"name": "Jasper County", "countryCode": "US", "stateCode": "MS", "latitude": "32.01911000", "longitude": "-89.11886000"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "MS", "latitude": "31.73421000", "longitude": "-91.03718000"}, {"name": "Jefferson Davis County", "countryCode": "US", "stateCode": "MS", "latitude": "31.56964000", "longitude": "-89.82300000"}, {"name": "Jones County", "countryCode": "US", "stateCode": "MS", "latitude": "31.62256000", "longitude": "-89.16879000"}, {"name": "Jonestown", "countryCode": "US", "stateCode": "MS", "latitude": "34.31955000", "longitude": "-90.45565000"}, {"name": "Kearney Park", "countryCode": "US", "stateCode": "MS", "latitude": "32.58903000", "longitude": "-90.31537000"}, {"name": "Kemper County", "countryCode": "US", "stateCode": "MS", "latitude": "32.75456000", "longitude": "-88.64116000"}, {"name": "Kiln", "countryCode": "US", "stateCode": "MS", "latitude": "30.40908000", "longitude": "-89.43505000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.05800000", "longitude": "-89.58956000"}, {"name": "Lafayette County", "countryCode": "US", "stateCode": "MS", "latitude": "34.35675000", "longitude": "-89.48492000"}, {"name": "Lamar County", "countryCode": "US", "stateCode": "MS", "latitude": "31.20587000", "longitude": "-89.50869000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.20178000", "longitude": "-90.28343000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.53464000", "longitude": "-88.86670000"}, {"name": "Lauderdale County", "countryCode": "US", "stateCode": "MS", "latitude": "32.40429000", "longitude": "-88.66254000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.69405000", "longitude": "-89.13061000"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "MS", "latitude": "31.55020000", "longitude": "-90.10699000"}, {"name": "Leake County", "countryCode": "US", "stateCode": "MS", "latitude": "32.75353000", "longitude": "-89.52406000"}, {"name": "Leakesville", "countryCode": "US", "stateCode": "MS", "latitude": "31.15574000", "longitude": "-88.55780000"}, {"name": "Lee County", "countryCode": "US", "stateCode": "MS", "latitude": "34.28989000", "longitude": "-88.68042000"}, {"name": "Leflore County", "countryCode": "US", "stateCode": "MS", "latitude": "33.55052000", "longitude": "-90.30106000"}, {"name": "Leland", "countryCode": "US", "stateCode": "MS", "latitude": "33.40539000", "longitude": "-90.89760000"}, {"name": "Lexington", "countryCode": "US", "stateCode": "MS", "latitude": "33.11318000", "longitude": "-90.05314000"}, {"name": "Liberty", "countryCode": "US", "stateCode": "MS", "latitude": "31.15823000", "longitude": "-90.81232000"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "MS", "latitude": "31.53239000", "longitude": "-90.45400000"}, {"name": "Long Beach", "countryCode": "US", "stateCode": "MS", "latitude": "30.35048000", "longitude": "-89.15282000"}, {"name": "Louisville", "countryCode": "US", "stateCode": "MS", "latitude": "33.12374000", "longitude": "-89.05506000"}, {"name": "Lowndes County", "countryCode": "US", "stateCode": "MS", "latitude": "33.47291000", "longitude": "-88.44331000"}, {"name": "Lucedale", "countryCode": "US", "stateCode": "MS", "latitude": "30.92519000", "longitude": "-88.59002000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.00129000", "longitude": "-89.45229000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.49474000", "longitude": "-89.12561000"}, {"name": "Lynchburg", "countryCode": "US", "stateCode": "MS", "latitude": "34.96232000", "longitude": "-90.09593000"}, {"name": "Macon", "countryCode": "US", "stateCode": "MS", "latitude": "33.10540000", "longitude": "-88.56088000"}, {"name": "Madison", "countryCode": "US", "stateCode": "MS", "latitude": "32.46181000", "longitude": "-90.11536000"}, {"name": "Madison County", "countryCode": "US", "stateCode": "MS", "latitude": "32.63466000", "longitude": "-90.03376000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.87377000", "longitude": "-89.73369000"}, {"name": "Magnolia", "countryCode": "US", "stateCode": "MS", "latitude": "31.14323000", "longitude": "-90.45871000"}, {"name": "Mantachie", "countryCode": "US", "stateCode": "MS", "latitude": "34.32427000", "longitude": "-88.49116000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.41736000", "longitude": "-88.64782000"}, {"name": "Marion County", "countryCode": "US", "stateCode": "MS", "latitude": "31.23082000", "longitude": "-89.82244000"}, {"name": "Marks", "countryCode": "US", "stateCode": "MS", "latitude": "34.25683000", "longitude": "-90.27298000"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "MS", "latitude": "34.76225000", "longitude": "-89.50305000"}, {"name": "Mayersville", "countryCode": "US", "stateCode": "MS", "latitude": "32.90207000", "longitude": "-91.05122000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.24379000", "longitude": "-90.45315000"}, {"name": "Meadville", "countryCode": "US", "stateCode": "MS", "latitude": "31.47239000", "longitude": "-90.89677000"}, {"name": "Mendenhall", "countryCode": "US", "stateCode": "MS", "latitude": "31.96182000", "longitude": "-89.87008000"}, {"name": "Meridian", "countryCode": "US", "stateCode": "MS", "latitude": "32.36431000", "longitude": "-88.70366000"}, {"name": "Meridian Station", "countryCode": "US", "stateCode": "MS", "latitude": "32.55049000", "longitude": "-88.61849000"}, {"name": "Metcalfe", "countryCode": "US", "stateCode": "MS", "latitude": "33.45400000", "longitude": "-91.00733000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.95039000", "longitude": "-90.28398000"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "MS", "latitude": "33.89224000", "longitude": "-88.48047000"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "MS", "latitude": "33.49410000", "longitude": "-89.61640000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.55378000", "longitude": "-90.10731000"}, {"name": "Moorhead", "countryCode": "US", "stateCode": "MS", "latitude": "33.45012000", "longitude": "-90.50564000"}, {"name": "Morgantown", "countryCode": "US", "stateCode": "MS", "latitude": "31.57267000", "longitude": "-91.34761000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.35376000", "longitude": "-89.65452000"}, {"name": "Moss Point", "countryCode": "US", "stateCode": "MS", "latitude": "30.41159000", "longitude": "-88.53446000"}, {"name": "Mound <PERSON>ou", "countryCode": "US", "stateCode": "MS", "latitude": "33.87817000", "longitude": "-90.72732000"}, {"name": "Natchez", "countryCode": "US", "stateCode": "MS", "latitude": "31.56017000", "longitude": "-91.40329000"}, {"name": "Nellieburg", "countryCode": "US", "stateCode": "MS", "latitude": "32.40681000", "longitude": "-88.77727000"}, {"name": "Neshoba County", "countryCode": "US", "stateCode": "MS", "latitude": "32.75350000", "longitude": "-89.11757000"}, {"name": "<PERSON>tleton", "countryCode": "US", "stateCode": "MS", "latitude": "34.08900000", "longitude": "-88.62227000"}, {"name": "New Albany", "countryCode": "US", "stateCode": "MS", "latitude": "34.49427000", "longitude": "-89.00784000"}, {"name": "New Augusta", "countryCode": "US", "stateCode": "MS", "latitude": "31.20263000", "longitude": "-89.03668000"}, {"name": "New Hope", "countryCode": "US", "stateCode": "MS", "latitude": "33.46817000", "longitude": "-88.32670000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.32126000", "longitude": "-89.16339000"}, {"name": "Newton County", "countryCode": "US", "stateCode": "MS", "latitude": "32.40023000", "longitude": "-89.11881000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.47714000", "longitude": "-89.69367000"}, {"name": "North Tunica", "countryCode": "US", "stateCode": "MS", "latitude": "34.70093000", "longitude": "-90.37815000"}, {"name": "Noxubee County", "countryCode": "US", "stateCode": "MS", "latitude": "33.11011000", "longitude": "-88.56982000"}, {"name": "Ocean Springs", "countryCode": "US", "stateCode": "MS", "latitude": "30.41131000", "longitude": "-88.82781000"}, {"name": "Okolona", "countryCode": "US", "stateCode": "MS", "latitude": "34.00178000", "longitude": "-88.75533000"}, {"name": "Oktibbeha County", "countryCode": "US", "stateCode": "MS", "latitude": "33.42495000", "longitude": "-88.87930000"}, {"name": "Olive Branch", "countryCode": "US", "stateCode": "MS", "latitude": "34.96176000", "longitude": "-89.82953000"}, {"name": "Oxford", "countryCode": "US", "stateCode": "MS", "latitude": "34.36650000", "longitude": "-89.51925000"}, {"name": "Panola County", "countryCode": "US", "stateCode": "MS", "latitude": "34.36394000", "longitude": "-89.95057000"}, {"name": "Pascagoula", "countryCode": "US", "stateCode": "MS", "latitude": "30.36576000", "longitude": "-88.55613000"}, {"name": "Pass Christian", "countryCode": "US", "stateCode": "MS", "latitude": "30.31575000", "longitude": "-89.24754000"}, {"name": "Pearl", "countryCode": "US", "stateCode": "MS", "latitude": "32.27459000", "longitude": "-90.13203000"}, {"name": "Pearl River", "countryCode": "US", "stateCode": "MS", "latitude": "32.78347000", "longitude": "-89.22784000"}, {"name": "Pearl River County", "countryCode": "US", "stateCode": "MS", "latitude": "30.76858000", "longitude": "-89.58978000"}, {"name": "Pearlington", "countryCode": "US", "stateCode": "MS", "latitude": "30.24658000", "longitude": "-89.61117000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.31292000", "longitude": "-89.79841000"}, {"name": "Perry County", "countryCode": "US", "stateCode": "MS", "latitude": "31.17202000", "longitude": "-88.99233000"}, {"name": "Petal", "countryCode": "US", "stateCode": "MS", "latitude": "31.34656000", "longitude": "-89.26006000"}, {"name": "Philadelphia", "countryCode": "US", "stateCode": "MS", "latitude": "32.77152000", "longitude": "-89.11673000"}, {"name": "Picayune", "countryCode": "US", "stateCode": "MS", "latitude": "30.52556000", "longitude": "-89.67788000"}, {"name": "<PERSON>ens", "countryCode": "US", "stateCode": "MS", "latitude": "32.88374000", "longitude": "-89.97147000"}, {"name": "Pike County", "countryCode": "US", "stateCode": "MS", "latitude": "31.17491000", "longitude": "-90.40416000"}, {"name": "Pittsboro", "countryCode": "US", "stateCode": "MS", "latitude": "33.94039000", "longitude": "-89.33757000"}, {"name": "Plantersville", "countryCode": "US", "stateCode": "MS", "latitude": "34.21344000", "longitude": "-88.66450000"}, {"name": "Pontotoc", "countryCode": "US", "stateCode": "MS", "latitude": "34.24788000", "longitude": "-88.99867000"}, {"name": "Pontotoc County", "countryCode": "US", "stateCode": "MS", "latitude": "34.22544000", "longitude": "-89.03741000"}, {"name": "Poplarville", "countryCode": "US", "stateCode": "MS", "latitude": "30.84019000", "longitude": "-89.53423000"}, {"name": "Port Gibson", "countryCode": "US", "stateCode": "MS", "latitude": "31.96099000", "longitude": "-90.98399000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.59850000", "longitude": "-89.86702000"}, {"name": "Prentiss County", "countryCode": "US", "stateCode": "MS", "latitude": "34.61829000", "longitude": "-88.52010000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.14324000", "longitude": "-89.40979000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.04015000", "longitude": "-88.72810000"}, {"name": "Quitman County", "countryCode": "US", "stateCode": "MS", "latitude": "34.25141000", "longitude": "-90.28912000"}, {"name": "Raleigh", "countryCode": "US", "stateCode": "MS", "latitude": "32.03349000", "longitude": "-89.52229000"}, {"name": "Rankin County", "countryCode": "US", "stateCode": "MS", "latitude": "32.26412000", "longitude": "-89.94580000"}, {"name": "Rawls Springs", "countryCode": "US", "stateCode": "MS", "latitude": "31.38073000", "longitude": "-89.37145000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.25931000", "longitude": "-90.42260000"}, {"name": "Richland", "countryCode": "US", "stateCode": "MS", "latitude": "32.23904000", "longitude": "-90.15842000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.34934000", "longitude": "-88.94005000"}, {"name": "Ridgeland", "countryCode": "US", "stateCode": "MS", "latitude": "32.42848000", "longitude": "-90.13231000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.72982000", "longitude": "-88.95062000"}, {"name": "Rolling Fork", "countryCode": "US", "stateCode": "MS", "latitude": "32.90652000", "longitude": "-90.87816000"}, {"name": "Rosedale", "countryCode": "US", "stateCode": "MS", "latitude": "33.85344000", "longitude": "-91.02789000"}, {"name": "Ruleville", "countryCode": "US", "stateCode": "MS", "latitude": "33.72595000", "longitude": "-90.55148000"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.43798000", "longitude": "-88.86809000"}, {"name": "Saltillo", "countryCode": "US", "stateCode": "MS", "latitude": "34.37649000", "longitude": "-88.68172000"}, {"name": "<PERSON>rdis", "countryCode": "US", "stateCode": "MS", "latitude": "34.43705000", "longitude": "-89.91592000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.63575000", "longitude": "-89.13504000"}, {"name": "Scott County", "countryCode": "US", "stateCode": "MS", "latitude": "32.40638000", "longitude": "-89.53764000"}, {"name": "Senatobia", "countryCode": "US", "stateCode": "MS", "latitude": "34.61760000", "longitude": "-89.96870000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.11622000", "longitude": "-88.71172000"}, {"name": "Sharkey County", "countryCode": "US", "stateCode": "MS", "latitude": "32.87972000", "longitude": "-90.81321000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.78960000", "longitude": "-89.09867000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.60211000", "longitude": "-90.77458000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.95094000", "longitude": "-90.76788000"}, {"name": "Simpson County", "countryCode": "US", "stateCode": "MS", "latitude": "31.91317000", "longitude": "-89.91949000"}, {"name": "Smith County", "countryCode": "US", "stateCode": "MS", "latitude": "32.01769000", "longitude": "-89.50668000"}, {"name": "Southaven", "countryCode": "US", "stateCode": "MS", "latitude": "34.98898000", "longitude": "-90.01259000"}, {"name": "Starkville", "countryCode": "US", "stateCode": "MS", "latitude": "33.45049000", "longitude": "-88.81961000"}, {"name": "Stone County", "countryCode": "US", "stateCode": "MS", "latitude": "30.78995000", "longitude": "-89.11771000"}, {"name": "Stonewall", "countryCode": "US", "stateCode": "MS", "latitude": "32.13181000", "longitude": "-88.79338000"}, {"name": "Summit", "countryCode": "US", "stateCode": "MS", "latitude": "31.28379000", "longitude": "-90.46843000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.41739000", "longitude": "-89.54229000"}, {"name": "Sunflower", "countryCode": "US", "stateCode": "MS", "latitude": "33.54290000", "longitude": "-90.53703000"}, {"name": "Sunflower County", "countryCode": "US", "stateCode": "MS", "latitude": "33.60231000", "longitude": "-90.58862000"}, {"name": "Tallahatchie County", "countryCode": "US", "stateCode": "MS", "latitude": "33.95047000", "longitude": "-90.17326000"}, {"name": "Tate County", "countryCode": "US", "stateCode": "MS", "latitude": "34.65032000", "longitude": "-89.94478000"}, {"name": "Taylorsville", "countryCode": "US", "stateCode": "MS", "latitude": "31.82960000", "longitude": "-89.42812000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "33.18290000", "longitude": "-90.22286000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "32.09626000", "longitude": "-90.29426000"}, {"name": "Tippah County", "countryCode": "US", "stateCode": "MS", "latitude": "34.76840000", "longitude": "-88.90890000"}, {"name": "Tishomingo County", "countryCode": "US", "stateCode": "MS", "latitude": "34.74043000", "longitude": "-88.23932000"}, {"name": "Tunica", "countryCode": "US", "stateCode": "MS", "latitude": "34.68455000", "longitude": "-90.38288000"}, {"name": "Tunica County", "countryCode": "US", "stateCode": "MS", "latitude": "34.65194000", "longitude": "-90.37551000"}, {"name": "Tunica Resorts", "countryCode": "US", "stateCode": "MS", "latitude": "34.83613000", "longitude": "-90.34723000"}, {"name": "Tupelo", "countryCode": "US", "stateCode": "MS", "latitude": "34.25807000", "longitude": "-88.70464000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "34.01483000", "longitude": "-90.43176000"}, {"name": "Tylertown", "countryCode": "US", "stateCode": "MS", "latitude": "31.11601000", "longitude": "-90.14203000"}, {"name": "Union", "countryCode": "US", "stateCode": "MS", "latitude": "32.57153000", "longitude": "-89.12145000"}, {"name": "Union County", "countryCode": "US", "stateCode": "MS", "latitude": "34.49047000", "longitude": "-89.00386000"}, {"name": "University", "countryCode": "US", "stateCode": "MS", "latitude": "34.36594000", "longitude": "-89.52536000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.54047000", "longitude": "-88.68752000"}, {"name": "Vardaman", "countryCode": "US", "stateCode": "MS", "latitude": "33.87567000", "longitude": "-89.17729000"}, {"name": "Verona", "countryCode": "US", "stateCode": "MS", "latitude": "34.19427000", "longitude": "-88.71977000"}, {"name": "Vicksburg", "countryCode": "US", "stateCode": "MS", "latitude": "32.35265000", "longitude": "-90.87788000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.64242000", "longitude": "-88.56974000"}, {"name": "Walls", "countryCode": "US", "stateCode": "MS", "latitude": "34.95824000", "longitude": "-90.15256000"}, {"name": "Walnut Grove", "countryCode": "US", "stateCode": "MS", "latitude": "32.59042000", "longitude": "-89.45840000"}, {"name": "Walthall", "countryCode": "US", "stateCode": "MS", "latitude": "33.60734000", "longitude": "-89.27729000"}, {"name": "Walthall County", "countryCode": "US", "stateCode": "MS", "latitude": "31.14842000", "longitude": "-90.10614000"}, {"name": "Warren County", "countryCode": "US", "stateCode": "MS", "latitude": "32.35723000", "longitude": "-90.85201000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "MS", "latitude": "33.28370000", "longitude": "-90.94745000"}, {"name": "Water Valley", "countryCode": "US", "stateCode": "MS", "latitude": "34.15150000", "longitude": "-89.63147000"}, {"name": "Waveland", "countryCode": "US", "stateCode": "MS", "latitude": "30.28686000", "longitude": "-89.37616000"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "MS", "latitude": "31.64078000", "longitude": "-88.69580000"}, {"name": "Waynesboro", "countryCode": "US", "stateCode": "MS", "latitude": "31.67488000", "longitude": "-88.64615000"}, {"name": "Webster County", "countryCode": "US", "stateCode": "MS", "latitude": "33.61307000", "longitude": "-89.28482000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "31.70128000", "longitude": "-90.39759000"}, {"name": "West Gulfport", "countryCode": "US", "stateCode": "MS", "latitude": "30.40409000", "longitude": "-89.09420000"}, {"name": "West Hattiesburg", "countryCode": "US", "stateCode": "MS", "latitude": "31.31906000", "longitude": "-89.37506000"}, {"name": "West Point", "countryCode": "US", "stateCode": "MS", "latitude": "33.60762000", "longitude": "-88.65033000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS", "latitude": "30.85824000", "longitude": "-89.13533000"}, {"name": "Wilkinson County", "countryCode": "US", "stateCode": "MS", "latitude": "31.16107000", "longitude": "-91.31092000"}, {"name": "Winona", "countryCode": "US", "stateCode": "MS", "latitude": "33.48207000", "longitude": "-89.72814000"}, {"name": "Winston County", "countryCode": "US", "stateCode": "MS", "latitude": "33.08849000", "longitude": "-89.03443000"}, {"name": "Woodville", "countryCode": "US", "stateCode": "MS", "latitude": "31.10462000", "longitude": "-91.29956000"}, {"name": "Yalobusha County", "countryCode": "US", "stateCode": "MS", "latitude": "34.02821000", "longitude": "-89.70763000"}, {"name": "Yazoo City", "countryCode": "US", "stateCode": "MS", "latitude": "32.85513000", "longitude": "-90.40565000"}, {"name": "Yazoo County", "countryCode": "US", "stateCode": "MS", "latitude": "32.78031000", "longitude": "-90.39642000"}]