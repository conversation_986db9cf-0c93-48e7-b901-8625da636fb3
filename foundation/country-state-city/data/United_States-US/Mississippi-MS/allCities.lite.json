[{"name": "Aberdeen", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Adams County", "countryCode": "US", "stateCode": "MS"}, {"name": "Alcorn County", "countryCode": "US", "stateCode": "MS"}, {"name": "Amite County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Arnold <PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Ashland", "countryCode": "US", "stateCode": "MS"}, {"name": "Attala County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>ld<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Batesville", "countryCode": "US", "stateCode": "MS"}, {"name": "Bay Saint Louis", "countryCode": "US", "stateCode": "MS"}, {"name": "Bay Springs", "countryCode": "US", "stateCode": "MS"}, {"name": "Beechwood", "countryCode": "US", "stateCode": "MS"}, {"name": "Belmont", "countryCode": "US", "stateCode": "MS"}, {"name": "Belzoni", "countryCode": "US", "stateCode": "MS"}, {"name": "Benton County", "countryCode": "US", "stateCode": "MS"}, {"name": "Biloxi", "countryCode": "US", "stateCode": "MS"}, {"name": "Bolivar County", "countryCode": "US", "stateCode": "MS"}, {"name": "Booneville", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Brookhaven", "countryCode": "US", "stateCode": "MS"}, {"name": "Brooksville", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Caledonia", "countryCode": "US", "stateCode": "MS"}, {"name": "Calhoun City", "countryCode": "US", "stateCode": "MS"}, {"name": "Calhoun County", "countryCode": "US", "stateCode": "MS"}, {"name": "Canton", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Carroll County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Carthage", "countryCode": "US", "stateCode": "MS"}, {"name": "Centreville", "countryCode": "US", "stateCode": "MS"}, {"name": "Charleston", "countryCode": "US", "stateCode": "MS"}, {"name": "Chickasaw County", "countryCode": "US", "stateCode": "MS"}, {"name": "Choctaw County", "countryCode": "US", "stateCode": "MS"}, {"name": "Claiborne County", "countryCode": "US", "stateCode": "MS"}, {"name": "Clarke County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Clay County", "countryCode": "US", "stateCode": "MS"}, {"name": "Cleary", "countryCode": "US", "stateCode": "MS"}, {"name": "Cleveland", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Coahoma County", "countryCode": "US", "stateCode": "MS"}, {"name": "Coldwater", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Collinsville", "countryCode": "US", "stateCode": "MS"}, {"name": "Columbia", "countryCode": "US", "stateCode": "MS"}, {"name": "Columbus", "countryCode": "US", "stateCode": "MS"}, {"name": "Columbus Air Force Base", "countryCode": "US", "stateCode": "MS"}, {"name": "Como", "countryCode": "US", "stateCode": "MS"}, {"name": "Conehatta", "countryCode": "US", "stateCode": "MS"}, {"name": "Copiah County", "countryCode": "US", "stateCode": "MS"}, {"name": "Corinth", "countryCode": "US", "stateCode": "MS"}, {"name": "Covington County", "countryCode": "US", "stateCode": "MS"}, {"name": "Crystal Springs", "countryCode": "US", "stateCode": "MS"}, {"name": "D'Iberville", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "De Soto County", "countryCode": "US", "stateCode": "MS"}, {"name": "Decatur", "countryCode": "US", "stateCode": "MS"}, {"name": "Der<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Diamondhead", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Ellisville", "countryCode": "US", "stateCode": "MS"}, {"name": "Escatawpa", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Farmington", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Flora", "countryCode": "US", "stateCode": "MS"}, {"name": "Florence", "countryCode": "US", "stateCode": "MS"}, {"name": "Flowood", "countryCode": "US", "stateCode": "MS"}, {"name": "Forest", "countryCode": "US", "stateCode": "MS"}, {"name": "Forrest County", "countryCode": "US", "stateCode": "MS"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "MS"}, {"name": "Friars Point", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "George County", "countryCode": "US", "stateCode": "MS"}, {"name": "Glendale", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Greene County", "countryCode": "US", "stateCode": "MS"}, {"name": "Greenville", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Grenada", "countryCode": "US", "stateCode": "MS"}, {"name": "Grenada County", "countryCode": "US", "stateCode": "MS"}, {"name": "Gulf Hills", "countryCode": "US", "stateCode": "MS"}, {"name": "Gulf Park Estates", "countryCode": "US", "stateCode": "MS"}, {"name": "Gulfport", "countryCode": "US", "stateCode": "MS"}, {"name": "Guntown", "countryCode": "US", "stateCode": "MS"}, {"name": "Hancock County", "countryCode": "US", "stateCode": "MS"}, {"name": "Harrison County", "countryCode": "US", "stateCode": "MS"}, {"name": "Hattiesburg", "countryCode": "US", "stateCode": "MS"}, {"name": "Hazlehurst", "countryCode": "US", "stateCode": "MS"}, {"name": "Helena", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Hickory Hills", "countryCode": "US", "stateCode": "MS"}, {"name": "Hide-A-Way Lake", "countryCode": "US", "stateCode": "MS"}, {"name": "Hillsboro", "countryCode": "US", "stateCode": "MS"}, {"name": "Hinds County", "countryCode": "US", "stateCode": "MS"}, {"name": "Hollandale", "countryCode": "US", "stateCode": "MS"}, {"name": "Holly Springs", "countryCode": "US", "stateCode": "MS"}, {"name": "Holmes County", "countryCode": "US", "stateCode": "MS"}, {"name": "Horn Lake", "countryCode": "US", "stateCode": "MS"}, {"name": "Houston", "countryCode": "US", "stateCode": "MS"}, {"name": "Humphreys County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Indianola", "countryCode": "US", "stateCode": "MS"}, {"name": "Issaquena County", "countryCode": "US", "stateCode": "MS"}, {"name": "Itawamba County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON> Ben<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "MS"}, {"name": "Jasper County", "countryCode": "US", "stateCode": "MS"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "MS"}, {"name": "Jefferson Davis County", "countryCode": "US", "stateCode": "MS"}, {"name": "Jones County", "countryCode": "US", "stateCode": "MS"}, {"name": "Jonestown", "countryCode": "US", "stateCode": "MS"}, {"name": "Kearney Park", "countryCode": "US", "stateCode": "MS"}, {"name": "Kemper County", "countryCode": "US", "stateCode": "MS"}, {"name": "Kiln", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Lafayette County", "countryCode": "US", "stateCode": "MS"}, {"name": "Lamar County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Lauderdale County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "MS"}, {"name": "Leake County", "countryCode": "US", "stateCode": "MS"}, {"name": "Leakesville", "countryCode": "US", "stateCode": "MS"}, {"name": "Lee County", "countryCode": "US", "stateCode": "MS"}, {"name": "Leflore County", "countryCode": "US", "stateCode": "MS"}, {"name": "Leland", "countryCode": "US", "stateCode": "MS"}, {"name": "Lexington", "countryCode": "US", "stateCode": "MS"}, {"name": "Liberty", "countryCode": "US", "stateCode": "MS"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "MS"}, {"name": "Long Beach", "countryCode": "US", "stateCode": "MS"}, {"name": "Louisville", "countryCode": "US", "stateCode": "MS"}, {"name": "Lowndes County", "countryCode": "US", "stateCode": "MS"}, {"name": "Lucedale", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Lynchburg", "countryCode": "US", "stateCode": "MS"}, {"name": "Macon", "countryCode": "US", "stateCode": "MS"}, {"name": "Madison", "countryCode": "US", "stateCode": "MS"}, {"name": "Madison County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Magnolia", "countryCode": "US", "stateCode": "MS"}, {"name": "Mantachie", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Marion County", "countryCode": "US", "stateCode": "MS"}, {"name": "Marks", "countryCode": "US", "stateCode": "MS"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "MS"}, {"name": "Mayersville", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Meadville", "countryCode": "US", "stateCode": "MS"}, {"name": "Mendenhall", "countryCode": "US", "stateCode": "MS"}, {"name": "Meridian", "countryCode": "US", "stateCode": "MS"}, {"name": "Meridian Station", "countryCode": "US", "stateCode": "MS"}, {"name": "Metcalfe", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "MS"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Moorhead", "countryCode": "US", "stateCode": "MS"}, {"name": "Morgantown", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Moss Point", "countryCode": "US", "stateCode": "MS"}, {"name": "Mound <PERSON>ou", "countryCode": "US", "stateCode": "MS"}, {"name": "Natchez", "countryCode": "US", "stateCode": "MS"}, {"name": "Nellieburg", "countryCode": "US", "stateCode": "MS"}, {"name": "Neshoba County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>tleton", "countryCode": "US", "stateCode": "MS"}, {"name": "New Albany", "countryCode": "US", "stateCode": "MS"}, {"name": "New Augusta", "countryCode": "US", "stateCode": "MS"}, {"name": "New Hope", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Newton County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "North Tunica", "countryCode": "US", "stateCode": "MS"}, {"name": "Noxubee County", "countryCode": "US", "stateCode": "MS"}, {"name": "Ocean Springs", "countryCode": "US", "stateCode": "MS"}, {"name": "Okolona", "countryCode": "US", "stateCode": "MS"}, {"name": "Oktibbeha County", "countryCode": "US", "stateCode": "MS"}, {"name": "Olive Branch", "countryCode": "US", "stateCode": "MS"}, {"name": "Oxford", "countryCode": "US", "stateCode": "MS"}, {"name": "Panola County", "countryCode": "US", "stateCode": "MS"}, {"name": "Pascagoula", "countryCode": "US", "stateCode": "MS"}, {"name": "Pass Christian", "countryCode": "US", "stateCode": "MS"}, {"name": "Pearl", "countryCode": "US", "stateCode": "MS"}, {"name": "Pearl River", "countryCode": "US", "stateCode": "MS"}, {"name": "Pearl River County", "countryCode": "US", "stateCode": "MS"}, {"name": "Pearlington", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Perry County", "countryCode": "US", "stateCode": "MS"}, {"name": "Petal", "countryCode": "US", "stateCode": "MS"}, {"name": "Philadelphia", "countryCode": "US", "stateCode": "MS"}, {"name": "Picayune", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>ens", "countryCode": "US", "stateCode": "MS"}, {"name": "Pike County", "countryCode": "US", "stateCode": "MS"}, {"name": "Pittsboro", "countryCode": "US", "stateCode": "MS"}, {"name": "Plantersville", "countryCode": "US", "stateCode": "MS"}, {"name": "Pontotoc", "countryCode": "US", "stateCode": "MS"}, {"name": "Pontotoc County", "countryCode": "US", "stateCode": "MS"}, {"name": "Poplarville", "countryCode": "US", "stateCode": "MS"}, {"name": "Port Gibson", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Prentiss County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Quitman County", "countryCode": "US", "stateCode": "MS"}, {"name": "Raleigh", "countryCode": "US", "stateCode": "MS"}, {"name": "Rankin County", "countryCode": "US", "stateCode": "MS"}, {"name": "Rawls Springs", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Richland", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Ridgeland", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Rolling Fork", "countryCode": "US", "stateCode": "MS"}, {"name": "Rosedale", "countryCode": "US", "stateCode": "MS"}, {"name": "Ruleville", "countryCode": "US", "stateCode": "MS"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Saltillo", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>rdis", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Scott County", "countryCode": "US", "stateCode": "MS"}, {"name": "Senatobia", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Sharkey County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Simpson County", "countryCode": "US", "stateCode": "MS"}, {"name": "Smith County", "countryCode": "US", "stateCode": "MS"}, {"name": "Southaven", "countryCode": "US", "stateCode": "MS"}, {"name": "Starkville", "countryCode": "US", "stateCode": "MS"}, {"name": "Stone County", "countryCode": "US", "stateCode": "MS"}, {"name": "Stonewall", "countryCode": "US", "stateCode": "MS"}, {"name": "Summit", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Sunflower", "countryCode": "US", "stateCode": "MS"}, {"name": "Sunflower County", "countryCode": "US", "stateCode": "MS"}, {"name": "Tallahatchie County", "countryCode": "US", "stateCode": "MS"}, {"name": "Tate County", "countryCode": "US", "stateCode": "MS"}, {"name": "Taylorsville", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Tippah County", "countryCode": "US", "stateCode": "MS"}, {"name": "Tishomingo County", "countryCode": "US", "stateCode": "MS"}, {"name": "Tunica", "countryCode": "US", "stateCode": "MS"}, {"name": "Tunica County", "countryCode": "US", "stateCode": "MS"}, {"name": "Tunica Resorts", "countryCode": "US", "stateCode": "MS"}, {"name": "Tupelo", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Tylertown", "countryCode": "US", "stateCode": "MS"}, {"name": "Union", "countryCode": "US", "stateCode": "MS"}, {"name": "Union County", "countryCode": "US", "stateCode": "MS"}, {"name": "University", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Vardaman", "countryCode": "US", "stateCode": "MS"}, {"name": "Verona", "countryCode": "US", "stateCode": "MS"}, {"name": "Vicksburg", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Walls", "countryCode": "US", "stateCode": "MS"}, {"name": "Walnut Grove", "countryCode": "US", "stateCode": "MS"}, {"name": "Walthall", "countryCode": "US", "stateCode": "MS"}, {"name": "Walthall County", "countryCode": "US", "stateCode": "MS"}, {"name": "Warren County", "countryCode": "US", "stateCode": "MS"}, {"name": "Washington County", "countryCode": "US", "stateCode": "MS"}, {"name": "Water Valley", "countryCode": "US", "stateCode": "MS"}, {"name": "Waveland", "countryCode": "US", "stateCode": "MS"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "MS"}, {"name": "Waynesboro", "countryCode": "US", "stateCode": "MS"}, {"name": "Webster County", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "West Gulfport", "countryCode": "US", "stateCode": "MS"}, {"name": "West Hattiesburg", "countryCode": "US", "stateCode": "MS"}, {"name": "West Point", "countryCode": "US", "stateCode": "MS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MS"}, {"name": "Wilkinson County", "countryCode": "US", "stateCode": "MS"}, {"name": "Winona", "countryCode": "US", "stateCode": "MS"}, {"name": "Winston County", "countryCode": "US", "stateCode": "MS"}, {"name": "Woodville", "countryCode": "US", "stateCode": "MS"}, {"name": "Yalobusha County", "countryCode": "US", "stateCode": "MS"}, {"name": "Yazoo City", "countryCode": "US", "stateCode": "MS"}, {"name": "Yazoo County", "countryCode": "US", "stateCode": "MS"}]