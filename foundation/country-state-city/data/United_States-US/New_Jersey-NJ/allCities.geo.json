[{"name": "Absecon", "countryCode": "US", "stateCode": "NJ", "latitude": "39.42845000", "longitude": "-74.49571000"}, {"name": "Allendale", "countryCode": "US", "stateCode": "NJ", "latitude": "41.04149000", "longitude": "-74.12903000"}, {"name": "Allentown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.17789000", "longitude": "-74.58349000"}, {"name": "Alloway", "countryCode": "US", "stateCode": "NJ", "latitude": "39.56095000", "longitude": "-75.36242000"}, {"name": "Alpha", "countryCode": "US", "stateCode": "NJ", "latitude": "40.66704000", "longitude": "-75.15740000"}, {"name": "Alpine", "countryCode": "US", "stateCode": "NJ", "latitude": "40.95593000", "longitude": "-73.93125000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.64093000", "longitude": "-74.88128000"}, {"name": "Asbury Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.22039000", "longitude": "-74.01208000"}, {"name": "Ashland", "countryCode": "US", "stateCode": "NJ", "latitude": "39.86317000", "longitude": "-75.00600000"}, {"name": "Atco", "countryCode": "US", "stateCode": "NJ", "latitude": "39.76984000", "longitude": "-74.88739000"}, {"name": "Atlantic City", "countryCode": "US", "stateCode": "NJ", "latitude": "39.36415000", "longitude": "-74.42306000"}, {"name": "Atlantic County", "countryCode": "US", "stateCode": "NJ", "latitude": "39.46883000", "longitude": "-74.63373000"}, {"name": "Atlantic Highlands", "countryCode": "US", "stateCode": "NJ", "latitude": "40.40789000", "longitude": "-74.03431000"}, {"name": "Audubon", "countryCode": "US", "stateCode": "NJ", "latitude": "39.89095000", "longitude": "-75.07295000"}, {"name": "Audubon Park", "countryCode": "US", "stateCode": "NJ", "latitude": "39.89650000", "longitude": "-75.08767000"}, {"name": "Avalon", "countryCode": "US", "stateCode": "NJ", "latitude": "39.10122000", "longitude": "-74.71766000"}, {"name": "Avenel", "countryCode": "US", "stateCode": "NJ", "latitude": "40.58038000", "longitude": "-74.28515000"}, {"name": "Avon-by-the-Sea", "countryCode": "US", "stateCode": "NJ", "latitude": "40.19234000", "longitude": "-74.01597000"}, {"name": "Barnegat", "countryCode": "US", "stateCode": "NJ", "latitude": "39.75318000", "longitude": "-74.22292000"}, {"name": "Barrington", "countryCode": "US", "stateCode": "NJ", "latitude": "39.86484000", "longitude": "-75.05517000"}, {"name": "Bayonne", "countryCode": "US", "stateCode": "NJ", "latitude": "40.66871000", "longitude": "-74.11431000"}, {"name": "Bayville", "countryCode": "US", "stateCode": "NJ", "latitude": "39.90929000", "longitude": "-74.15486000"}, {"name": "Beach Haven", "countryCode": "US", "stateCode": "NJ", "latitude": "39.55928000", "longitude": "-74.24320000"}, {"name": "Beach Haven West", "countryCode": "US", "stateCode": "NJ", "latitude": "39.66984000", "longitude": "-74.23181000"}, {"name": "Beachwood", "countryCode": "US", "stateCode": "NJ", "latitude": "39.93901000", "longitude": "-74.19292000"}, {"name": "Beattystown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.81315000", "longitude": "-74.84294000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.75400000", "longitude": "-75.35741000"}, {"name": "Bedminster", "countryCode": "US", "stateCode": "NJ", "latitude": "40.68066000", "longitude": "-74.64544000"}, {"name": "Belford", "countryCode": "US", "stateCode": "NJ", "latitude": "40.42594000", "longitude": "-74.08681000"}, {"name": "Belleville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.79371000", "longitude": "-74.15014000"}, {"name": "Bellmawr", "countryCode": "US", "stateCode": "NJ", "latitude": "39.86761000", "longitude": "-75.09462000"}, {"name": "Belmar", "countryCode": "US", "stateCode": "NJ", "latitude": "40.17845000", "longitude": "-74.02180000"}, {"name": "Belvidere", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82982000", "longitude": "-75.07767000"}, {"name": "Bergen County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.95977000", "longitude": "-74.07441000"}, {"name": "Bergenfield", "countryCode": "US", "stateCode": "NJ", "latitude": "40.92760000", "longitude": "-73.99736000"}, {"name": "Berkeley Heights", "countryCode": "US", "stateCode": "NJ", "latitude": "40.68343000", "longitude": "-74.44265000"}, {"name": "Berlin", "countryCode": "US", "stateCode": "NJ", "latitude": "39.79123000", "longitude": "-74.92905000"}, {"name": "Bernardsville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.71871000", "longitude": "-74.56932000"}, {"name": "Beverly", "countryCode": "US", "stateCode": "NJ", "latitude": "40.06539000", "longitude": "-74.91906000"}, {"name": "Blackwood", "countryCode": "US", "stateCode": "NJ", "latitude": "39.80234000", "longitude": "-75.06406000"}, {"name": "Bloomfield", "countryCode": "US", "stateCode": "NJ", "latitude": "40.80677000", "longitude": "-74.18542000"}, {"name": "Bloomingdale", "countryCode": "US", "stateCode": "NJ", "latitude": "41.00204000", "longitude": "-74.32654000"}, {"name": "Bogota", "countryCode": "US", "stateCode": "NJ", "latitude": "40.87621000", "longitude": "-74.02986000"}, {"name": "Boonton", "countryCode": "US", "stateCode": "NJ", "latitude": "40.90260000", "longitude": "-74.40710000"}, {"name": "Bordentown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.14622000", "longitude": "-74.71183000"}, {"name": "Bound Brook", "countryCode": "US", "stateCode": "NJ", "latitude": "40.56844000", "longitude": "-74.53849000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.20234000", "longitude": "-74.01208000"}, {"name": "Bradley Gardens", "countryCode": "US", "stateCode": "NJ", "latitude": "40.56288000", "longitude": "-74.65460000"}, {"name": "Brass Castle", "countryCode": "US", "stateCode": "NJ", "latitude": "40.76482000", "longitude": "-75.01101000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.42734000", "longitude": "-75.23408000"}, {"name": "Bridgewater", "countryCode": "US", "stateCode": "NJ", "latitude": "40.60079000", "longitude": "-74.64815000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.10789000", "longitude": "-74.05653000"}, {"name": "Brigantine", "countryCode": "US", "stateCode": "NJ", "latitude": "39.41012000", "longitude": "-74.36459000"}, {"name": "Brookdale", "countryCode": "US", "stateCode": "NJ", "latitude": "40.83371000", "longitude": "-74.18292000"}, {"name": "Brooklawn", "countryCode": "US", "stateCode": "NJ", "latitude": "39.87817000", "longitude": "-75.12073000"}, {"name": "Browns Mills", "countryCode": "US", "stateCode": "NJ", "latitude": "39.97261000", "longitude": "-74.58293000"}, {"name": "Brownville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.40066000", "longitude": "-74.29515000"}, {"name": "Budd Lake", "countryCode": "US", "stateCode": "NJ", "latitude": "40.87121000", "longitude": "-74.73405000"}, {"name": "Buena", "countryCode": "US", "stateCode": "NJ", "latitude": "39.51373000", "longitude": "-74.92462000"}, {"name": "Burlington", "countryCode": "US", "stateCode": "NJ", "latitude": "40.07122000", "longitude": "-74.86489000"}, {"name": "Burlington County", "countryCode": "US", "stateCode": "NJ", "latitude": "39.87769000", "longitude": "-74.66820000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "41.00371000", "longitude": "-74.34154000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.83982000", "longitude": "-74.27654000"}, {"name": "Califon", "countryCode": "US", "stateCode": "NJ", "latitude": "40.71954000", "longitude": "-74.83572000"}, {"name": "Camden", "countryCode": "US", "stateCode": "NJ", "latitude": "39.92595000", "longitude": "-75.11962000"}, {"name": "Camden County", "countryCode": "US", "stateCode": "NJ", "latitude": "39.80353000", "longitude": "-74.95976000"}, {"name": "Cape May", "countryCode": "US", "stateCode": "NJ", "latitude": "38.93511000", "longitude": "-74.90601000"}, {"name": "Cape May County", "countryCode": "US", "stateCode": "NJ", "latitude": "39.08513000", "longitude": "-74.84998000"}, {"name": "Cape May Court House", "countryCode": "US", "stateCode": "NJ", "latitude": "39.08261000", "longitude": "-74.82378000"}, {"name": "Carlstadt", "countryCode": "US", "stateCode": "NJ", "latitude": "40.84038000", "longitude": "-74.09070000"}, {"name": "Carneys Point", "countryCode": "US", "stateCode": "NJ", "latitude": "39.71122000", "longitude": "-75.47020000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.57733000", "longitude": "-74.22820000"}, {"name": "Cedar Glen Lakes", "countryCode": "US", "stateCode": "NJ", "latitude": "39.95234000", "longitude": "-74.39987000"}, {"name": "Cedar Glen West", "countryCode": "US", "stateCode": "NJ", "latitude": "40.04206000", "longitude": "-74.29265000"}, {"name": "Cedar Grove", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85177000", "longitude": "-74.22903000"}, {"name": "Chatham", "countryCode": "US", "stateCode": "NJ", "latitude": "40.74093000", "longitude": "-74.38376000"}, {"name": "Cherry Hill", "countryCode": "US", "stateCode": "NJ", "latitude": "39.93484000", "longitude": "-75.03073000"}, {"name": "Cherry Hill Mall", "countryCode": "US", "stateCode": "NJ", "latitude": "39.93595000", "longitude": "-75.00906000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.73234000", "longitude": "-74.88100000"}, {"name": "Chester", "countryCode": "US", "stateCode": "NJ", "latitude": "40.78427000", "longitude": "-74.69683000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.99678000", "longitude": "-74.99267000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.64094000", "longitude": "-74.31070000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.66011000", "longitude": "-75.09212000"}, {"name": "Clearbrook Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.30983000", "longitude": "-74.46460000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.81150000", "longitude": "-74.98294000"}, {"name": "Cliffside Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82149000", "longitude": "-73.98764000"}, {"name": "Cliffwood Beach", "countryCode": "US", "stateCode": "NJ", "latitude": "40.44205000", "longitude": "-74.21681000"}, {"name": "Clifton", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85843000", "longitude": "-74.16376000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.63677000", "longitude": "-74.90989000"}, {"name": "Closter", "countryCode": "US", "stateCode": "NJ", "latitude": "40.97315000", "longitude": "-73.96153000"}, {"name": "Collings Lakes", "countryCode": "US", "stateCode": "NJ", "latitude": "39.59567000", "longitude": "-74.********"}, {"name": "Collingswood", "countryCode": "US", "stateCode": "NJ", "latitude": "39.91817000", "longitude": "-75.07128000"}, {"name": "Colonia", "countryCode": "US", "stateCode": "NJ", "latitude": "40.57455000", "longitude": "-74.30209000"}, {"name": "Concordia", "countryCode": "US", "stateCode": "NJ", "latitude": "40.31094000", "longitude": "-74.44821000"}, {"name": "Country Lake Estates", "countryCode": "US", "stateCode": "NJ", "latitude": "39.94262000", "longitude": "-74.54404000"}, {"name": "Cranbury", "countryCode": "US", "stateCode": "NJ", "latitude": "40.31622000", "longitude": "-74.51376000"}, {"name": "Crandon Lakes", "countryCode": "US", "stateCode": "NJ", "latitude": "41.12426000", "longitude": "-74.83989000"}, {"name": "Cranford", "countryCode": "US", "stateCode": "NJ", "latitude": "40.65844000", "longitude": "-74.29959000"}, {"name": "Cresskill", "countryCode": "US", "stateCode": "NJ", "latitude": "40.94149000", "longitude": "-73.95930000"}, {"name": "Crestwood Village", "countryCode": "US", "stateCode": "NJ", "latitude": "39.94817000", "longitude": "-74.36070000"}, {"name": "Cumberland County", "countryCode": "US", "stateCode": "NJ", "latitude": "39.32807000", "longitude": "-75.12934000"}, {"name": "Dayton", "countryCode": "US", "stateCode": "NJ", "latitude": "40.37261000", "longitude": "-74.51015000"}, {"name": "Delanco", "countryCode": "US", "stateCode": "NJ", "latitude": "40.05067000", "longitude": "-74.95350000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.95732000", "longitude": "-73.96347000"}, {"name": "Dover", "countryCode": "US", "stateCode": "NJ", "latitude": "40.88399000", "longitude": "-74.56210000"}, {"name": "Dover Beaches North", "countryCode": "US", "stateCode": "NJ", "latitude": "39.99123000", "longitude": "-74.06375000"}, {"name": "Dover Beaches South", "countryCode": "US", "stateCode": "NJ", "latitude": "39.95567000", "longitude": "-74.07430000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.94065000", "longitude": "-73.99681000"}, {"name": "Dunellen", "countryCode": "US", "stateCode": "NJ", "latitude": "40.58927000", "longitude": "-74.47182000"}, {"name": "East Brunswick", "countryCode": "US", "stateCode": "NJ", "latitude": "40.42788000", "longitude": "-74.41598000"}, {"name": "East Franklin", "countryCode": "US", "stateCode": "NJ", "latitude": "40.49330000", "longitude": "-74.47110000"}, {"name": "East Freehold", "countryCode": "US", "stateCode": "NJ", "latitude": "40.28094000", "longitude": "-74.25126000"}, {"name": "East Hanover", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82010000", "longitude": "-74.36487000"}, {"name": "East Newark", "countryCode": "US", "stateCode": "NJ", "latitude": "40.74843000", "longitude": "-74.16181000"}, {"name": "East Orange", "countryCode": "US", "stateCode": "NJ", "latitude": "40.76732000", "longitude": "-74.20487000"}, {"name": "East Rutherford", "countryCode": "US", "stateCode": "NJ", "latitude": "40.83399000", "longitude": "-74.09709000"}, {"name": "Eatontown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.29622000", "longitude": "-74.05097000"}, {"name": "Echelon", "countryCode": "US", "stateCode": "NJ", "latitude": "39.84845000", "longitude": "-74.99572000"}, {"name": "Edgewater", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82704000", "longitude": "-73.97569000"}, {"name": "Edgewater Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.06817000", "longitude": "-74.90072000"}, {"name": "Edison", "countryCode": "US", "stateCode": "NJ", "latitude": "40.51872000", "longitude": "-74.41210000"}, {"name": "Egg Harbor City", "countryCode": "US", "stateCode": "NJ", "latitude": "39.52873000", "longitude": "-74.64794000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.66399000", "longitude": "-74.21070000"}, {"name": "Ellisburg", "countryCode": "US", "stateCode": "NJ", "latitude": "39.91372000", "longitude": "-75.01045000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.59511000", "longitude": "-75.17018000"}, {"name": "Elmwood Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.90399000", "longitude": "-74.11848000"}, {"name": "Elwood", "countryCode": "US", "stateCode": "NJ", "latitude": "39.57651000", "longitude": "-74.71683000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.97621000", "longitude": "-74.02625000"}, {"name": "Englewood", "countryCode": "US", "stateCode": "NJ", "latitude": "40.89288000", "longitude": "-73.97264000"}, {"name": "Englewood Cliffs", "countryCode": "US", "stateCode": "NJ", "latitude": "40.88538000", "longitude": "-73.95236000"}, {"name": "Englishtown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.29733000", "longitude": "-74.35820000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "38.98776000", "longitude": "-74.90170000"}, {"name": "Essex County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.78707000", "longitude": "-74.24687000"}, {"name": "Essex Fells", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82454000", "longitude": "-74.28459000"}, {"name": "Estell Manor", "countryCode": "US", "stateCode": "NJ", "latitude": "39.41206000", "longitude": "-74.74239000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.26983000", "longitude": "-74.79988000"}, {"name": "Fair Haven", "countryCode": "US", "stateCode": "NJ", "latitude": "40.36067000", "longitude": "-74.03819000"}, {"name": "Fair Lawn", "countryCode": "US", "stateCode": "NJ", "latitude": "40.94038000", "longitude": "-74.13181000"}, {"name": "Fairfield", "countryCode": "US", "stateCode": "NJ", "latitude": "40.88371000", "longitude": "-74.30598000"}, {"name": "Fairton", "countryCode": "US", "stateCode": "NJ", "latitude": "39.38178000", "longitude": "-75.21991000"}, {"name": "Fairview", "countryCode": "US", "stateCode": "NJ", "latitude": "40.81260000", "longitude": "-73.99903000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.64094000", "longitude": "-74.38348000"}, {"name": "Farmingdale", "countryCode": "US", "stateCode": "NJ", "latitude": "40.19650000", "longitude": "-74.16848000"}, {"name": "Finderne", "countryCode": "US", "stateCode": "NJ", "latitude": "40.56316000", "longitude": "-74.57766000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.51233000", "longitude": "-74.85933000"}, {"name": "Florence", "countryCode": "US", "stateCode": "NJ", "latitude": "39.73428000", "longitude": "-74.91822000"}, {"name": "Florham Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.78788000", "longitude": "-74.38821000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.60206000", "longitude": "-74.84267000"}, {"name": "Fords", "countryCode": "US", "stateCode": "NJ", "latitude": "40.52927000", "longitude": "-74.31598000"}, {"name": "Forked River", "countryCode": "US", "stateCode": "NJ", "latitude": "39.83984000", "longitude": "-74.19014000"}, {"name": "Fort Dix", "countryCode": "US", "stateCode": "NJ", "latitude": "40.02984000", "longitude": "-74.61849000"}, {"name": "Fort Lee", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85093000", "longitude": "-73.97014000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "41.12204000", "longitude": "-74.58044000"}, {"name": "Franklin Center", "countryCode": "US", "stateCode": "NJ", "latitude": "40.53153000", "longitude": "-74.54141000"}, {"name": "Franklin Lakes", "countryCode": "US", "stateCode": "NJ", "latitude": "41.01676000", "longitude": "-74.20570000"}, {"name": "Franklin Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.43899000", "longitude": "-74.53515000"}, {"name": "Freehold", "countryCode": "US", "stateCode": "NJ", "latitude": "40.26011000", "longitude": "-74.27376000"}, {"name": "Frenchtown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.52621000", "longitude": "-75.06156000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.88149000", "longitude": "-74.11320000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.65177000", "longitude": "-74.32293000"}, {"name": "Gibbsboro", "countryCode": "US", "stateCode": "NJ", "latitude": "39.83817000", "longitude": "-74.96489000"}, {"name": "Gibbstown", "countryCode": "US", "stateCode": "NJ", "latitude": "39.82511000", "longitude": "-75.28352000"}, {"name": "Gladstone", "countryCode": "US", "stateCode": "NJ", "latitude": "40.72260000", "longitude": "-74.66544000"}, {"name": "Glassboro", "countryCode": "US", "stateCode": "NJ", "latitude": "39.70289000", "longitude": "-75.11184000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.69677000", "longitude": "-74.94072000"}, {"name": "Glen Ridge", "countryCode": "US", "stateCode": "NJ", "latitude": "40.80538000", "longitude": "-74.20376000"}, {"name": "Glen Rock", "countryCode": "US", "stateCode": "NJ", "latitude": "40.96288000", "longitude": "-74.13292000"}, {"name": "Glendora", "countryCode": "US", "stateCode": "NJ", "latitude": "39.83956000", "longitude": "-75.07351000"}, {"name": "Gloucester City", "countryCode": "US", "stateCode": "NJ", "latitude": "39.89178000", "longitude": "-75.11629000"}, {"name": "Gloucester County", "countryCode": "US", "stateCode": "NJ", "latitude": "39.71731000", "longitude": "-75.14167000"}, {"name": "Golden Triangle", "countryCode": "US", "stateCode": "NJ", "latitude": "39.92789000", "longitude": "-75.03878000"}, {"name": "<PERSON> Knoll", "countryCode": "US", "stateCode": "NJ", "latitude": "40.60010000", "longitude": "-74.61210000"}, {"name": "Greentree", "countryCode": "US", "stateCode": "NJ", "latitude": "39.89706000", "longitude": "-74.95572000"}, {"name": "Groveville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.16983000", "longitude": "-74.67155000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.79205000", "longitude": "-74.00375000"}, {"name": "Hackensack", "countryCode": "US", "stateCode": "NJ", "latitude": "40.88593000", "longitude": "-74.04347000"}, {"name": "Hackettstown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85399000", "longitude": "-74.82906000"}, {"name": "Haddon Heights", "countryCode": "US", "stateCode": "NJ", "latitude": "39.87734000", "longitude": "-75.06462000"}, {"name": "Haddonfield", "countryCode": "US", "stateCode": "NJ", "latitude": "39.89150000", "longitude": "-75.03767000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.93565000", "longitude": "-74.18626000"}, {"name": "Hamburg", "countryCode": "US", "stateCode": "NJ", "latitude": "41.15343000", "longitude": "-74.57627000"}, {"name": "Hamilton Square", "countryCode": "US", "stateCode": "NJ", "latitude": "40.22733000", "longitude": "-74.65321000"}, {"name": "Hammonton", "countryCode": "US", "stateCode": "NJ", "latitude": "39.63651000", "longitude": "-74.80239000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.70704000", "longitude": "-74.95600000"}, {"name": "Hanover", "countryCode": "US", "stateCode": "NJ", "latitude": "40.80454000", "longitude": "-74.36682000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "41.05454000", "longitude": "-74.93212000"}, {"name": "Harrington Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.98371000", "longitude": "-73.97986000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.74649000", "longitude": "-74.15626000"}, {"name": "Hasbrouck Heights", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85816000", "longitude": "-74.08070000"}, {"name": "Haworth", "countryCode": "US", "stateCode": "NJ", "latitude": "40.96093000", "longitude": "-73.99014000"}, {"name": "Hawthorne", "countryCode": "US", "stateCode": "NJ", "latitude": "40.94926000", "longitude": "-74.15375000"}, {"name": "Hazlet Township", "countryCode": "US", "stateCode": "NJ", "latitude": "40.42685010", "longitude": "-74.20682440"}, {"name": "Heathcote", "countryCode": "US", "stateCode": "NJ", "latitude": "40.38872000", "longitude": "-74.57571000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.37677000", "longitude": "-74.42460000"}, {"name": "High Bridge", "countryCode": "US", "stateCode": "NJ", "latitude": "40.66705000", "longitude": "-74.89572000"}, {"name": "Highland Lake", "countryCode": "US", "stateCode": "NJ", "latitude": "41.17676000", "longitude": "-74.45655000"}, {"name": "Highland Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.49594000", "longitude": "-74.42432000"}, {"name": "Highlands", "countryCode": "US", "stateCode": "NJ", "latitude": "40.40372000", "longitude": "-73.99153000"}, {"name": "Hightstown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.26955000", "longitude": "-74.52321000"}, {"name": "Hillsdale", "countryCode": "US", "stateCode": "NJ", "latitude": "41.00260000", "longitude": "-74.04042000"}, {"name": "Hillside", "countryCode": "US", "stateCode": "NJ", "latitude": "40.70121000", "longitude": "-74.23015000"}, {"name": "Ho-Ho-Kus", "countryCode": "US", "stateCode": "NJ", "latitude": "40.99649000", "longitude": "-74.10125000"}, {"name": "Hoboken", "countryCode": "US", "stateCode": "NJ", "latitude": "40.74399000", "longitude": "-74.03236000"}, {"name": "Holiday City South", "countryCode": "US", "stateCode": "NJ", "latitude": "39.95324000", "longitude": "-74.23778000"}, {"name": "Holiday City-Berkeley", "countryCode": "US", "stateCode": "NJ", "latitude": "39.96380000", "longitude": "-74.27803000"}, {"name": "Holiday Heights", "countryCode": "US", "stateCode": "NJ", "latitude": "39.94595000", "longitude": "-74.25403000"}, {"name": "Hopatcong", "countryCode": "US", "stateCode": "NJ", "latitude": "40.93288000", "longitude": "-74.65933000"}, {"name": "Hopatcong Hills", "countryCode": "US", "stateCode": "NJ", "latitude": "40.94399000", "longitude": "-74.67072000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.38927000", "longitude": "-74.76183000"}, {"name": "Hudson County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.73094000", "longitude": "-74.07594000"}, {"name": "Hunterdon County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.56729000", "longitude": "-74.91222000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.73232000", "longitude": "-74.23487000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.57538000", "longitude": "-74.32237000"}, {"name": "Island Heights", "countryCode": "US", "stateCode": "NJ", "latitude": "39.94206000", "longitude": "-74.14986000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.77650000", "longitude": "-74.86238000"}, {"name": "Jamesburg", "countryCode": "US", "stateCode": "NJ", "latitude": "40.35261000", "longitude": "-74.44015000"}, {"name": "Jersey City", "countryCode": "US", "stateCode": "NJ", "latitude": "40.72816000", "longitude": "-74.07764000"}, {"name": "Keansburg", "countryCode": "US", "stateCode": "NJ", "latitude": "40.44177000", "longitude": "-74.12986000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.76843000", "longitude": "-74.14542000"}, {"name": "Kendall Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.42094000", "longitude": "-74.56071000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.67649000", "longitude": "-74.29070000"}, {"name": "Kenvil", "countryCode": "US", "stateCode": "NJ", "latitude": "40.87982000", "longitude": "-74.61849000"}, {"name": "Keyport", "countryCode": "US", "stateCode": "NJ", "latitude": "40.43316000", "longitude": "-74.19959000"}, {"name": "Kingston", "countryCode": "US", "stateCode": "NJ", "latitude": "40.37538000", "longitude": "-74.61349000"}, {"name": "Kingston Estates", "countryCode": "US", "stateCode": "NJ", "latitude": "39.92372000", "longitude": "-74.98795000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "41.00176000", "longitude": "-74.36710000"}, {"name": "Lake Como", "countryCode": "US", "stateCode": "NJ", "latitude": "40.15984000", "longitude": "-74.02819000"}, {"name": "Lake Mohawk", "countryCode": "US", "stateCode": "NJ", "latitude": "41.01843000", "longitude": "-74.66016000"}, {"name": "Lake Telemark", "countryCode": "US", "stateCode": "NJ", "latitude": "40.95677000", "longitude": "-74.49793000"}, {"name": "Lakehurst", "countryCode": "US", "stateCode": "NJ", "latitude": "40.********", "longitude": "-74.31126000"}, {"name": "Lakewood", "countryCode": "US", "stateCode": "NJ", "latitude": "40.09789000", "longitude": "-74.21764000"}, {"name": "Lambertville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.36594000", "longitude": "-74.94294000"}, {"name": "Landing", "countryCode": "US", "stateCode": "NJ", "latitude": "40.90510000", "longitude": "-74.66516000"}, {"name": "Laurel <PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.33956000", "longitude": "-75.02990000"}, {"name": "Laurel Springs", "countryCode": "US", "stateCode": "NJ", "latitude": "39.82011000", "longitude": "-75.00628000"}, {"name": "Laurence <PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.45677000", "longitude": "-74.24653000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.97040000", "longitude": "-74.06875000"}, {"name": "Lawnside", "countryCode": "US", "stateCode": "NJ", "latitude": "39.86650000", "longitude": "-75.02823000"}, {"name": "Lawrenceville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.29733000", "longitude": "-74.72960000"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "NJ", "latitude": "40.64177000", "longitude": "-74.83600000"}, {"name": "Leisure Knoll", "countryCode": "US", "stateCode": "NJ", "latitude": "40.01901000", "longitude": "-74.29209000"}, {"name": "Leisure Village", "countryCode": "US", "stateCode": "NJ", "latitude": "40.04262000", "longitude": "-74.18486000"}, {"name": "Leisure Village East", "countryCode": "US", "stateCode": "NJ", "latitude": "40.03012000", "longitude": "-74.16431000"}, {"name": "Leisure Village West-Pine Lake Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.00416000", "longitude": "-74.26629000"}, {"name": "Leisuretowne", "countryCode": "US", "stateCode": "NJ", "latitude": "39.89234000", "longitude": "-74.70210000"}, {"name": "Leonardo", "countryCode": "US", "stateCode": "NJ", "latitude": "40.41733000", "longitude": "-74.06208000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.86149000", "longitude": "-73.98819000"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.92427000", "longitude": "-74.30209000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.33067000", "longitude": "-74.12097000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.62205000", "longitude": "-74.24459000"}, {"name": "Lindenwold", "countryCode": "US", "stateCode": "NJ", "latitude": "39.82428000", "longitude": "-74.99767000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.33984000", "longitude": "-74.57516000"}, {"name": "Little Falls", "countryCode": "US", "stateCode": "NJ", "latitude": "40.86899000", "longitude": "-74.20820000"}, {"name": "Little Ferry", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85288000", "longitude": "-74.04208000"}, {"name": "Little Silver", "countryCode": "US", "stateCode": "NJ", "latitude": "40.33678000", "longitude": "-74.04708000"}, {"name": "Livingston", "countryCode": "US", "stateCode": "NJ", "latitude": "40.79593000", "longitude": "-74.31487000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.88232000", "longitude": "-74.08320000"}, {"name": "Long Branch", "countryCode": "US", "stateCode": "NJ", "latitude": "40.30428000", "longitude": "-73.99236000"}, {"name": "Long Valley", "countryCode": "US", "stateCode": "NJ", "latitude": "40.78593000", "longitude": "-74.78016000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.81204000", "longitude": "-74.12431000"}, {"name": "Madison", "countryCode": "US", "stateCode": "NJ", "latitude": "40.75982000", "longitude": "-74.41710000"}, {"name": "Madison Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.45150000", "longitude": "-74.30792000"}, {"name": "Magnolia", "countryCode": "US", "stateCode": "NJ", "latitude": "39.85456000", "longitude": "-75.03906000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "41.08871000", "longitude": "-74.14376000"}, {"name": "Manahawkin", "countryCode": "US", "stateCode": "NJ", "latitude": "39.69540000", "longitude": "-74.25875000"}, {"name": "Manasquan", "countryCode": "US", "stateCode": "NJ", "latitude": "40.12623000", "longitude": "-74.04930000"}, {"name": "Mantua Township", "countryCode": "US", "stateCode": "NJ", "latitude": "39.75687570", "longitude": "-75.20697550"}, {"name": "Manville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.54094000", "longitude": "-74.58766000"}, {"name": "Maple Shade", "countryCode": "US", "stateCode": "NJ", "latitude": "39.95261000", "longitude": "-74.99239000"}, {"name": "Maplewood", "countryCode": "US", "stateCode": "NJ", "latitude": "40.73121000", "longitude": "-74.27348000"}, {"name": "Margate City", "countryCode": "US", "stateCode": "NJ", "latitude": "39.32789000", "longitude": "-74.50349000"}, {"name": "Marlboro", "countryCode": "US", "stateCode": "NJ", "latitude": "40.31539000", "longitude": "-74.24626000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.89122000", "longitude": "-74.92183000"}, {"name": "Martinsville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.60121000", "longitude": "-74.55905000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.41483000", "longitude": "-74.22959000"}, {"name": "Mays Landing", "countryCode": "US", "stateCode": "NJ", "latitude": "39.45234000", "longitude": "-74.72766000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.90260000", "longitude": "-74.06181000"}, {"name": "McGuire AFB", "countryCode": "US", "stateCode": "NJ", "latitude": "40.03977000", "longitude": "-74.58174000"}, {"name": "Medford Lakes", "countryCode": "US", "stateCode": "NJ", "latitude": "39.85845000", "longitude": "-74.80294000"}, {"name": "Mendham", "countryCode": "US", "stateCode": "NJ", "latitude": "40.77593000", "longitude": "-74.60071000"}, {"name": "Mercer County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.28340000", "longitude": "-74.70169000"}, {"name": "Mercerville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.23705000", "longitude": "-74.68655000"}, {"name": "Mercerville-Hamilton Square", "countryCode": "US", "stateCode": "NJ", "latitude": "40.23126000", "longitude": "-74.67223000"}, {"name": "Merchantville", "countryCode": "US", "stateCode": "NJ", "latitude": "39.94734000", "longitude": "-75.06656000"}, {"name": "Metuchen", "countryCode": "US", "stateCode": "NJ", "latitude": "40.54316000", "longitude": "-74.36320000"}, {"name": "Middlebush", "countryCode": "US", "stateCode": "NJ", "latitude": "40.49760000", "longitude": "-74.52932000"}, {"name": "Middlesex", "countryCode": "US", "stateCode": "NJ", "latitude": "40.57260000", "longitude": "-74.49265000"}, {"name": "Middlesex County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.44004000", "longitude": "-74.40889000"}, {"name": "Midland Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.98926000", "longitude": "-74.14070000"}, {"name": "Milford", "countryCode": "US", "stateCode": "NJ", "latitude": "40.56871000", "longitude": "-75.09462000"}, {"name": "Milltown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.45622000", "longitude": "-74.44321000"}, {"name": "Millville", "countryCode": "US", "stateCode": "NJ", "latitude": "39.40206000", "longitude": "-75.03934000"}, {"name": "Monmouth Beach", "countryCode": "US", "stateCode": "NJ", "latitude": "40.33039000", "longitude": "-73.98153000"}, {"name": "Monmouth County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.28755000", "longitude": "-74.15815000"}, {"name": "Monmouth Junction", "countryCode": "US", "stateCode": "NJ", "latitude": "40.37900000", "longitude": "-74.54654000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82593000", "longitude": "-74.20903000"}, {"name": "Montvale", "countryCode": "US", "stateCode": "NJ", "latitude": "41.04676000", "longitude": "-74.02292000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.84121000", "longitude": "-74.04514000"}, {"name": "Moorestown-Lenola", "countryCode": "US", "stateCode": "NJ", "latitude": "39.96591000", "longitude": "-74.96441000"}, {"name": "Morganville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.37650000", "longitude": "-74.24431000"}, {"name": "Morris County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.86203000", "longitude": "-74.54444000"}, {"name": "Morris Plains", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82177000", "longitude": "-74.48099000"}, {"name": "Morristown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.79677000", "longitude": "-74.48154000"}, {"name": "Mount Arlington", "countryCode": "US", "stateCode": "NJ", "latitude": "40.92593000", "longitude": "-74.63488000"}, {"name": "Mount Ephraim", "countryCode": "US", "stateCode": "NJ", "latitude": "39.87845000", "longitude": "-75.09267000"}, {"name": "Mount Holly", "countryCode": "US", "stateCode": "NJ", "latitude": "39.99289000", "longitude": "-74.78766000"}, {"name": "Mount Laurel", "countryCode": "US", "stateCode": "NJ", "latitude": "39.93400000", "longitude": "-74.89100000"}, {"name": "Mountain Lakes", "countryCode": "US", "stateCode": "NJ", "latitude": "40.89482000", "longitude": "-74.43293000"}, {"name": "Mountainside", "countryCode": "US", "stateCode": "NJ", "latitude": "40.67232000", "longitude": "-74.35737000"}, {"name": "Mullica Hill", "countryCode": "US", "stateCode": "NJ", "latitude": "39.73928000", "longitude": "-75.22407000"}, {"name": "Mystic Island", "countryCode": "US", "stateCode": "NJ", "latitude": "39.54428000", "longitude": "-74.38237000"}, {"name": "National Park", "countryCode": "US", "stateCode": "NJ", "latitude": "39.86595000", "longitude": "-75.17879000"}, {"name": "Navesink", "countryCode": "US", "stateCode": "NJ", "latitude": "40.39955000", "longitude": "-74.03542000"}, {"name": "Neptune City", "countryCode": "US", "stateCode": "NJ", "latitude": "40.20011000", "longitude": "-74.02792000"}, {"name": "Netcong", "countryCode": "US", "stateCode": "NJ", "latitude": "40.89899000", "longitude": "-74.70655000"}, {"name": "New Brunswick", "countryCode": "US", "stateCode": "NJ", "latitude": "40.48622000", "longitude": "-74.45182000"}, {"name": "New Egypt", "countryCode": "US", "stateCode": "NJ", "latitude": "40.06761000", "longitude": "-74.53071000"}, {"name": "New Milford", "countryCode": "US", "stateCode": "NJ", "latitude": "40.93510000", "longitude": "-74.01903000"}, {"name": "New Providence", "countryCode": "US", "stateCode": "NJ", "latitude": "40.69843000", "longitude": "-74.40154000"}, {"name": "Newark", "countryCode": "US", "stateCode": "NJ", "latitude": "40.73566000", "longitude": "-74.17237000"}, {"name": "Newfield", "countryCode": "US", "stateCode": "NJ", "latitude": "39.54688000", "longitude": "-75.02636000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "41.05815000", "longitude": "-74.75267000"}, {"name": "North Arlington", "countryCode": "US", "stateCode": "NJ", "latitude": "40.78843000", "longitude": "-74.13320000"}, {"name": "North Beach Haven", "countryCode": "US", "stateCode": "NJ", "latitude": "39.57317000", "longitude": "-74.23153000"}, {"name": "North Bergen", "countryCode": "US", "stateCode": "NJ", "latitude": "40.80427000", "longitude": "-74.01208000"}, {"name": "North Caldwell", "countryCode": "US", "stateCode": "NJ", "latitude": "40.86482000", "longitude": "-74.25820000"}, {"name": "North Cape May", "countryCode": "US", "stateCode": "NJ", "latitude": "38.98206000", "longitude": "-74.95795000"}, {"name": "North Haledon", "countryCode": "US", "stateCode": "NJ", "latitude": "40.95510000", "longitude": "-74.18598000"}, {"name": "North Middletown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.43955000", "longitude": "-74.11903000"}, {"name": "North Plainfield", "countryCode": "US", "stateCode": "NJ", "latitude": "40.63010000", "longitude": "-74.42737000"}, {"name": "North Wildwood", "countryCode": "US", "stateCode": "NJ", "latitude": "39.00067000", "longitude": "-74.79933000"}, {"name": "Northfield", "countryCode": "US", "stateCode": "NJ", "latitude": "39.37039000", "longitude": "-74.55015000"}, {"name": "Northvale", "countryCode": "US", "stateCode": "NJ", "latitude": "41.00649000", "longitude": "-73.94903000"}, {"name": "Norwood", "countryCode": "US", "stateCode": "NJ", "latitude": "40.99815000", "longitude": "-73.96180000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82232000", "longitude": "-74.15987000"}, {"name": "Oak Valley", "countryCode": "US", "stateCode": "NJ", "latitude": "39.80122000", "longitude": "-75.16240000"}, {"name": "Oakhurst", "countryCode": "US", "stateCode": "NJ", "latitude": "40.27094000", "longitude": "-74.01625000"}, {"name": "Oakland", "countryCode": "US", "stateCode": "NJ", "latitude": "41.01315000", "longitude": "-74.26431000"}, {"name": "Oak<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.90095000", "longitude": "-75.08462000"}, {"name": "Ocean Acres", "countryCode": "US", "stateCode": "NJ", "latitude": "39.74345000", "longitude": "-74.28098000"}, {"name": "Ocean City", "countryCode": "US", "stateCode": "NJ", "latitude": "39.27762000", "longitude": "-74.57460000"}, {"name": "Ocean County", "countryCode": "US", "stateCode": "NJ", "latitude": "39.86600000", "longitude": "-74.25003000"}, {"name": "Ocean Gate", "countryCode": "US", "stateCode": "NJ", "latitude": "39.92679000", "longitude": "-74.13375000"}, {"name": "Ocean Grove", "countryCode": "US", "stateCode": "NJ", "latitude": "40.21206000", "longitude": "-74.00653000"}, {"name": "Oceanport", "countryCode": "US", "stateCode": "NJ", "latitude": "40.31817000", "longitude": "-74.01514000"}, {"name": "Ogdensburg", "countryCode": "US", "stateCode": "NJ", "latitude": "41.08176000", "longitude": "-74.59238000"}, {"name": "Old Bridge", "countryCode": "US", "stateCode": "NJ", "latitude": "40.41483000", "longitude": "-74.36543000"}, {"name": "Old Tappan", "countryCode": "US", "stateCode": "NJ", "latitude": "41.01065000", "longitude": "-73.99125000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.54817000", "longitude": "-75.15463000"}, {"name": "Oradell", "countryCode": "US", "stateCode": "NJ", "latitude": "40.95871000", "longitude": "-74.03681000"}, {"name": "Orange", "countryCode": "US", "stateCode": "NJ", "latitude": "40.77066000", "longitude": "-74.23265000"}, {"name": "Oxford", "countryCode": "US", "stateCode": "NJ", "latitude": "40.80315000", "longitude": "-74.98962000"}, {"name": "Palisades Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.84816000", "longitude": "-73.99764000"}, {"name": "Palmyra", "countryCode": "US", "stateCode": "NJ", "latitude": "40.00706000", "longitude": "-75.02823000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.94454000", "longitude": "-74.07542000"}, {"name": "Park Ridge", "countryCode": "US", "stateCode": "NJ", "latitude": "41.03760000", "longitude": "-74.04070000"}, {"name": "Parsippany", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85788000", "longitude": "-74.42599000"}, {"name": "Passaic", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85677000", "longitude": "-74.12848000"}, {"name": "Passaic County", "countryCode": "US", "stateCode": "NJ", "latitude": "41.03370000", "longitude": "-74.30032000"}, {"name": "Paterson", "countryCode": "US", "stateCode": "NJ", "latitude": "40.91677000", "longitude": "-74.17181000"}, {"name": "Paul<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.83039000", "longitude": "-75.24046000"}, {"name": "Peapack", "countryCode": "US", "stateCode": "NJ", "latitude": "40.71677000", "longitude": "-74.65655000"}, {"name": "Pemberton", "countryCode": "US", "stateCode": "NJ", "latitude": "39.97206000", "longitude": "-74.68294000"}, {"name": "Pemberton Heights", "countryCode": "US", "stateCode": "NJ", "latitude": "39.96261000", "longitude": "-74.67877000"}, {"name": "Pennington", "countryCode": "US", "stateCode": "NJ", "latitude": "40.32844000", "longitude": "-74.79072000"}, {"name": "Penns Grove", "countryCode": "US", "stateCode": "NJ", "latitude": "39.72956000", "longitude": "-75.46797000"}, {"name": "Pennsauken", "countryCode": "US", "stateCode": "NJ", "latitude": "39.95622000", "longitude": "-75.05795000"}, {"name": "Pennsville", "countryCode": "US", "stateCode": "NJ", "latitude": "39.65345000", "longitude": "-75.51659000"}, {"name": "Perth Amboy", "countryCode": "US", "stateCode": "NJ", "latitude": "40.50677000", "longitude": "-74.26542000"}, {"name": "Phillipsburg", "countryCode": "US", "stateCode": "NJ", "latitude": "40.69371000", "longitude": "-75.19018000"}, {"name": "Pine Beach", "countryCode": "US", "stateCode": "NJ", "latitude": "39.93595000", "longitude": "-74.17097000"}, {"name": "Pine Hill", "countryCode": "US", "stateCode": "NJ", "latitude": "39.78428000", "longitude": "-74.99211000"}, {"name": "Pine Lake Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.00317000", "longitude": "-74.25653000"}, {"name": "Pine Ridge at Crestwood", "countryCode": "US", "stateCode": "NJ", "latitude": "39.95456000", "longitude": "-74.31515000"}, {"name": "Piscataway", "countryCode": "US", "stateCode": "NJ", "latitude": "40.49927000", "longitude": "-74.39904000"}, {"name": "Pitman", "countryCode": "US", "stateCode": "NJ", "latitude": "39.73289000", "longitude": "-75.13157000"}, {"name": "Plainfield", "countryCode": "US", "stateCode": "NJ", "latitude": "40.63371000", "longitude": "-74.40737000"}, {"name": "Plainsboro Center", "countryCode": "US", "stateCode": "NJ", "latitude": "40.33177000", "longitude": "-74.59460000"}, {"name": "Pleasantville", "countryCode": "US", "stateCode": "NJ", "latitude": "39.38984000", "longitude": "-74.52404000"}, {"name": "Point Pleasant", "countryCode": "US", "stateCode": "NJ", "latitude": "40.08317000", "longitude": "-74.06819000"}, {"name": "Point Pleasant Beach", "countryCode": "US", "stateCode": "NJ", "latitude": "40.09123000", "longitude": "-74.04791000"}, {"name": "Pomona", "countryCode": "US", "stateCode": "NJ", "latitude": "39.47845000", "longitude": "-74.57516000"}, {"name": "Pompton Lakes", "countryCode": "US", "stateCode": "NJ", "latitude": "41.00538000", "longitude": "-74.29070000"}, {"name": "Port Monmouth", "countryCode": "US", "stateCode": "NJ", "latitude": "40.43011000", "longitude": "-74.09847000"}, {"name": "Port Norris", "countryCode": "US", "stateCode": "NJ", "latitude": "39.24595000", "longitude": "-75.03518000"}, {"name": "Port Reading", "countryCode": "US", "stateCode": "NJ", "latitude": "40.56538000", "longitude": "-74.26042000"}, {"name": "Port Republic", "countryCode": "US", "stateCode": "NJ", "latitude": "39.52067000", "longitude": "-74.48571000"}, {"name": "Presidential Lakes Estates", "countryCode": "US", "stateCode": "NJ", "latitude": "39.91373000", "longitude": "-74.56460000"}, {"name": "Princeton", "countryCode": "US", "stateCode": "NJ", "latitude": "40.34872000", "longitude": "-74.65905000"}, {"name": "Princeton Junction", "countryCode": "US", "stateCode": "NJ", "latitude": "40.31733000", "longitude": "-74.61988000"}, {"name": "Princeton Meadows", "countryCode": "US", "stateCode": "NJ", "latitude": "40.33177000", "longitude": "-74.56377000"}, {"name": "Prospect Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.93704000", "longitude": "-74.17431000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.60816000", "longitude": "-74.27765000"}, {"name": "Ram<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.92872000", "longitude": "-74.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "41.********", "longitude": "-74.********"}, {"name": "Ramtown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.********", "longitude": "-74.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.********", "longitude": "-74.********"}, {"name": "Raritan", "countryCode": "US", "stateCode": "NJ", "latitude": "40.********", "longitude": "-74.********"}, {"name": "Red Bank", "countryCode": "US", "stateCode": "NJ", "latitude": "40.********", "longitude": "-74.********"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.********", "longitude": "-75.********"}, {"name": "Ridgefield", "countryCode": "US", "stateCode": "NJ", "latitude": "40.********", "longitude": "-74.********"}, {"name": "Ridgefield Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.********", "longitude": "-74.********"}, {"name": "Ridgewood", "countryCode": "US", "stateCode": "NJ", "latitude": "40.********", "longitude": "-74.********"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "41.********", "longitude": "-74.********"}, {"name": "Rio Grande", "countryCode": "US", "stateCode": "NJ", "latitude": "39.********", "longitude": "-74.********"}, {"name": "River Edge", "countryCode": "US", "stateCode": "NJ", "latitude": "40.********", "longitude": "-74.********"}, {"name": "River Vale", "countryCode": "US", "stateCode": "NJ", "latitude": "40.99538000", "longitude": "-74.01208000"}, {"name": "Riverdale", "countryCode": "US", "stateCode": "NJ", "latitude": "40.99399000", "longitude": "-74.30348000"}, {"name": "Riverton", "countryCode": "US", "stateCode": "NJ", "latitude": "40.01150000", "longitude": "-75.01489000"}, {"name": "Robbinsville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.21455000", "longitude": "-74.61932000"}, {"name": "Robertsville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.34622000", "longitude": "-74.28792000"}, {"name": "Rochelle Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.90732000", "longitude": "-74.07514000"}, {"name": "Rockaway", "countryCode": "US", "stateCode": "NJ", "latitude": "40.90121000", "longitude": "-74.51432000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.11594000", "longitude": "-74.78627000"}, {"name": "Roseland", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82066000", "longitude": "-74.29376000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.65223000", "longitude": "-74.25882000"}, {"name": "Roselle Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.66455000", "longitude": "-74.26431000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.47817000", "longitude": "-75.13129000"}, {"name": "Rossmoor", "countryCode": "US", "stateCode": "NJ", "latitude": "40.33650000", "longitude": "-74.47349000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.37205000", "longitude": "-73.99903000"}, {"name": "Runnemede", "countryCode": "US", "stateCode": "NJ", "latitude": "39.85234000", "longitude": "-75.06795000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82649000", "longitude": "-74.10681000"}, {"name": "Saddle Brook", "countryCode": "US", "stateCode": "NJ", "latitude": "40.89899000", "longitude": "-74.09264000"}, {"name": "Saddle River", "countryCode": "US", "stateCode": "NJ", "latitude": "41.03176000", "longitude": "-74.10209000"}, {"name": "Salem", "countryCode": "US", "stateCode": "NJ", "latitude": "39.57178000", "longitude": "-75.46714000"}, {"name": "Salem County", "countryCode": "US", "stateCode": "NJ", "latitude": "39.57658000", "longitude": "-75.35791000"}, {"name": "Sayreville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.45927000", "longitude": "-74.36098000"}, {"name": "Sayreville Junction", "countryCode": "US", "stateCode": "NJ", "latitude": "40.46538000", "longitude": "-74.33043000"}, {"name": "Scotch Plains", "countryCode": "US", "stateCode": "NJ", "latitude": "40.65538000", "longitude": "-74.38987000"}, {"name": "Sea Bright", "countryCode": "US", "stateCode": "NJ", "latitude": "40.36150000", "longitude": "-73.97403000"}, {"name": "Sea Girt", "countryCode": "US", "stateCode": "NJ", "latitude": "40.13206000", "longitude": "-74.03458000"}, {"name": "Sea Isle City", "countryCode": "US", "stateCode": "NJ", "latitude": "39.15345000", "longitude": "-74.69294000"}, {"name": "Seabrook Farms", "countryCode": "US", "stateCode": "NJ", "latitude": "39.50095000", "longitude": "-75.21796000"}, {"name": "Seaside Heights", "countryCode": "US", "stateCode": "NJ", "latitude": "39.94429000", "longitude": "-74.07291000"}, {"name": "Seaside Park", "countryCode": "US", "stateCode": "NJ", "latitude": "39.92679000", "longitude": "-74.07708000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.78955000", "longitude": "-74.05653000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.55205000", "longitude": "-74.25876000"}, {"name": "Shark River Hills", "countryCode": "US", "stateCode": "NJ", "latitude": "40.19400000", "longitude": "-74.04875000"}, {"name": "Ship Bottom", "countryCode": "US", "stateCode": "NJ", "latitude": "39.64290000", "longitude": "-74.18042000"}, {"name": "Short Hills", "countryCode": "US", "stateCode": "NJ", "latitude": "40.74788000", "longitude": "-74.32543000"}, {"name": "Shrewsbury", "countryCode": "US", "stateCode": "NJ", "latitude": "40.32955000", "longitude": "-74.06153000"}, {"name": "Sicklerville", "countryCode": "US", "stateCode": "NJ", "latitude": "39.71734000", "longitude": "-74.96933000"}, {"name": "Silver Ridge", "countryCode": "US", "stateCode": "NJ", "latitude": "39.95928000", "longitude": "-74.21848000"}, {"name": "Singac", "countryCode": "US", "stateCode": "NJ", "latitude": "40.88677000", "longitude": "-74.24098000"}, {"name": "Sixmile Run", "countryCode": "US", "stateCode": "NJ", "latitude": "40.45761000", "longitude": "-74.51154000"}, {"name": "Smithville", "countryCode": "US", "stateCode": "NJ", "latitude": "39.49401000", "longitude": "-74.45709000"}, {"name": "Society Hill", "countryCode": "US", "stateCode": "NJ", "latitude": "40.53399000", "longitude": "-74.45793000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.84400000", "longitude": "-75.02267000"}, {"name": "Somers Point", "countryCode": "US", "stateCode": "NJ", "latitude": "39.31762000", "longitude": "-74.59460000"}, {"name": "Somerset", "countryCode": "US", "stateCode": "NJ", "latitude": "40.49760000", "longitude": "-74.48849000"}, {"name": "Somerset County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.56351000", "longitude": "-74.61631000"}, {"name": "Somerville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.57427000", "longitude": "-74.60988000"}, {"name": "South Amboy", "countryCode": "US", "stateCode": "NJ", "latitude": "40.47788000", "longitude": "-74.29070000"}, {"name": "South Belmar", "countryCode": "US", "stateCode": "NJ", "latitude": "40.17095000", "longitude": "-74.02736000"}, {"name": "South Bound Brook", "countryCode": "US", "stateCode": "NJ", "latitude": "40.55344000", "longitude": "-74.53154000"}, {"name": "South Old Bridge", "countryCode": "US", "stateCode": "NJ", "latitude": "40.40816000", "longitude": "-74.35432000"}, {"name": "South Orange", "countryCode": "US", "stateCode": "NJ", "latitude": "40.74899000", "longitude": "-74.26126000"}, {"name": "South Plainfield", "countryCode": "US", "stateCode": "NJ", "latitude": "40.57927000", "longitude": "-74.41154000"}, {"name": "South River", "countryCode": "US", "stateCode": "NJ", "latitude": "40.44649000", "longitude": "-74.38598000"}, {"name": "South Toms River", "countryCode": "US", "stateCode": "NJ", "latitude": "39.94206000", "longitude": "-74.20431000"}, {"name": "South Vineland", "countryCode": "US", "stateCode": "NJ", "latitude": "39.44595000", "longitude": "-75.02879000"}, {"name": "Sparta", "countryCode": "US", "stateCode": "NJ", "latitude": "41.03343000", "longitude": "-74.63849000"}, {"name": "Spotswood", "countryCode": "US", "stateCode": "NJ", "latitude": "40.39177000", "longitude": "-74.39848000"}, {"name": "Spring Lake", "countryCode": "US", "stateCode": "NJ", "latitude": "40.15345000", "longitude": "-74.02819000"}, {"name": "Spring Lake Heights", "countryCode": "US", "stateCode": "NJ", "latitude": "40.15039000", "longitude": "-74.03097000"}, {"name": "Springdale", "countryCode": "US", "stateCode": "NJ", "latitude": "39.90261000", "longitude": "-74.96628000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "NJ", "latitude": "40.70491000", "longitude": "-74.31723000"}, {"name": "Stan<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.90288000", "longitude": "-74.70905000"}, {"name": "Stratford", "countryCode": "US", "stateCode": "NJ", "latitude": "39.82678000", "longitude": "-75.01545000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.39594000", "longitude": "-74.21348000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.86843000", "longitude": "-74.64044000"}, {"name": "Summit", "countryCode": "US", "stateCode": "NJ", "latitude": "40.71562000", "longitude": "-74.36468000"}, {"name": "Surf City", "countryCode": "US", "stateCode": "NJ", "latitude": "39.66206000", "longitude": "-74.16514000"}, {"name": "Sussex", "countryCode": "US", "stateCode": "NJ", "latitude": "41.20982000", "longitude": "-74.60766000"}, {"name": "Sussex County", "countryCode": "US", "stateCode": "NJ", "latitude": "41.13946000", "longitude": "-74.69023000"}, {"name": "Swedesboro", "countryCode": "US", "stateCode": "NJ", "latitude": "39.74761000", "longitude": "-75.31047000"}, {"name": "Teaneck", "countryCode": "US", "stateCode": "NJ", "latitude": "40.89760000", "longitude": "-74.01597000"}, {"name": "Ten Mile Run", "countryCode": "US", "stateCode": "NJ", "latitude": "40.41301000", "longitude": "-74.60223000"}, {"name": "Ten<PERSON>ly", "countryCode": "US", "stateCode": "NJ", "latitude": "40.92538000", "longitude": "-73.96292000"}, {"name": "Tinton Falls", "countryCode": "US", "stateCode": "NJ", "latitude": "40.30428000", "longitude": "-74.10042000"}, {"name": "Toms River", "countryCode": "US", "stateCode": "NJ", "latitude": "39.95373000", "longitude": "-74.19792000"}, {"name": "Totowa", "countryCode": "US", "stateCode": "NJ", "latitude": "40.90510000", "longitude": "-74.20987000"}, {"name": "Trenton", "countryCode": "US", "stateCode": "NJ", "latitude": "40.21705000", "longitude": "-74.74294000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.60317000", "longitude": "-74.34015000"}, {"name": "Turnersville", "countryCode": "US", "stateCode": "NJ", "latitude": "39.77317000", "longitude": "-75.05128000"}, {"name": "Twin Rivers", "countryCode": "US", "stateCode": "NJ", "latitude": "40.26400000", "longitude": "-74.49126000"}, {"name": "Union", "countryCode": "US", "stateCode": "NJ", "latitude": "40.69760000", "longitude": "-74.26320000"}, {"name": "Union Beach", "countryCode": "US", "stateCode": "NJ", "latitude": "40.44650000", "longitude": "-74.17820000"}, {"name": "Union City", "countryCode": "US", "stateCode": "NJ", "latitude": "40.77955000", "longitude": "-74.02375000"}, {"name": "Union County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.65980000", "longitude": "-74.30859000"}, {"name": "Upper Montclair", "countryCode": "US", "stateCode": "NJ", "latitude": "40.84621000", "longitude": "-74.20126000"}, {"name": "Upper Pohatcong", "countryCode": "US", "stateCode": "NJ", "latitude": "40.67747000", "longitude": "-75.15580000"}, {"name": "Upper Saddle River", "countryCode": "US", "stateCode": "NJ", "latitude": "41.05843000", "longitude": "-74.09848000"}, {"name": "Ventnor City", "countryCode": "US", "stateCode": "NJ", "latitude": "39.34039000", "longitude": "-74.47737000"}, {"name": "Vernon Center", "countryCode": "US", "stateCode": "NJ", "latitude": "41.18879000", "longitude": "-74.50405000"}, {"name": "Vernon Valley", "countryCode": "US", "stateCode": "NJ", "latitude": "41.23676000", "longitude": "-74.48710000"}, {"name": "Verona", "countryCode": "US", "stateCode": "NJ", "latitude": "40.82982000", "longitude": "-74.24015000"}, {"name": "Victory Gardens", "countryCode": "US", "stateCode": "NJ", "latitude": "40.87593000", "longitude": "-74.54238000"}, {"name": "Victory Lakes", "countryCode": "US", "stateCode": "NJ", "latitude": "39.63317000", "longitude": "-74.96600000"}, {"name": "Villas", "countryCode": "US", "stateCode": "NJ", "latitude": "39.02872000", "longitude": "-74.93851000"}, {"name": "Vincentown", "countryCode": "US", "stateCode": "NJ", "latitude": "39.93400000", "longitude": "-74.74849000"}, {"name": "Vineland", "countryCode": "US", "stateCode": "NJ", "latitude": "39.48623000", "longitude": "-75.02573000"}, {"name": "Vista Center", "countryCode": "US", "stateCode": "NJ", "latitude": "40.15928000", "longitude": "-74.31792000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.48122000", "longitude": "-74.48321000"}, {"name": "Waldwick", "countryCode": "US", "stateCode": "NJ", "latitude": "41.01065000", "longitude": "-74.11792000"}, {"name": "Wallington", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85316000", "longitude": "-74.11375000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.23178000", "longitude": "-74.02542000"}, {"name": "Wanaque", "countryCode": "US", "stateCode": "NJ", "latitude": "41.03815000", "longitude": "-74.29404000"}, {"name": "Waretown", "countryCode": "US", "stateCode": "NJ", "latitude": "39.79151000", "longitude": "-74.19514000"}, {"name": "Warren County", "countryCode": "US", "stateCode": "NJ", "latitude": "40.85725000", "longitude": "-74.99702000"}, {"name": "Warren Township", "countryCode": "US", "stateCode": "NJ", "latitude": "40.60822000", "longitude": "-74.51803000"}, {"name": "Washington", "countryCode": "US", "stateCode": "NJ", "latitude": "40.75843000", "longitude": "-74.97934000"}, {"name": "Watchung", "countryCode": "US", "stateCode": "NJ", "latitude": "40.63788000", "longitude": "-74.45099000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.92538000", "longitude": "-74.27654000"}, {"name": "Weehawken", "countryCode": "US", "stateCode": "NJ", "latitude": "40.76955000", "longitude": "-74.02042000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.79456000", "longitude": "-75.14879000"}, {"name": "West Belmar", "countryCode": "US", "stateCode": "NJ", "latitude": "40.16928000", "longitude": "-74.03542000"}, {"name": "West Cape May", "countryCode": "US", "stateCode": "NJ", "latitude": "38.93872000", "longitude": "-74.94184000"}, {"name": "West Freehold", "countryCode": "US", "stateCode": "NJ", "latitude": "40.24206000", "longitude": "-74.30126000"}, {"name": "West Long Branch", "countryCode": "US", "stateCode": "NJ", "latitude": "40.29039000", "longitude": "-74.01764000"}, {"name": "West Milford", "countryCode": "US", "stateCode": "NJ", "latitude": "41.13121000", "longitude": "-74.36737000"}, {"name": "West New York", "countryCode": "US", "stateCode": "NJ", "latitude": "40.78788000", "longitude": "-74.01431000"}, {"name": "West Orange", "countryCode": "US", "stateCode": "NJ", "latitude": "40.79871000", "longitude": "-74.23904000"}, {"name": "Westfield", "countryCode": "US", "stateCode": "NJ", "latitude": "40.65899000", "longitude": "-74.34737000"}, {"name": "Weston", "countryCode": "US", "stateCode": "NJ", "latitude": "40.53510000", "longitude": "-74.59071000"}, {"name": "Westville", "countryCode": "US", "stateCode": "NJ", "latitude": "39.86789000", "longitude": "-75.13156000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.99121000", "longitude": "-74.03264000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.89315000", "longitude": "-74.58183000"}, {"name": "White Horse", "countryCode": "US", "stateCode": "NJ", "latitude": "40.19067000", "longitude": "-74.70238000"}, {"name": "White Meadow Lake", "countryCode": "US", "stateCode": "NJ", "latitude": "40.92371000", "longitude": "-74.51071000"}, {"name": "Whitehouse Station", "countryCode": "US", "stateCode": "NJ", "latitude": "40.61538000", "longitude": "-74.77044000"}, {"name": "Whitesboro", "countryCode": "US", "stateCode": "NJ", "latitude": "39.03900000", "longitude": "-74.85684000"}, {"name": "Whitesboro-Burleigh", "countryCode": "US", "stateCode": "NJ", "latitude": "39.04305000", "longitude": "-74.86538000"}, {"name": "Whittingham", "countryCode": "US", "stateCode": "NJ", "latitude": "40.32982000", "longitude": "-74.44511000"}, {"name": "Wildwood", "countryCode": "US", "stateCode": "NJ", "latitude": "38.99178000", "longitude": "-74.81489000"}, {"name": "Wildwood Crest", "countryCode": "US", "stateCode": "NJ", "latitude": "38.97484000", "longitude": "-74.83350000"}, {"name": "Williamstown", "countryCode": "US", "stateCode": "NJ", "latitude": "39.68623000", "longitude": "-74.99517000"}, {"name": "Willingboro", "countryCode": "US", "stateCode": "NJ", "latitude": "40.02789000", "longitude": "-74.86905000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "40.64260000", "longitude": "-74.28543000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "39.91734000", "longitude": "-75.09629000"}, {"name": "Wood-Ridge", "countryCode": "US", "stateCode": "NJ", "latitude": "40.84566000", "longitude": "-74.08792000"}, {"name": "<PERSON>bine", "countryCode": "US", "stateCode": "NJ", "latitude": "39.24178000", "longitude": "-74.81517000"}, {"name": "Woodbridge", "countryCode": "US", "stateCode": "NJ", "latitude": "40.55760000", "longitude": "-74.28459000"}, {"name": "Woodbury", "countryCode": "US", "stateCode": "NJ", "latitude": "39.83817000", "longitude": "-75.15268000"}, {"name": "Woodbury Heights", "countryCode": "US", "stateCode": "NJ", "latitude": "39.81706000", "longitude": "-75.15518000"}, {"name": "Woodcliff Lake", "countryCode": "US", "stateCode": "NJ", "latitude": "41.02343000", "longitude": "-74.06653000"}, {"name": "Woodland Park", "countryCode": "US", "stateCode": "NJ", "latitude": "40.88982000", "longitude": "-74.19487000"}, {"name": "Woodstown", "countryCode": "US", "stateCode": "NJ", "latitude": "39.65150000", "longitude": "-75.32825000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ", "latitude": "41.00954000", "longitude": "-74.17292000"}, {"name": "Yardville", "countryCode": "US", "stateCode": "NJ", "latitude": "40.18122000", "longitude": "-74.66432000"}, {"name": "Yorketown", "countryCode": "US", "stateCode": "NJ", "latitude": "40.30789000", "longitude": "-74.33765000"}]