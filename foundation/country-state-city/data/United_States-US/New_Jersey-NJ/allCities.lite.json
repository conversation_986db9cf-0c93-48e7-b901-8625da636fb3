[{"name": "Absecon", "countryCode": "US", "stateCode": "NJ"}, {"name": "Allendale", "countryCode": "US", "stateCode": "NJ"}, {"name": "Allentown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Alloway", "countryCode": "US", "stateCode": "NJ"}, {"name": "Alpha", "countryCode": "US", "stateCode": "NJ"}, {"name": "Alpine", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Asbury Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ashland", "countryCode": "US", "stateCode": "NJ"}, {"name": "Atco", "countryCode": "US", "stateCode": "NJ"}, {"name": "Atlantic City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Atlantic County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Atlantic Highlands", "countryCode": "US", "stateCode": "NJ"}, {"name": "Audubon", "countryCode": "US", "stateCode": "NJ"}, {"name": "Audubon Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Avalon", "countryCode": "US", "stateCode": "NJ"}, {"name": "Avenel", "countryCode": "US", "stateCode": "NJ"}, {"name": "Avon-by-the-Sea", "countryCode": "US", "stateCode": "NJ"}, {"name": "Barnegat", "countryCode": "US", "stateCode": "NJ"}, {"name": "Barrington", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bayonne", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bayville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Beach Haven", "countryCode": "US", "stateCode": "NJ"}, {"name": "Beach Haven West", "countryCode": "US", "stateCode": "NJ"}, {"name": "Beachwood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Beattystown", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bedminster", "countryCode": "US", "stateCode": "NJ"}, {"name": "Belford", "countryCode": "US", "stateCode": "NJ"}, {"name": "Belleville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bellmawr", "countryCode": "US", "stateCode": "NJ"}, {"name": "Belmar", "countryCode": "US", "stateCode": "NJ"}, {"name": "Belvidere", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bergen County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bergenfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "Berkeley Heights", "countryCode": "US", "stateCode": "NJ"}, {"name": "Berlin", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bernardsville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Beverly", "countryCode": "US", "stateCode": "NJ"}, {"name": "Blackwood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bloomfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bloomingdale", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bogota", "countryCode": "US", "stateCode": "NJ"}, {"name": "Boonton", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bordentown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bound Brook", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bradley Gardens", "countryCode": "US", "stateCode": "NJ"}, {"name": "Brass Castle", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Bridgewater", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Brigantine", "countryCode": "US", "stateCode": "NJ"}, {"name": "Brookdale", "countryCode": "US", "stateCode": "NJ"}, {"name": "Brooklawn", "countryCode": "US", "stateCode": "NJ"}, {"name": "Browns Mills", "countryCode": "US", "stateCode": "NJ"}, {"name": "Brownville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Budd Lake", "countryCode": "US", "stateCode": "NJ"}, {"name": "Buena", "countryCode": "US", "stateCode": "NJ"}, {"name": "Burlington", "countryCode": "US", "stateCode": "NJ"}, {"name": "Burlington County", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Califon", "countryCode": "US", "stateCode": "NJ"}, {"name": "Camden", "countryCode": "US", "stateCode": "NJ"}, {"name": "Camden County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cape May", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cape May County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cape May Court House", "countryCode": "US", "stateCode": "NJ"}, {"name": "Carlstadt", "countryCode": "US", "stateCode": "NJ"}, {"name": "Carneys Point", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cedar Glen Lakes", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cedar Glen West", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cedar Grove", "countryCode": "US", "stateCode": "NJ"}, {"name": "Chatham", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cherry Hill", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cherry Hill Mall", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Chester", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Clearbrook Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cliffside Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cliffwood Beach", "countryCode": "US", "stateCode": "NJ"}, {"name": "Clifton", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Closter", "countryCode": "US", "stateCode": "NJ"}, {"name": "Collings Lakes", "countryCode": "US", "stateCode": "NJ"}, {"name": "Collingswood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Colonia", "countryCode": "US", "stateCode": "NJ"}, {"name": "Concordia", "countryCode": "US", "stateCode": "NJ"}, {"name": "Country Lake Estates", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cranbury", "countryCode": "US", "stateCode": "NJ"}, {"name": "Crandon Lakes", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cranford", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cresskill", "countryCode": "US", "stateCode": "NJ"}, {"name": "Crestwood Village", "countryCode": "US", "stateCode": "NJ"}, {"name": "Cumberland County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Dayton", "countryCode": "US", "stateCode": "NJ"}, {"name": "Delanco", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Dover", "countryCode": "US", "stateCode": "NJ"}, {"name": "Dover Beaches North", "countryCode": "US", "stateCode": "NJ"}, {"name": "Dover Beaches South", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Dunellen", "countryCode": "US", "stateCode": "NJ"}, {"name": "East Brunswick", "countryCode": "US", "stateCode": "NJ"}, {"name": "East Franklin", "countryCode": "US", "stateCode": "NJ"}, {"name": "East Freehold", "countryCode": "US", "stateCode": "NJ"}, {"name": "East Hanover", "countryCode": "US", "stateCode": "NJ"}, {"name": "East Newark", "countryCode": "US", "stateCode": "NJ"}, {"name": "East Orange", "countryCode": "US", "stateCode": "NJ"}, {"name": "East Rutherford", "countryCode": "US", "stateCode": "NJ"}, {"name": "Eatontown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Echelon", "countryCode": "US", "stateCode": "NJ"}, {"name": "Edgewater", "countryCode": "US", "stateCode": "NJ"}, {"name": "Edgewater Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Edison", "countryCode": "US", "stateCode": "NJ"}, {"name": "Egg Harbor City", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ellisburg", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Elmwood Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Elwood", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Englewood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Englewood Cliffs", "countryCode": "US", "stateCode": "NJ"}, {"name": "Englishtown", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Essex County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Essex Fells", "countryCode": "US", "stateCode": "NJ"}, {"name": "Estell Manor", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Fair Haven", "countryCode": "US", "stateCode": "NJ"}, {"name": "Fair Lawn", "countryCode": "US", "stateCode": "NJ"}, {"name": "Fairfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "Fairton", "countryCode": "US", "stateCode": "NJ"}, {"name": "Fairview", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Farmingdale", "countryCode": "US", "stateCode": "NJ"}, {"name": "Finderne", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Florence", "countryCode": "US", "stateCode": "NJ"}, {"name": "Florham Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Fords", "countryCode": "US", "stateCode": "NJ"}, {"name": "Forked River", "countryCode": "US", "stateCode": "NJ"}, {"name": "Fort Dix", "countryCode": "US", "stateCode": "NJ"}, {"name": "Fort Lee", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Franklin Center", "countryCode": "US", "stateCode": "NJ"}, {"name": "Franklin Lakes", "countryCode": "US", "stateCode": "NJ"}, {"name": "Franklin Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Freehold", "countryCode": "US", "stateCode": "NJ"}, {"name": "Frenchtown", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Gibbsboro", "countryCode": "US", "stateCode": "NJ"}, {"name": "Gibbstown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Gladstone", "countryCode": "US", "stateCode": "NJ"}, {"name": "Glassboro", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Glen Ridge", "countryCode": "US", "stateCode": "NJ"}, {"name": "Glen Rock", "countryCode": "US", "stateCode": "NJ"}, {"name": "Glendora", "countryCode": "US", "stateCode": "NJ"}, {"name": "Gloucester City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Gloucester County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Golden Triangle", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON> Knoll", "countryCode": "US", "stateCode": "NJ"}, {"name": "Greentree", "countryCode": "US", "stateCode": "NJ"}, {"name": "Groveville", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hackensack", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hackettstown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Haddon Heights", "countryCode": "US", "stateCode": "NJ"}, {"name": "Haddonfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hamburg", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hamilton Square", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hammonton", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hanover", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Harrington Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hasbrouck Heights", "countryCode": "US", "stateCode": "NJ"}, {"name": "Haworth", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hawthorne", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hazlet Township", "countryCode": "US", "stateCode": "NJ"}, {"name": "Heathcote", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "High Bridge", "countryCode": "US", "stateCode": "NJ"}, {"name": "Highland Lake", "countryCode": "US", "stateCode": "NJ"}, {"name": "Highland Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Highlands", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hightstown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hillsdale", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hillside", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ho-Ho-Kus", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hoboken", "countryCode": "US", "stateCode": "NJ"}, {"name": "Holiday City South", "countryCode": "US", "stateCode": "NJ"}, {"name": "Holiday City-Berkeley", "countryCode": "US", "stateCode": "NJ"}, {"name": "Holiday Heights", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hopatcong", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hopatcong Hills", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hudson County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Hunterdon County", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Island Heights", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Jamesburg", "countryCode": "US", "stateCode": "NJ"}, {"name": "Jersey City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Keansburg", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Kendall Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Kenvil", "countryCode": "US", "stateCode": "NJ"}, {"name": "Keyport", "countryCode": "US", "stateCode": "NJ"}, {"name": "Kingston", "countryCode": "US", "stateCode": "NJ"}, {"name": "Kingston Estates", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lake Como", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lake Mohawk", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lake Telemark", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lakehurst", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lakewood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lambertville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Landing", "countryCode": "US", "stateCode": "NJ"}, {"name": "Laurel <PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Laurel Springs", "countryCode": "US", "stateCode": "NJ"}, {"name": "Laurence <PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lawnside", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lawrenceville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "NJ"}, {"name": "Leisure Knoll", "countryCode": "US", "stateCode": "NJ"}, {"name": "Leisure Village", "countryCode": "US", "stateCode": "NJ"}, {"name": "Leisure Village East", "countryCode": "US", "stateCode": "NJ"}, {"name": "Leisure Village West-Pine Lake Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Leisuretowne", "countryCode": "US", "stateCode": "NJ"}, {"name": "Leonardo", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Lindenwold", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Little Falls", "countryCode": "US", "stateCode": "NJ"}, {"name": "Little Ferry", "countryCode": "US", "stateCode": "NJ"}, {"name": "Little Silver", "countryCode": "US", "stateCode": "NJ"}, {"name": "Livingston", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Long Branch", "countryCode": "US", "stateCode": "NJ"}, {"name": "Long Valley", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Madison", "countryCode": "US", "stateCode": "NJ"}, {"name": "Madison Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Magnolia", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Manahawkin", "countryCode": "US", "stateCode": "NJ"}, {"name": "Manasquan", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mantua Township", "countryCode": "US", "stateCode": "NJ"}, {"name": "Manville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Maple Shade", "countryCode": "US", "stateCode": "NJ"}, {"name": "Maplewood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Margate City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Marlboro", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Martinsville", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mays Landing", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "McGuire AFB", "countryCode": "US", "stateCode": "NJ"}, {"name": "Medford Lakes", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mendham", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mercer County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mercerville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mercerville-Hamilton Square", "countryCode": "US", "stateCode": "NJ"}, {"name": "Merchantville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Metuchen", "countryCode": "US", "stateCode": "NJ"}, {"name": "Middlebush", "countryCode": "US", "stateCode": "NJ"}, {"name": "Middlesex", "countryCode": "US", "stateCode": "NJ"}, {"name": "Middlesex County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Midland Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Milford", "countryCode": "US", "stateCode": "NJ"}, {"name": "Milltown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Millville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Monmouth Beach", "countryCode": "US", "stateCode": "NJ"}, {"name": "Monmouth County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Monmouth Junction", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Montvale", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Moorestown-Lenola", "countryCode": "US", "stateCode": "NJ"}, {"name": "Morganville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Morris County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Morris Plains", "countryCode": "US", "stateCode": "NJ"}, {"name": "Morristown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mount Arlington", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mount Ephraim", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mount Holly", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mount Laurel", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mountain Lakes", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mountainside", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mullica Hill", "countryCode": "US", "stateCode": "NJ"}, {"name": "Mystic Island", "countryCode": "US", "stateCode": "NJ"}, {"name": "National Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Navesink", "countryCode": "US", "stateCode": "NJ"}, {"name": "Neptune City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Netcong", "countryCode": "US", "stateCode": "NJ"}, {"name": "New Brunswick", "countryCode": "US", "stateCode": "NJ"}, {"name": "New Egypt", "countryCode": "US", "stateCode": "NJ"}, {"name": "New Milford", "countryCode": "US", "stateCode": "NJ"}, {"name": "New Providence", "countryCode": "US", "stateCode": "NJ"}, {"name": "Newark", "countryCode": "US", "stateCode": "NJ"}, {"name": "Newfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "North Arlington", "countryCode": "US", "stateCode": "NJ"}, {"name": "North Beach Haven", "countryCode": "US", "stateCode": "NJ"}, {"name": "North Bergen", "countryCode": "US", "stateCode": "NJ"}, {"name": "North Caldwell", "countryCode": "US", "stateCode": "NJ"}, {"name": "North Cape May", "countryCode": "US", "stateCode": "NJ"}, {"name": "North Haledon", "countryCode": "US", "stateCode": "NJ"}, {"name": "North Middletown", "countryCode": "US", "stateCode": "NJ"}, {"name": "North Plainfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "North Wildwood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Northfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "Northvale", "countryCode": "US", "stateCode": "NJ"}, {"name": "Norwood", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Oak Valley", "countryCode": "US", "stateCode": "NJ"}, {"name": "Oakhurst", "countryCode": "US", "stateCode": "NJ"}, {"name": "Oakland", "countryCode": "US", "stateCode": "NJ"}, {"name": "Oak<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ocean Acres", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ocean City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ocean County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ocean Gate", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ocean Grove", "countryCode": "US", "stateCode": "NJ"}, {"name": "Oceanport", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ogdensburg", "countryCode": "US", "stateCode": "NJ"}, {"name": "Old Bridge", "countryCode": "US", "stateCode": "NJ"}, {"name": "Old Tappan", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Oradell", "countryCode": "US", "stateCode": "NJ"}, {"name": "Orange", "countryCode": "US", "stateCode": "NJ"}, {"name": "Oxford", "countryCode": "US", "stateCode": "NJ"}, {"name": "Palisades Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Palmyra", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Park Ridge", "countryCode": "US", "stateCode": "NJ"}, {"name": "Parsippany", "countryCode": "US", "stateCode": "NJ"}, {"name": "Passaic", "countryCode": "US", "stateCode": "NJ"}, {"name": "Passaic County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Paterson", "countryCode": "US", "stateCode": "NJ"}, {"name": "Paul<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Peapack", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pemberton", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pemberton Heights", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pennington", "countryCode": "US", "stateCode": "NJ"}, {"name": "Penns Grove", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pennsauken", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pennsville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Perth Amboy", "countryCode": "US", "stateCode": "NJ"}, {"name": "Phillipsburg", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pine Beach", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pine Hill", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pine Lake Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pine Ridge at Crestwood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Piscataway", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pitman", "countryCode": "US", "stateCode": "NJ"}, {"name": "Plainfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "Plainsboro Center", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pleasantville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Point Pleasant", "countryCode": "US", "stateCode": "NJ"}, {"name": "Point Pleasant Beach", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pomona", "countryCode": "US", "stateCode": "NJ"}, {"name": "Pompton Lakes", "countryCode": "US", "stateCode": "NJ"}, {"name": "Port Monmouth", "countryCode": "US", "stateCode": "NJ"}, {"name": "Port Norris", "countryCode": "US", "stateCode": "NJ"}, {"name": "Port Reading", "countryCode": "US", "stateCode": "NJ"}, {"name": "Port Republic", "countryCode": "US", "stateCode": "NJ"}, {"name": "Presidential Lakes Estates", "countryCode": "US", "stateCode": "NJ"}, {"name": "Princeton", "countryCode": "US", "stateCode": "NJ"}, {"name": "Princeton Junction", "countryCode": "US", "stateCode": "NJ"}, {"name": "Princeton Meadows", "countryCode": "US", "stateCode": "NJ"}, {"name": "Prospect Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ram<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ramtown", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Raritan", "countryCode": "US", "stateCode": "NJ"}, {"name": "Red Bank", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ridgefield", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ridgefield Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ridgewood", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Rio Grande", "countryCode": "US", "stateCode": "NJ"}, {"name": "River Edge", "countryCode": "US", "stateCode": "NJ"}, {"name": "River Vale", "countryCode": "US", "stateCode": "NJ"}, {"name": "Riverdale", "countryCode": "US", "stateCode": "NJ"}, {"name": "Riverton", "countryCode": "US", "stateCode": "NJ"}, {"name": "Robbinsville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Robertsville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Rochelle Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Rockaway", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Roseland", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Roselle Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Rossmoor", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Runnemede", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Saddle Brook", "countryCode": "US", "stateCode": "NJ"}, {"name": "Saddle River", "countryCode": "US", "stateCode": "NJ"}, {"name": "Salem", "countryCode": "US", "stateCode": "NJ"}, {"name": "Salem County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sayreville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sayreville Junction", "countryCode": "US", "stateCode": "NJ"}, {"name": "Scotch Plains", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sea Bright", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sea Girt", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sea Isle City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Seabrook Farms", "countryCode": "US", "stateCode": "NJ"}, {"name": "Seaside Heights", "countryCode": "US", "stateCode": "NJ"}, {"name": "Seaside Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Shark River Hills", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ship Bottom", "countryCode": "US", "stateCode": "NJ"}, {"name": "Short Hills", "countryCode": "US", "stateCode": "NJ"}, {"name": "Shrewsbury", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sicklerville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Silver Ridge", "countryCode": "US", "stateCode": "NJ"}, {"name": "Singac", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sixmile Run", "countryCode": "US", "stateCode": "NJ"}, {"name": "Smithville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Society Hill", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Somers Point", "countryCode": "US", "stateCode": "NJ"}, {"name": "Somerset", "countryCode": "US", "stateCode": "NJ"}, {"name": "Somerset County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Somerville", "countryCode": "US", "stateCode": "NJ"}, {"name": "South Amboy", "countryCode": "US", "stateCode": "NJ"}, {"name": "South Belmar", "countryCode": "US", "stateCode": "NJ"}, {"name": "South Bound Brook", "countryCode": "US", "stateCode": "NJ"}, {"name": "South Old Bridge", "countryCode": "US", "stateCode": "NJ"}, {"name": "South Orange", "countryCode": "US", "stateCode": "NJ"}, {"name": "South Plainfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "South River", "countryCode": "US", "stateCode": "NJ"}, {"name": "South Toms River", "countryCode": "US", "stateCode": "NJ"}, {"name": "South Vineland", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sparta", "countryCode": "US", "stateCode": "NJ"}, {"name": "Spotswood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Spring Lake", "countryCode": "US", "stateCode": "NJ"}, {"name": "Spring Lake Heights", "countryCode": "US", "stateCode": "NJ"}, {"name": "Springdale", "countryCode": "US", "stateCode": "NJ"}, {"name": "Springfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "Stan<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Stratford", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Summit", "countryCode": "US", "stateCode": "NJ"}, {"name": "Surf City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sussex", "countryCode": "US", "stateCode": "NJ"}, {"name": "Sussex County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Swedesboro", "countryCode": "US", "stateCode": "NJ"}, {"name": "Teaneck", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ten Mile Run", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ten<PERSON>ly", "countryCode": "US", "stateCode": "NJ"}, {"name": "Tinton Falls", "countryCode": "US", "stateCode": "NJ"}, {"name": "Toms River", "countryCode": "US", "stateCode": "NJ"}, {"name": "Totowa", "countryCode": "US", "stateCode": "NJ"}, {"name": "Trenton", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Turnersville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Twin Rivers", "countryCode": "US", "stateCode": "NJ"}, {"name": "Union", "countryCode": "US", "stateCode": "NJ"}, {"name": "Union Beach", "countryCode": "US", "stateCode": "NJ"}, {"name": "Union City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Union County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Upper Montclair", "countryCode": "US", "stateCode": "NJ"}, {"name": "Upper Pohatcong", "countryCode": "US", "stateCode": "NJ"}, {"name": "Upper Saddle River", "countryCode": "US", "stateCode": "NJ"}, {"name": "Ventnor City", "countryCode": "US", "stateCode": "NJ"}, {"name": "Vernon Center", "countryCode": "US", "stateCode": "NJ"}, {"name": "Vernon Valley", "countryCode": "US", "stateCode": "NJ"}, {"name": "Verona", "countryCode": "US", "stateCode": "NJ"}, {"name": "Victory Gardens", "countryCode": "US", "stateCode": "NJ"}, {"name": "Victory Lakes", "countryCode": "US", "stateCode": "NJ"}, {"name": "Villas", "countryCode": "US", "stateCode": "NJ"}, {"name": "Vincentown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Vineland", "countryCode": "US", "stateCode": "NJ"}, {"name": "Vista Center", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Waldwick", "countryCode": "US", "stateCode": "NJ"}, {"name": "Wallington", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Wanaque", "countryCode": "US", "stateCode": "NJ"}, {"name": "Waretown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Warren County", "countryCode": "US", "stateCode": "NJ"}, {"name": "Warren Township", "countryCode": "US", "stateCode": "NJ"}, {"name": "Washington", "countryCode": "US", "stateCode": "NJ"}, {"name": "Watchung", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Weehawken", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "West Belmar", "countryCode": "US", "stateCode": "NJ"}, {"name": "West Cape May", "countryCode": "US", "stateCode": "NJ"}, {"name": "West Freehold", "countryCode": "US", "stateCode": "NJ"}, {"name": "West Long Branch", "countryCode": "US", "stateCode": "NJ"}, {"name": "West Milford", "countryCode": "US", "stateCode": "NJ"}, {"name": "West New York", "countryCode": "US", "stateCode": "NJ"}, {"name": "West Orange", "countryCode": "US", "stateCode": "NJ"}, {"name": "Westfield", "countryCode": "US", "stateCode": "NJ"}, {"name": "Weston", "countryCode": "US", "stateCode": "NJ"}, {"name": "Westville", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "White Horse", "countryCode": "US", "stateCode": "NJ"}, {"name": "White Meadow Lake", "countryCode": "US", "stateCode": "NJ"}, {"name": "Whitehouse Station", "countryCode": "US", "stateCode": "NJ"}, {"name": "Whitesboro", "countryCode": "US", "stateCode": "NJ"}, {"name": "Whitesboro-Burleigh", "countryCode": "US", "stateCode": "NJ"}, {"name": "Whittingham", "countryCode": "US", "stateCode": "NJ"}, {"name": "Wildwood", "countryCode": "US", "stateCode": "NJ"}, {"name": "Wildwood Crest", "countryCode": "US", "stateCode": "NJ"}, {"name": "Williamstown", "countryCode": "US", "stateCode": "NJ"}, {"name": "Willingboro", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Wood-Ridge", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON>bine", "countryCode": "US", "stateCode": "NJ"}, {"name": "Woodbridge", "countryCode": "US", "stateCode": "NJ"}, {"name": "Woodbury", "countryCode": "US", "stateCode": "NJ"}, {"name": "Woodbury Heights", "countryCode": "US", "stateCode": "NJ"}, {"name": "Woodcliff Lake", "countryCode": "US", "stateCode": "NJ"}, {"name": "Woodland Park", "countryCode": "US", "stateCode": "NJ"}, {"name": "Woodstown", "countryCode": "US", "stateCode": "NJ"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NJ"}, {"name": "Yardville", "countryCode": "US", "stateCode": "NJ"}, {"name": "Yorketown", "countryCode": "US", "stateCode": "NJ"}]