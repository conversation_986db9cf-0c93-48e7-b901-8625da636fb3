[{"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Acushnet", "countryCode": "US", "stateCode": "MA"}, {"name": "Acushnet Center", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Agawam", "countryCode": "US", "stateCode": "MA"}, {"name": "Amesbury", "countryCode": "US", "stateCode": "MA"}, {"name": "Amherst", "countryCode": "US", "stateCode": "MA"}, {"name": "Amherst Center", "countryCode": "US", "stateCode": "MA"}, {"name": "Andover", "countryCode": "US", "stateCode": "MA"}, {"name": "Arlington", "countryCode": "US", "stateCode": "MA"}, {"name": "Ashburnham", "countryCode": "US", "stateCode": "MA"}, {"name": "Ashby", "countryCode": "US", "stateCode": "MA"}, {"name": "Ashfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Ashland", "countryCode": "US", "stateCode": "MA"}, {"name": "Assonet", "countryCode": "US", "stateCode": "MA"}, {"name": "At<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Attleboro", "countryCode": "US", "stateCode": "MA"}, {"name": "Auburn", "countryCode": "US", "stateCode": "MA"}, {"name": "Avon", "countryCode": "US", "stateCode": "MA"}, {"name": "Ayer", "countryCode": "US", "stateCode": "MA"}, {"name": "Baldwinville", "countryCode": "US", "stateCode": "MA"}, {"name": "Barnstable", "countryCode": "US", "stateCode": "MA"}, {"name": "Barnstable County", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Bedford", "countryCode": "US", "stateCode": "MA"}, {"name": "Belchertown", "countryCode": "US", "stateCode": "MA"}, {"name": "Bellingham", "countryCode": "US", "stateCode": "MA"}, {"name": "Belmont", "countryCode": "US", "stateCode": "MA"}, {"name": "Berkley", "countryCode": "US", "stateCode": "MA"}, {"name": "Berkshire County", "countryCode": "US", "stateCode": "MA"}, {"name": "Berlin", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Beverly", "countryCode": "US", "stateCode": "MA"}, {"name": "Beverly Cove", "countryCode": "US", "stateCode": "MA"}, {"name": "Billerica", "countryCode": "US", "stateCode": "MA"}, {"name": "Blackstone", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Bolton", "countryCode": "US", "stateCode": "MA"}, {"name": "Bondsville", "countryCode": "US", "stateCode": "MA"}, {"name": "Boston", "countryCode": "US", "stateCode": "MA"}, {"name": "Bourne", "countryCode": "US", "stateCode": "MA"}, {"name": "Boxborough", "countryCode": "US", "stateCode": "MA"}, {"name": "Boxford", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Braintree", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Bridgewater", "countryCode": "US", "stateCode": "MA"}, {"name": "Brimfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Bristol County", "countryCode": "US", "stateCode": "MA"}, {"name": "Brockton", "countryCode": "US", "stateCode": "MA"}, {"name": "Brookline", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Burlington", "countryCode": "US", "stateCode": "MA"}, {"name": "Buzzards Bay", "countryCode": "US", "stateCode": "MA"}, {"name": "Cambridge", "countryCode": "US", "stateCode": "MA"}, {"name": "Canton", "countryCode": "US", "stateCode": "MA"}, {"name": "Carlisle", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Centerville", "countryCode": "US", "stateCode": "MA"}, {"name": "Charlemont", "countryCode": "US", "stateCode": "MA"}, {"name": "Charlton", "countryCode": "US", "stateCode": "MA"}, {"name": "Chatham", "countryCode": "US", "stateCode": "MA"}, {"name": "Chelmsford", "countryCode": "US", "stateCode": "MA"}, {"name": "Chelsea", "countryCode": "US", "stateCode": "MA"}, {"name": "Chesterfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Chicopee", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Cochituate", "countryCode": "US", "stateCode": "MA"}, {"name": "Cohasset", "countryCode": "US", "stateCode": "MA"}, {"name": "Colrain", "countryCode": "US", "stateCode": "MA"}, {"name": "Concord", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Cordaville", "countryCode": "US", "stateCode": "MA"}, {"name": "Cotuit", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Dartmouth", "countryCode": "US", "stateCode": "MA"}, {"name": "Dedham", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Dennis Port", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Dover", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Dukes County", "countryCode": "US", "stateCode": "MA"}, {"name": "Dunst<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Duxbury", "countryCode": "US", "stateCode": "MA"}, {"name": "East Bridgewater", "countryCode": "US", "stateCode": "MA"}, {"name": "East Brookfield", "countryCode": "US", "stateCode": "MA"}, {"name": "East Dennis", "countryCode": "US", "stateCode": "MA"}, {"name": "East Douglas", "countryCode": "US", "stateCode": "MA"}, {"name": "East Falmouth", "countryCode": "US", "stateCode": "MA"}, {"name": "East Harwich", "countryCode": "US", "stateCode": "MA"}, {"name": "East Longmeadow", "countryCode": "US", "stateCode": "MA"}, {"name": "East Pepperell", "countryCode": "US", "stateCode": "MA"}, {"name": "East Sandwich", "countryCode": "US", "stateCode": "MA"}, {"name": "Eastham", "countryCode": "US", "stateCode": "MA"}, {"name": "Easthampton", "countryCode": "US", "stateCode": "MA"}, {"name": "Easton", "countryCode": "US", "stateCode": "MA"}, {"name": "Edgartown", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>g", "countryCode": "US", "stateCode": "MA"}, {"name": "Essex", "countryCode": "US", "stateCode": "MA"}, {"name": "Essex County", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Fairhaven", "countryCode": "US", "stateCode": "MA"}, {"name": "Fall River", "countryCode": "US", "stateCode": "MA"}, {"name": "Falmouth", "countryCode": "US", "stateCode": "MA"}, {"name": "Fiskdale", "countryCode": "US", "stateCode": "MA"}, {"name": "Fitchburg", "countryCode": "US", "stateCode": "MA"}, {"name": "Forestdale", "countryCode": "US", "stateCode": "MA"}, {"name": "Foxborough", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Framingham Center", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "MA"}, {"name": "Freetown", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Gloucester", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Granville", "countryCode": "US", "stateCode": "MA"}, {"name": "Great Barrington", "countryCode": "US", "stateCode": "MA"}, {"name": "Green Harbor-Cedar Crest", "countryCode": "US", "stateCode": "MA"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Groton", "countryCode": "US", "stateCode": "MA"}, {"name": "Groveland", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Halifax", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Hampden County", "countryCode": "US", "stateCode": "MA"}, {"name": "Hampshire County", "countryCode": "US", "stateCode": "MA"}, {"name": "Hanover", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Harvard", "countryCode": "US", "stateCode": "MA"}, {"name": "Harwich", "countryCode": "US", "stateCode": "MA"}, {"name": "Harwich Center", "countryCode": "US", "stateCode": "MA"}, {"name": "Harwich Port", "countryCode": "US", "stateCode": "MA"}, {"name": "Hatfield", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Head of Westport", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Hinsdale", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Holden", "countryCode": "US", "stateCode": "MA"}, {"name": "Holland", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Holyoke", "countryCode": "US", "stateCode": "MA"}, {"name": "Hopedale", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>kin<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Housatonic", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Hull", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Ipswich", "countryCode": "US", "stateCode": "MA"}, {"name": "Jamaica Plain", "countryCode": "US", "stateCode": "MA"}, {"name": "Kingston", "countryCode": "US", "stateCode": "MA"}, {"name": "Lancaster", "countryCode": "US", "stateCode": "MA"}, {"name": "Lanesborough", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Leicester", "countryCode": "US", "stateCode": "MA"}, {"name": "Lenox", "countryCode": "US", "stateCode": "MA"}, {"name": "Leominster", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Lexington", "countryCode": "US", "stateCode": "MA"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "MA"}, {"name": "Littleton Common", "countryCode": "US", "stateCode": "MA"}, {"name": "Longmeadow", "countryCode": "US", "stateCode": "MA"}, {"name": "Lowell", "countryCode": "US", "stateCode": "MA"}, {"name": "Ludlow", "countryCode": "US", "stateCode": "MA"}, {"name": "Lunenburg", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Lynnfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Malden", "countryCode": "US", "stateCode": "MA"}, {"name": "Manchester-by-the-Sea", "countryCode": "US", "stateCode": "MA"}, {"name": "Mansfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Mansfield Center", "countryCode": "US", "stateCode": "MA"}, {"name": "Marblehead", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Marion Center", "countryCode": "US", "stateCode": "MA"}, {"name": "Marlborough", "countryCode": "US", "stateCode": "MA"}, {"name": "Marshfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Marshfield Hills", "countryCode": "US", "stateCode": "MA"}, {"name": "Marstons Mills", "countryCode": "US", "stateCode": "MA"}, {"name": "Mashpee", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Mattapoisett Center", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Medfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Medford", "countryCode": "US", "stateCode": "MA"}, {"name": "Medway", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Merrimac", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Middleborough", "countryCode": "US", "stateCode": "MA"}, {"name": "Middleborough Center", "countryCode": "US", "stateCode": "MA"}, {"name": "Middlesex County", "countryCode": "US", "stateCode": "MA"}, {"name": "Middleton", "countryCode": "US", "stateCode": "MA"}, {"name": "Milford", "countryCode": "US", "stateCode": "MA"}, {"name": "Millbury", "countryCode": "US", "stateCode": "MA"}, {"name": "Millers Falls", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Millis-Clicquot", "countryCode": "US", "stateCode": "MA"}, {"name": "Millville", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Monson Center", "countryCode": "US", "stateCode": "MA"}, {"name": "Montague", "countryCode": "US", "stateCode": "MA"}, {"name": "Monument Beach", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Nantucket", "countryCode": "US", "stateCode": "MA"}, {"name": "Nantucket County", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Needham", "countryCode": "US", "stateCode": "MA"}, {"name": "New Bedford", "countryCode": "US", "stateCode": "MA"}, {"name": "New Marlborough", "countryCode": "US", "stateCode": "MA"}, {"name": "Newburyport", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Norfolk", "countryCode": "US", "stateCode": "MA"}, {"name": "Norfolk County", "countryCode": "US", "stateCode": "MA"}, {"name": "North Adams", "countryCode": "US", "stateCode": "MA"}, {"name": "North Amherst", "countryCode": "US", "stateCode": "MA"}, {"name": "North Andover", "countryCode": "US", "stateCode": "MA"}, {"name": "North Attleborough Center", "countryCode": "US", "stateCode": "MA"}, {"name": "North Brookfield", "countryCode": "US", "stateCode": "MA"}, {"name": "North Chicopee", "countryCode": "US", "stateCode": "MA"}, {"name": "North Eastham", "countryCode": "US", "stateCode": "MA"}, {"name": "North Falmouth", "countryCode": "US", "stateCode": "MA"}, {"name": "North Lakeville", "countryCode": "US", "stateCode": "MA"}, {"name": "North Pembroke", "countryCode": "US", "stateCode": "MA"}, {"name": "North Plymouth", "countryCode": "US", "stateCode": "MA"}, {"name": "North Reading", "countryCode": "US", "stateCode": "MA"}, {"name": "North Scituate", "countryCode": "US", "stateCode": "MA"}, {"name": "North Seekonk", "countryCode": "US", "stateCode": "MA"}, {"name": "North Westport", "countryCode": "US", "stateCode": "MA"}, {"name": "Northampton", "countryCode": "US", "stateCode": "MA"}, {"name": "Northborough", "countryCode": "US", "stateCode": "MA"}, {"name": "Northbridge", "countryCode": "US", "stateCode": "MA"}, {"name": "Northfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Northwest Harwich", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Norton Center", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Norwood", "countryCode": "US", "stateCode": "MA"}, {"name": "Oak Bluffs", "countryCode": "US", "stateCode": "MA"}, {"name": "Oakham", "countryCode": "US", "stateCode": "MA"}, {"name": "Ocean Bluff-Brant Rock", "countryCode": "US", "stateCode": "MA"}, {"name": "Ocean Grove", "countryCode": "US", "stateCode": "MA"}, {"name": "Onset", "countryCode": "US", "stateCode": "MA"}, {"name": "Orange", "countryCode": "US", "stateCode": "MA"}, {"name": "Orleans", "countryCode": "US", "stateCode": "MA"}, {"name": "Osterville", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Oxford", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Pelham", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Pinehurst", "countryCode": "US", "stateCode": "MA"}, {"name": "Pittsfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Plainville", "countryCode": "US", "stateCode": "MA"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "MA"}, {"name": "Plymouth County", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>ly<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Pocasset", "countryCode": "US", "stateCode": "MA"}, {"name": "Princeton", "countryCode": "US", "stateCode": "MA"}, {"name": "Provincetown", "countryCode": "US", "stateCode": "MA"}, {"name": "Quincy", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Raynham Center", "countryCode": "US", "stateCode": "MA"}, {"name": "Reading", "countryCode": "US", "stateCode": "MA"}, {"name": "Re<PERSON>both", "countryCode": "US", "stateCode": "MA"}, {"name": "Revere", "countryCode": "US", "stateCode": "MA"}, {"name": "Richmond", "countryCode": "US", "stateCode": "MA"}, {"name": "Rochester", "countryCode": "US", "stateCode": "MA"}, {"name": "Rockland", "countryCode": "US", "stateCode": "MA"}, {"name": "Rockport", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Rutland", "countryCode": "US", "stateCode": "MA"}, {"name": "Sagamore", "countryCode": "US", "stateCode": "MA"}, {"name": "Salem", "countryCode": "US", "stateCode": "MA"}, {"name": "Salisbury", "countryCode": "US", "stateCode": "MA"}, {"name": "Sandwich", "countryCode": "US", "stateCode": "MA"}, {"name": "Saugus", "countryCode": "US", "stateCode": "MA"}, {"name": "Scituate", "countryCode": "US", "stateCode": "MA"}, {"name": "Seekonk", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Sheffield", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Shelburne Falls", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Shrewsbury", "countryCode": "US", "stateCode": "MA"}, {"name": "Shutesbury", "countryCode": "US", "stateCode": "MA"}, {"name": "Smith Mills", "countryCode": "US", "stateCode": "MA"}, {"name": "Somerset", "countryCode": "US", "stateCode": "MA"}, {"name": "Somerville", "countryCode": "US", "stateCode": "MA"}, {"name": "South Amherst", "countryCode": "US", "stateCode": "MA"}, {"name": "South Ashburnham", "countryCode": "US", "stateCode": "MA"}, {"name": "South Boston", "countryCode": "US", "stateCode": "MA"}, {"name": "South Deerfield", "countryCode": "US", "stateCode": "MA"}, {"name": "South Dennis", "countryCode": "US", "stateCode": "MA"}, {"name": "South Duxbury", "countryCode": "US", "stateCode": "MA"}, {"name": "South Hadley", "countryCode": "US", "stateCode": "MA"}, {"name": "South Lancaster", "countryCode": "US", "stateCode": "MA"}, {"name": "South Peabody", "countryCode": "US", "stateCode": "MA"}, {"name": "South Yarmouth", "countryCode": "US", "stateCode": "MA"}, {"name": "Southampton", "countryCode": "US", "stateCode": "MA"}, {"name": "Southborough", "countryCode": "US", "stateCode": "MA"}, {"name": "Southbridge", "countryCode": "US", "stateCode": "MA"}, {"name": "Southwick", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Springfield", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Stockbridge", "countryCode": "US", "stateCode": "MA"}, {"name": "Stoneham", "countryCode": "US", "stateCode": "MA"}, {"name": "Stoughton", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Sturbridge", "countryCode": "US", "stateCode": "MA"}, {"name": "Sudbury", "countryCode": "US", "stateCode": "MA"}, {"name": "Suffolk County", "countryCode": "US", "stateCode": "MA"}, {"name": "Sunderland", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Swampscott", "countryCode": "US", "stateCode": "MA"}, {"name": "Swansea", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Teaticket", "countryCode": "US", "stateCode": "MA"}, {"name": "Templeton", "countryCode": "US", "stateCode": "MA"}, {"name": "Tewksbury", "countryCode": "US", "stateCode": "MA"}, {"name": "Three Rivers", "countryCode": "US", "stateCode": "MA"}, {"name": "Topsfield", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Truro", "countryCode": "US", "stateCode": "MA"}, {"name": "Turners Falls", "countryCode": "US", "stateCode": "MA"}, {"name": "Tyngsboro", "countryCode": "US", "stateCode": "MA"}, {"name": "Upton", "countryCode": "US", "stateCode": "MA"}, {"name": "Uxbridge", "countryCode": "US", "stateCode": "MA"}, {"name": "Vineyard Haven", "countryCode": "US", "stateCode": "MA"}, {"name": "Wakefield", "countryCode": "US", "stateCode": "MA"}, {"name": "Wales", "countryCode": "US", "stateCode": "MA"}, {"name": "Walpole", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Ware", "countryCode": "US", "stateCode": "MA"}, {"name": "Wareham Center", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Watertown", "countryCode": "US", "stateCode": "MA"}, {"name": "Wayland", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Wellfleet", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Wen<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "West Barnstable", "countryCode": "US", "stateCode": "MA"}, {"name": "West Boylston", "countryCode": "US", "stateCode": "MA"}, {"name": "West Bridgewater", "countryCode": "US", "stateCode": "MA"}, {"name": "West Brookfield", "countryCode": "US", "stateCode": "MA"}, {"name": "West Chatham", "countryCode": "US", "stateCode": "MA"}, {"name": "West Concord", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON> Dennis", "countryCode": "US", "stateCode": "MA"}, {"name": "West Falmouth", "countryCode": "US", "stateCode": "MA"}, {"name": "West Newbury", "countryCode": "US", "stateCode": "MA"}, {"name": "West Springfield", "countryCode": "US", "stateCode": "MA"}, {"name": "West Stockbridge", "countryCode": "US", "stateCode": "MA"}, {"name": "West Tisbury", "countryCode": "US", "stateCode": "MA"}, {"name": "West Wareham", "countryCode": "US", "stateCode": "MA"}, {"name": "West Yarmouth", "countryCode": "US", "stateCode": "MA"}, {"name": "Westborough", "countryCode": "US", "stateCode": "MA"}, {"name": "Westfield", "countryCode": "US", "stateCode": "MA"}, {"name": "Westford", "countryCode": "US", "stateCode": "MA"}, {"name": "Westhampton", "countryCode": "US", "stateCode": "MA"}, {"name": "Westminster", "countryCode": "US", "stateCode": "MA"}, {"name": "Weston", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Weweantic", "countryCode": "US", "stateCode": "MA"}, {"name": "Weymouth", "countryCode": "US", "stateCode": "MA"}, {"name": "Whately", "countryCode": "US", "stateCode": "MA"}, {"name": "White Island Shores", "countryCode": "US", "stateCode": "MA"}, {"name": "Whitinsville", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Williamsburg", "countryCode": "US", "stateCode": "MA"}, {"name": "Williamstown", "countryCode": "US", "stateCode": "MA"}, {"name": "Wilmington", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Winchester", "countryCode": "US", "stateCode": "MA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA"}, {"name": "Woburn", "countryCode": "US", "stateCode": "MA"}, {"name": "Worcester", "countryCode": "US", "stateCode": "MA"}, {"name": "Worcester County", "countryCode": "US", "stateCode": "MA"}, {"name": "Wrentham", "countryCode": "US", "stateCode": "MA"}, {"name": "Yarmouth", "countryCode": "US", "stateCode": "MA"}, {"name": "Yarmouth Port", "countryCode": "US", "stateCode": "MA"}]