[{"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.10482000", "longitude": "-70.94532000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.48509000", "longitude": "-71.43284000"}, {"name": "Acushnet", "countryCode": "US", "stateCode": "MA", "latitude": "41.68066000", "longitude": "-70.90782000"}, {"name": "Acushnet Center", "countryCode": "US", "stateCode": "MA", "latitude": "41.68508000", "longitude": "-70.90642000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.62425000", "longitude": "-73.11760000"}, {"name": "Agawam", "countryCode": "US", "stateCode": "MA", "latitude": "42.06954000", "longitude": "-72.61481000"}, {"name": "Amesbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.85842000", "longitude": "-70.93005000"}, {"name": "Amherst", "countryCode": "US", "stateCode": "MA", "latitude": "42.36723000", "longitude": "-72.51852000"}, {"name": "Amherst Center", "countryCode": "US", "stateCode": "MA", "latitude": "42.37537000", "longitude": "-72.51925000"}, {"name": "Andover", "countryCode": "US", "stateCode": "MA", "latitude": "42.65843000", "longitude": "-71.13700000"}, {"name": "Arlington", "countryCode": "US", "stateCode": "MA", "latitude": "42.41537000", "longitude": "-71.15644000"}, {"name": "Ashburnham", "countryCode": "US", "stateCode": "MA", "latitude": "42.63620000", "longitude": "-71.90785000"}, {"name": "Ashby", "countryCode": "US", "stateCode": "MA", "latitude": "42.67787000", "longitude": "-71.82035000"}, {"name": "Ashfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.52647000", "longitude": "-72.78843000"}, {"name": "Ashland", "countryCode": "US", "stateCode": "MA", "latitude": "42.26121000", "longitude": "-71.46340000"}, {"name": "Assonet", "countryCode": "US", "stateCode": "MA", "latitude": "41.79594000", "longitude": "-71.06782000"}, {"name": "At<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.59592000", "longitude": "-72.22675000"}, {"name": "Attleboro", "countryCode": "US", "stateCode": "MA", "latitude": "41.94454000", "longitude": "-71.28561000"}, {"name": "Auburn", "countryCode": "US", "stateCode": "MA", "latitude": "42.19454000", "longitude": "-71.83563000"}, {"name": "Avon", "countryCode": "US", "stateCode": "MA", "latitude": "42.13066000", "longitude": "-71.04116000"}, {"name": "Ayer", "countryCode": "US", "stateCode": "MA", "latitude": "42.56120000", "longitude": "-71.58979000"}, {"name": "Baldwinville", "countryCode": "US", "stateCode": "MA", "latitude": "42.60842000", "longitude": "-72.07591000"}, {"name": "Barnstable", "countryCode": "US", "stateCode": "MA", "latitude": "41.70011000", "longitude": "-70.29947000"}, {"name": "Barnstable County", "countryCode": "US", "stateCode": "MA", "latitude": "41.68419000", "longitude": "-70.27405000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.42287000", "longitude": "-72.10508000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.33203000", "longitude": "-73.08288000"}, {"name": "Bedford", "countryCode": "US", "stateCode": "MA", "latitude": "42.49065000", "longitude": "-71.27617000"}, {"name": "Belchertown", "countryCode": "US", "stateCode": "MA", "latitude": "42.27704000", "longitude": "-72.40092000"}, {"name": "Bellingham", "countryCode": "US", "stateCode": "MA", "latitude": "42.08676000", "longitude": "-71.47451000"}, {"name": "Belmont", "countryCode": "US", "stateCode": "MA", "latitude": "42.39593000", "longitude": "-71.17867000"}, {"name": "Berkley", "countryCode": "US", "stateCode": "MA", "latitude": "41.84593000", "longitude": "-71.08282000"}, {"name": "Berkshire County", "countryCode": "US", "stateCode": "MA", "latitude": "42.37067000", "longitude": "-73.20640000"}, {"name": "Berlin", "countryCode": "US", "stateCode": "MA", "latitude": "42.38120000", "longitude": "-71.63701000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.67092000", "longitude": "-72.54953000"}, {"name": "Beverly", "countryCode": "US", "stateCode": "MA", "latitude": "42.55843000", "longitude": "-70.88005000"}, {"name": "Beverly Cove", "countryCode": "US", "stateCode": "MA", "latitude": "42.55343000", "longitude": "-70.85366000"}, {"name": "Billerica", "countryCode": "US", "stateCode": "MA", "latitude": "42.55843000", "longitude": "-71.26895000"}, {"name": "Blackstone", "countryCode": "US", "stateCode": "MA", "latitude": "42.01788000", "longitude": "-71.54117000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.61177000", "longitude": "-70.93837000"}, {"name": "Bolton", "countryCode": "US", "stateCode": "MA", "latitude": "42.43343000", "longitude": "-71.60784000"}, {"name": "Bondsville", "countryCode": "US", "stateCode": "MA", "latitude": "42.21259000", "longitude": "-72.34536000"}, {"name": "Boston", "countryCode": "US", "stateCode": "MA", "latitude": "42.35843000", "longitude": "-71.05977000"}, {"name": "Bourne", "countryCode": "US", "stateCode": "MA", "latitude": "41.74122000", "longitude": "-70.59892000"}, {"name": "Boxborough", "countryCode": "US", "stateCode": "MA", "latitude": "42.49084000", "longitude": "-71.52851000"}, {"name": "Boxford", "countryCode": "US", "stateCode": "MA", "latitude": "42.66120000", "longitude": "-70.99672000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.39176000", "longitude": "-71.70368000"}, {"name": "Braintree", "countryCode": "US", "stateCode": "MA", "latitude": "42.20384000", "longitude": "-71.00215000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.76011000", "longitude": "-70.08280000"}, {"name": "Bridgewater", "countryCode": "US", "stateCode": "MA", "latitude": "41.99038000", "longitude": "-70.97504000"}, {"name": "Brimfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.12287000", "longitude": "-72.20091000"}, {"name": "Bristol County", "countryCode": "US", "stateCode": "MA", "latitude": "41.75709000", "longitude": "-71.08852000"}, {"name": "Brockton", "countryCode": "US", "stateCode": "MA", "latitude": "42.08343000", "longitude": "-71.01838000"}, {"name": "Brookline", "countryCode": "US", "stateCode": "MA", "latitude": "42.33176000", "longitude": "-71.12116000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.59231000", "longitude": "-72.79176000"}, {"name": "Burlington", "countryCode": "US", "stateCode": "MA", "latitude": "42.50482000", "longitude": "-71.19561000"}, {"name": "Buzzards Bay", "countryCode": "US", "stateCode": "MA", "latitude": "41.74538000", "longitude": "-70.61809000"}, {"name": "Cambridge", "countryCode": "US", "stateCode": "MA", "latitude": "42.37510000", "longitude": "-71.10561000"}, {"name": "Canton", "countryCode": "US", "stateCode": "MA", "latitude": "42.15843000", "longitude": "-71.14477000"}, {"name": "Carlisle", "countryCode": "US", "stateCode": "MA", "latitude": "42.52926000", "longitude": "-71.34950000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.88344000", "longitude": "-70.76254000"}, {"name": "Centerville", "countryCode": "US", "stateCode": "MA", "latitude": "41.64872000", "longitude": "-70.34808000"}, {"name": "Charlemont", "countryCode": "US", "stateCode": "MA", "latitude": "42.62786000", "longitude": "-72.86982000"}, {"name": "Charlton", "countryCode": "US", "stateCode": "MA", "latitude": "42.13565000", "longitude": "-71.97007000"}, {"name": "Chatham", "countryCode": "US", "stateCode": "MA", "latitude": "41.68206000", "longitude": "-69.95974000"}, {"name": "Chelmsford", "countryCode": "US", "stateCode": "MA", "latitude": "42.59981000", "longitude": "-71.36728000"}, {"name": "Chelsea", "countryCode": "US", "stateCode": "MA", "latitude": "42.39176000", "longitude": "-71.03283000"}, {"name": "Chesterfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.39175000", "longitude": "-72.83982000"}, {"name": "Chicopee", "countryCode": "US", "stateCode": "MA", "latitude": "42.14870000", "longitude": "-72.60787000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.41676000", "longitude": "-71.68285000"}, {"name": "Cochituate", "countryCode": "US", "stateCode": "MA", "latitude": "42.32093000", "longitude": "-71.36423000"}, {"name": "Cohasset", "countryCode": "US", "stateCode": "MA", "latitude": "42.24177000", "longitude": "-70.80365000"}, {"name": "Colrain", "countryCode": "US", "stateCode": "MA", "latitude": "42.67314000", "longitude": "-72.69676000"}, {"name": "Concord", "countryCode": "US", "stateCode": "MA", "latitude": "42.46037000", "longitude": "-71.34895000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.50981000", "longitude": "-72.69953000"}, {"name": "Cordaville", "countryCode": "US", "stateCode": "MA", "latitude": "42.26898000", "longitude": "-71.52395000"}, {"name": "Cotuit", "countryCode": "US", "stateCode": "MA", "latitude": "41.61678000", "longitude": "-70.43697000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.47370000", "longitude": "-73.16621000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.57509000", "longitude": "-70.93005000"}, {"name": "Dartmouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.59419180", "longitude": "-71.11043520"}, {"name": "Dedham", "countryCode": "US", "stateCode": "MA", "latitude": "42.24177000", "longitude": "-71.16616000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.73539000", "longitude": "-70.19391000"}, {"name": "Dennis Port", "countryCode": "US", "stateCode": "MA", "latitude": "41.65845000", "longitude": "-70.12863000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.54470000", "longitude": "-71.61318000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.81399000", "longitude": "-71.12032000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.05426000", "longitude": "-71.73951000"}, {"name": "Dover", "countryCode": "US", "stateCode": "MA", "latitude": "42.24593000", "longitude": "-71.28283000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.67037000", "longitude": "-71.30201000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.04510000", "longitude": "-71.93007000"}, {"name": "Dukes County", "countryCode": "US", "stateCode": "MA", "latitude": "41.38877000", "longitude": "-70.69877000"}, {"name": "Dunst<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.67509000", "longitude": "-71.48284000"}, {"name": "Duxbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.04177000", "longitude": "-70.67226000"}, {"name": "East Bridgewater", "countryCode": "US", "stateCode": "MA", "latitude": "42.03343000", "longitude": "-70.95921000"}, {"name": "East Brookfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.22787000", "longitude": "-72.04674000"}, {"name": "East Dennis", "countryCode": "US", "stateCode": "MA", "latitude": "41.74261000", "longitude": "-70.16196000"}, {"name": "East Douglas", "countryCode": "US", "stateCode": "MA", "latitude": "42.07232000", "longitude": "-71.71340000"}, {"name": "East Falmouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.57844000", "longitude": "-70.55864000"}, {"name": "East Harwich", "countryCode": "US", "stateCode": "MA", "latitude": "41.70011000", "longitude": "-70.02724000"}, {"name": "East Longmeadow", "countryCode": "US", "stateCode": "MA", "latitude": "42.06454000", "longitude": "-72.51259000"}, {"name": "East Pepperell", "countryCode": "US", "stateCode": "MA", "latitude": "42.66537000", "longitude": "-71.57312000"}, {"name": "East Sandwich", "countryCode": "US", "stateCode": "MA", "latitude": "41.74177000", "longitude": "-70.45169000"}, {"name": "Eastham", "countryCode": "US", "stateCode": "MA", "latitude": "41.83011000", "longitude": "-69.97391000"}, {"name": "Easthampton", "countryCode": "US", "stateCode": "MA", "latitude": "42.26676000", "longitude": "-72.66898000"}, {"name": "Easton", "countryCode": "US", "stateCode": "MA", "latitude": "42.02454000", "longitude": "-71.12866000"}, {"name": "Edgartown", "countryCode": "US", "stateCode": "MA", "latitude": "41.38901000", "longitude": "-70.51336000"}, {"name": "<PERSON><PERSON>g", "countryCode": "US", "stateCode": "MA", "latitude": "42.60009000", "longitude": "-72.39814000"}, {"name": "Essex", "countryCode": "US", "stateCode": "MA", "latitude": "42.63204000", "longitude": "-70.78283000"}, {"name": "Essex County", "countryCode": "US", "stateCode": "MA", "latitude": "42.63887000", "longitude": "-70.86792000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.40843000", "longitude": "-71.05366000"}, {"name": "Fairhaven", "countryCode": "US", "stateCode": "MA", "latitude": "41.63760000", "longitude": "-70.90365000"}, {"name": "Fall River", "countryCode": "US", "stateCode": "MA", "latitude": "41.70149000", "longitude": "-71.15505000"}, {"name": "Falmouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.55150000", "longitude": "-70.61475000"}, {"name": "Fiskdale", "countryCode": "US", "stateCode": "MA", "latitude": "42.11621000", "longitude": "-72.11341000"}, {"name": "Fitchburg", "countryCode": "US", "stateCode": "MA", "latitude": "42.58342000", "longitude": "-71.80230000"}, {"name": "Forestdale", "countryCode": "US", "stateCode": "MA", "latitude": "41.69177000", "longitude": "-70.49947000"}, {"name": "Foxborough", "countryCode": "US", "stateCode": "MA", "latitude": "42.06538000", "longitude": "-71.24783000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.27926000", "longitude": "-71.41617000"}, {"name": "Framingham Center", "countryCode": "US", "stateCode": "MA", "latitude": "42.29732000", "longitude": "-71.43701000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.08343000", "longitude": "-71.39673000"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "MA", "latitude": "42.58312000", "longitude": "-72.59187000"}, {"name": "Freetown", "countryCode": "US", "stateCode": "MA", "latitude": "41.76677000", "longitude": "-71.03282000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.57509000", "longitude": "-71.99813000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.64036000", "longitude": "-72.49953000"}, {"name": "Gloucester", "countryCode": "US", "stateCode": "MA", "latitude": "42.61405000", "longitude": "-70.66313000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.20704000", "longitude": "-71.68562000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.25648000", "longitude": "-72.51620000"}, {"name": "Granville", "countryCode": "US", "stateCode": "MA", "latitude": "42.06676000", "longitude": "-72.86149000"}, {"name": "Great Barrington", "countryCode": "US", "stateCode": "MA", "latitude": "42.19592000", "longitude": "-73.36206000"}, {"name": "Green Harbor-Cedar Crest", "countryCode": "US", "stateCode": "MA", "latitude": "42.07495000", "longitude": "-70.65843000"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.58759000", "longitude": "-72.59953000"}, {"name": "Groton", "countryCode": "US", "stateCode": "MA", "latitude": "42.61120000", "longitude": "-71.57451000"}, {"name": "Groveland", "countryCode": "US", "stateCode": "MA", "latitude": "42.76037000", "longitude": "-71.03145000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.34176000", "longitude": "-72.58842000"}, {"name": "Halifax", "countryCode": "US", "stateCode": "MA", "latitude": "41.99121000", "longitude": "-70.86199000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.25620000", "longitude": "-71.76757000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.06398000", "longitude": "-72.41342000"}, {"name": "Hampden County", "countryCode": "US", "stateCode": "MA", "latitude": "42.13511000", "longitude": "-72.63162000"}, {"name": "Hampshire County", "countryCode": "US", "stateCode": "MA", "latitude": "42.34014000", "longitude": "-72.66377000"}, {"name": "Hanover", "countryCode": "US", "stateCode": "MA", "latitude": "42.11316000", "longitude": "-70.81199000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.07510000", "longitude": "-70.88004000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.35009000", "longitude": "-72.19952000"}, {"name": "Harvard", "countryCode": "US", "stateCode": "MA", "latitude": "42.50009000", "longitude": "-71.58284000"}, {"name": "Harwich", "countryCode": "US", "stateCode": "MA", "latitude": "41.68622000", "longitude": "-70.07585000"}, {"name": "Harwich Center", "countryCode": "US", "stateCode": "MA", "latitude": "41.69235000", "longitude": "-70.06938000"}, {"name": "Harwich Port", "countryCode": "US", "stateCode": "MA", "latitude": "41.66678000", "longitude": "-70.07863000"}, {"name": "Hatfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.37092000", "longitude": "-72.59814000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.77620000", "longitude": "-71.07728000"}, {"name": "Head of Westport", "countryCode": "US", "stateCode": "MA", "latitude": "41.62094000", "longitude": "-71.06199000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.24177000", "longitude": "-70.88977000"}, {"name": "Hinsdale", "countryCode": "US", "stateCode": "MA", "latitude": "42.43870000", "longitude": "-73.12538000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.15510000", "longitude": "-71.00866000"}, {"name": "Holden", "countryCode": "US", "stateCode": "MA", "latitude": "42.35176000", "longitude": "-71.86341000"}, {"name": "Holland", "countryCode": "US", "stateCode": "MA", "latitude": "42.06398000", "longitude": "-72.15730000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.20010000", "longitude": "-71.42450000"}, {"name": "Holyoke", "countryCode": "US", "stateCode": "MA", "latitude": "42.20426000", "longitude": "-72.61620000"}, {"name": "Hopedale", "countryCode": "US", "stateCode": "MA", "latitude": "42.13065000", "longitude": "-71.54117000"}, {"name": "<PERSON>kin<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.22871000", "longitude": "-71.52256000"}, {"name": "Housatonic", "countryCode": "US", "stateCode": "MA", "latitude": "42.25425000", "longitude": "-73.36622000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.47370000", "longitude": "-72.00619000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.39176000", "longitude": "-71.56618000"}, {"name": "Hull", "countryCode": "US", "stateCode": "MA", "latitude": "42.30204000", "longitude": "-70.90782000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.65289000", "longitude": "-70.28280000"}, {"name": "Ipswich", "countryCode": "US", "stateCode": "MA", "latitude": "42.67926000", "longitude": "-70.84116000"}, {"name": "Jamaica Plain", "countryCode": "US", "stateCode": "MA", "latitude": "42.30982000", "longitude": "-71.12033000"}, {"name": "Kingston", "countryCode": "US", "stateCode": "MA", "latitude": "41.99455000", "longitude": "-70.72448000"}, {"name": "Lancaster", "countryCode": "US", "stateCode": "MA", "latitude": "42.45565000", "longitude": "-71.67312000"}, {"name": "Lanesborough", "countryCode": "US", "stateCode": "MA", "latitude": "42.51731000", "longitude": "-73.22816000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.70704000", "longitude": "-71.16311000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.30425000", "longitude": "-73.24816000"}, {"name": "Leicester", "countryCode": "US", "stateCode": "MA", "latitude": "42.24593000", "longitude": "-71.90868000"}, {"name": "Lenox", "countryCode": "US", "stateCode": "MA", "latitude": "42.35648000", "longitude": "-73.28483000"}, {"name": "Leominster", "countryCode": "US", "stateCode": "MA", "latitude": "42.52509000", "longitude": "-71.75979000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.45203000", "longitude": "-72.50148000"}, {"name": "Lexington", "countryCode": "US", "stateCode": "MA", "latitude": "42.44732000", "longitude": "-71.22450000"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "MA", "latitude": "42.42593000", "longitude": "-71.30395000"}, {"name": "Littleton Common", "countryCode": "US", "stateCode": "MA", "latitude": "42.54593000", "longitude": "-71.47451000"}, {"name": "Longmeadow", "countryCode": "US", "stateCode": "MA", "latitude": "42.05010000", "longitude": "-72.58287000"}, {"name": "Lowell", "countryCode": "US", "stateCode": "MA", "latitude": "42.63342000", "longitude": "-71.31617000"}, {"name": "Ludlow", "countryCode": "US", "stateCode": "MA", "latitude": "42.16009000", "longitude": "-72.47592000"}, {"name": "Lunenburg", "countryCode": "US", "stateCode": "MA", "latitude": "42.59453000", "longitude": "-71.72452000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.46676000", "longitude": "-70.94949000"}, {"name": "Lynnfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.53898000", "longitude": "-71.04811000"}, {"name": "Malden", "countryCode": "US", "stateCode": "MA", "latitude": "42.42510000", "longitude": "-71.06616000"}, {"name": "Manchester-by-the-Sea", "countryCode": "US", "stateCode": "MA", "latitude": "42.57787000", "longitude": "-70.76894000"}, {"name": "Mansfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.03343000", "longitude": "-71.21894000"}, {"name": "Mansfield Center", "countryCode": "US", "stateCode": "MA", "latitude": "42.02262000", "longitude": "-71.21808000"}, {"name": "Marblehead", "countryCode": "US", "stateCode": "MA", "latitude": "42.50010000", "longitude": "-70.85783000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.70010000", "longitude": "-70.76281000"}, {"name": "Marion Center", "countryCode": "US", "stateCode": "MA", "latitude": "41.70424000", "longitude": "-70.76286000"}, {"name": "Marlborough", "countryCode": "US", "stateCode": "MA", "latitude": "42.34593000", "longitude": "-71.55229000"}, {"name": "Marshfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.09177000", "longitude": "-70.70559000"}, {"name": "Marshfield Hills", "countryCode": "US", "stateCode": "MA", "latitude": "42.14594000", "longitude": "-70.73976000"}, {"name": "Marstons Mills", "countryCode": "US", "stateCode": "MA", "latitude": "41.65622000", "longitude": "-70.41614000"}, {"name": "Mashpee", "countryCode": "US", "stateCode": "MA", "latitude": "41.64844000", "longitude": "-70.48114000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.65844000", "longitude": "-70.81615000"}, {"name": "Mattapoisett Center", "countryCode": "US", "stateCode": "MA", "latitude": "41.66595000", "longitude": "-70.80720000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.43343000", "longitude": "-71.44951000"}, {"name": "Medfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.18760000", "longitude": "-71.30645000"}, {"name": "Medford", "countryCode": "US", "stateCode": "MA", "latitude": "42.41843000", "longitude": "-71.10616000"}, {"name": "Medway", "countryCode": "US", "stateCode": "MA", "latitude": "42.14176000", "longitude": "-71.39673000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.45843000", "longitude": "-71.06616000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.10565000", "longitude": "-71.55229000"}, {"name": "Merrimac", "countryCode": "US", "stateCode": "MA", "latitude": "42.83065000", "longitude": "-71.00228000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.72620000", "longitude": "-71.19089000"}, {"name": "Middleborough", "countryCode": "US", "stateCode": "MA", "latitude": "41.89316000", "longitude": "-70.91115000"}, {"name": "Middleborough Center", "countryCode": "US", "stateCode": "MA", "latitude": "41.89460000", "longitude": "-70.92618000"}, {"name": "Middlesex County", "countryCode": "US", "stateCode": "MA", "latitude": "42.48555000", "longitude": "-71.39184000"}, {"name": "Middleton", "countryCode": "US", "stateCode": "MA", "latitude": "42.59509000", "longitude": "-71.01616000"}, {"name": "Milford", "countryCode": "US", "stateCode": "MA", "latitude": "42.13982000", "longitude": "-71.51617000"}, {"name": "Millbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.19398000", "longitude": "-71.76007000"}, {"name": "Millers Falls", "countryCode": "US", "stateCode": "MA", "latitude": "42.58203000", "longitude": "-72.49259000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.16760000", "longitude": "-71.35784000"}, {"name": "Millis-Clicquot", "countryCode": "US", "stateCode": "MA", "latitude": "42.16480000", "longitude": "-71.35442000"}, {"name": "Millville", "countryCode": "US", "stateCode": "MA", "latitude": "42.02788000", "longitude": "-71.58090000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.24954000", "longitude": "-71.06616000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.10426000", "longitude": "-72.31897000"}, {"name": "Monson Center", "countryCode": "US", "stateCode": "MA", "latitude": "42.09898000", "longitude": "-72.30481000"}, {"name": "Montague", "countryCode": "US", "stateCode": "MA", "latitude": "42.53564000", "longitude": "-72.53509000"}, {"name": "Monument Beach", "countryCode": "US", "stateCode": "MA", "latitude": "41.71955000", "longitude": "-70.61198000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.42649000", "longitude": "-70.91894000"}, {"name": "Nantucket", "countryCode": "US", "stateCode": "MA", "latitude": "41.28346000", "longitude": "-70.09946000"}, {"name": "Nantucket County", "countryCode": "US", "stateCode": "MA", "latitude": "41.26955000", "longitude": "-70.02171000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.28343000", "longitude": "-71.34950000"}, {"name": "Needham", "countryCode": "US", "stateCode": "MA", "latitude": "42.28343000", "longitude": "-71.23283000"}, {"name": "New Bedford", "countryCode": "US", "stateCode": "MA", "latitude": "41.63526000", "longitude": "-70.92701000"}, {"name": "New Marlborough", "countryCode": "US", "stateCode": "MA", "latitude": "42.12287000", "longitude": "-73.22872000"}, {"name": "Newburyport", "countryCode": "US", "stateCode": "MA", "latitude": "42.81259000", "longitude": "-70.87728000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.33704000", "longitude": "-71.20922000"}, {"name": "Norfolk", "countryCode": "US", "stateCode": "MA", "latitude": "42.11954000", "longitude": "-71.32506000"}, {"name": "Norfolk County", "countryCode": "US", "stateCode": "MA", "latitude": "42.17097000", "longitude": "-71.18381000"}, {"name": "North Adams", "countryCode": "US", "stateCode": "MA", "latitude": "42.70092000", "longitude": "-73.10871000"}, {"name": "North Amherst", "countryCode": "US", "stateCode": "MA", "latitude": "42.41037000", "longitude": "-72.53092000"}, {"name": "North Andover", "countryCode": "US", "stateCode": "MA", "latitude": "42.69870000", "longitude": "-71.13506000"}, {"name": "North Attleborough Center", "countryCode": "US", "stateCode": "MA", "latitude": "41.97263000", "longitude": "-71.32474000"}, {"name": "North Brookfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.26676000", "longitude": "-72.08285000"}, {"name": "North Chicopee", "countryCode": "US", "stateCode": "MA", "latitude": "42.18343000", "longitude": "-72.59953000"}, {"name": "North Eastham", "countryCode": "US", "stateCode": "MA", "latitude": "41.86511000", "longitude": "-69.99113000"}, {"name": "North Falmouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.64594000", "longitude": "-70.61836000"}, {"name": "North Lakeville", "countryCode": "US", "stateCode": "MA", "latitude": "41.85760000", "longitude": "-70.94226000"}, {"name": "North Pembroke", "countryCode": "US", "stateCode": "MA", "latitude": "42.09316000", "longitude": "-70.79254000"}, {"name": "North Plymouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.97094000", "longitude": "-70.68281000"}, {"name": "North Reading", "countryCode": "US", "stateCode": "MA", "latitude": "42.57509000", "longitude": "-71.07867000"}, {"name": "North Scituate", "countryCode": "US", "stateCode": "MA", "latitude": "42.21899000", "longitude": "-70.78560000"}, {"name": "North Seekonk", "countryCode": "US", "stateCode": "MA", "latitude": "41.88927000", "longitude": "-71.33005000"}, {"name": "North Westport", "countryCode": "US", "stateCode": "MA", "latitude": "41.66038000", "longitude": "-71.08838000"}, {"name": "Northampton", "countryCode": "US", "stateCode": "MA", "latitude": "42.32509000", "longitude": "-72.64120000"}, {"name": "Northborough", "countryCode": "US", "stateCode": "MA", "latitude": "42.31954000", "longitude": "-71.64118000"}, {"name": "Northbridge", "countryCode": "US", "stateCode": "MA", "latitude": "42.15148000", "longitude": "-71.64951000"}, {"name": "Northfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.69592000", "longitude": "-72.45287000"}, {"name": "Northwest Harwich", "countryCode": "US", "stateCode": "MA", "latitude": "41.69029000", "longitude": "-70.10250000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.96677000", "longitude": "-71.18699000"}, {"name": "Norton Center", "countryCode": "US", "stateCode": "MA", "latitude": "41.97254000", "longitude": "-71.18535000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.16177000", "longitude": "-70.79393000"}, {"name": "Norwood", "countryCode": "US", "stateCode": "MA", "latitude": "42.19454000", "longitude": "-71.19950000"}, {"name": "Oak Bluffs", "countryCode": "US", "stateCode": "MA", "latitude": "41.45428000", "longitude": "-70.56197000"}, {"name": "Oakham", "countryCode": "US", "stateCode": "MA", "latitude": "42.35287000", "longitude": "-72.04535000"}, {"name": "Ocean Bluff-Brant Rock", "countryCode": "US", "stateCode": "MA", "latitude": "42.10234000", "longitude": "-70.65736000"}, {"name": "Ocean Grove", "countryCode": "US", "stateCode": "MA", "latitude": "41.72927000", "longitude": "-71.20921000"}, {"name": "Onset", "countryCode": "US", "stateCode": "MA", "latitude": "41.74177000", "longitude": "-70.65781000"}, {"name": "Orange", "countryCode": "US", "stateCode": "MA", "latitude": "42.59036000", "longitude": "-72.30981000"}, {"name": "Orleans", "countryCode": "US", "stateCode": "MA", "latitude": "41.78983000", "longitude": "-69.98974000"}, {"name": "Osterville", "countryCode": "US", "stateCode": "MA", "latitude": "41.62844000", "longitude": "-70.38697000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.19315000", "longitude": "-73.09177000"}, {"name": "Oxford", "countryCode": "US", "stateCode": "MA", "latitude": "42.11676000", "longitude": "-71.86479000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.15843000", "longitude": "-72.32869000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.31120000", "longitude": "-71.92813000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.52787000", "longitude": "-70.92866000"}, {"name": "Pelham", "countryCode": "US", "stateCode": "MA", "latitude": "42.39315000", "longitude": "-72.40370000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.66592000", "longitude": "-71.58840000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.54870000", "longitude": "-72.13286000"}, {"name": "Pinehurst", "countryCode": "US", "stateCode": "MA", "latitude": "42.52926000", "longitude": "-71.22811000"}, {"name": "Pittsfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.45008000", "longitude": "-73.24538000"}, {"name": "Plainville", "countryCode": "US", "stateCode": "MA", "latitude": "42.00427000", "longitude": "-71.33283000"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.95844000", "longitude": "-70.66726000"}, {"name": "Plymouth County", "countryCode": "US", "stateCode": "MA", "latitude": "41.98743000", "longitude": "-70.73707000"}, {"name": "<PERSON>ly<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.95288000", "longitude": "-70.81448000"}, {"name": "Pocasset", "countryCode": "US", "stateCode": "MA", "latitude": "41.68622000", "longitude": "-70.61614000"}, {"name": "Princeton", "countryCode": "US", "stateCode": "MA", "latitude": "42.44870000", "longitude": "-71.87730000"}, {"name": "Provincetown", "countryCode": "US", "stateCode": "MA", "latitude": "42.05295000", "longitude": "-70.18640000"}, {"name": "Quincy", "countryCode": "US", "stateCode": "MA", "latitude": "42.25288000", "longitude": "-71.00227000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.16260000", "longitude": "-71.04116000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.94871000", "longitude": "-71.07310000"}, {"name": "Raynham Center", "countryCode": "US", "stateCode": "MA", "latitude": "41.92371000", "longitude": "-71.05227000"}, {"name": "Reading", "countryCode": "US", "stateCode": "MA", "latitude": "42.52565000", "longitude": "-71.09533000"}, {"name": "Re<PERSON>both", "countryCode": "US", "stateCode": "MA", "latitude": "41.84038000", "longitude": "-71.24949000"}, {"name": "Revere", "countryCode": "US", "stateCode": "MA", "latitude": "42.40843000", "longitude": "-71.01199000"}, {"name": "Richmond", "countryCode": "US", "stateCode": "MA", "latitude": "42.37314000", "longitude": "-73.36761000"}, {"name": "Rochester", "countryCode": "US", "stateCode": "MA", "latitude": "41.73177000", "longitude": "-70.82004000"}, {"name": "Rockland", "countryCode": "US", "stateCode": "MA", "latitude": "42.13066000", "longitude": "-70.91616000"}, {"name": "Rockport", "countryCode": "US", "stateCode": "MA", "latitude": "42.65565000", "longitude": "-70.62032000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.71676000", "longitude": "-70.87866000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.67759000", "longitude": "-72.18786000"}, {"name": "Rutland", "countryCode": "US", "stateCode": "MA", "latitude": "42.36954000", "longitude": "-71.94813000"}, {"name": "Sagamore", "countryCode": "US", "stateCode": "MA", "latitude": "41.77011000", "longitude": "-70.52836000"}, {"name": "Salem", "countryCode": "US", "stateCode": "MA", "latitude": "42.51954000", "longitude": "-70.89672000"}, {"name": "Salisbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.84176000", "longitude": "-70.86061000"}, {"name": "Sandwich", "countryCode": "US", "stateCode": "MA", "latitude": "41.75900000", "longitude": "-70.49392000"}, {"name": "Saugus", "countryCode": "US", "stateCode": "MA", "latitude": "42.46482000", "longitude": "-71.01005000"}, {"name": "Scituate", "countryCode": "US", "stateCode": "MA", "latitude": "42.19593000", "longitude": "-70.72587000"}, {"name": "Seekonk", "countryCode": "US", "stateCode": "MA", "latitude": "41.80843000", "longitude": "-71.33700000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.12371000", "longitude": "-71.17866000"}, {"name": "Sheffield", "countryCode": "US", "stateCode": "MA", "latitude": "42.11037000", "longitude": "-73.35511000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.58981000", "longitude": "-72.68842000"}, {"name": "Shelburne Falls", "countryCode": "US", "stateCode": "MA", "latitude": "42.60425000", "longitude": "-72.73926000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.23899000", "longitude": "-71.36978000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.54370000", "longitude": "-71.64951000"}, {"name": "Shrewsbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.29593000", "longitude": "-71.71285000"}, {"name": "Shutesbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.45648000", "longitude": "-72.40981000"}, {"name": "Smith Mills", "countryCode": "US", "stateCode": "MA", "latitude": "41.63899000", "longitude": "-70.99115000"}, {"name": "Somerset", "countryCode": "US", "stateCode": "MA", "latitude": "41.76955000", "longitude": "-71.12866000"}, {"name": "Somerville", "countryCode": "US", "stateCode": "MA", "latitude": "42.38760000", "longitude": "-71.09950000"}, {"name": "South Amherst", "countryCode": "US", "stateCode": "MA", "latitude": "42.34037000", "longitude": "-72.50509000"}, {"name": "South Ashburnham", "countryCode": "US", "stateCode": "MA", "latitude": "42.61037000", "longitude": "-71.93897000"}, {"name": "South Boston", "countryCode": "US", "stateCode": "MA", "latitude": "42.33343000", "longitude": "-71.04949000"}, {"name": "South Deerfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.47731000", "longitude": "-72.60787000"}, {"name": "South Dennis", "countryCode": "US", "stateCode": "MA", "latitude": "41.68956000", "longitude": "-70.15641000"}, {"name": "South Duxbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.02316000", "longitude": "-70.68281000"}, {"name": "South Hadley", "countryCode": "US", "stateCode": "MA", "latitude": "42.25842000", "longitude": "-72.57453000"}, {"name": "South Lancaster", "countryCode": "US", "stateCode": "MA", "latitude": "42.44454000", "longitude": "-71.68701000"}, {"name": "South Peabody", "countryCode": "US", "stateCode": "MA", "latitude": "42.50982000", "longitude": "-70.94949000"}, {"name": "South Yarmouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.66678000", "longitude": "-70.18474000"}, {"name": "Southampton", "countryCode": "US", "stateCode": "MA", "latitude": "42.22926000", "longitude": "-72.73009000"}, {"name": "Southborough", "countryCode": "US", "stateCode": "MA", "latitude": "42.30565000", "longitude": "-71.52451000"}, {"name": "Southbridge", "countryCode": "US", "stateCode": "MA", "latitude": "42.07510000", "longitude": "-72.03341000"}, {"name": "Southwick", "countryCode": "US", "stateCode": "MA", "latitude": "42.05482000", "longitude": "-72.77037000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.24398000", "longitude": "-71.99230000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.10148000", "longitude": "-72.58981000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.43759000", "longitude": "-71.76063000"}, {"name": "Stockbridge", "countryCode": "US", "stateCode": "MA", "latitude": "42.28759000", "longitude": "-73.32039000"}, {"name": "Stoneham", "countryCode": "US", "stateCode": "MA", "latitude": "42.48010000", "longitude": "-71.09950000"}, {"name": "Stoughton", "countryCode": "US", "stateCode": "MA", "latitude": "42.12510000", "longitude": "-71.10227000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.43704000", "longitude": "-71.50562000"}, {"name": "Sturbridge", "countryCode": "US", "stateCode": "MA", "latitude": "42.10843000", "longitude": "-72.07869000"}, {"name": "Sudbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.38343000", "longitude": "-71.41617000"}, {"name": "Suffolk County", "countryCode": "US", "stateCode": "MA", "latitude": "42.35550000", "longitude": "-71.06575000"}, {"name": "Sunderland", "countryCode": "US", "stateCode": "MA", "latitude": "42.24454000", "longitude": "-71.77174000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.15010000", "longitude": "-71.76285000"}, {"name": "Swampscott", "countryCode": "US", "stateCode": "MA", "latitude": "42.47093000", "longitude": "-70.91755000"}, {"name": "Swansea", "countryCode": "US", "stateCode": "MA", "latitude": "41.74816000", "longitude": "-71.18977000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "41.90010000", "longitude": "-71.08977000"}, {"name": "Teaticket", "countryCode": "US", "stateCode": "MA", "latitude": "41.56455000", "longitude": "-70.59586000"}, {"name": "Templeton", "countryCode": "US", "stateCode": "MA", "latitude": "42.55564000", "longitude": "-72.06758000"}, {"name": "Tewksbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.61065000", "longitude": "-71.23422000"}, {"name": "Three Rivers", "countryCode": "US", "stateCode": "MA", "latitude": "42.18120000", "longitude": "-72.36064000"}, {"name": "Topsfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.63759000", "longitude": "-70.94950000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.66676000", "longitude": "-71.70507000"}, {"name": "Truro", "countryCode": "US", "stateCode": "MA", "latitude": "41.99344000", "longitude": "-70.04975000"}, {"name": "Turners Falls", "countryCode": "US", "stateCode": "MA", "latitude": "42.60425000", "longitude": "-72.55648000"}, {"name": "Tyngsboro", "countryCode": "US", "stateCode": "MA", "latitude": "42.67676000", "longitude": "-71.42451000"}, {"name": "Upton", "countryCode": "US", "stateCode": "MA", "latitude": "42.17454000", "longitude": "-71.60229000"}, {"name": "Uxbridge", "countryCode": "US", "stateCode": "MA", "latitude": "42.07732000", "longitude": "-71.62951000"}, {"name": "Vineyard Haven", "countryCode": "US", "stateCode": "MA", "latitude": "41.45428000", "longitude": "-70.60364000"}, {"name": "Wakefield", "countryCode": "US", "stateCode": "MA", "latitude": "42.50648000", "longitude": "-71.07283000"}, {"name": "Wales", "countryCode": "US", "stateCode": "MA", "latitude": "42.06954000", "longitude": "-72.22230000"}, {"name": "Walpole", "countryCode": "US", "stateCode": "MA", "latitude": "42.14177000", "longitude": "-71.24950000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.37649000", "longitude": "-71.23561000"}, {"name": "Ware", "countryCode": "US", "stateCode": "MA", "latitude": "42.25981000", "longitude": "-72.23980000"}, {"name": "Wareham Center", "countryCode": "US", "stateCode": "MA", "latitude": "41.76677000", "longitude": "-70.72615000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.21259000", "longitude": "-72.19119000"}, {"name": "Watertown", "countryCode": "US", "stateCode": "MA", "latitude": "42.37093000", "longitude": "-71.18283000"}, {"name": "Wayland", "countryCode": "US", "stateCode": "MA", "latitude": "42.36260000", "longitude": "-71.36145000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.05010000", "longitude": "-71.88007000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.29649000", "longitude": "-71.29256000"}, {"name": "Wellfleet", "countryCode": "US", "stateCode": "MA", "latitude": "41.93761000", "longitude": "-70.03280000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.54814000", "longitude": "-72.39675000"}, {"name": "Wen<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.60426000", "longitude": "-70.89116000"}, {"name": "West Barnstable", "countryCode": "US", "stateCode": "MA", "latitude": "41.70566000", "longitude": "-70.37447000"}, {"name": "West Boylston", "countryCode": "US", "stateCode": "MA", "latitude": "42.36676000", "longitude": "-71.78563000"}, {"name": "West Bridgewater", "countryCode": "US", "stateCode": "MA", "latitude": "42.01899000", "longitude": "-71.00782000"}, {"name": "West Brookfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.23537000", "longitude": "-72.14119000"}, {"name": "West Chatham", "countryCode": "US", "stateCode": "MA", "latitude": "41.68122000", "longitude": "-69.99113000"}, {"name": "West Concord", "countryCode": "US", "stateCode": "MA", "latitude": "42.45843000", "longitude": "-71.39534000"}, {"name": "<PERSON> Dennis", "countryCode": "US", "stateCode": "MA", "latitude": "41.66456000", "longitude": "-70.17280000"}, {"name": "West Falmouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.60427000", "longitude": "-70.63447000"}, {"name": "West Newbury", "countryCode": "US", "stateCode": "MA", "latitude": "42.80148000", "longitude": "-70.98978000"}, {"name": "West Springfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.10704000", "longitude": "-72.62037000"}, {"name": "West Stockbridge", "countryCode": "US", "stateCode": "MA", "latitude": "42.34592000", "longitude": "-73.36622000"}, {"name": "West Tisbury", "countryCode": "US", "stateCode": "MA", "latitude": "41.38122000", "longitude": "-70.67447000"}, {"name": "West Wareham", "countryCode": "US", "stateCode": "MA", "latitude": "41.78983000", "longitude": "-70.76031000"}, {"name": "West Yarmouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.65011000", "longitude": "-70.24113000"}, {"name": "Westborough", "countryCode": "US", "stateCode": "MA", "latitude": "42.26954000", "longitude": "-71.61618000"}, {"name": "Westfield", "countryCode": "US", "stateCode": "MA", "latitude": "42.12509000", "longitude": "-72.74954000"}, {"name": "Westford", "countryCode": "US", "stateCode": "MA", "latitude": "42.57926000", "longitude": "-71.43784000"}, {"name": "Westhampton", "countryCode": "US", "stateCode": "MA", "latitude": "42.30287000", "longitude": "-72.77454000"}, {"name": "Westminster", "countryCode": "US", "stateCode": "MA", "latitude": "42.54592000", "longitude": "-71.91063000"}, {"name": "Weston", "countryCode": "US", "stateCode": "MA", "latitude": "42.36676000", "longitude": "-71.30311000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.21399000", "longitude": "-71.22450000"}, {"name": "Weweantic", "countryCode": "US", "stateCode": "MA", "latitude": "41.73538000", "longitude": "-70.73198000"}, {"name": "Weymouth", "countryCode": "US", "stateCode": "MA", "latitude": "42.22093000", "longitude": "-70.93977000"}, {"name": "Whately", "countryCode": "US", "stateCode": "MA", "latitude": "42.43981000", "longitude": "-72.63481000"}, {"name": "White Island Shores", "countryCode": "US", "stateCode": "MA", "latitude": "41.80010000", "longitude": "-70.63475000"}, {"name": "Whitinsville", "countryCode": "US", "stateCode": "MA", "latitude": "42.11121000", "longitude": "-71.66618000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.08066000", "longitude": "-70.93560000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.12371000", "longitude": "-72.43147000"}, {"name": "Williamsburg", "countryCode": "US", "stateCode": "MA", "latitude": "42.39314000", "longitude": "-72.73009000"}, {"name": "Williamstown", "countryCode": "US", "stateCode": "MA", "latitude": "42.71202000", "longitude": "-73.20372000"}, {"name": "Wilmington", "countryCode": "US", "stateCode": "MA", "latitude": "42.54648000", "longitude": "-71.17367000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.68620000", "longitude": "-72.04397000"}, {"name": "Winchester", "countryCode": "US", "stateCode": "MA", "latitude": "42.45232000", "longitude": "-71.13700000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MA", "latitude": "42.37510000", "longitude": "-70.98283000"}, {"name": "Woburn", "countryCode": "US", "stateCode": "MA", "latitude": "42.47926000", "longitude": "-71.15228000"}, {"name": "Worcester", "countryCode": "US", "stateCode": "MA", "latitude": "42.26259000", "longitude": "-71.80229000"}, {"name": "Worcester County", "countryCode": "US", "stateCode": "MA", "latitude": "42.35140000", "longitude": "-71.90774000"}, {"name": "Wrentham", "countryCode": "US", "stateCode": "MA", "latitude": "42.06677000", "longitude": "-71.32811000"}, {"name": "Yarmouth", "countryCode": "US", "stateCode": "MA", "latitude": "41.70567000", "longitude": "-70.22863000"}, {"name": "Yarmouth Port", "countryCode": "US", "stateCode": "MA", "latitude": "41.70205000", "longitude": "-70.24947000"}]