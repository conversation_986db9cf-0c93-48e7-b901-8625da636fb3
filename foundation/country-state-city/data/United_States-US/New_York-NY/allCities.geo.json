[{"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.80923000", "longitude": "-76.02409000"}, {"name": "Adams Center", "countryCode": "US", "stateCode": "NY", "latitude": "43.86006000", "longitude": "-76.00548000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.10285000", "longitude": "-77.23359000"}, {"name": "Airmont", "countryCode": "US", "stateCode": "NY", "latitude": "41.10093000", "longitude": "-74.11625000"}, {"name": "Akron", "countryCode": "US", "stateCode": "NY", "latitude": "43.02089000", "longitude": "-78.49530000"}, {"name": "Alabama", "countryCode": "US", "stateCode": "NY", "latitude": "43.09645000", "longitude": "-78.39086000"}, {"name": "Albany", "countryCode": "US", "stateCode": "NY", "latitude": "42.65258000", "longitude": "-73.75623000"}, {"name": "Albany County", "countryCode": "US", "stateCode": "NY", "latitude": "42.60018000", "longitude": "-73.97356000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.77343000", "longitude": "-73.64318000"}, {"name": "Albion", "countryCode": "US", "stateCode": "NY", "latitude": "43.24645000", "longitude": "-78.19363000"}, {"name": "Alden", "countryCode": "US", "stateCode": "NY", "latitude": "42.90006000", "longitude": "-78.49197000"}, {"name": "Alexandria Bay", "countryCode": "US", "stateCode": "NY", "latitude": "44.33588000", "longitude": "-75.91773000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.25424000", "longitude": "-77.79055000"}, {"name": "Allegany", "countryCode": "US", "stateCode": "NY", "latitude": "42.09006000", "longitude": "-78.49419000"}, {"name": "Allegany County", "countryCode": "US", "stateCode": "NY", "latitude": "42.25739000", "longitude": "-78.02756000"}, {"name": "Altamont", "countryCode": "US", "stateCode": "NY", "latitude": "42.70063000", "longitude": "-74.03374000"}, {"name": "Amagansett", "countryCode": "US", "stateCode": "NY", "latitude": "40.97371000", "longitude": "-72.14369000"}, {"name": "Amherst", "countryCode": "US", "stateCode": "NY", "latitude": "42.97839000", "longitude": "-78.79976000"}, {"name": "Amityville", "countryCode": "US", "stateCode": "NY", "latitude": "40.67899000", "longitude": "-73.41707000"}, {"name": "Amsterdam", "countryCode": "US", "stateCode": "NY", "latitude": "42.93869000", "longitude": "-74.18819000"}, {"name": "Andover", "countryCode": "US", "stateCode": "NY", "latitude": "42.15646000", "longitude": "-77.79555000"}, {"name": "Angola", "countryCode": "US", "stateCode": "NY", "latitude": "42.63839000", "longitude": "-79.02782000"}, {"name": "Angola on the Lake", "countryCode": "US", "stateCode": "NY", "latitude": "42.65478000", "longitude": "-79.04893000"}, {"name": "Apalachin", "countryCode": "US", "stateCode": "NY", "latitude": "42.06952000", "longitude": "-76.15465000"}, {"name": "Aquebogue", "countryCode": "US", "stateCode": "NY", "latitude": "40.94454000", "longitude": "-72.62704000"}, {"name": "Arcade", "countryCode": "US", "stateCode": "NY", "latitude": "42.53395000", "longitude": "-78.42307000"}, {"name": "Ardsley", "countryCode": "US", "stateCode": "NY", "latitude": "41.01065000", "longitude": "-73.84375000"}, {"name": "Arlington", "countryCode": "US", "stateCode": "NY", "latitude": "41.69593000", "longitude": "-73.89680000"}, {"name": "Armonk", "countryCode": "US", "stateCode": "NY", "latitude": "41.12648000", "longitude": "-73.71402000"}, {"name": "Arrochar", "countryCode": "US", "stateCode": "NY", "latitude": "40.59844000", "longitude": "-74.07264000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.59122000", "longitude": "-73.79597000"}, {"name": "Astoria", "countryCode": "US", "stateCode": "NY", "latitude": "40.77205000", "longitude": "-73.93014000"}, {"name": "Athens", "countryCode": "US", "stateCode": "NY", "latitude": "42.26036000", "longitude": "-73.80957000"}, {"name": "Atlantic Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.58899000", "longitude": "-73.72902000"}, {"name": "Attica", "countryCode": "US", "stateCode": "NY", "latitude": "42.86423000", "longitude": "-78.28029000"}, {"name": "Auburn", "countryCode": "US", "stateCode": "NY", "latitude": "42.93173000", "longitude": "-76.56605000"}, {"name": "Augusta", "countryCode": "US", "stateCode": "NY", "latitude": "42.97479000", "longitude": "-75.50129000"}, {"name": "Averill Park", "countryCode": "US", "stateCode": "NY", "latitude": "42.63397000", "longitude": "-73.55373000"}, {"name": "Avon", "countryCode": "US", "stateCode": "NY", "latitude": "42.91201000", "longitude": "-77.74556000"}, {"name": "Babylon", "countryCode": "US", "stateCode": "NY", "latitude": "40.69566000", "longitude": "-73.32568000"}, {"name": "Bainbridge", "countryCode": "US", "stateCode": "NY", "latitude": "42.29341000", "longitude": "-75.47935000"}, {"name": "Baiting Hollow", "countryCode": "US", "stateCode": "NY", "latitude": "40.95621000", "longitude": "-72.74427000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.65649000", "longitude": "-73.60930000"}, {"name": "Baldwin Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "40.63955000", "longitude": "-73.60846000"}, {"name": "Baldwinsville", "countryCode": "US", "stateCode": "NY", "latitude": "43.15868000", "longitude": "-76.33271000"}, {"name": "Ballston Spa", "countryCode": "US", "stateCode": "NY", "latitude": "43.00091000", "longitude": "-73.84901000"}, {"name": "Balmville", "countryCode": "US", "stateCode": "NY", "latitude": "41.53482000", "longitude": "-74.01486000"}, {"name": "Bardonia", "countryCode": "US", "stateCode": "NY", "latitude": "41.10954000", "longitude": "-73.99625000"}, {"name": "Barnum Island", "countryCode": "US", "stateCode": "NY", "latitude": "40.60455000", "longitude": "-73.64402000"}, {"name": "Batavia", "countryCode": "US", "stateCode": "NY", "latitude": "42.99812000", "longitude": "-78.18752000"}, {"name": "Bath", "countryCode": "US", "stateCode": "NY", "latitude": "42.33702000", "longitude": "-77.31776000"}, {"name": "Bath Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.60455000", "longitude": "-74.00431000"}, {"name": "Baxter Estates", "countryCode": "US", "stateCode": "NY", "latitude": "40.83482000", "longitude": "-73.69541000"}, {"name": "Bay Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.63316000", "longitude": "-73.67041000"}, {"name": "Bay Shore", "countryCode": "US", "stateCode": "NY", "latitude": "40.72510000", "longitude": "-73.24539000"}, {"name": "Bay Wood", "countryCode": "US", "stateCode": "NY", "latitude": "40.75010000", "longitude": "-73.29123000"}, {"name": "Baychester", "countryCode": "US", "stateCode": "NY", "latitude": "40.86928000", "longitude": "-73.83645000"}, {"name": "Bayport", "countryCode": "US", "stateCode": "NY", "latitude": "40.73843000", "longitude": "-73.05067000"}, {"name": "Bayside", "countryCode": "US", "stateCode": "NY", "latitude": "40.76844000", "longitude": "-73.77708000"}, {"name": "Bayville", "countryCode": "US", "stateCode": "NY", "latitude": "40.91065000", "longitude": "-73.56207000"}, {"name": "Beacon", "countryCode": "US", "stateCode": "NY", "latitude": "41.50482000", "longitude": "-73.96958000"}, {"name": "Beaver Dam Lake", "countryCode": "US", "stateCode": "NY", "latitude": "41.44743000", "longitude": "-74.11463000"}, {"name": "Beaverdam Lake-Salisbury Mills", "countryCode": "US", "stateCode": "NY", "latitude": "41.44162000", "longitude": "-74.11629000"}, {"name": "Bedford", "countryCode": "US", "stateCode": "NY", "latitude": "41.20426000", "longitude": "-73.64374000"}, {"name": "Bedford Hills", "countryCode": "US", "stateCode": "NY", "latitude": "41.23676000", "longitude": "-73.69458000"}, {"name": "Bellaire", "countryCode": "US", "stateCode": "NY", "latitude": "40.71399000", "longitude": "-73.75402000"}, {"name": "Belle Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "40.57594000", "longitude": "-73.84819000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.72427000", "longitude": "-73.71513000"}, {"name": "Bellerose Terrace", "countryCode": "US", "stateCode": "NY", "latitude": "40.72066000", "longitude": "-73.72596000"}, {"name": "Bellmore", "countryCode": "US", "stateCode": "NY", "latitude": "40.66871000", "longitude": "-73.52707000"}, {"name": "Bellport", "countryCode": "US", "stateCode": "NY", "latitude": "40.75704000", "longitude": "-72.93927000"}, {"name": "Belmont", "countryCode": "US", "stateCode": "NY", "latitude": "42.22312000", "longitude": "-78.03445000"}, {"name": "Bensonhurst", "countryCode": "US", "stateCode": "NY", "latitude": "40.60177000", "longitude": "-73.99403000"}, {"name": "Bergen", "countryCode": "US", "stateCode": "NY", "latitude": "43.08534000", "longitude": "-77.94223000"}, {"name": "Bergen Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.62038000", "longitude": "-73.90680000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.74427000", "longitude": "-73.48207000"}, {"name": "Big Flats", "countryCode": "US", "stateCode": "NY", "latitude": "42.13730000", "longitude": "-76.93691000"}, {"name": "Billington Heights", "countryCode": "US", "stateCode": "NY", "latitude": "42.78423000", "longitude": "-78.62642000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.09869000", "longitude": "-75.91797000"}, {"name": "Black River", "countryCode": "US", "stateCode": "NY", "latitude": "44.01256000", "longitude": "-75.79437000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.79728000", "longitude": "-78.82337000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.06343000", "longitude": "-73.95764000"}, {"name": "Bloomfield", "countryCode": "US", "stateCode": "NY", "latitude": "40.61260000", "longitude": "-74.17820000"}, {"name": "Blue Point", "countryCode": "US", "stateCode": "NY", "latitude": "40.74399000", "longitude": "-73.03455000"}, {"name": "Bohemia", "countryCode": "US", "stateCode": "NY", "latitude": "40.76926000", "longitude": "-73.11511000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.06673000", "longitude": "-78.16779000"}, {"name": "Boonville", "countryCode": "US", "stateCode": "NY", "latitude": "43.48368000", "longitude": "-75.33656000"}, {"name": "Borough Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.63399000", "longitude": "-73.99681000"}, {"name": "Boston", "countryCode": "US", "stateCode": "NY", "latitude": "42.62895000", "longitude": "-78.73753000"}, {"name": "Brentwood", "countryCode": "US", "stateCode": "NY", "latitude": "40.78121000", "longitude": "-73.24623000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.23812000", "longitude": "-76.14076000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.39732000", "longitude": "-73.61707000"}, {"name": "<PERSON> Hill", "countryCode": "US", "stateCode": "NY", "latitude": "41.42398000", "longitude": "-73.60429000"}, {"name": "Briarcliff Manor", "countryCode": "US", "stateCode": "NY", "latitude": "41.14565000", "longitude": "-73.82375000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.70935000", "longitude": "-73.81529000"}, {"name": "Bridgehampton", "countryCode": "US", "stateCode": "NY", "latitude": "40.93788000", "longitude": "-72.30092000"}, {"name": "Bridgeport", "countryCode": "US", "stateCode": "NY", "latitude": "43.15535000", "longitude": "-75.96936000"}, {"name": "Brighton", "countryCode": "US", "stateCode": "NY", "latitude": "43.14756000", "longitude": "-77.55055000"}, {"name": "Brighton Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.57788000", "longitude": "-73.95958000"}, {"name": "Brightwaters", "countryCode": "US", "stateCode": "NY", "latitude": "40.72093000", "longitude": "-73.26734000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.54398000", "longitude": "-73.86819000"}, {"name": "Broad Channel", "countryCode": "US", "stateCode": "NY", "latitude": "40.60316000", "longitude": "-73.82041000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.05868000", "longitude": "-74.19652000"}, {"name": "Brockport", "countryCode": "US", "stateCode": "NY", "latitude": "43.21367000", "longitude": "-77.93918000"}, {"name": "Brocton", "countryCode": "US", "stateCode": "NY", "latitude": "42.38867000", "longitude": "-79.44116000"}, {"name": "Bronx", "countryCode": "US", "stateCode": "NY", "latitude": "40.82732000", "longitude": "-73.92357000"}, {"name": "Bronxville", "countryCode": "US", "stateCode": "NY", "latitude": "40.93815000", "longitude": "-73.83208000"}, {"name": "Brookhaven", "countryCode": "US", "stateCode": "NY", "latitude": "40.77927000", "longitude": "-72.91538000"}, {"name": "Brooklyn", "countryCode": "US", "stateCode": "NY", "latitude": "40.65010000", "longitude": "-73.94958000"}, {"name": "Brooklyn Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.69538000", "longitude": "-73.99375000"}, {"name": "Brookville", "countryCode": "US", "stateCode": "NY", "latitude": "40.81316000", "longitude": "-73.56735000"}, {"name": "Broome County", "countryCode": "US", "stateCode": "NY", "latitude": "42.16022000", "longitude": "-75.81962000"}, {"name": "Brownsville", "countryCode": "US", "stateCode": "NY", "latitude": "40.66094000", "longitude": "-73.92014000"}, {"name": "Brownville", "countryCode": "US", "stateCode": "NY", "latitude": "44.00700000", "longitude": "-75.98409000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.26204000", "longitude": "-73.93819000"}, {"name": "Buffalo", "countryCode": "US", "stateCode": "NY", "latitude": "42.88645000", "longitude": "-78.87837000"}, {"name": "Bushwick", "countryCode": "US", "stateCode": "NY", "latitude": "40.69427000", "longitude": "-73.91875000"}, {"name": "Cairo", "countryCode": "US", "stateCode": "NY", "latitude": "42.29897000", "longitude": "-73.99847000"}, {"name": "Calcium", "countryCode": "US", "stateCode": "NY", "latitude": "44.02173000", "longitude": "-75.84604000"}, {"name": "Caledonia", "countryCode": "US", "stateCode": "NY", "latitude": "42.97312000", "longitude": "-77.85278000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.90649000", "longitude": "-72.74343000"}, {"name": "Cambria Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.69455000", "longitude": "-73.73847000"}, {"name": "Cambridge", "countryCode": "US", "stateCode": "NY", "latitude": "43.02813000", "longitude": "-73.38122000"}, {"name": "Camden", "countryCode": "US", "stateCode": "NY", "latitude": "43.33451000", "longitude": "-75.74796000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.03923000", "longitude": "-76.30410000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.90563000", "longitude": "-74.57181000"}, {"name": "Canandaigua", "countryCode": "US", "stateCode": "NY", "latitude": "42.87423000", "longitude": "-77.28804000"}, {"name": "Canarsie", "countryCode": "US", "stateCode": "NY", "latitude": "40.64372000", "longitude": "-73.90069000"}, {"name": "Canastota", "countryCode": "US", "stateCode": "NY", "latitude": "43.07951000", "longitude": "-75.75074000"}, {"name": "Canisteo", "countryCode": "US", "stateCode": "NY", "latitude": "42.27035000", "longitude": "-77.60582000"}, {"name": "Canton", "countryCode": "US", "stateCode": "NY", "latitude": "44.59562000", "longitude": "-75.16909000"}, {"name": "<PERSON>e <PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.75260000", "longitude": "-73.61041000"}, {"name": "Carmel", "countryCode": "US", "stateCode": "NY", "latitude": "41.43009000", "longitude": "-73.68013000"}, {"name": "Carmel Hamlet", "countryCode": "US", "stateCode": "NY", "latitude": "41.41485000", "longitude": "-73.68524000"}, {"name": "Carthage", "countryCode": "US", "stateCode": "NY", "latitude": "43.97812000", "longitude": "-75.60936000"}, {"name": "Castleton-on-Hudson", "countryCode": "US", "stateCode": "NY", "latitude": "42.51841000", "longitude": "-73.75123000"}, {"name": "Catskill", "countryCode": "US", "stateCode": "NY", "latitude": "42.21731000", "longitude": "-73.86457000"}, {"name": "Cattaraugus County", "countryCode": "US", "stateCode": "NY", "latitude": "42.24863000", "longitude": "-78.67885000"}, {"name": "Cayuga County", "countryCode": "US", "stateCode": "NY", "latitude": "43.01033000", "longitude": "-76.57436000"}, {"name": "Cayuga Heights", "countryCode": "US", "stateCode": "NY", "latitude": "42.46010000", "longitude": "-76.48776000"}, {"name": "Cazenovia", "countryCode": "US", "stateCode": "NY", "latitude": "42.93007000", "longitude": "-75.85269000"}, {"name": "Cedarhurst", "countryCode": "US", "stateCode": "NY", "latitude": "40.62288000", "longitude": "-73.72430000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.10950000", "longitude": "-79.28310000"}, {"name": "Center Moriches", "countryCode": "US", "stateCode": "NY", "latitude": "40.80038000", "longitude": "-72.78982000"}, {"name": "Centereach", "countryCode": "US", "stateCode": "NY", "latitude": "40.85843000", "longitude": "-73.09955000"}, {"name": "Centerport", "countryCode": "US", "stateCode": "NY", "latitude": "40.88538000", "longitude": "-73.37623000"}, {"name": "Central Islip", "countryCode": "US", "stateCode": "NY", "latitude": "40.79065000", "longitude": "-73.20178000"}, {"name": "Central Square", "countryCode": "US", "stateCode": "NY", "latitude": "43.28674000", "longitude": "-76.14604000"}, {"name": "Central Valley", "countryCode": "US", "stateCode": "NY", "latitude": "41.33176000", "longitude": "-74.12098000"}, {"name": "Chadwicks", "countryCode": "US", "stateCode": "NY", "latitude": "43.02785000", "longitude": "-75.27155000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "44.98643000", "longitude": "-73.44653000"}, {"name": "Chappaqua", "countryCode": "US", "stateCode": "NY", "latitude": "41.15954000", "longitude": "-73.76485000"}, {"name": "Charleston", "countryCode": "US", "stateCode": "NY", "latitude": "40.53677000", "longitude": "-74.23737000"}, {"name": "Chatham", "countryCode": "US", "stateCode": "NY", "latitude": "42.36425000", "longitude": "-73.59484000"}, {"name": "Chautauqua County", "countryCode": "US", "stateCode": "NY", "latitude": "42.30294000", "longitude": "-79.40576000"}, {"name": "Cheektowaga", "countryCode": "US", "stateCode": "NY", "latitude": "42.90339000", "longitude": "-78.75475000"}, {"name": "Chelsea", "countryCode": "US", "stateCode": "NY", "latitude": "40.60066000", "longitude": "-74.19487000"}, {"name": "Chemung County", "countryCode": "US", "stateCode": "NY", "latitude": "42.14125000", "longitude": "-76.76003000"}, {"name": "Chenango Bridge", "countryCode": "US", "stateCode": "NY", "latitude": "42.16674000", "longitude": "-75.86242000"}, {"name": "Chenango County", "countryCode": "US", "stateCode": "NY", "latitude": "42.49351000", "longitude": "-75.61158000"}, {"name": "Chester", "countryCode": "US", "stateCode": "NY", "latitude": "41.36259000", "longitude": "-74.27126000"}, {"name": "Chestnut Ridge", "countryCode": "US", "stateCode": "NY", "latitude": "41.08426000", "longitude": "-74.05570000"}, {"name": "Chittenango", "countryCode": "US", "stateCode": "NY", "latitude": "43.04507000", "longitude": "-75.86658000"}, {"name": "Churchville", "countryCode": "US", "stateCode": "NY", "latitude": "43.10423000", "longitude": "-77.88445000"}, {"name": "City Island", "countryCode": "US", "stateCode": "NY", "latitude": "40.84732000", "longitude": "-73.78652000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.97673000", "longitude": "-78.59197000"}, {"name": "Clarence Center", "countryCode": "US", "stateCode": "NY", "latitude": "43.01061000", "longitude": "-78.63753000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.09229000", "longitude": "-75.37962000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.23312000", "longitude": "-77.92751000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "44.23949000", "longitude": "-76.08578000"}, {"name": "Clifton", "countryCode": "US", "stateCode": "NY", "latitude": "40.62010000", "longitude": "-74.07709000"}, {"name": "Clifton Springs", "countryCode": "US", "stateCode": "NY", "latitude": "42.96173000", "longitude": "-77.13998000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.04840000", "longitude": "-75.37850000"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "NY", "latitude": "44.74623000", "longitude": "-73.67817000"}, {"name": "Clintondale", "countryCode": "US", "stateCode": "NY", "latitude": "41.69482000", "longitude": "-74.05125000"}, {"name": "Clyde", "countryCode": "US", "stateCode": "NY", "latitude": "43.08423000", "longitude": "-76.86940000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.02089000", "longitude": "-79.63005000"}, {"name": "Cobleskill", "countryCode": "US", "stateCode": "NY", "latitude": "42.67785000", "longitude": "-74.48542000"}, {"name": "Cohoes", "countryCode": "US", "stateCode": "NY", "latitude": "42.77424000", "longitude": "-73.70012000"}, {"name": "Cold Spring", "countryCode": "US", "stateCode": "NY", "latitude": "41.42009000", "longitude": "-73.95458000"}, {"name": "Cold Spring Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "40.87149000", "longitude": "-73.45679000"}, {"name": "College Point", "countryCode": "US", "stateCode": "NY", "latitude": "40.78760000", "longitude": "-73.84597000"}, {"name": "Colonie", "countryCode": "US", "stateCode": "NY", "latitude": "42.71786000", "longitude": "-73.83346000"}, {"name": "Columbia County", "countryCode": "US", "stateCode": "NY", "latitude": "42.25008000", "longitude": "-73.63185000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.84288000", "longitude": "-73.29289000"}, {"name": "Concord", "countryCode": "US", "stateCode": "NY", "latitude": "40.60816000", "longitude": "-74.08431000"}, {"name": "Coney Island", "countryCode": "US", "stateCode": "NY", "latitude": "40.57788000", "longitude": "-73.99403000"}, {"name": "Congers", "countryCode": "US", "stateCode": "NY", "latitude": "41.15065000", "longitude": "-73.94542000"}, {"name": "Constantia", "countryCode": "US", "stateCode": "NY", "latitude": "43.24785000", "longitude": "-76.00020000"}, {"name": "Cooperstown", "countryCode": "US", "stateCode": "NY", "latitude": "42.70048000", "longitude": "-74.92426000"}, {"name": "Copiague", "countryCode": "US", "stateCode": "NY", "latitude": "40.68149000", "longitude": "-73.39984000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.86871000", "longitude": "-73.00149000"}, {"name": "Corinth", "countryCode": "US", "stateCode": "NY", "latitude": "43.24452000", "longitude": "-73.83234000"}, {"name": "Corning", "countryCode": "US", "stateCode": "NY", "latitude": "42.14285000", "longitude": "-77.05469000"}, {"name": "Cornwall", "countryCode": "US", "stateCode": "NY", "latitude": "41.44482000", "longitude": "-74.01570000"}, {"name": "Corona", "countryCode": "US", "stateCode": "NY", "latitude": "40.74705000", "longitude": "-73.86014000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.60118000", "longitude": "-76.18048000"}, {"name": "Cortland County", "countryCode": "US", "stateCode": "NY", "latitude": "42.59501000", "longitude": "-76.07027000"}, {"name": "Cortland West", "countryCode": "US", "stateCode": "NY", "latitude": "42.59431000", "longitude": "-76.22587000"}, {"name": "Country Knolls", "countryCode": "US", "stateCode": "NY", "latitude": "42.91508000", "longitude": "-73.80512000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.35092000", "longitude": "-73.80290000"}, {"name": "Crompond", "countryCode": "US", "stateCode": "NY", "latitude": "41.29509000", "longitude": "-73.86541000"}, {"name": "Croton-on-Hudson", "countryCode": "US", "stateCode": "NY", "latitude": "41.20843000", "longitude": "-73.89125000"}, {"name": "Crown Heights", "countryCode": "US", "stateCode": "NY", "latitude": "41.63732000", "longitude": "-73.93792000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.23343000", "longitude": "-73.92264000"}, {"name": "Cuba", "countryCode": "US", "stateCode": "NY", "latitude": "42.21757000", "longitude": "-78.27529000"}, {"name": "Cumberland Head", "countryCode": "US", "stateCode": "NY", "latitude": "44.71643000", "longitude": "-73.40263000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.01066000", "longitude": "-72.48509000"}, {"name": "Cypress Hills", "countryCode": "US", "stateCode": "NY", "latitude": "40.67705000", "longitude": "-73.89125000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "44.72143000", "longitude": "-73.72375000"}, {"name": "Dansville", "countryCode": "US", "stateCode": "NY", "latitude": "42.56090000", "longitude": "-77.69611000"}, {"name": "Deer Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.76177000", "longitude": "-73.32929000"}, {"name": "Delaware County", "countryCode": "US", "stateCode": "NY", "latitude": "42.19809000", "longitude": "-74.96647000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.48923000", "longitude": "-78.48085000"}, {"name": "Delhi", "countryCode": "US", "stateCode": "NY", "latitude": "42.27814000", "longitude": "-74.91599000"}, {"name": "Delmar", "countryCode": "US", "stateCode": "NY", "latitude": "42.62202000", "longitude": "-73.83262000"}, {"name": "De<PERSON>w", "countryCode": "US", "stateCode": "NY", "latitude": "42.90395000", "longitude": "-78.69225000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.06008000", "longitude": "-75.42768000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "44.00784000", "longitude": "-76.04437000"}, {"name": "Dix <PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.80482000", "longitude": "-73.33623000"}, {"name": "Dobbs Ferry", "countryCode": "US", "stateCode": "NY", "latitude": "41.01454000", "longitude": "-73.87264000"}, {"name": "Dolgeville", "countryCode": "US", "stateCode": "NY", "latitude": "43.10090000", "longitude": "-74.77293000"}, {"name": "Dongan Hills", "countryCode": "US", "stateCode": "NY", "latitude": "40.58844000", "longitude": "-74.09625000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.76871000", "longitude": "-73.74708000"}, {"name": "Dover Plains", "countryCode": "US", "stateCode": "NY", "latitude": "41.74121000", "longitude": "-73.57652000"}, {"name": "Dryden", "countryCode": "US", "stateCode": "NY", "latitude": "42.49091000", "longitude": "-76.29716000"}, {"name": "Dundee", "countryCode": "US", "stateCode": "NY", "latitude": "42.52340000", "longitude": "-76.97663000"}, {"name": "Dunkirk", "countryCode": "US", "stateCode": "NY", "latitude": "42.47950000", "longitude": "-79.33393000"}, {"name": "Durham", "countryCode": "US", "stateCode": "NY", "latitude": "42.39953000", "longitude": "-74.17236000"}, {"name": "Dutchess County", "countryCode": "US", "stateCode": "NY", "latitude": "41.76515000", "longitude": "-73.74286000"}, {"name": "Dyker Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.62149000", "longitude": "-74.00958000"}, {"name": "East Atlantic Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.78982000", "longitude": "-73.74708000"}, {"name": "East Aurora", "countryCode": "US", "stateCode": "NY", "latitude": "42.76784000", "longitude": "-78.61336000"}, {"name": "East Elmhurst", "countryCode": "US", "stateCode": "NY", "latitude": "40.76121000", "longitude": "-73.86514000"}, {"name": "East Farmingdale", "countryCode": "US", "stateCode": "NY", "latitude": "40.72927000", "longitude": "-73.41679000"}, {"name": "East Flatbush", "countryCode": "US", "stateCode": "NY", "latitude": "40.65371000", "longitude": "-73.93042000"}, {"name": "East Garden City", "countryCode": "US", "stateCode": "NY", "latitude": "40.73083000", "longitude": "-73.59806000"}, {"name": "East Glenville", "countryCode": "US", "stateCode": "NY", "latitude": "42.89452000", "longitude": "-73.92790000"}, {"name": "East Greenbush", "countryCode": "US", "stateCode": "NY", "latitude": "42.59091000", "longitude": "-73.70179000"}, {"name": "East Hampton", "countryCode": "US", "stateCode": "NY", "latitude": "40.96343000", "longitude": "-72.18480000"}, {"name": "East Hampton North", "countryCode": "US", "stateCode": "NY", "latitude": "40.97276000", "longitude": "-72.18911000"}, {"name": "East Harlem", "countryCode": "US", "stateCode": "NY", "latitude": "40.79472000", "longitude": "-73.94250000"}, {"name": "East Hills", "countryCode": "US", "stateCode": "NY", "latitude": "40.79371000", "longitude": "-73.62707000"}, {"name": "East Islip", "countryCode": "US", "stateCode": "NY", "latitude": "40.73204000", "longitude": "-73.18567000"}, {"name": "East Ithaca", "countryCode": "US", "stateCode": "NY", "latitude": "42.43952000", "longitude": "-76.47855000"}, {"name": "East Massapequa", "countryCode": "US", "stateCode": "NY", "latitude": "40.67343000", "longitude": "-73.43651000"}, {"name": "East Meadow", "countryCode": "US", "stateCode": "NY", "latitude": "40.71399000", "longitude": "-73.55902000"}, {"name": "East Moriches", "countryCode": "US", "stateCode": "NY", "latitude": "40.80510000", "longitude": "-72.76093000"}, {"name": "East New York", "countryCode": "US", "stateCode": "NY", "latitude": "40.66677000", "longitude": "-73.88236000"}, {"name": "East Northport", "countryCode": "US", "stateCode": "NY", "latitude": "40.87676000", "longitude": "-73.32456000"}, {"name": "East Norwich", "countryCode": "US", "stateCode": "NY", "latitude": "40.84677000", "longitude": "-73.53512000"}, {"name": "East Patchogue", "countryCode": "US", "stateCode": "NY", "latitude": "40.76704000", "longitude": "-72.99622000"}, {"name": "East Quogue", "countryCode": "US", "stateCode": "NY", "latitude": "40.84066000", "longitude": "-72.58148000"}, {"name": "East Rochester", "countryCode": "US", "stateCode": "NY", "latitude": "43.10867000", "longitude": "-77.48750000"}, {"name": "East Rockaway", "countryCode": "US", "stateCode": "NY", "latitude": "40.64205000", "longitude": "-73.66957000"}, {"name": "East Setauket", "countryCode": "US", "stateCode": "NY", "latitude": "40.94149000", "longitude": "-73.10594000"}, {"name": "East Shoreham", "countryCode": "US", "stateCode": "NY", "latitude": "40.94482000", "longitude": "-72.87955000"}, {"name": "East Syracuse", "countryCode": "US", "stateCode": "NY", "latitude": "43.06534000", "longitude": "-76.07853000"}, {"name": "East Tremont", "countryCode": "US", "stateCode": "NY", "latitude": "40.84538000", "longitude": "-73.89097000"}, {"name": "East Village", "countryCode": "US", "stateCode": "NY", "latitude": "40.72927000", "longitude": "-73.98736000"}, {"name": "East Williston", "countryCode": "US", "stateCode": "NY", "latitude": "40.75843000", "longitude": "-73.63485000"}, {"name": "Eastchester", "countryCode": "US", "stateCode": "NY", "latitude": "40.95833000", "longitude": "-73.80861000"}, {"name": "Eastport", "countryCode": "US", "stateCode": "NY", "latitude": "40.82593000", "longitude": "-72.73177000"}, {"name": "Eatons Neck", "countryCode": "US", "stateCode": "NY", "latitude": "40.93065000", "longitude": "-73.40151000"}, {"name": "Eden", "countryCode": "US", "stateCode": "NY", "latitude": "42.65228000", "longitude": "-78.89698000"}, {"name": "Edgemere", "countryCode": "US", "stateCode": "NY", "latitude": "40.59622000", "longitude": "-73.76763000"}, {"name": "Edinburg", "countryCode": "US", "stateCode": "NY", "latitude": "43.22174000", "longitude": "-74.10402000"}, {"name": "Eggertsville", "countryCode": "US", "stateCode": "NY", "latitude": "42.96339000", "longitude": "-78.80392000"}, {"name": "Elbridge", "countryCode": "US", "stateCode": "NY", "latitude": "43.03451000", "longitude": "-76.44799000"}, {"name": "Elizabethtown", "countryCode": "US", "stateCode": "NY", "latitude": "44.21616000", "longitude": "-73.59097000"}, {"name": "Ellenville", "countryCode": "US", "stateCode": "NY", "latitude": "41.71704000", "longitude": "-74.39571000"}, {"name": "Elma Center", "countryCode": "US", "stateCode": "NY", "latitude": "42.82978000", "longitude": "-78.63614000"}, {"name": "Elmhurst", "countryCode": "US", "stateCode": "NY", "latitude": "40.73649000", "longitude": "-73.87791000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.08980000", "longitude": "-76.80773000"}, {"name": "Elmira Heights", "countryCode": "US", "stateCode": "NY", "latitude": "42.12980000", "longitude": "-76.82079000"}, {"name": "Elmont", "countryCode": "US", "stateCode": "NY", "latitude": "40.70094000", "longitude": "-73.71291000"}, {"name": "Elmsford", "countryCode": "US", "stateCode": "NY", "latitude": "41.05510000", "longitude": "-73.82013000"}, {"name": "Eltingville", "countryCode": "US", "stateCode": "NY", "latitude": "40.54538000", "longitude": "-74.16570000"}, {"name": "Elwood", "countryCode": "US", "stateCode": "NY", "latitude": "40.84538000", "longitude": "-73.33512000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.60872000", "longitude": "-74.09598000"}, {"name": "End<PERSON>tt", "countryCode": "US", "stateCode": "NY", "latitude": "42.09841000", "longitude": "-76.04937000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.11285000", "longitude": "-76.02103000"}, {"name": "Erie County", "countryCode": "US", "stateCode": "NY", "latitude": "42.75824000", "longitude": "-78.77966000"}, {"name": "Essex County", "countryCode": "US", "stateCode": "NY", "latitude": "44.11722000", "longitude": "-73.77271000"}, {"name": "Fairmount", "countryCode": "US", "stateCode": "NY", "latitude": "43.04729000", "longitude": "-76.23854000"}, {"name": "Fairport", "countryCode": "US", "stateCode": "NY", "latitude": "43.09867000", "longitude": "-77.44194000"}, {"name": "Fairview", "countryCode": "US", "stateCode": "NY", "latitude": "41.72370000", "longitude": "-73.91986000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.11867000", "longitude": "-79.19838000"}, {"name": "Fallsburg", "countryCode": "US", "stateCode": "NY", "latitude": "41.73204000", "longitude": "-74.60127000"}, {"name": "Far Rockaway", "countryCode": "US", "stateCode": "NY", "latitude": "40.60538000", "longitude": "-73.75513000"}, {"name": "Farmingdale", "countryCode": "US", "stateCode": "NY", "latitude": "40.73260000", "longitude": "-73.44540000"}, {"name": "Farmingville", "countryCode": "US", "stateCode": "NY", "latitude": "40.83121000", "longitude": "-73.02955000"}, {"name": "Fayetteville", "countryCode": "US", "stateCode": "NY", "latitude": "43.02979000", "longitude": "-76.00436000"}, {"name": "Financial District", "countryCode": "US", "stateCode": "NY", "latitude": "40.70789000", "longitude": "-74.00857000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.43926000", "longitude": "-74.04514000"}, {"name": "Fishkill", "countryCode": "US", "stateCode": "NY", "latitude": "41.53565000", "longitude": "-73.89903000"}, {"name": "Flanders", "countryCode": "US", "stateCode": "NY", "latitude": "40.90343000", "longitude": "-72.61759000"}, {"name": "Flatbush", "countryCode": "US", "stateCode": "NY", "latitude": "40.65205000", "longitude": "-73.95903000"}, {"name": "Flatlands", "countryCode": "US", "stateCode": "NY", "latitude": "40.62122000", "longitude": "-73.93486000"}, {"name": "Floral Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.72371000", "longitude": "-73.70485000"}, {"name": "Florida", "countryCode": "US", "stateCode": "NY", "latitude": "41.33176000", "longitude": "-74.35682000"}, {"name": "Flower Hill", "countryCode": "US", "stateCode": "NY", "latitude": "40.80732000", "longitude": "-73.68124000"}, {"name": "Fonda", "countryCode": "US", "stateCode": "NY", "latitude": "42.95452000", "longitude": "-74.37652000"}, {"name": "Fordham", "countryCode": "US", "stateCode": "NY", "latitude": "40.85927000", "longitude": "-73.89847000"}, {"name": "Forest Hills", "countryCode": "US", "stateCode": "NY", "latitude": "40.71621000", "longitude": "-73.85014000"}, {"name": "Fort Covington Hamlet", "countryCode": "US", "stateCode": "NY", "latitude": "44.97178000", "longitude": "-74.50757000"}, {"name": "Fort Drum", "countryCode": "US", "stateCode": "NY", "latitude": "44.05843000", "longitude": "-75.76189000"}, {"name": "Fort Edward", "countryCode": "US", "stateCode": "NY", "latitude": "43.26702000", "longitude": "-73.58456000"}, {"name": "Fort Hamilton", "countryCode": "US", "stateCode": "NY", "latitude": "40.61872000", "longitude": "-74.03320000"}, {"name": "Fort Montgomery", "countryCode": "US", "stateCode": "NY", "latitude": "41.33148000", "longitude": "-73.98681000"}, {"name": "Fort Plain", "countryCode": "US", "stateCode": "NY", "latitude": "42.93146000", "longitude": "-74.62264000"}, {"name": "Fort Salonga", "countryCode": "US", "stateCode": "NY", "latitude": "40.91260000", "longitude": "-73.30095000"}, {"name": "Fort Wadsworth", "countryCode": "US", "stateCode": "NY", "latitude": "40.60113000", "longitude": "-74.05738000"}, {"name": "Frankfort", "countryCode": "US", "stateCode": "NY", "latitude": "43.03896000", "longitude": "-75.07044000"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "NY", "latitude": "44.59293000", "longitude": "-74.30376000"}, {"name": "Franklin Square", "countryCode": "US", "stateCode": "NY", "latitude": "40.70732000", "longitude": "-73.67596000"}, {"name": "Franklinville", "countryCode": "US", "stateCode": "NY", "latitude": "42.33701000", "longitude": "-78.45808000"}, {"name": "Fredonia", "countryCode": "US", "stateCode": "NY", "latitude": "42.44006000", "longitude": "-79.33171000"}, {"name": "Freeport", "countryCode": "US", "stateCode": "NY", "latitude": "40.65760000", "longitude": "-73.58318000"}, {"name": "Fresh Meadows", "countryCode": "US", "stateCode": "NY", "latitude": "40.73482000", "longitude": "-73.79347000"}, {"name": "Frewsburg", "countryCode": "US", "stateCode": "NY", "latitude": "42.05450000", "longitude": "-79.15810000"}, {"name": "Friendship", "countryCode": "US", "stateCode": "NY", "latitude": "42.20646000", "longitude": "-78.13751000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.32285000", "longitude": "-76.41716000"}, {"name": "Fulton County", "countryCode": "US", "stateCode": "NY", "latitude": "43.11385000", "longitude": "-74.42217000"}, {"name": "Galeville", "countryCode": "US", "stateCode": "NY", "latitude": "43.09007000", "longitude": "-76.17298000"}, {"name": "Gang Mills", "countryCode": "US", "stateCode": "NY", "latitude": "42.14619000", "longitude": "-77.11164000"}, {"name": "Garden City", "countryCode": "US", "stateCode": "NY", "latitude": "40.72677000", "longitude": "-73.63430000"}, {"name": "Garden City Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.74093000", "longitude": "-73.66263000"}, {"name": "Garden City South", "countryCode": "US", "stateCode": "NY", "latitude": "40.71232000", "longitude": "-73.66096000"}, {"name": "Gardnertown", "countryCode": "US", "stateCode": "NY", "latitude": "41.53509000", "longitude": "-74.07014000"}, {"name": "Gasport", "countryCode": "US", "stateCode": "NY", "latitude": "43.19922000", "longitude": "-78.57614000"}, {"name": "Gates-North Gates", "countryCode": "US", "stateCode": "NY", "latitude": "43.16547000", "longitude": "-77.70066000"}, {"name": "Genesee County", "countryCode": "US", "stateCode": "NY", "latitude": "43.00093000", "longitude": "-78.19371000"}, {"name": "Geneseo", "countryCode": "US", "stateCode": "NY", "latitude": "42.79590000", "longitude": "-77.81695000"}, {"name": "Geneva", "countryCode": "US", "stateCode": "NY", "latitude": "42.86896000", "longitude": "-76.97774000"}, {"name": "Glasco", "countryCode": "US", "stateCode": "NY", "latitude": "42.04370000", "longitude": "-73.94736000"}, {"name": "Glen Cove", "countryCode": "US", "stateCode": "NY", "latitude": "40.86232000", "longitude": "-73.63374000"}, {"name": "Glen Head", "countryCode": "US", "stateCode": "NY", "latitude": "40.83538000", "longitude": "-73.62374000"}, {"name": "Glen Oaks", "countryCode": "US", "stateCode": "NY", "latitude": "40.74705000", "longitude": "-73.71152000"}, {"name": "Glendale", "countryCode": "US", "stateCode": "NY", "latitude": "40.70149000", "longitude": "-73.88680000"}, {"name": "Glens Falls", "countryCode": "US", "stateCode": "NY", "latitude": "43.30952000", "longitude": "-73.64401000"}, {"name": "Glens Falls North", "countryCode": "US", "stateCode": "NY", "latitude": "43.33506000", "longitude": "-73.68251000"}, {"name": "Glenwood Landing", "countryCode": "US", "stateCode": "NY", "latitude": "40.83066000", "longitude": "-73.63874000"}, {"name": "Gloversville", "countryCode": "US", "stateCode": "NY", "latitude": "43.05285000", "longitude": "-74.34375000"}, {"name": "Goldens Bridge", "countryCode": "US", "stateCode": "NY", "latitude": "41.29343000", "longitude": "-73.67680000"}, {"name": "Gordon Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.85871000", "longitude": "-72.97066000"}, {"name": "Goshen", "countryCode": "US", "stateCode": "NY", "latitude": "41.40204000", "longitude": "-74.32432000"}, {"name": "Gouverneur", "countryCode": "US", "stateCode": "NY", "latitude": "44.33673000", "longitude": "-75.46299000"}, {"name": "Gowanda", "countryCode": "US", "stateCode": "NY", "latitude": "42.46312000", "longitude": "-78.93587000"}, {"name": "Gramercy Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.73750000", "longitude": "-73.98611000"}, {"name": "Grand Island", "countryCode": "US", "stateCode": "NY", "latitude": "43.03311000", "longitude": "-78.96254000"}, {"name": "Grandyle Village", "countryCode": "US", "stateCode": "NY", "latitude": "42.99645000", "longitude": "-78.95504000"}, {"name": "Graniteville", "countryCode": "US", "stateCode": "NY", "latitude": "40.62483000", "longitude": "-74.14848000"}, {"name": "Grant City", "countryCode": "US", "stateCode": "NY", "latitude": "40.58205000", "longitude": "-74.10486000"}, {"name": "Granville", "countryCode": "US", "stateCode": "NY", "latitude": "43.40785000", "longitude": "-73.25955000"}, {"name": "Gravesend", "countryCode": "US", "stateCode": "NY", "latitude": "40.59760000", "longitude": "-73.96514000"}, {"name": "Great Kills", "countryCode": "US", "stateCode": "NY", "latitude": "40.55427000", "longitude": "-74.15153000"}, {"name": "Great Neck", "countryCode": "US", "stateCode": "NY", "latitude": "40.80066000", "longitude": "-73.72846000"}, {"name": "Great Neck Estates", "countryCode": "US", "stateCode": "NY", "latitude": "40.78705000", "longitude": "-73.73680000"}, {"name": "Great Neck Gardens", "countryCode": "US", "stateCode": "NY", "latitude": "40.79722000", "longitude": "-73.72389000"}, {"name": "Great Neck Plaza", "countryCode": "US", "stateCode": "NY", "latitude": "40.78677000", "longitude": "-73.72652000"}, {"name": "Great River", "countryCode": "US", "stateCode": "NY", "latitude": "40.72121000", "longitude": "-73.15761000"}, {"name": "Greece", "countryCode": "US", "stateCode": "NY", "latitude": "43.20978000", "longitude": "-77.69306000"}, {"name": "Green Island", "countryCode": "US", "stateCode": "NY", "latitude": "42.74424000", "longitude": "-73.69151000"}, {"name": "Greenburgh", "countryCode": "US", "stateCode": "NY", "latitude": "41.03287000", "longitude": "-73.84291000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.32924000", "longitude": "-75.76991000"}, {"name": "Greene County", "countryCode": "US", "stateCode": "NY", "latitude": "42.27652000", "longitude": "-74.12271000"}, {"name": "Greenlawn", "countryCode": "US", "stateCode": "NY", "latitude": "40.86899000", "longitude": "-73.36512000"}, {"name": "Greenpoint", "countryCode": "US", "stateCode": "NY", "latitude": "40.72371000", "longitude": "-73.95097000"}, {"name": "Greenport", "countryCode": "US", "stateCode": "NY", "latitude": "41.10343000", "longitude": "-72.35925000"}, {"name": "Greenport West", "countryCode": "US", "stateCode": "NY", "latitude": "41.10178000", "longitude": "-72.37195000"}, {"name": "Greenvale", "countryCode": "US", "stateCode": "NY", "latitude": "40.81066000", "longitude": "-73.62846000"}, {"name": "Greenville", "countryCode": "US", "stateCode": "NY", "latitude": "40.99315000", "longitude": "-73.81986000"}, {"name": "Greenwich", "countryCode": "US", "stateCode": "NY", "latitude": "43.09063000", "longitude": "-73.49873000"}, {"name": "Greenwood Lake", "countryCode": "US", "stateCode": "NY", "latitude": "41.22259000", "longitude": "-74.29432000"}, {"name": "Groton", "countryCode": "US", "stateCode": "NY", "latitude": "42.58785000", "longitude": "-76.36688000"}, {"name": "<PERSON><PERSON><PERSON> Hill", "countryCode": "US", "stateCode": "NY", "latitude": "40.61872000", "longitude": "-74.09348000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.31729000", "longitude": "-73.84818000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.97452000", "longitude": "-74.15096000"}, {"name": "Halesite", "countryCode": "US", "stateCode": "NY", "latitude": "40.88843000", "longitude": "-73.41540000"}, {"name": "Hamburg", "countryCode": "US", "stateCode": "NY", "latitude": "42.71589000", "longitude": "-78.82948000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.82701000", "longitude": "-75.54462000"}, {"name": "Hamilton County", "countryCode": "US", "stateCode": "NY", "latitude": "43.66112000", "longitude": "-74.49736000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.30312000", "longitude": "-77.92112000"}, {"name": "Hampton Bays", "countryCode": "US", "stateCode": "NY", "latitude": "40.86899000", "longitude": "-72.51759000"}, {"name": "Hampton Manor", "countryCode": "US", "stateCode": "NY", "latitude": "42.62091000", "longitude": "-73.72845000"}, {"name": "Hannawa Falls", "countryCode": "US", "stateCode": "NY", "latitude": "44.61228000", "longitude": "-74.97103000"}, {"name": "Harbor Isle", "countryCode": "US", "stateCode": "NY", "latitude": "40.60344000", "longitude": "-73.66457000"}, {"name": "Harlem", "countryCode": "US", "stateCode": "NY", "latitude": "40.80788000", "longitude": "-73.94542000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.30843000", "longitude": "-74.14459000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.96478000", "longitude": "-78.67753000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.96899000", "longitude": "-73.71263000"}, {"name": "Hartford", "countryCode": "US", "stateCode": "NY", "latitude": "43.36368000", "longitude": "-73.39372000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.01899000", "longitude": "-73.79819000"}, {"name": "Hastings-on-Hudson", "countryCode": "US", "stateCode": "NY", "latitude": "40.99454000", "longitude": "-73.87875000"}, {"name": "Ha<PERSON>pa<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.82565000", "longitude": "-73.20261000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.19759000", "longitude": "-73.96458000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.76676000", "longitude": "-73.90152000"}, {"name": "Hawthorne", "countryCode": "US", "stateCode": "NY", "latitude": "41.10732000", "longitude": "-73.79597000"}, {"name": "Head of the Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "40.90343000", "longitude": "-73.15789000"}, {"name": "Hell's Kitchen", "countryCode": "US", "stateCode": "NY", "latitude": "40.76496000", "longitude": "-73.99090000"}, {"name": "Hempstead", "countryCode": "US", "stateCode": "NY", "latitude": "40.70621000", "longitude": "-73.61874000"}, {"name": "Heritage Hills", "countryCode": "US", "stateCode": "NY", "latitude": "41.33954000", "longitude": "-73.69735000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.02563000", "longitude": "-74.98599000"}, {"name": "Herkimer County", "countryCode": "US", "stateCode": "NY", "latitude": "43.41970000", "longitude": "-74.96250000"}, {"name": "Herricks", "countryCode": "US", "stateCode": "NY", "latitude": "40.75538000", "longitude": "-73.66680000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.64316000", "longitude": "-73.69569000"}, {"name": "Hewlett Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "40.63621000", "longitude": "-73.68152000"}, {"name": "Hicksville", "countryCode": "US", "stateCode": "NY", "latitude": "40.76843000", "longitude": "-73.52513000"}, {"name": "Highland", "countryCode": "US", "stateCode": "NY", "latitude": "41.72093000", "longitude": "-73.96014000"}, {"name": "Highland Falls", "countryCode": "US", "stateCode": "NY", "latitude": "41.36926000", "longitude": "-73.96625000"}, {"name": "Highland Mills", "countryCode": "US", "stateCode": "NY", "latitude": "41.34704000", "longitude": "-74.12626000"}, {"name": "Hillcrest", "countryCode": "US", "stateCode": "NY", "latitude": "41.12787000", "longitude": "-74.04097000"}, {"name": "Hillside", "countryCode": "US", "stateCode": "NY", "latitude": "40.70788000", "longitude": "-73.78680000"}, {"name": "Hillside Lake", "countryCode": "US", "stateCode": "NY", "latitude": "41.61482000", "longitude": "-73.79819000"}, {"name": "Hilton", "countryCode": "US", "stateCode": "NY", "latitude": "43.28812000", "longitude": "-77.79334000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.81232000", "longitude": "-73.07844000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.90229000", "longitude": "-77.41971000"}, {"name": "Holland", "countryCode": "US", "stateCode": "NY", "latitude": "42.64117000", "longitude": "-78.54169000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.22645000", "longitude": "-78.02668000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.71344000", "longitude": "-73.76708000"}, {"name": "Holtsville", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.04511000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.63701000", "longitude": "-76.17882000"}, {"name": "Honeoye Falls", "countryCode": "US", "stateCode": "NY", "latitude": "42.95229000", "longitude": "-77.59028000"}, {"name": "Hoosick Falls", "countryCode": "US", "stateCode": "NY", "latitude": "42.90119000", "longitude": "-73.35150000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.32785000", "longitude": "-77.66110000"}, {"name": "Horseheads", "countryCode": "US", "stateCode": "NY", "latitude": "42.16702000", "longitude": "-76.82051000"}, {"name": "Horseheads North", "countryCode": "US", "stateCode": "NY", "latitude": "42.19278000", "longitude": "-76.80782000"}, {"name": "Houghton", "countryCode": "US", "stateCode": "NY", "latitude": "42.42340000", "longitude": "-78.15723000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.65788000", "longitude": "-73.83625000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.25286000", "longitude": "-73.79096000"}, {"name": "Hudson Falls", "countryCode": "US", "stateCode": "NY", "latitude": "43.30063000", "longitude": "-73.58595000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.53733000", "longitude": "-74.19459000"}, {"name": "Huntington", "countryCode": "US", "stateCode": "NY", "latitude": "40.86815000", "longitude": "-73.42568000"}, {"name": "Huntington Bay", "countryCode": "US", "stateCode": "NY", "latitude": "40.89982000", "longitude": "-73.41484000"}, {"name": "Huntington Station", "countryCode": "US", "stateCode": "NY", "latitude": "40.85343000", "longitude": "-73.41151000"}, {"name": "Hunts Point", "countryCode": "US", "stateCode": "NY", "latitude": "40.81260000", "longitude": "-73.88402000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.92454000", "longitude": "-74.06125000"}, {"name": "Hyde Park", "countryCode": "US", "stateCode": "NY", "latitude": "41.78482000", "longitude": "-73.93319000"}, {"name": "Ilion", "countryCode": "US", "stateCode": "NY", "latitude": "43.01507000", "longitude": "-75.03543000"}, {"name": "Inwood", "countryCode": "US", "stateCode": "NY", "latitude": "40.86566000", "longitude": "-73.92680000"}, {"name": "Irondequoit", "countryCode": "US", "stateCode": "NY", "latitude": "43.21340000", "longitude": "-77.57972000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.03922000", "longitude": "-73.86823000"}, {"name": "Island Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.60427000", "longitude": "-73.65541000"}, {"name": "Islandia", "countryCode": "US", "stateCode": "NY", "latitude": "40.80426000", "longitude": "-73.16900000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.72982000", "longitude": "-73.21039000"}, {"name": "Islip Terrace", "countryCode": "US", "stateCode": "NY", "latitude": "40.74315000", "longitude": "-73.19262000"}, {"name": "Ithaca", "countryCode": "US", "stateCode": "NY", "latitude": "42.44063000", "longitude": "-76.49661000"}, {"name": "Jackson Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.75566000", "longitude": "-73.88541000"}, {"name": "Jamaica", "countryCode": "US", "stateCode": "NY", "latitude": "40.69149000", "longitude": "-73.80569000"}, {"name": "Jamesport", "countryCode": "US", "stateCode": "NY", "latitude": "40.94954000", "longitude": "-72.58148000"}, {"name": "Jamestown", "countryCode": "US", "stateCode": "NY", "latitude": "42.09700000", "longitude": "-79.23533000"}, {"name": "Jamestown West", "countryCode": "US", "stateCode": "NY", "latitude": "42.08851000", "longitude": "-79.28110000"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "NY", "latitude": "43.99885000", "longitude": "-76.05211000"}, {"name": "Jefferson Heights", "countryCode": "US", "stateCode": "NY", "latitude": "42.23398000", "longitude": "-73.88235000"}, {"name": "Jefferson Valley-Yorktown", "countryCode": "US", "stateCode": "NY", "latitude": "41.31797000", "longitude": "-73.80066000"}, {"name": "Jericho", "countryCode": "US", "stateCode": "NY", "latitude": "40.79204000", "longitude": "-73.53985000"}, {"name": "Johnson City", "countryCode": "US", "stateCode": "NY", "latitude": "42.11563000", "longitude": "-75.95881000"}, {"name": "Johnstown", "countryCode": "US", "stateCode": "NY", "latitude": "43.00674000", "longitude": "-74.36764000"}, {"name": "Jordan", "countryCode": "US", "stateCode": "NY", "latitude": "43.06534000", "longitude": "-76.47299000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.12121000", "longitude": "-74.06709000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.25898000", "longitude": "-73.68541000"}, {"name": "Keeseville", "countryCode": "US", "stateCode": "NY", "latitude": "44.50505000", "longitude": "-73.48013000"}, {"name": "Kenmore", "countryCode": "US", "stateCode": "NY", "latitude": "42.96589000", "longitude": "-78.87004000"}, {"name": "Kensington", "countryCode": "US", "stateCode": "NY", "latitude": "40.79343000", "longitude": "-73.72208000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.77482000", "longitude": "-74.29821000"}, {"name": "Keuka Park", "countryCode": "US", "stateCode": "NY", "latitude": "42.61535000", "longitude": "-77.09219000"}, {"name": "Kew Gardens", "countryCode": "US", "stateCode": "NY", "latitude": "40.71427000", "longitude": "-73.83097000"}, {"name": "Kew Gardens Hills", "countryCode": "US", "stateCode": "NY", "latitude": "40.73002000", "longitude": "-73.82340000"}, {"name": "Kiantone", "countryCode": "US", "stateCode": "NY", "latitude": "42.02200000", "longitude": "-79.19810000"}, {"name": "Kinderhook", "countryCode": "US", "stateCode": "NY", "latitude": "42.39536000", "longitude": "-73.69790000"}, {"name": "Kings Bridge", "countryCode": "US", "stateCode": "NY", "latitude": "40.87871000", "longitude": "-73.90514000"}, {"name": "Kings County", "countryCode": "US", "stateCode": "NY", "latitude": "40.63439000", "longitude": "-73.95027000"}, {"name": "Kings Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.88621000", "longitude": "-73.25734000"}, {"name": "Kings Point", "countryCode": "US", "stateCode": "NY", "latitude": "40.81982000", "longitude": "-73.73513000"}, {"name": "Kingston", "countryCode": "US", "stateCode": "NY", "latitude": "41.92704000", "longitude": "-73.99736000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.34204000", "longitude": "-74.16792000"}, {"name": "Lackawanna", "countryCode": "US", "stateCode": "NY", "latitude": "42.82561000", "longitude": "-78.82337000"}, {"name": "Lake Carmel", "countryCode": "US", "stateCode": "NY", "latitude": "41.46148000", "longitude": "-73.67096000"}, {"name": "Lake Erie Beach", "countryCode": "US", "stateCode": "NY", "latitude": "42.62423000", "longitude": "-79.06698000"}, {"name": "Lake Grove", "countryCode": "US", "stateCode": "NY", "latitude": "40.85288000", "longitude": "-73.11511000"}, {"name": "Lake Katrine", "countryCode": "US", "stateCode": "NY", "latitude": "41.98565000", "longitude": "-73.98819000"}, {"name": "Lake Luzerne", "countryCode": "US", "stateCode": "NY", "latitude": "43.31285000", "longitude": "-73.83484000"}, {"name": "Lake Mohegan", "countryCode": "US", "stateCode": "NY", "latitude": "41.31787000", "longitude": "-73.84625000"}, {"name": "Lake Placid", "countryCode": "US", "stateCode": "NY", "latitude": "44.27962000", "longitude": "-73.98198000"}, {"name": "Lake Pleasant", "countryCode": "US", "stateCode": "NY", "latitude": "43.47090000", "longitude": "-74.41265000"}, {"name": "Lake Ronkonkoma", "countryCode": "US", "stateCode": "NY", "latitude": "40.83510000", "longitude": "-73.13122000"}, {"name": "Lake Success", "countryCode": "US", "stateCode": "NY", "latitude": "40.77066000", "longitude": "-73.71763000"}, {"name": "Lakeland", "countryCode": "US", "stateCode": "NY", "latitude": "43.09034000", "longitude": "-76.24048000"}, {"name": "Lakeview", "countryCode": "US", "stateCode": "NY", "latitude": "40.68538000", "longitude": "-73.65263000"}, {"name": "Lakewood", "countryCode": "US", "stateCode": "NY", "latitude": "42.10422000", "longitude": "-79.33310000"}, {"name": "Lancaster", "countryCode": "US", "stateCode": "NY", "latitude": "42.90061000", "longitude": "-78.67031000"}, {"name": "Lansing", "countryCode": "US", "stateCode": "NY", "latitude": "42.48424000", "longitude": "-76.47994000"}, {"name": "Larchmont", "countryCode": "US", "stateCode": "NY", "latitude": "40.92788000", "longitude": "-73.75180000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.74702000", "longitude": "-73.75901000"}, {"name": "Lattingtown", "countryCode": "US", "stateCode": "NY", "latitude": "40.89538000", "longitude": "-73.60096000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.96954000", "longitude": "-72.56203000"}, {"name": "Laurel Hollow", "countryCode": "US", "stateCode": "NY", "latitude": "40.85677000", "longitude": "-73.46957000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.67019000", "longitude": "-73.74659000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.61566000", "longitude": "-73.72958000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.97839000", "longitude": "-77.98418000"}, {"name": "Levittown", "countryCode": "US", "stateCode": "NY", "latitude": "40.72593000", "longitude": "-73.51429000"}, {"name": "Lewis County", "countryCode": "US", "stateCode": "NY", "latitude": "43.78469000", "longitude": "-75.44879000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.17256000", "longitude": "-79.03588000"}, {"name": "Liberty", "countryCode": "US", "stateCode": "NY", "latitude": "41.80120000", "longitude": "-74.74655000"}, {"name": "Lido Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.58899000", "longitude": "-73.62541000"}, {"name": "Lima", "countryCode": "US", "stateCode": "NY", "latitude": "42.90479000", "longitude": "-77.61139000"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "NY", "latitude": "41.95065000", "longitude": "-73.99403000"}, {"name": "Lincolndale", "countryCode": "US", "stateCode": "NY", "latitude": "41.32287000", "longitude": "-73.71819000"}, {"name": "Lindenhurst", "countryCode": "US", "stateCode": "NY", "latitude": "40.68677000", "longitude": "-73.37345000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.02841000", "longitude": "-77.13969000"}, {"name": "Little Falls", "countryCode": "US", "stateCode": "NY", "latitude": "43.04340000", "longitude": "-74.85960000"}, {"name": "Little Neck", "countryCode": "US", "stateCode": "NY", "latitude": "40.76289000", "longitude": "-73.73225000"}, {"name": "Little Valley", "countryCode": "US", "stateCode": "NY", "latitude": "42.25256000", "longitude": "-78.80559000"}, {"name": "Liverpool", "countryCode": "US", "stateCode": "NY", "latitude": "43.10646000", "longitude": "-76.21770000"}, {"name": "Livingston County", "countryCode": "US", "stateCode": "NY", "latitude": "42.72808000", "longitude": "-77.77549000"}, {"name": "Livingston Manor", "countryCode": "US", "stateCode": "NY", "latitude": "41.90037000", "longitude": "-74.82822000"}, {"name": "Livonia", "countryCode": "US", "stateCode": "NY", "latitude": "42.82145000", "longitude": "-77.66861000"}, {"name": "Lloyd Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "40.90343000", "longitude": "-73.45984000"}, {"name": "Lockport", "countryCode": "US", "stateCode": "NY", "latitude": "43.17061000", "longitude": "-78.69031000"}, {"name": "Locust Valley", "countryCode": "US", "stateCode": "NY", "latitude": "40.87593000", "longitude": "-73.59707000"}, {"name": "Long Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.58844000", "longitude": "-73.65791000"}, {"name": "Long Island City", "countryCode": "US", "stateCode": "NY", "latitude": "40.74482000", "longitude": "-73.94875000"}, {"name": "Lorenz Park", "countryCode": "US", "stateCode": "NY", "latitude": "42.26370000", "longitude": "-73.76846000"}, {"name": "Lowville", "countryCode": "US", "stateCode": "NY", "latitude": "43.78674000", "longitude": "-75.49185000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.65483000", "longitude": "-73.67180000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.08146000", "longitude": "-76.12576000"}, {"name": "Lyons", "countryCode": "US", "stateCode": "NY", "latitude": "43.06423000", "longitude": "-76.99025000"}, {"name": "Macedon", "countryCode": "US", "stateCode": "NY", "latitude": "43.06923000", "longitude": "-77.29887000"}, {"name": "Madison County", "countryCode": "US", "stateCode": "NY", "latitude": "42.91277000", "longitude": "-75.66967000"}, {"name": "Mahopac", "countryCode": "US", "stateCode": "NY", "latitude": "41.37232000", "longitude": "-73.73346000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "44.84866000", "longitude": "-74.29490000"}, {"name": "Malverne", "countryCode": "US", "stateCode": "NY", "latitude": "40.67899000", "longitude": "-73.67402000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.94871000", "longitude": "-73.73263000"}, {"name": "Manchester", "countryCode": "US", "stateCode": "NY", "latitude": "42.96979000", "longitude": "-77.23026000"}, {"name": "Man<PERSON>set", "countryCode": "US", "stateCode": "NY", "latitude": "40.79788000", "longitude": "-73.69957000"}, {"name": "Manhasset Hills", "countryCode": "US", "stateCode": "NY", "latitude": "40.75927000", "longitude": "-73.67985000"}, {"name": "Manhattan", "countryCode": "US", "stateCode": "NY", "latitude": "40.78343000", "longitude": "-73.96625000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.00201000", "longitude": "-75.97686000"}, {"name": "Manorhaven", "countryCode": "US", "stateCode": "NY", "latitude": "40.84316000", "longitude": "-73.71485000"}, {"name": "Manorville", "countryCode": "US", "stateCode": "NY", "latitude": "40.87371000", "longitude": "-72.80788000"}, {"name": "Marbletown", "countryCode": "US", "stateCode": "NY", "latitude": "41.88343000", "longitude": "-74.11320000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.98284000", "longitude": "-76.34049000"}, {"name": "Mariners Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "40.63677000", "longitude": "-74.15875000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.14340000", "longitude": "-77.18915000"}, {"name": "Marlboro", "countryCode": "US", "stateCode": "NY", "latitude": "41.60565000", "longitude": "-73.97153000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.72316000", "longitude": "-73.91264000"}, {"name": "Massapequa", "countryCode": "US", "stateCode": "NY", "latitude": "40.68066000", "longitude": "-73.47429000"}, {"name": "Massapequa Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.68038000", "longitude": "-73.45512000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "44.92810000", "longitude": "-74.89186000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.80204000", "longitude": "-72.84094000"}, {"name": "Mastic Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.76677000", "longitude": "-72.85205000"}, {"name": "Mattituck", "countryCode": "US", "stateCode": "NY", "latitude": "40.99121000", "longitude": "-72.53425000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.09784000", "longitude": "-76.14520000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.48398000", "longitude": "-74.21765000"}, {"name": "Mayville", "countryCode": "US", "stateCode": "NY", "latitude": "42.25395000", "longitude": "-79.50449000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.59618000", "longitude": "-76.09326000"}, {"name": "McKownville", "countryCode": "US", "stateCode": "NY", "latitude": "42.68397000", "longitude": "-73.84762000"}, {"name": "Mechanicstown", "countryCode": "US", "stateCode": "NY", "latitude": "41.44287000", "longitude": "-74.38849000"}, {"name": "Mechanicville", "countryCode": "US", "stateCode": "NY", "latitude": "42.90285000", "longitude": "-73.68734000"}, {"name": "Medford", "countryCode": "US", "stateCode": "NY", "latitude": "40.81760000", "longitude": "-73.00011000"}, {"name": "Medina", "countryCode": "US", "stateCode": "NY", "latitude": "43.22006000", "longitude": "-78.38697000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.82455000", "longitude": "-73.91041000"}, {"name": "Melrose Park", "countryCode": "US", "stateCode": "NY", "latitude": "42.90868000", "longitude": "-76.54022000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.79343000", "longitude": "-73.41512000"}, {"name": "Men<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.69202000", "longitude": "-73.72456000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.66288000", "longitude": "-73.55152000"}, {"name": "Merritt Park", "countryCode": "US", "stateCode": "NY", "latitude": "41.53848000", "longitude": "-73.87238000"}, {"name": "Mexico", "countryCode": "US", "stateCode": "NY", "latitude": "43.45951000", "longitude": "-76.22882000"}, {"name": "Middle Island", "countryCode": "US", "stateCode": "NY", "latitude": "40.88427000", "longitude": "-72.93733000"}, {"name": "Middle Village", "countryCode": "US", "stateCode": "NY", "latitude": "40.71649000", "longitude": "-73.88125000"}, {"name": "Middleburgh", "countryCode": "US", "stateCode": "NY", "latitude": "42.59869000", "longitude": "-74.33292000"}, {"name": "Middleport", "countryCode": "US", "stateCode": "NY", "latitude": "43.21256000", "longitude": "-78.47641000"}, {"name": "Middletown", "countryCode": "US", "stateCode": "NY", "latitude": "41.44593000", "longitude": "-74.42293000"}, {"name": "Midland Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.57316000", "longitude": "-74.09459000"}, {"name": "Mill Neck", "countryCode": "US", "stateCode": "NY", "latitude": "40.88704000", "longitude": "-73.55512000"}, {"name": "Millbrook", "countryCode": "US", "stateCode": "NY", "latitude": "41.78509000", "longitude": "-73.69402000"}, {"name": "Miller <PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.95982000", "longitude": "-72.99621000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.03369000", "longitude": "-73.85262000"}, {"name": "Mineola", "countryCode": "US", "stateCode": "NY", "latitude": "40.74927000", "longitude": "-73.64068000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.39812000", "longitude": "-76.47744000"}, {"name": "Mineville", "countryCode": "US", "stateCode": "NY", "latitude": "44.09283000", "longitude": "-73.51818000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.07618000", "longitude": "-76.00075000"}, {"name": "Mohawk", "countryCode": "US", "stateCode": "NY", "latitude": "43.01146000", "longitude": "-75.00404000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.33065000", "longitude": "-74.18681000"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "NY", "latitude": "43.16512000", "longitude": "-77.63626000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.11121000", "longitude": "-74.06848000"}, {"name": "<PERSON>au<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.03594000", "longitude": "-71.95451000"}, {"name": "Montebello", "countryCode": "US", "stateCode": "NY", "latitude": "41.13593000", "longitude": "-74.11848000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.52759000", "longitude": "-74.23682000"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "NY", "latitude": "42.90228000", "longitude": "-74.43968000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.65565000", "longitude": "-74.68933000"}, {"name": "Montour Falls", "countryCode": "US", "stateCode": "NY", "latitude": "42.34730000", "longitude": "-76.84524000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.25232000", "longitude": "-73.93153000"}, {"name": "Moravia", "countryCode": "US", "stateCode": "NY", "latitude": "42.71257000", "longitude": "-76.42160000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.80732000", "longitude": "-72.82121000"}, {"name": "Morningside Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.81000000", "longitude": "-73.96250000"}, {"name": "Morris Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.84982000", "longitude": "-73.91986000"}, {"name": "Morris Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.85232000", "longitude": "-73.85347000"}, {"name": "Morrisania", "countryCode": "US", "stateCode": "NY", "latitude": "40.82927000", "longitude": "-73.90653000"}, {"name": "Morrisonville", "countryCode": "US", "stateCode": "NY", "latitude": "44.69310000", "longitude": "-73.56208000"}, {"name": "Morrisville", "countryCode": "US", "stateCode": "NY", "latitude": "42.89868000", "longitude": "-75.64018000"}, {"name": "Mott Haven", "countryCode": "US", "stateCode": "NY", "latitude": "40.80899000", "longitude": "-73.92291000"}, {"name": "Mount Ivy", "countryCode": "US", "stateCode": "NY", "latitude": "41.18676000", "longitude": "-74.03486000"}, {"name": "Mount Kisco", "countryCode": "US", "stateCode": "NY", "latitude": "41.20426000", "longitude": "-73.72708000"}, {"name": "Mount Morris", "countryCode": "US", "stateCode": "NY", "latitude": "42.72562000", "longitude": "-77.87417000"}, {"name": "Mount Sinai", "countryCode": "US", "stateCode": "NY", "latitude": "40.94704000", "longitude": "-73.02955000"}, {"name": "Mount Vernon", "countryCode": "US", "stateCode": "NY", "latitude": "40.91260000", "longitude": "-73.83708000"}, {"name": "Mountain Lodge Park", "countryCode": "US", "stateCode": "NY", "latitude": "41.38843000", "longitude": "-74.14181000"}, {"name": "Munsey Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.79871000", "longitude": "-73.67985000"}, {"name": "Munsons Corners", "countryCode": "US", "stateCode": "NY", "latitude": "42.58229000", "longitude": "-76.20910000"}, {"name": "Muttontown", "countryCode": "US", "stateCode": "NY", "latitude": "40.82399000", "longitude": "-73.54763000"}, {"name": "Myers <PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.60620000", "longitude": "-73.87291000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.08871000", "longitude": "-74.01347000"}, {"name": "Napanoch", "countryCode": "US", "stateCode": "NY", "latitude": "41.74398000", "longitude": "-74.37154000"}, {"name": "Naples", "countryCode": "US", "stateCode": "NY", "latitude": "42.61535000", "longitude": "-77.40249000"}, {"name": "Nassau", "countryCode": "US", "stateCode": "NY", "latitude": "42.51591000", "longitude": "-73.61012000"}, {"name": "Nassau County", "countryCode": "US", "stateCode": "NY", "latitude": "40.73217000", "longitude": "-73.58545000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.97507000", "longitude": "-76.14131000"}, {"name": "Neponsit", "countryCode": "US", "stateCode": "NY", "latitude": "40.57177000", "longitude": "-73.86152000"}, {"name": "Nesconset", "countryCode": "US", "stateCode": "NY", "latitude": "40.85204000", "longitude": "-73.15400000"}, {"name": "New Brighton", "countryCode": "US", "stateCode": "NY", "latitude": "40.64233000", "longitude": "-74.09292000"}, {"name": "New Cassel", "countryCode": "US", "stateCode": "NY", "latitude": "40.75899000", "longitude": "-73.56957000"}, {"name": "New City", "countryCode": "US", "stateCode": "NY", "latitude": "41.14760000", "longitude": "-73.98931000"}, {"name": "New Dorp", "countryCode": "US", "stateCode": "NY", "latitude": "40.57399000", "longitude": "-74.11598000"}, {"name": "New Dorp Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.56538000", "longitude": "-74.10292000"}, {"name": "New Hartford", "countryCode": "US", "stateCode": "NY", "latitude": "43.07340000", "longitude": "-75.28767000"}, {"name": "New Hempstead", "countryCode": "US", "stateCode": "NY", "latitude": "41.14982000", "longitude": "-74.03375000"}, {"name": "New Hyde Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.73510000", "longitude": "-73.68791000"}, {"name": "New Paltz", "countryCode": "US", "stateCode": "NY", "latitude": "41.74759000", "longitude": "-74.08681000"}, {"name": "New Rochelle", "countryCode": "US", "stateCode": "NY", "latitude": "40.91149000", "longitude": "-73.78235000"}, {"name": "New Springville", "countryCode": "US", "stateCode": "NY", "latitude": "40.59344000", "longitude": "-74.16320000"}, {"name": "New Square", "countryCode": "US", "stateCode": "NY", "latitude": "41.13956000", "longitude": "-74.02942000"}, {"name": "New Windsor", "countryCode": "US", "stateCode": "NY", "latitude": "41.47676000", "longitude": "-74.02375000"}, {"name": "New York City", "countryCode": "US", "stateCode": "NY", "latitude": "40.71427000", "longitude": "-74.00597000"}, {"name": "New York County", "countryCode": "US", "stateCode": "NY", "latitude": "40.77427000", "longitude": "-73.96981000"}, {"name": "New York Mills", "countryCode": "US", "stateCode": "NY", "latitude": "43.10535000", "longitude": "-75.29128000"}, {"name": "Newark", "countryCode": "US", "stateCode": "NY", "latitude": "43.04673000", "longitude": "-77.09525000"}, {"name": "Newburgh", "countryCode": "US", "stateCode": "NY", "latitude": "41.50343000", "longitude": "-74.01042000"}, {"name": "Newfane", "countryCode": "US", "stateCode": "NY", "latitude": "43.28672000", "longitude": "-78.71031000"}, {"name": "Niagara County", "countryCode": "US", "stateCode": "NY", "latitude": "43.17314000", "longitude": "-78.69095000"}, {"name": "Niagara Falls", "countryCode": "US", "stateCode": "NY", "latitude": "43.09450000", "longitude": "-79.05671000"}, {"name": "Niskayuna", "countryCode": "US", "stateCode": "NY", "latitude": "42.77980000", "longitude": "-73.84568000"}, {"name": "Nissequogue", "countryCode": "US", "stateCode": "NY", "latitude": "40.90399000", "longitude": "-73.19789000"}, {"name": "Niverville", "countryCode": "US", "stateCode": "NY", "latitude": "42.44092000", "longitude": "-73.66095000"}, {"name": "Norfolk", "countryCode": "US", "stateCode": "NY", "latitude": "44.80089000", "longitude": "-74.99103000"}, {"name": "North Amityville", "countryCode": "US", "stateCode": "NY", "latitude": "40.69760000", "longitude": "-73.42512000"}, {"name": "North Babylon", "countryCode": "US", "stateCode": "NY", "latitude": "40.71649000", "longitude": "-73.32179000"}, {"name": "North Ballston Spa", "countryCode": "US", "stateCode": "NY", "latitude": "43.01969000", "longitude": "-73.85109000"}, {"name": "North Bay Shore", "countryCode": "US", "stateCode": "NY", "latitude": "40.73621000", "longitude": "-73.26262000"}, {"name": "North Bellmore", "countryCode": "US", "stateCode": "NY", "latitude": "40.69149000", "longitude": "-73.53346000"}, {"name": "North Bellport", "countryCode": "US", "stateCode": "NY", "latitude": "40.77427000", "longitude": "-72.94288000"}, {"name": "North Boston", "countryCode": "US", "stateCode": "NY", "latitude": "42.68562000", "longitude": "-78.77670000"}, {"name": "North Castle", "countryCode": "US", "stateCode": "NY", "latitude": "41.14000000", "longitude": "-73.68389000"}, {"name": "North Collins", "countryCode": "US", "stateCode": "NY", "latitude": "42.59534000", "longitude": "-78.94115000"}, {"name": "North Elba", "countryCode": "US", "stateCode": "NY", "latitude": "44.24338000", "longitude": "-73.95431000"}, {"name": "North Gates", "countryCode": "US", "stateCode": "NY", "latitude": "43.17645000", "longitude": "-77.70139000"}, {"name": "North Great River", "countryCode": "US", "stateCode": "NY", "latitude": "40.74732000", "longitude": "-73.16984000"}, {"name": "North Hills", "countryCode": "US", "stateCode": "NY", "latitude": "40.78093000", "longitude": "-73.67652000"}, {"name": "North Lindenhurst", "countryCode": "US", "stateCode": "NY", "latitude": "40.71427000", "longitude": "-73.38151000"}, {"name": "North Massapequa", "countryCode": "US", "stateCode": "NY", "latitude": "40.70093000", "longitude": "-73.46207000"}, {"name": "North Merrick", "countryCode": "US", "stateCode": "NY", "latitude": "40.69121000", "longitude": "-73.56318000"}, {"name": "North New Hyde Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.74316000", "longitude": "-73.69319000"}, {"name": "North Patchogue", "countryCode": "US", "stateCode": "NY", "latitude": "40.78704000", "longitude": "-73.00900000"}, {"name": "North Sea", "countryCode": "US", "stateCode": "NY", "latitude": "40.93288000", "longitude": "-72.41425000"}, {"name": "North Syracuse", "countryCode": "US", "stateCode": "NY", "latitude": "43.13479000", "longitude": "-76.12992000"}, {"name": "North Tonawanda", "countryCode": "US", "stateCode": "NY", "latitude": "43.03867000", "longitude": "-78.86420000"}, {"name": "North Valley Stream", "countryCode": "US", "stateCode": "NY", "latitude": "40.68510000", "longitude": "-73.70180000"}, {"name": "North Wantagh", "countryCode": "US", "stateCode": "NY", "latitude": "40.69343000", "longitude": "-73.50763000"}, {"name": "Northeast Ithaca", "countryCode": "US", "stateCode": "NY", "latitude": "42.47032000", "longitude": "-76.46228000"}, {"name": "Northport", "countryCode": "US", "stateCode": "NY", "latitude": "40.90093000", "longitude": "-73.34317000"}, {"name": "Northumberland", "countryCode": "US", "stateCode": "NY", "latitude": "43.12730000", "longitude": "-73.58817000"}, {"name": "Northville", "countryCode": "US", "stateCode": "NY", "latitude": "40.97010000", "longitude": "-72.61898000"}, {"name": "Northwest Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "41.00982000", "longitude": "-72.22119000"}, {"name": "Northwest Ithaca", "countryCode": "US", "stateCode": "NY", "latitude": "42.47059000", "longitude": "-76.54145000"}, {"name": "Norwich", "countryCode": "US", "stateCode": "NY", "latitude": "42.53118000", "longitude": "-75.52351000"}, {"name": "Norwood", "countryCode": "US", "stateCode": "NY", "latitude": "44.75145000", "longitude": "-74.99436000"}, {"name": "Noyack", "countryCode": "US", "stateCode": "NY", "latitude": "40.99566000", "longitude": "-72.34119000"}, {"name": "Nunda", "countryCode": "US", "stateCode": "NY", "latitude": "42.57951000", "longitude": "-77.94250000"}, {"name": "N<PERSON>ck", "countryCode": "US", "stateCode": "NY", "latitude": "41.09065000", "longitude": "-73.91791000"}, {"name": "Oakdale", "countryCode": "US", "stateCode": "NY", "latitude": "40.74399000", "longitude": "-73.13872000"}, {"name": "Oakfield", "countryCode": "US", "stateCode": "NY", "latitude": "43.06589000", "longitude": "-78.26974000"}, {"name": "Oakwood", "countryCode": "US", "stateCode": "NY", "latitude": "40.56399000", "longitude": "-74.11598000"}, {"name": "Oceanside", "countryCode": "US", "stateCode": "NY", "latitude": "40.63871000", "longitude": "-73.64013000"}, {"name": "Ogdensburg", "countryCode": "US", "stateCode": "NY", "latitude": "44.69423000", "longitude": "-75.48634000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.33783000", "longitude": "-78.71476000"}, {"name": "Old Bethpage", "countryCode": "US", "stateCode": "NY", "latitude": "40.76260000", "longitude": "-73.45318000"}, {"name": "Old Brookville", "countryCode": "US", "stateCode": "NY", "latitude": "40.83204000", "longitude": "-73.60485000"}, {"name": "Old Westbury", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.59957000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.07756000", "longitude": "-78.42974000"}, {"name": "Oneida", "countryCode": "US", "stateCode": "NY", "latitude": "43.09257000", "longitude": "-75.65129000"}, {"name": "Oneida County", "countryCode": "US", "stateCode": "NY", "latitude": "43.24175000", "longitude": "-75.43584000"}, {"name": "Oneonta", "countryCode": "US", "stateCode": "NY", "latitude": "42.45286000", "longitude": "-75.06377000"}, {"name": "Onondaga County", "countryCode": "US", "stateCode": "NY", "latitude": "43.00580000", "longitude": "-76.19464000"}, {"name": "Ontario", "countryCode": "US", "stateCode": "NY", "latitude": "43.22090000", "longitude": "-77.28304000"}, {"name": "Ontario County", "countryCode": "US", "stateCode": "NY", "latitude": "42.85285000", "longitude": "-77.29982000"}, {"name": "Orange County", "countryCode": "US", "stateCode": "NY", "latitude": "41.40214000", "longitude": "-74.30557000"}, {"name": "Orange Lake", "countryCode": "US", "stateCode": "NY", "latitude": "41.53982000", "longitude": "-74.09820000"}, {"name": "Orangeburg", "countryCode": "US", "stateCode": "NY", "latitude": "41.04649000", "longitude": "-73.94958000"}, {"name": "Orchard Park", "countryCode": "US", "stateCode": "NY", "latitude": "42.76756000", "longitude": "-78.74392000"}, {"name": "Oriskany", "countryCode": "US", "stateCode": "NY", "latitude": "43.15729000", "longitude": "-75.33267000"}, {"name": "Orleans County", "countryCode": "US", "stateCode": "NY", "latitude": "43.25070000", "longitude": "-78.18901000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.16287000", "longitude": "-73.86152000"}, {"name": "Oswego", "countryCode": "US", "stateCode": "NY", "latitude": "43.45535000", "longitude": "-76.51050000"}, {"name": "Oswego County", "countryCode": "US", "stateCode": "NY", "latitude": "43.46389000", "longitude": "-76.20868000"}, {"name": "Otisville", "countryCode": "US", "stateCode": "NY", "latitude": "41.47343000", "longitude": "-74.53849000"}, {"name": "Otsego County", "countryCode": "US", "stateCode": "NY", "latitude": "42.63376000", "longitude": "-75.03261000"}, {"name": "Owego", "countryCode": "US", "stateCode": "NY", "latitude": "42.10341000", "longitude": "-76.26215000"}, {"name": "Oxford", "countryCode": "US", "stateCode": "NY", "latitude": "42.44202000", "longitude": "-75.59769000"}, {"name": "Oyster Bay", "countryCode": "US", "stateCode": "NY", "latitude": "40.86565000", "longitude": "-73.53207000"}, {"name": "Oyster Bay Cove", "countryCode": "US", "stateCode": "NY", "latitude": "40.87093000", "longitude": "-73.51096000"}, {"name": "Ozone Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.67677000", "longitude": "-73.84375000"}, {"name": "Painted Post", "countryCode": "US", "stateCode": "NY", "latitude": "42.16202000", "longitude": "-77.09414000"}, {"name": "Palenville", "countryCode": "US", "stateCode": "NY", "latitude": "42.17453000", "longitude": "-74.02014000"}, {"name": "Palmyra", "countryCode": "US", "stateCode": "NY", "latitude": "43.06395000", "longitude": "-77.23332000"}, {"name": "Park Slope", "countryCode": "US", "stateCode": "NY", "latitude": "40.67010000", "longitude": "-73.98597000"}, {"name": "Parkchester", "countryCode": "US", "stateCode": "NY", "latitude": "40.83899000", "longitude": "-73.86041000"}, {"name": "Patchogue", "countryCode": "US", "stateCode": "NY", "latitude": "40.76565000", "longitude": "-73.01511000"}, {"name": "Pawling", "countryCode": "US", "stateCode": "NY", "latitude": "41.56204000", "longitude": "-73.60263000"}, {"name": "Peach Lake", "countryCode": "US", "stateCode": "NY", "latitude": "41.36759000", "longitude": "-73.57790000"}, {"name": "Pearl River", "countryCode": "US", "stateCode": "NY", "latitude": "41.05899000", "longitude": "-74.02181000"}, {"name": "Peekskill", "countryCode": "US", "stateCode": "NY", "latitude": "41.29009000", "longitude": "-73.92042000"}, {"name": "Pelham", "countryCode": "US", "stateCode": "NY", "latitude": "40.90982000", "longitude": "-73.80791000"}, {"name": "Pelham Manor", "countryCode": "US", "stateCode": "NY", "latitude": "40.89538000", "longitude": "-73.80708000"}, {"name": "Penn Yan", "countryCode": "US", "stateCode": "NY", "latitude": "42.66090000", "longitude": "-77.05386000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.71562000", "longitude": "-78.00556000"}, {"name": "Perth", "countryCode": "US", "stateCode": "NY", "latitude": "43.01757000", "longitude": "-74.19402000"}, {"name": "Peru", "countryCode": "US", "stateCode": "NY", "latitude": "44.57838000", "longitude": "-73.52680000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.95756000", "longitude": "-77.05747000"}, {"name": "Philadelphia", "countryCode": "US", "stateCode": "NY", "latitude": "44.15450000", "longitude": "-75.70882000"}, {"name": "Philmont", "countryCode": "US", "stateCode": "NY", "latitude": "42.24842000", "longitude": "-73.65318000"}, {"name": "Phoenix", "countryCode": "US", "stateCode": "NY", "latitude": "43.23118000", "longitude": "-76.30076000"}, {"name": "Piermont", "countryCode": "US", "stateCode": "NY", "latitude": "41.04204000", "longitude": "-73.91819000"}, {"name": "Pine Bush", "countryCode": "US", "stateCode": "NY", "latitude": "41.60815000", "longitude": "-74.29904000"}, {"name": "Pine Plains", "countryCode": "US", "stateCode": "NY", "latitude": "41.97981000", "longitude": "-73.65596000"}, {"name": "Pittsford", "countryCode": "US", "stateCode": "NY", "latitude": "43.09062000", "longitude": "-77.51500000"}, {"name": "Plainedge", "countryCode": "US", "stateCode": "NY", "latitude": "40.71732000", "longitude": "-73.48374000"}, {"name": "Plainview", "countryCode": "US", "stateCode": "NY", "latitude": "40.77649000", "longitude": "-73.46735000"}, {"name": "Plandome", "countryCode": "US", "stateCode": "NY", "latitude": "40.80677000", "longitude": "-73.70346000"}, {"name": "Plandome Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.80260000", "longitude": "-73.70430000"}, {"name": "Plattekill", "countryCode": "US", "stateCode": "NY", "latitude": "41.61759000", "longitude": "-74.07598000"}, {"name": "Plattsburgh", "countryCode": "US", "stateCode": "NY", "latitude": "44.69949000", "longitude": "-73.45291000"}, {"name": "Plattsburgh West", "countryCode": "US", "stateCode": "NY", "latitude": "44.68315000", "longitude": "-73.50295000"}, {"name": "Pleasant Valley", "countryCode": "US", "stateCode": "NY", "latitude": "41.74454000", "longitude": "-73.82124000"}, {"name": "Pleasantville", "countryCode": "US", "stateCode": "NY", "latitude": "41.13287000", "longitude": "-73.79263000"}, {"name": "Poestenkill", "countryCode": "US", "stateCode": "NY", "latitude": "42.69036000", "longitude": "-73.56456000"}, {"name": "Point Lookout", "countryCode": "US", "stateCode": "NY", "latitude": "40.59233000", "longitude": "-73.58068000"}, {"name": "Pomona", "countryCode": "US", "stateCode": "NY", "latitude": "41.16704000", "longitude": "-74.04320000"}, {"name": "Port Byron", "countryCode": "US", "stateCode": "NY", "latitude": "43.03451000", "longitude": "-76.62383000"}, {"name": "Port Chester", "countryCode": "US", "stateCode": "NY", "latitude": "41.00176000", "longitude": "-73.66568000"}, {"name": "Port Dickinson", "countryCode": "US", "stateCode": "NY", "latitude": "42.13341000", "longitude": "-75.89631000"}, {"name": "Port Ewen", "countryCode": "US", "stateCode": "NY", "latitude": "41.90537000", "longitude": "-73.97625000"}, {"name": "Port Henry", "countryCode": "US", "stateCode": "NY", "latitude": "44.04839000", "longitude": "-73.45985000"}, {"name": "Port Jefferson", "countryCode": "US", "stateCode": "NY", "latitude": "40.94649000", "longitude": "-73.06927000"}, {"name": "Port Jefferson Station", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.04733000"}, {"name": "Port Jervis", "countryCode": "US", "stateCode": "NY", "latitude": "41.37509000", "longitude": "-74.69266000"}, {"name": "Port Morris", "countryCode": "US", "stateCode": "NY", "latitude": "40.80149000", "longitude": "-73.90958000"}, {"name": "Port Richmond", "countryCode": "US", "stateCode": "NY", "latitude": "40.63316000", "longitude": "-74.13653000"}, {"name": "Port Washington", "countryCode": "US", "stateCode": "NY", "latitude": "40.82566000", "longitude": "-73.69819000"}, {"name": "Port Washington North", "countryCode": "US", "stateCode": "NY", "latitude": "40.84482000", "longitude": "-73.70180000"}, {"name": "Portland", "countryCode": "US", "stateCode": "NY", "latitude": "42.37978000", "longitude": "-79.46755000"}, {"name": "Potsdam", "countryCode": "US", "stateCode": "NY", "latitude": "44.66978000", "longitude": "-74.98131000"}, {"name": "Poughkeepsie", "countryCode": "US", "stateCode": "NY", "latitude": "41.70037000", "longitude": "-73.92097000"}, {"name": "Pound Ridge", "countryCode": "US", "stateCode": "NY", "latitude": "41.20871000", "longitude": "-73.57485000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.56701000", "longitude": "-76.12770000"}, {"name": "Purchase", "countryCode": "US", "stateCode": "NY", "latitude": "41.04093000", "longitude": "-73.71457000"}, {"name": "Putnam County", "countryCode": "US", "stateCode": "NY", "latitude": "41.42666000", "longitude": "-73.74951000"}, {"name": "Putnam Lake", "countryCode": "US", "stateCode": "NY", "latitude": "41.46204000", "longitude": "-73.54624000"}, {"name": "Queens", "countryCode": "US", "stateCode": "NY", "latitude": "40.68149000", "longitude": "-73.83652000"}, {"name": "Queens County", "countryCode": "US", "stateCode": "NY", "latitude": "40.65749000", "longitude": "-73.83875000"}, {"name": "Queens Village", "countryCode": "US", "stateCode": "NY", "latitude": "40.72677000", "longitude": "-73.74152000"}, {"name": "Queensbury", "countryCode": "US", "stateCode": "NY", "latitude": "43.37729000", "longitude": "-73.61317000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.16201000", "longitude": "-78.97532000"}, {"name": "Ransomville", "countryCode": "US", "stateCode": "NY", "latitude": "43.23867000", "longitude": "-78.90976000"}, {"name": "Rapids", "countryCode": "US", "stateCode": "NY", "latitude": "43.09839000", "longitude": "-78.64086000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.46841000", "longitude": "-73.81624000"}, {"name": "Red Hook", "countryCode": "US", "stateCode": "NY", "latitude": "41.99509000", "longitude": "-73.87541000"}, {"name": "Red Oaks Mill", "countryCode": "US", "stateCode": "NY", "latitude": "41.65565000", "longitude": "-73.87486000"}, {"name": "Rego Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.72649000", "longitude": "-73.85264000"}, {"name": "Remsenburg-Speonk", "countryCode": "US", "stateCode": "NY", "latitude": "40.82642000", "longitude": "-72.69673000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.64258000", "longitude": "-73.74290000"}, {"name": "Rensselaer County", "countryCode": "US", "stateCode": "NY", "latitude": "42.71105000", "longitude": "-73.50972000"}, {"name": "Rhinebeck", "countryCode": "US", "stateCode": "NY", "latitude": "41.92676000", "longitude": "-73.91264000"}, {"name": "Richfield Springs", "countryCode": "US", "stateCode": "NY", "latitude": "42.85341000", "longitude": "-74.98543000"}, {"name": "Richland", "countryCode": "US", "stateCode": "NY", "latitude": "43.56951000", "longitude": "-76.04770000"}, {"name": "Richmond County", "countryCode": "US", "stateCode": "NY", "latitude": "40.58344000", "longitude": "-74.14959000"}, {"name": "Richmond Hill", "countryCode": "US", "stateCode": "NY", "latitude": "40.69983000", "longitude": "-73.83125000"}, {"name": "Ridge", "countryCode": "US", "stateCode": "NY", "latitude": "40.89399000", "longitude": "-72.89594000"}, {"name": "Ridgewood", "countryCode": "US", "stateCode": "NY", "latitude": "40.70010000", "longitude": "-73.90569000"}, {"name": "Riverdale", "countryCode": "US", "stateCode": "NY", "latitude": "40.90056000", "longitude": "-73.90639000"}, {"name": "Riverhead", "countryCode": "US", "stateCode": "NY", "latitude": "40.91704000", "longitude": "-72.66204000"}, {"name": "Riverside", "countryCode": "US", "stateCode": "NY", "latitude": "40.88121000", "longitude": "-72.67787000"}, {"name": "Rochester", "countryCode": "US", "stateCode": "NY", "latitude": "43.15478000", "longitude": "-77.61556000"}, {"name": "Rock Hill", "countryCode": "US", "stateCode": "NY", "latitude": "41.62593000", "longitude": "-74.59766000"}, {"name": "Rockaway Point", "countryCode": "US", "stateCode": "NY", "latitude": "40.56066000", "longitude": "-73.91514000"}, {"name": "Rockland County", "countryCode": "US", "stateCode": "NY", "latitude": "41.15243000", "longitude": "-74.02409000"}, {"name": "Rockville Centre", "countryCode": "US", "stateCode": "NY", "latitude": "40.65871000", "longitude": "-73.64124000"}, {"name": "Rocky Point", "countryCode": "US", "stateCode": "NY", "latitude": "40.95260000", "longitude": "-72.********"}, {"name": "Roessleville", "countryCode": "US", "stateCode": "NY", "latitude": "42.********", "longitude": "-73.********"}, {"name": "Rome", "countryCode": "US", "stateCode": "NY", "latitude": "43.********", "longitude": "-75.********"}, {"name": "Ronkonko<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.********"}, {"name": "Rosebank", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-74.********"}, {"name": "Rosedale", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.********"}, {"name": "Rosendale Village", "countryCode": "US", "stateCode": "NY", "latitude": "41.********", "longitude": "-74.********"}, {"name": "Roslyn", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.********"}, {"name": "Roslyn Estates", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.********"}, {"name": "Roslyn Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.********"}, {"name": "Roslyn Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-73.********"}, {"name": "Rossville", "countryCode": "US", "stateCode": "NY", "latitude": "40.********", "longitude": "-74.********"}, {"name": "Rotterdam", "countryCode": "US", "stateCode": "NY", "latitude": "42.78702000", "longitude": "-73.97096000"}, {"name": "Rouses Point", "countryCode": "US", "stateCode": "NY", "latitude": "44.99393000", "longitude": "-73.36486000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.98065000", "longitude": "-73.68374000"}, {"name": "Rye Brook", "countryCode": "US", "stateCode": "NY", "latitude": "41.01926000", "longitude": "-73.68346000"}, {"name": "Sackets Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "43.94617000", "longitude": "-76.11909000"}, {"name": "Sag Harbor", "countryCode": "US", "stateCode": "NY", "latitude": "40.99788000", "longitude": "-72.29258000"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.08034000", "longitude": "-78.47502000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.87899000", "longitude": "-73.15678000"}, {"name": "Saint <PERSON>ville", "countryCode": "US", "stateCode": "NY", "latitude": "42.99813000", "longitude": "-74.68292000"}, {"name": "Salamanca", "countryCode": "US", "stateCode": "NY", "latitude": "42.15784000", "longitude": "-78.71503000"}, {"name": "Salisbury", "countryCode": "US", "stateCode": "NY", "latitude": "40.74566000", "longitude": "-73.56013000"}, {"name": "Sanborn", "countryCode": "US", "stateCode": "NY", "latitude": "43.13672000", "longitude": "-78.88476000"}, {"name": "Sands Point", "countryCode": "US", "stateCode": "NY", "latitude": "40.85177000", "longitude": "-73.71874000"}, {"name": "Saranac Lake", "countryCode": "US", "stateCode": "NY", "latitude": "44.32950000", "longitude": "-74.13127000"}, {"name": "Saratoga County", "countryCode": "US", "stateCode": "NY", "latitude": "43.10738000", "longitude": "-73.86390000"}, {"name": "Saratoga Springs", "countryCode": "US", "stateCode": "NY", "latitude": "43.08313000", "longitude": "-73.78457000"}, {"name": "Saugerties", "countryCode": "US", "stateCode": "NY", "latitude": "42.07759000", "longitude": "-73.95291000"}, {"name": "Saugerties South", "countryCode": "US", "stateCode": "NY", "latitude": "42.06139000", "longitude": "-73.95067000"}, {"name": "Sayville", "countryCode": "US", "stateCode": "NY", "latitude": "40.73593000", "longitude": "-73.08206000"}, {"name": "Scarsdale", "countryCode": "US", "stateCode": "NY", "latitude": "41.00510000", "longitude": "-73.78458000"}, {"name": "Schenectady", "countryCode": "US", "stateCode": "NY", "latitude": "42.81424000", "longitude": "-73.93957000"}, {"name": "Schenectady County", "countryCode": "US", "stateCode": "NY", "latitude": "42.81812000", "longitude": "-74.05857000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.66591000", "longitude": "-74.30958000"}, {"name": "Schoharie County", "countryCode": "US", "stateCode": "NY", "latitude": "42.58822000", "longitude": "-74.44212000"}, {"name": "Schuyler County", "countryCode": "US", "stateCode": "NY", "latitude": "42.39380000", "longitude": "-76.87518000"}, {"name": "Sc<PERSON>ylerville", "countryCode": "US", "stateCode": "NY", "latitude": "43.10008000", "longitude": "-73.58178000"}, {"name": "Scotchtown", "countryCode": "US", "stateCode": "NY", "latitude": "41.48148000", "longitude": "-74.36015000"}, {"name": "Scotia", "countryCode": "US", "stateCode": "NY", "latitude": "42.82647000", "longitude": "-73.96429000"}, {"name": "Scottsville", "countryCode": "US", "stateCode": "NY", "latitude": "43.02590000", "longitude": "-77.74528000"}, {"name": "Sea Cliff", "countryCode": "US", "stateCode": "NY", "latitude": "40.84899000", "longitude": "-73.64485000"}, {"name": "<PERSON>ford", "countryCode": "US", "stateCode": "NY", "latitude": "40.66593000", "longitude": "-73.48818000"}, {"name": "Searingtown", "countryCode": "US", "stateCode": "NY", "latitude": "40.77482000", "longitude": "-73.65568000"}, {"name": "Seaside", "countryCode": "US", "stateCode": "NY", "latitude": "40.58316000", "longitude": "-73.82819000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.86649000", "longitude": "-73.03566000"}, {"name": "Seneca County", "countryCode": "US", "stateCode": "NY", "latitude": "42.78108000", "longitude": "-76.82378000"}, {"name": "Seneca Falls", "countryCode": "US", "stateCode": "NY", "latitude": "42.91062000", "longitude": "-76.79662000"}, {"name": "<PERSON>lls", "countryCode": "US", "stateCode": "NY", "latitude": "43.12007000", "longitude": "-76.28632000"}, {"name": "Setauket-East Setauket", "countryCode": "US", "stateCode": "NY", "latitude": "40.93064000", "longitude": "-73.10179000"}, {"name": "Sheepshead Bay", "countryCode": "US", "stateCode": "NY", "latitude": "40.59122000", "longitude": "-73.94458000"}, {"name": "Shelter Island", "countryCode": "US", "stateCode": "NY", "latitude": "41.06815000", "longitude": "-72.33869000"}, {"name": "Shelter Island Heights", "countryCode": "US", "stateCode": "NY", "latitude": "41.08399000", "longitude": "-72.35592000"}, {"name": "Shenorock", "countryCode": "US", "stateCode": "NY", "latitude": "41.33176000", "longitude": "-73.73819000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.67813000", "longitude": "-75.49851000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.07368000", "longitude": "-75.59824000"}, {"name": "Shinnecock Hills", "countryCode": "US", "stateCode": "NY", "latitude": "40.89093000", "longitude": "-72.46370000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.80149000", "longitude": "-72.86760000"}, {"name": "Shokan", "countryCode": "US", "stateCode": "NY", "latitude": "41.97343000", "longitude": "-74.21209000"}, {"name": "Shortsville", "countryCode": "US", "stateCode": "NY", "latitude": "42.95590000", "longitude": "-77.22081000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.32759000", "longitude": "-73.81958000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.31480000", "longitude": "-75.39157000"}, {"name": "Silver Creek", "countryCode": "US", "stateCode": "NY", "latitude": "42.54423000", "longitude": "-79.16671000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.94701000", "longitude": "-76.42910000"}, {"name": "Sleepy Hollow", "countryCode": "US", "stateCode": "NY", "latitude": "41.08565000", "longitude": "-73.85847000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.89339000", "longitude": "-78.79392000"}, {"name": "Sloatsburg", "countryCode": "US", "stateCode": "NY", "latitude": "41.15454000", "longitude": "-74.19292000"}, {"name": "Smithtown", "countryCode": "US", "stateCode": "NY", "latitude": "40.85593000", "longitude": "-73.20067000"}, {"name": "Sodus", "countryCode": "US", "stateCode": "NY", "latitude": "43.23784000", "longitude": "-77.06136000"}, {"name": "Solvay", "countryCode": "US", "stateCode": "NY", "latitude": "43.05812000", "longitude": "-76.20743000"}, {"name": "Sound Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.95621000", "longitude": "-72.96788000"}, {"name": "South Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.58329000", "longitude": "-74.07609000"}, {"name": "South Blooming Grove", "countryCode": "US", "stateCode": "NY", "latitude": "41.37337000", "longitude": "-74.17843000"}, {"name": "South Corning", "countryCode": "US", "stateCode": "NY", "latitude": "42.12174000", "longitude": "-77.03719000"}, {"name": "South Fallsburg", "countryCode": "US", "stateCode": "NY", "latitude": "41.72065000", "longitude": "-74.63433000"}, {"name": "South Farmingdale", "countryCode": "US", "stateCode": "NY", "latitude": "40.72066000", "longitude": "-73.44012000"}, {"name": "South Floral Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.71427000", "longitude": "-73.70013000"}, {"name": "South Glens Falls", "countryCode": "US", "stateCode": "NY", "latitude": "43.29924000", "longitude": "-73.63512000"}, {"name": "South Hempstead", "countryCode": "US", "stateCode": "NY", "latitude": "40.68094000", "longitude": "-73.61541000"}, {"name": "South Hill", "countryCode": "US", "stateCode": "NY", "latitude": "42.42924000", "longitude": "-76.49494000"}, {"name": "South Huntington", "countryCode": "US", "stateCode": "NY", "latitude": "40.82371000", "longitude": "-73.39873000"}, {"name": "South Lockport", "countryCode": "US", "stateCode": "NY", "latitude": "43.15006000", "longitude": "-78.69670000"}, {"name": "South Nyack", "countryCode": "US", "stateCode": "NY", "latitude": "41.08315000", "longitude": "-73.92014000"}, {"name": "South Valley Stream", "countryCode": "US", "stateCode": "NY", "latitude": "40.65594000", "longitude": "-73.71763000"}, {"name": "Southampton", "countryCode": "US", "stateCode": "NY", "latitude": "40.88427000", "longitude": "-72.38953000"}, {"name": "Southold", "countryCode": "US", "stateCode": "NY", "latitude": "41.06482000", "longitude": "-72.42620000"}, {"name": "Southport", "countryCode": "US", "stateCode": "NY", "latitude": "42.05480000", "longitude": "-76.81912000"}, {"name": "Spackenkill", "countryCode": "US", "stateCode": "NY", "latitude": "41.65593000", "longitude": "-73.91347000"}, {"name": "Sparkill", "countryCode": "US", "stateCode": "NY", "latitude": "41.03121000", "longitude": "-73.92708000"}, {"name": "Spencerport", "countryCode": "US", "stateCode": "NY", "latitude": "43.18645000", "longitude": "-77.80390000"}, {"name": "Spring Valley", "countryCode": "US", "stateCode": "NY", "latitude": "41.11315000", "longitude": "-74.04375000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "NY", "latitude": "42.83618000", "longitude": "-74.85348000"}, {"name": "Springfield Gardens", "countryCode": "US", "stateCode": "NY", "latitude": "40.66312000", "longitude": "-73.76221000"}, {"name": "Springs", "countryCode": "US", "stateCode": "NY", "latitude": "41.01621000", "longitude": "-72.15924000"}, {"name": "Springville", "countryCode": "US", "stateCode": "NY", "latitude": "42.50840000", "longitude": "-78.66725000"}, {"name": "Spuyten Duyvil", "countryCode": "US", "stateCode": "NY", "latitude": "40.88121000", "longitude": "-73.91736000"}, {"name": "St. Lawrence County", "countryCode": "US", "stateCode": "NY", "latitude": "44.50062000", "longitude": "-75.11631000"}, {"name": "Stamford", "countryCode": "US", "stateCode": "NY", "latitude": "42.40730000", "longitude": "-74.61432000"}, {"name": "Stapleton", "countryCode": "US", "stateCode": "NY", "latitude": "40.62649000", "longitude": "-74.07764000"}, {"name": "Staten Island", "countryCode": "US", "stateCode": "NY", "latitude": "40.56233000", "longitude": "-74.13986000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.77455000", "longitude": "-73.90375000"}, {"name": "Steuben County", "countryCode": "US", "stateCode": "NY", "latitude": "42.26781000", "longitude": "-77.38380000"}, {"name": "Stewart Manor", "countryCode": "US", "stateCode": "NY", "latitude": "40.71927000", "longitude": "-73.68846000"}, {"name": "Stillwater", "countryCode": "US", "stateCode": "NY", "latitude": "42.93841000", "longitude": "-73.65317000"}, {"name": "Stone Ridge", "countryCode": "US", "stateCode": "NY", "latitude": "41.85315000", "longitude": "-74.13903000"}, {"name": "Stony Brook", "countryCode": "US", "stateCode": "NY", "latitude": "40.92565000", "longitude": "-73.14094000"}, {"name": "Stony Point", "countryCode": "US", "stateCode": "NY", "latitude": "41.22954000", "longitude": "-73.98708000"}, {"name": "Stottville", "countryCode": "US", "stateCode": "NY", "latitude": "42.28620000", "longitude": "-73.73873000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.11482000", "longitude": "-74.14959000"}, {"name": "Suffolk County", "countryCode": "US", "stateCode": "NY", "latitude": "40.94046000", "longitude": "-72.68524000"}, {"name": "Sullivan County", "countryCode": "US", "stateCode": "NY", "latitude": "41.71642000", "longitude": "-74.76814000"}, {"name": "Sunnyside", "countryCode": "US", "stateCode": "NY", "latitude": "40.73982000", "longitude": "-73.93542000"}, {"name": "Sunset Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.64548000", "longitude": "-74.01241000"}, {"name": "Sylvan Beach", "countryCode": "US", "stateCode": "NY", "latitude": "42.46479000", "longitude": "-77.10830000"}, {"name": "S<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.82621000", "longitude": "-73.50207000"}, {"name": "Syracuse", "countryCode": "US", "stateCode": "NY", "latitude": "43.04812000", "longitude": "-76.14742000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.02204000", "longitude": "-73.94736000"}, {"name": "Tarrytown", "countryCode": "US", "stateCode": "NY", "latitude": "41.07621000", "longitude": "-73.85875000"}, {"name": "Terrace Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.72149000", "longitude": "-73.76930000"}, {"name": "Terryville", "countryCode": "US", "stateCode": "NY", "latitude": "40.90899000", "longitude": "-73.06511000"}, {"name": "The Bronx", "countryCode": "US", "stateCode": "NY", "latitude": "40.84985000", "longitude": "-73.86641000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.21065000", "longitude": "-74.01764000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.78621000", "longitude": "-73.71374000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.12343000", "longitude": "-73.77902000"}, {"name": "Throgs Neck", "countryCode": "US", "stateCode": "NY", "latitude": "40.82260000", "longitude": "-73.81958000"}, {"name": "Ticonderoga", "countryCode": "US", "stateCode": "NY", "latitude": "43.84867000", "longitude": "-73.42345000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.82898000", "longitude": "-74.06848000"}, {"name": "Tioga County", "countryCode": "US", "stateCode": "NY", "latitude": "42.17030000", "longitude": "-76.30632000"}, {"name": "Tivoli", "countryCode": "US", "stateCode": "NY", "latitude": "42.05842000", "longitude": "-73.90930000"}, {"name": "Tompkins County", "countryCode": "US", "stateCode": "NY", "latitude": "42.45202000", "longitude": "-76.47366000"}, {"name": "Tompkinsville", "countryCode": "US", "stateCode": "NY", "latitude": "40.63812000", "longitude": "-74.07795000"}, {"name": "Tonawanda", "countryCode": "US", "stateCode": "NY", "latitude": "43.02033000", "longitude": "-78.88031000"}, {"name": "Town Line", "countryCode": "US", "stateCode": "NY", "latitude": "42.89061000", "longitude": "-78.57780000"}, {"name": "Tremont", "countryCode": "US", "stateCode": "NY", "latitude": "40.84954000", "longitude": "-73.90569000"}, {"name": "Tribes Hill", "countryCode": "US", "stateCode": "NY", "latitude": "42.95535000", "longitude": "-74.28513000"}, {"name": "Troy", "countryCode": "US", "stateCode": "NY", "latitude": "42.72841000", "longitude": "-73.69179000"}, {"name": "Trumansburg", "countryCode": "US", "stateCode": "NY", "latitude": "42.54229000", "longitude": "-76.66606000"}, {"name": "<PERSON><PERSON>ah<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.95038000", "longitude": "-73.82736000"}, {"name": "Tupper Lake", "countryCode": "US", "stateCode": "NY", "latitude": "44.22395000", "longitude": "-74.46406000"}, {"name": "Ulster County", "countryCode": "US", "stateCode": "NY", "latitude": "41.88815000", "longitude": "-74.25857000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.32536000", "longitude": "-75.31240000"}, {"name": "Union Springs", "countryCode": "US", "stateCode": "NY", "latitude": "42.83979000", "longitude": "-76.69328000"}, {"name": "Uniondale", "countryCode": "US", "stateCode": "NY", "latitude": "40.70038000", "longitude": "-73.59291000"}, {"name": "Unionport", "countryCode": "US", "stateCode": "NY", "latitude": "40.82732000", "longitude": "-73.85013000"}, {"name": "University Gardens", "countryCode": "US", "stateCode": "NY", "latitude": "40.77732000", "longitude": "-73.72263000"}, {"name": "University Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.86010000", "longitude": "-73.90930000"}, {"name": "Upper Brookville", "countryCode": "US", "stateCode": "NY", "latitude": "40.83871000", "longitude": "-73.56513000"}, {"name": "Upper Nyack", "countryCode": "US", "stateCode": "NY", "latitude": "41.10704000", "longitude": "-73.92014000"}, {"name": "Utica", "countryCode": "US", "stateCode": "NY", "latitude": "43.10090000", "longitude": "-75.23266000"}, {"name": "Vails Gate", "countryCode": "US", "stateCode": "NY", "latitude": "41.45426000", "longitude": "-74.05764000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.41342000", "longitude": "-73.67317000"}, {"name": "Valhalla", "countryCode": "US", "stateCode": "NY", "latitude": "41.07482000", "longitude": "-73.77513000"}, {"name": "Valley Cottage", "countryCode": "US", "stateCode": "NY", "latitude": "41.11815000", "longitude": "-73.95542000"}, {"name": "Valley Stream", "countryCode": "US", "stateCode": "NY", "latitude": "40.66427000", "longitude": "-73.70846000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.84843000", "longitude": "-73.86375000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.07951000", "longitude": "-75.53934000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.25287000", "longitude": "-73.95986000"}, {"name": "Victor", "countryCode": "US", "stateCode": "NY", "latitude": "42.98256000", "longitude": "-77.40888000"}, {"name": "Village Green", "countryCode": "US", "stateCode": "NY", "latitude": "43.13340000", "longitude": "-76.31299000"}, {"name": "Village of the Branch", "countryCode": "US", "stateCode": "NY", "latitude": "40.85621000", "longitude": "-73.18733000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.13648000", "longitude": "-74.08236000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.34285000", "longitude": "-76.35771000"}, {"name": "Voorheesville", "countryCode": "US", "stateCode": "NY", "latitude": "42.65397000", "longitude": "-73.92874000"}, {"name": "Wading River", "countryCode": "US", "stateCode": "NY", "latitude": "40.95038000", "longitude": "-72.84260000"}, {"name": "Wakefield", "countryCode": "US", "stateCode": "NY", "latitude": "40.89788000", "longitude": "-73.85236000"}, {"name": "Walden", "countryCode": "US", "stateCode": "NY", "latitude": "41.56120000", "longitude": "-74.18848000"}, {"name": "Wallkill", "countryCode": "US", "stateCode": "NY", "latitude": "41.60565000", "longitude": "-74.18404000"}, {"name": "Walton", "countryCode": "US", "stateCode": "NY", "latitude": "42.16953000", "longitude": "-75.12934000"}, {"name": "Walton Park", "countryCode": "US", "stateCode": "NY", "latitude": "41.30982000", "longitude": "-74.22904000"}, {"name": "Wampsville", "countryCode": "US", "stateCode": "NY", "latitude": "43.07535000", "longitude": "-75.70685000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.74617000", "longitude": "-78.90309000"}, {"name": "Wantagh", "countryCode": "US", "stateCode": "NY", "latitude": "40.68371000", "longitude": "-73.51013000"}, {"name": "Wappingers Falls", "countryCode": "US", "stateCode": "NY", "latitude": "41.59648000", "longitude": "-73.91097000"}, {"name": "Warren County", "countryCode": "US", "stateCode": "NY", "latitude": "43.56098000", "longitude": "-73.84601000"}, {"name": "Warrensburg", "countryCode": "US", "stateCode": "NY", "latitude": "43.49674000", "longitude": "-73.77623000"}, {"name": "Warsaw", "countryCode": "US", "stateCode": "NY", "latitude": "42.74006000", "longitude": "-78.13279000"}, {"name": "Warwick", "countryCode": "US", "stateCode": "NY", "latitude": "41.25648000", "longitude": "-74.35988000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "NY", "latitude": "43.31370000", "longitude": "-73.43076000"}, {"name": "Washington Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.85010000", "longitude": "-73.93541000"}, {"name": "Washington Mills", "countryCode": "US", "stateCode": "NY", "latitude": "43.05007000", "longitude": "-75.27294000"}, {"name": "Washingtonville", "countryCode": "US", "stateCode": "NY", "latitude": "41.42787000", "longitude": "-74.16598000"}, {"name": "Watchtower", "countryCode": "US", "stateCode": "NY", "latitude": "41.63776000", "longitude": "-74.26027000"}, {"name": "Water Mill", "countryCode": "US", "stateCode": "NY", "latitude": "40.91959000", "longitude": "-72.34274000"}, {"name": "Waterford", "countryCode": "US", "stateCode": "NY", "latitude": "42.79258000", "longitude": "-73.68123000"}, {"name": "Waterloo", "countryCode": "US", "stateCode": "NY", "latitude": "42.90479000", "longitude": "-76.86274000"}, {"name": "Watertown", "countryCode": "US", "stateCode": "NY", "latitude": "43.97478000", "longitude": "-75.91076000"}, {"name": "Waterville", "countryCode": "US", "stateCode": "NY", "latitude": "42.93118000", "longitude": "-75.37989000"}, {"name": "Watervliet", "countryCode": "US", "stateCode": "NY", "latitude": "42.73008000", "longitude": "-73.70123000"}, {"name": "Watkins Glen", "countryCode": "US", "stateCode": "NY", "latitude": "42.38063000", "longitude": "-76.87329000"}, {"name": "Waverly", "countryCode": "US", "stateCode": "NY", "latitude": "42.01035000", "longitude": "-76.52717000"}, {"name": "Wawarsing", "countryCode": "US", "stateCode": "NY", "latitude": "41.75898000", "longitude": "-74.35738000"}, {"name": "Wayland", "countryCode": "US", "stateCode": "NY", "latitude": "42.56784000", "longitude": "-77.58971000"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "NY", "latitude": "43.06588000", "longitude": "-76.97845000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.21229000", "longitude": "-77.42999000"}, {"name": "Weedsport", "countryCode": "US", "stateCode": "NY", "latitude": "43.04868000", "longitude": "-76.56272000"}, {"name": "Wellsville", "countryCode": "US", "stateCode": "NY", "latitude": "42.12201000", "longitude": "-77.94806000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "41.15926000", "longitude": "-74.06986000"}, {"name": "West Albany", "countryCode": "US", "stateCode": "NY", "latitude": "42.68313000", "longitude": "-73.77845000"}, {"name": "West Babylon", "countryCode": "US", "stateCode": "NY", "latitude": "40.71816000", "longitude": "-73.35429000"}, {"name": "West Bay Shore", "countryCode": "US", "stateCode": "NY", "latitude": "40.70871000", "longitude": "-73.28123000"}, {"name": "West Carthage", "countryCode": "US", "stateCode": "NY", "latitude": "43.97423000", "longitude": "-75.61519000"}, {"name": "West Elmira", "countryCode": "US", "stateCode": "NY", "latitude": "42.07813000", "longitude": "-76.84524000"}, {"name": "West End", "countryCode": "US", "stateCode": "NY", "latitude": "42.46869000", "longitude": "-75.09378000"}, {"name": "West Glens Falls", "countryCode": "US", "stateCode": "NY", "latitude": "43.30007000", "longitude": "-73.68401000"}, {"name": "West Haverstraw", "countryCode": "US", "stateCode": "NY", "latitude": "41.20954000", "longitude": "-73.98542000"}, {"name": "West Hempstead", "countryCode": "US", "stateCode": "NY", "latitude": "40.70482000", "longitude": "-73.65013000"}, {"name": "West Hills", "countryCode": "US", "stateCode": "NY", "latitude": "40.81621000", "longitude": "-73.43234000"}, {"name": "West Hurley", "countryCode": "US", "stateCode": "NY", "latitude": "41.99731000", "longitude": "-74.10486000"}, {"name": "West Islip", "countryCode": "US", "stateCode": "NY", "latitude": "40.70621000", "longitude": "-73.30623000"}, {"name": "West Nyack", "countryCode": "US", "stateCode": "NY", "latitude": "41.09649000", "longitude": "-73.97292000"}, {"name": "West Point", "countryCode": "US", "stateCode": "NY", "latitude": "41.39148000", "longitude": "-73.95597000"}, {"name": "West Sand Lake", "countryCode": "US", "stateCode": "NY", "latitude": "42.64341000", "longitude": "-73.60873000"}, {"name": "West Sayville", "countryCode": "US", "stateCode": "NY", "latitude": "40.72788000", "longitude": "-73.09761000"}, {"name": "West Seneca", "countryCode": "US", "stateCode": "NY", "latitude": "42.85006000", "longitude": "-78.79975000"}, {"name": "Westbury", "countryCode": "US", "stateCode": "NY", "latitude": "40.75566000", "longitude": "-73.58763000"}, {"name": "Westchester County", "countryCode": "US", "stateCode": "NY", "latitude": "41.15148000", "longitude": "-73.75339000"}, {"name": "Westerleigh", "countryCode": "US", "stateCode": "NY", "latitude": "40.62121000", "longitude": "-74.13181000"}, {"name": "Westfield", "countryCode": "US", "stateCode": "NY", "latitude": "42.32228000", "longitude": "-79.57810000"}, {"name": "Westhampton", "countryCode": "US", "stateCode": "NY", "latitude": "40.82454000", "longitude": "-72.66621000"}, {"name": "Westhampton Beach", "countryCode": "US", "stateCode": "NY", "latitude": "40.80316000", "longitude": "-72.61454000"}, {"name": "Westmere", "countryCode": "US", "stateCode": "NY", "latitude": "42.69119000", "longitude": "-73.86873000"}, {"name": "Weston Mills", "countryCode": "US", "stateCode": "NY", "latitude": "42.07590000", "longitude": "-78.37252000"}, {"name": "Westvale", "countryCode": "US", "stateCode": "NY", "latitude": "43.04757000", "longitude": "-76.22048000"}, {"name": "Wheatley Heights", "countryCode": "US", "stateCode": "NY", "latitude": "40.76371000", "longitude": "-73.36984000"}, {"name": "White Plains", "countryCode": "US", "stateCode": "NY", "latitude": "41.03399000", "longitude": "-73.76291000"}, {"name": "Whitehall", "countryCode": "US", "stateCode": "NY", "latitude": "43.55562000", "longitude": "-73.40372000"}, {"name": "Whitesboro", "countryCode": "US", "stateCode": "NY", "latitude": "43.12201000", "longitude": "-75.29156000"}, {"name": "Whitestone", "countryCode": "US", "stateCode": "NY", "latitude": "40.79455000", "longitude": "-73.81847000"}, {"name": "Williamsburg", "countryCode": "US", "stateCode": "NY", "latitude": "40.71427000", "longitude": "-73.95347000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.22395000", "longitude": "-77.18609000"}, {"name": "Williamsville", "countryCode": "US", "stateCode": "NY", "latitude": "42.96395000", "longitude": "-78.73781000"}, {"name": "Williston Park", "countryCode": "US", "stateCode": "NY", "latitude": "40.75649000", "longitude": "-73.64485000"}, {"name": "Willowbrook", "countryCode": "US", "stateCode": "NY", "latitude": "40.60316000", "longitude": "-74.13848000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.30978000", "longitude": "-78.82615000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "43.22062000", "longitude": "-76.81496000"}, {"name": "Woodbury", "countryCode": "US", "stateCode": "NY", "latitude": "41.36454000", "longitude": "-74.10598000"}, {"name": "Woodhaven", "countryCode": "US", "stateCode": "NY", "latitude": "40.68927000", "longitude": "-73.85791000"}, {"name": "Woodlawn", "countryCode": "US", "stateCode": "NY", "latitude": "40.89816000", "longitude": "-73.86736000"}, {"name": "Woodmere", "countryCode": "US", "stateCode": "NY", "latitude": "40.63205000", "longitude": "-73.71263000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.54344000", "longitude": "-74.19764000"}, {"name": "Woodside", "countryCode": "US", "stateCode": "NY", "latitude": "40.74538000", "longitude": "-73.90541000"}, {"name": "Woodstock", "countryCode": "US", "stateCode": "NY", "latitude": "42.04092000", "longitude": "-74.11820000"}, {"name": "Worcester", "countryCode": "US", "stateCode": "NY", "latitude": "42.59146000", "longitude": "-74.75043000"}, {"name": "Wurtsboro", "countryCode": "US", "stateCode": "NY", "latitude": "41.57676000", "longitude": "-74.48710000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.75399000", "longitude": "-73.36040000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.94149000", "longitude": "-73.79902000"}, {"name": "Wynantskill", "countryCode": "US", "stateCode": "NY", "latitude": "42.69675000", "longitude": "-73.64428000"}, {"name": "Wyoming County", "countryCode": "US", "stateCode": "NY", "latitude": "42.70238000", "longitude": "-78.22444000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "40.83677000", "longitude": "-72.91705000"}, {"name": "Yates County", "countryCode": "US", "stateCode": "NY", "latitude": "42.63344000", "longitude": "-77.10546000"}, {"name": "Yonkers", "countryCode": "US", "stateCode": "NY", "latitude": "40.93121000", "longitude": "-73.89875000"}, {"name": "Yorkshire", "countryCode": "US", "stateCode": "NY", "latitude": "42.53006000", "longitude": "-78.47280000"}, {"name": "Yorktown Heights", "countryCode": "US", "stateCode": "NY", "latitude": "41.27093000", "longitude": "-73.77763000"}, {"name": "Yorkville", "countryCode": "US", "stateCode": "NY", "latitude": "43.11285000", "longitude": "-75.27100000"}, {"name": "Youngstown", "countryCode": "US", "stateCode": "NY", "latitude": "43.24728000", "longitude": "-79.05005000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY", "latitude": "42.01676000", "longitude": "-74.07625000"}]