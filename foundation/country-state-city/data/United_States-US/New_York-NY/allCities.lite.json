[{"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Adams Center", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Airmont", "countryCode": "US", "stateCode": "NY"}, {"name": "Akron", "countryCode": "US", "stateCode": "NY"}, {"name": "Alabama", "countryCode": "US", "stateCode": "NY"}, {"name": "Albany", "countryCode": "US", "stateCode": "NY"}, {"name": "Albany County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Albion", "countryCode": "US", "stateCode": "NY"}, {"name": "Alden", "countryCode": "US", "stateCode": "NY"}, {"name": "Alexandria Bay", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Allegany", "countryCode": "US", "stateCode": "NY"}, {"name": "Allegany County", "countryCode": "US", "stateCode": "NY"}, {"name": "Altamont", "countryCode": "US", "stateCode": "NY"}, {"name": "Amagansett", "countryCode": "US", "stateCode": "NY"}, {"name": "Amherst", "countryCode": "US", "stateCode": "NY"}, {"name": "Amityville", "countryCode": "US", "stateCode": "NY"}, {"name": "Amsterdam", "countryCode": "US", "stateCode": "NY"}, {"name": "Andover", "countryCode": "US", "stateCode": "NY"}, {"name": "Angola", "countryCode": "US", "stateCode": "NY"}, {"name": "Angola on the Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "Apalachin", "countryCode": "US", "stateCode": "NY"}, {"name": "Aquebogue", "countryCode": "US", "stateCode": "NY"}, {"name": "Arcade", "countryCode": "US", "stateCode": "NY"}, {"name": "Ardsley", "countryCode": "US", "stateCode": "NY"}, {"name": "Arlington", "countryCode": "US", "stateCode": "NY"}, {"name": "Armonk", "countryCode": "US", "stateCode": "NY"}, {"name": "Arrochar", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Astoria", "countryCode": "US", "stateCode": "NY"}, {"name": "Athens", "countryCode": "US", "stateCode": "NY"}, {"name": "Atlantic Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "Attica", "countryCode": "US", "stateCode": "NY"}, {"name": "Auburn", "countryCode": "US", "stateCode": "NY"}, {"name": "Augusta", "countryCode": "US", "stateCode": "NY"}, {"name": "Averill Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Avon", "countryCode": "US", "stateCode": "NY"}, {"name": "Babylon", "countryCode": "US", "stateCode": "NY"}, {"name": "Bainbridge", "countryCode": "US", "stateCode": "NY"}, {"name": "Baiting Hollow", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Baldwin Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "Baldwinsville", "countryCode": "US", "stateCode": "NY"}, {"name": "Ballston Spa", "countryCode": "US", "stateCode": "NY"}, {"name": "Balmville", "countryCode": "US", "stateCode": "NY"}, {"name": "Bardonia", "countryCode": "US", "stateCode": "NY"}, {"name": "Barnum Island", "countryCode": "US", "stateCode": "NY"}, {"name": "Batavia", "countryCode": "US", "stateCode": "NY"}, {"name": "Bath", "countryCode": "US", "stateCode": "NY"}, {"name": "Bath Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "Baxter Estates", "countryCode": "US", "stateCode": "NY"}, {"name": "Bay Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Bay Shore", "countryCode": "US", "stateCode": "NY"}, {"name": "Bay Wood", "countryCode": "US", "stateCode": "NY"}, {"name": "Baychester", "countryCode": "US", "stateCode": "NY"}, {"name": "Bayport", "countryCode": "US", "stateCode": "NY"}, {"name": "Bayside", "countryCode": "US", "stateCode": "NY"}, {"name": "Bayville", "countryCode": "US", "stateCode": "NY"}, {"name": "Beacon", "countryCode": "US", "stateCode": "NY"}, {"name": "Beaver Dam Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "Beaverdam Lake-Salisbury Mills", "countryCode": "US", "stateCode": "NY"}, {"name": "Bedford", "countryCode": "US", "stateCode": "NY"}, {"name": "Bedford Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "Bellaire", "countryCode": "US", "stateCode": "NY"}, {"name": "Belle Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Bellerose Terrace", "countryCode": "US", "stateCode": "NY"}, {"name": "Bellmore", "countryCode": "US", "stateCode": "NY"}, {"name": "Bellport", "countryCode": "US", "stateCode": "NY"}, {"name": "Belmont", "countryCode": "US", "stateCode": "NY"}, {"name": "Bensonhurst", "countryCode": "US", "stateCode": "NY"}, {"name": "Bergen", "countryCode": "US", "stateCode": "NY"}, {"name": "Bergen Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Big Flats", "countryCode": "US", "stateCode": "NY"}, {"name": "Billington Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Black River", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Bloomfield", "countryCode": "US", "stateCode": "NY"}, {"name": "Blue Point", "countryCode": "US", "stateCode": "NY"}, {"name": "Bohemia", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Boonville", "countryCode": "US", "stateCode": "NY"}, {"name": "Borough Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Boston", "countryCode": "US", "stateCode": "NY"}, {"name": "Brentwood", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON> Hill", "countryCode": "US", "stateCode": "NY"}, {"name": "Briarcliff Manor", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Bridgehampton", "countryCode": "US", "stateCode": "NY"}, {"name": "Bridgeport", "countryCode": "US", "stateCode": "NY"}, {"name": "Brighton", "countryCode": "US", "stateCode": "NY"}, {"name": "Brighton Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "Brightwaters", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Broad Channel", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Brockport", "countryCode": "US", "stateCode": "NY"}, {"name": "Brocton", "countryCode": "US", "stateCode": "NY"}, {"name": "Bronx", "countryCode": "US", "stateCode": "NY"}, {"name": "Bronxville", "countryCode": "US", "stateCode": "NY"}, {"name": "Brookhaven", "countryCode": "US", "stateCode": "NY"}, {"name": "Brooklyn", "countryCode": "US", "stateCode": "NY"}, {"name": "Brooklyn Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Brookville", "countryCode": "US", "stateCode": "NY"}, {"name": "Broome County", "countryCode": "US", "stateCode": "NY"}, {"name": "Brownsville", "countryCode": "US", "stateCode": "NY"}, {"name": "Brownville", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Buffalo", "countryCode": "US", "stateCode": "NY"}, {"name": "Bushwick", "countryCode": "US", "stateCode": "NY"}, {"name": "Cairo", "countryCode": "US", "stateCode": "NY"}, {"name": "Calcium", "countryCode": "US", "stateCode": "NY"}, {"name": "Caledonia", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Cambria Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Cambridge", "countryCode": "US", "stateCode": "NY"}, {"name": "Camden", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Canandaigua", "countryCode": "US", "stateCode": "NY"}, {"name": "Canarsie", "countryCode": "US", "stateCode": "NY"}, {"name": "Canastota", "countryCode": "US", "stateCode": "NY"}, {"name": "Canisteo", "countryCode": "US", "stateCode": "NY"}, {"name": "Canton", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>e <PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Carmel", "countryCode": "US", "stateCode": "NY"}, {"name": "Carmel Hamlet", "countryCode": "US", "stateCode": "NY"}, {"name": "Carthage", "countryCode": "US", "stateCode": "NY"}, {"name": "Castleton-on-Hudson", "countryCode": "US", "stateCode": "NY"}, {"name": "Catskill", "countryCode": "US", "stateCode": "NY"}, {"name": "Cattaraugus County", "countryCode": "US", "stateCode": "NY"}, {"name": "Cayuga County", "countryCode": "US", "stateCode": "NY"}, {"name": "Cayuga Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Cazenovia", "countryCode": "US", "stateCode": "NY"}, {"name": "Cedarhurst", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Center Moriches", "countryCode": "US", "stateCode": "NY"}, {"name": "Centereach", "countryCode": "US", "stateCode": "NY"}, {"name": "Centerport", "countryCode": "US", "stateCode": "NY"}, {"name": "Central Islip", "countryCode": "US", "stateCode": "NY"}, {"name": "Central Square", "countryCode": "US", "stateCode": "NY"}, {"name": "Central Valley", "countryCode": "US", "stateCode": "NY"}, {"name": "Chadwicks", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Chappaqua", "countryCode": "US", "stateCode": "NY"}, {"name": "Charleston", "countryCode": "US", "stateCode": "NY"}, {"name": "Chatham", "countryCode": "US", "stateCode": "NY"}, {"name": "Chautauqua County", "countryCode": "US", "stateCode": "NY"}, {"name": "Cheektowaga", "countryCode": "US", "stateCode": "NY"}, {"name": "Chelsea", "countryCode": "US", "stateCode": "NY"}, {"name": "Chemung County", "countryCode": "US", "stateCode": "NY"}, {"name": "Chenango Bridge", "countryCode": "US", "stateCode": "NY"}, {"name": "Chenango County", "countryCode": "US", "stateCode": "NY"}, {"name": "Chester", "countryCode": "US", "stateCode": "NY"}, {"name": "Chestnut Ridge", "countryCode": "US", "stateCode": "NY"}, {"name": "Chittenango", "countryCode": "US", "stateCode": "NY"}, {"name": "Churchville", "countryCode": "US", "stateCode": "NY"}, {"name": "City Island", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Clarence Center", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Clifton", "countryCode": "US", "stateCode": "NY"}, {"name": "Clifton Springs", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "NY"}, {"name": "Clintondale", "countryCode": "US", "stateCode": "NY"}, {"name": "Clyde", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Cobleskill", "countryCode": "US", "stateCode": "NY"}, {"name": "Cohoes", "countryCode": "US", "stateCode": "NY"}, {"name": "Cold Spring", "countryCode": "US", "stateCode": "NY"}, {"name": "Cold Spring Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "College Point", "countryCode": "US", "stateCode": "NY"}, {"name": "Colonie", "countryCode": "US", "stateCode": "NY"}, {"name": "Columbia County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Concord", "countryCode": "US", "stateCode": "NY"}, {"name": "Coney Island", "countryCode": "US", "stateCode": "NY"}, {"name": "Congers", "countryCode": "US", "stateCode": "NY"}, {"name": "Constantia", "countryCode": "US", "stateCode": "NY"}, {"name": "Cooperstown", "countryCode": "US", "stateCode": "NY"}, {"name": "Copiague", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Corinth", "countryCode": "US", "stateCode": "NY"}, {"name": "Corning", "countryCode": "US", "stateCode": "NY"}, {"name": "Cornwall", "countryCode": "US", "stateCode": "NY"}, {"name": "Corona", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Cortland County", "countryCode": "US", "stateCode": "NY"}, {"name": "Cortland West", "countryCode": "US", "stateCode": "NY"}, {"name": "Country Knolls", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Crompond", "countryCode": "US", "stateCode": "NY"}, {"name": "Croton-on-Hudson", "countryCode": "US", "stateCode": "NY"}, {"name": "Crown Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Cuba", "countryCode": "US", "stateCode": "NY"}, {"name": "Cumberland Head", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Cypress Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Dansville", "countryCode": "US", "stateCode": "NY"}, {"name": "Deer Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Delaware County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Delhi", "countryCode": "US", "stateCode": "NY"}, {"name": "Delmar", "countryCode": "US", "stateCode": "NY"}, {"name": "De<PERSON>w", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Dix <PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Dobbs Ferry", "countryCode": "US", "stateCode": "NY"}, {"name": "Dolgeville", "countryCode": "US", "stateCode": "NY"}, {"name": "Dongan Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Dover Plains", "countryCode": "US", "stateCode": "NY"}, {"name": "Dryden", "countryCode": "US", "stateCode": "NY"}, {"name": "Dundee", "countryCode": "US", "stateCode": "NY"}, {"name": "Dunkirk", "countryCode": "US", "stateCode": "NY"}, {"name": "Durham", "countryCode": "US", "stateCode": "NY"}, {"name": "Dutchess County", "countryCode": "US", "stateCode": "NY"}, {"name": "Dyker Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "East Atlantic Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "East Aurora", "countryCode": "US", "stateCode": "NY"}, {"name": "East Elmhurst", "countryCode": "US", "stateCode": "NY"}, {"name": "East Farmingdale", "countryCode": "US", "stateCode": "NY"}, {"name": "East Flatbush", "countryCode": "US", "stateCode": "NY"}, {"name": "East Garden City", "countryCode": "US", "stateCode": "NY"}, {"name": "East Glenville", "countryCode": "US", "stateCode": "NY"}, {"name": "East Greenbush", "countryCode": "US", "stateCode": "NY"}, {"name": "East Hampton", "countryCode": "US", "stateCode": "NY"}, {"name": "East Hampton North", "countryCode": "US", "stateCode": "NY"}, {"name": "East Harlem", "countryCode": "US", "stateCode": "NY"}, {"name": "East Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "East Islip", "countryCode": "US", "stateCode": "NY"}, {"name": "East Ithaca", "countryCode": "US", "stateCode": "NY"}, {"name": "East Massapequa", "countryCode": "US", "stateCode": "NY"}, {"name": "East Meadow", "countryCode": "US", "stateCode": "NY"}, {"name": "East Moriches", "countryCode": "US", "stateCode": "NY"}, {"name": "East New York", "countryCode": "US", "stateCode": "NY"}, {"name": "East Northport", "countryCode": "US", "stateCode": "NY"}, {"name": "East Norwich", "countryCode": "US", "stateCode": "NY"}, {"name": "East Patchogue", "countryCode": "US", "stateCode": "NY"}, {"name": "East Quogue", "countryCode": "US", "stateCode": "NY"}, {"name": "East Rochester", "countryCode": "US", "stateCode": "NY"}, {"name": "East Rockaway", "countryCode": "US", "stateCode": "NY"}, {"name": "East Setauket", "countryCode": "US", "stateCode": "NY"}, {"name": "East Shoreham", "countryCode": "US", "stateCode": "NY"}, {"name": "East Syracuse", "countryCode": "US", "stateCode": "NY"}, {"name": "East Tremont", "countryCode": "US", "stateCode": "NY"}, {"name": "East Village", "countryCode": "US", "stateCode": "NY"}, {"name": "East Williston", "countryCode": "US", "stateCode": "NY"}, {"name": "Eastchester", "countryCode": "US", "stateCode": "NY"}, {"name": "Eastport", "countryCode": "US", "stateCode": "NY"}, {"name": "Eatons Neck", "countryCode": "US", "stateCode": "NY"}, {"name": "Eden", "countryCode": "US", "stateCode": "NY"}, {"name": "Edgemere", "countryCode": "US", "stateCode": "NY"}, {"name": "Edinburg", "countryCode": "US", "stateCode": "NY"}, {"name": "Eggertsville", "countryCode": "US", "stateCode": "NY"}, {"name": "Elbridge", "countryCode": "US", "stateCode": "NY"}, {"name": "Elizabethtown", "countryCode": "US", "stateCode": "NY"}, {"name": "Ellenville", "countryCode": "US", "stateCode": "NY"}, {"name": "Elma Center", "countryCode": "US", "stateCode": "NY"}, {"name": "Elmhurst", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Elmira Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Elmont", "countryCode": "US", "stateCode": "NY"}, {"name": "Elmsford", "countryCode": "US", "stateCode": "NY"}, {"name": "Eltingville", "countryCode": "US", "stateCode": "NY"}, {"name": "Elwood", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "End<PERSON>tt", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Erie County", "countryCode": "US", "stateCode": "NY"}, {"name": "Essex County", "countryCode": "US", "stateCode": "NY"}, {"name": "Fairmount", "countryCode": "US", "stateCode": "NY"}, {"name": "Fairport", "countryCode": "US", "stateCode": "NY"}, {"name": "Fairview", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Fallsburg", "countryCode": "US", "stateCode": "NY"}, {"name": "Far Rockaway", "countryCode": "US", "stateCode": "NY"}, {"name": "Farmingdale", "countryCode": "US", "stateCode": "NY"}, {"name": "Farmingville", "countryCode": "US", "stateCode": "NY"}, {"name": "Fayetteville", "countryCode": "US", "stateCode": "NY"}, {"name": "Financial District", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Fishkill", "countryCode": "US", "stateCode": "NY"}, {"name": "Flanders", "countryCode": "US", "stateCode": "NY"}, {"name": "Flatbush", "countryCode": "US", "stateCode": "NY"}, {"name": "Flatlands", "countryCode": "US", "stateCode": "NY"}, {"name": "Floral Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Florida", "countryCode": "US", "stateCode": "NY"}, {"name": "Flower Hill", "countryCode": "US", "stateCode": "NY"}, {"name": "Fonda", "countryCode": "US", "stateCode": "NY"}, {"name": "Fordham", "countryCode": "US", "stateCode": "NY"}, {"name": "Forest Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "Fort Covington Hamlet", "countryCode": "US", "stateCode": "NY"}, {"name": "Fort Drum", "countryCode": "US", "stateCode": "NY"}, {"name": "Fort Edward", "countryCode": "US", "stateCode": "NY"}, {"name": "Fort Hamilton", "countryCode": "US", "stateCode": "NY"}, {"name": "Fort Montgomery", "countryCode": "US", "stateCode": "NY"}, {"name": "Fort Plain", "countryCode": "US", "stateCode": "NY"}, {"name": "Fort Salonga", "countryCode": "US", "stateCode": "NY"}, {"name": "Fort Wadsworth", "countryCode": "US", "stateCode": "NY"}, {"name": "Frankfort", "countryCode": "US", "stateCode": "NY"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "NY"}, {"name": "Franklin Square", "countryCode": "US", "stateCode": "NY"}, {"name": "Franklinville", "countryCode": "US", "stateCode": "NY"}, {"name": "Fredonia", "countryCode": "US", "stateCode": "NY"}, {"name": "Freeport", "countryCode": "US", "stateCode": "NY"}, {"name": "Fresh Meadows", "countryCode": "US", "stateCode": "NY"}, {"name": "Frewsburg", "countryCode": "US", "stateCode": "NY"}, {"name": "Friendship", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Fulton County", "countryCode": "US", "stateCode": "NY"}, {"name": "Galeville", "countryCode": "US", "stateCode": "NY"}, {"name": "Gang Mills", "countryCode": "US", "stateCode": "NY"}, {"name": "Garden City", "countryCode": "US", "stateCode": "NY"}, {"name": "Garden City Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Garden City South", "countryCode": "US", "stateCode": "NY"}, {"name": "Gardnertown", "countryCode": "US", "stateCode": "NY"}, {"name": "Gasport", "countryCode": "US", "stateCode": "NY"}, {"name": "Gates-North Gates", "countryCode": "US", "stateCode": "NY"}, {"name": "Genesee County", "countryCode": "US", "stateCode": "NY"}, {"name": "Geneseo", "countryCode": "US", "stateCode": "NY"}, {"name": "Geneva", "countryCode": "US", "stateCode": "NY"}, {"name": "Glasco", "countryCode": "US", "stateCode": "NY"}, {"name": "Glen Cove", "countryCode": "US", "stateCode": "NY"}, {"name": "Glen Head", "countryCode": "US", "stateCode": "NY"}, {"name": "Glen Oaks", "countryCode": "US", "stateCode": "NY"}, {"name": "Glendale", "countryCode": "US", "stateCode": "NY"}, {"name": "Glens Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "Glens Falls North", "countryCode": "US", "stateCode": "NY"}, {"name": "Glenwood Landing", "countryCode": "US", "stateCode": "NY"}, {"name": "Gloversville", "countryCode": "US", "stateCode": "NY"}, {"name": "Goldens Bridge", "countryCode": "US", "stateCode": "NY"}, {"name": "Gordon Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Goshen", "countryCode": "US", "stateCode": "NY"}, {"name": "Gouverneur", "countryCode": "US", "stateCode": "NY"}, {"name": "Gowanda", "countryCode": "US", "stateCode": "NY"}, {"name": "Gramercy Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Grand Island", "countryCode": "US", "stateCode": "NY"}, {"name": "Grandyle Village", "countryCode": "US", "stateCode": "NY"}, {"name": "Graniteville", "countryCode": "US", "stateCode": "NY"}, {"name": "Grant City", "countryCode": "US", "stateCode": "NY"}, {"name": "Granville", "countryCode": "US", "stateCode": "NY"}, {"name": "Gravesend", "countryCode": "US", "stateCode": "NY"}, {"name": "Great Kills", "countryCode": "US", "stateCode": "NY"}, {"name": "Great Neck", "countryCode": "US", "stateCode": "NY"}, {"name": "Great Neck Estates", "countryCode": "US", "stateCode": "NY"}, {"name": "Great Neck Gardens", "countryCode": "US", "stateCode": "NY"}, {"name": "Great Neck Plaza", "countryCode": "US", "stateCode": "NY"}, {"name": "Great River", "countryCode": "US", "stateCode": "NY"}, {"name": "Greece", "countryCode": "US", "stateCode": "NY"}, {"name": "Green Island", "countryCode": "US", "stateCode": "NY"}, {"name": "Greenburgh", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Greene County", "countryCode": "US", "stateCode": "NY"}, {"name": "Greenlawn", "countryCode": "US", "stateCode": "NY"}, {"name": "Greenpoint", "countryCode": "US", "stateCode": "NY"}, {"name": "Greenport", "countryCode": "US", "stateCode": "NY"}, {"name": "Greenport West", "countryCode": "US", "stateCode": "NY"}, {"name": "Greenvale", "countryCode": "US", "stateCode": "NY"}, {"name": "Greenville", "countryCode": "US", "stateCode": "NY"}, {"name": "Greenwich", "countryCode": "US", "stateCode": "NY"}, {"name": "Greenwood Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "Groton", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON> Hill", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Halesite", "countryCode": "US", "stateCode": "NY"}, {"name": "Hamburg", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Hamilton County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Hampton Bays", "countryCode": "US", "stateCode": "NY"}, {"name": "Hampton Manor", "countryCode": "US", "stateCode": "NY"}, {"name": "Hannawa Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "Harbor Isle", "countryCode": "US", "stateCode": "NY"}, {"name": "Harlem", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Hartford", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Hastings-on-Hudson", "countryCode": "US", "stateCode": "NY"}, {"name": "Ha<PERSON>pa<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Hawthorne", "countryCode": "US", "stateCode": "NY"}, {"name": "Head of the Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "Hell's Kitchen", "countryCode": "US", "stateCode": "NY"}, {"name": "Hempstead", "countryCode": "US", "stateCode": "NY"}, {"name": "Heritage Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Herkimer County", "countryCode": "US", "stateCode": "NY"}, {"name": "Herricks", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Hewlett Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "Hicksville", "countryCode": "US", "stateCode": "NY"}, {"name": "Highland", "countryCode": "US", "stateCode": "NY"}, {"name": "Highland Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "Highland Mills", "countryCode": "US", "stateCode": "NY"}, {"name": "Hillcrest", "countryCode": "US", "stateCode": "NY"}, {"name": "Hillside", "countryCode": "US", "stateCode": "NY"}, {"name": "Hillside Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "Hilton", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Holland", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Holtsville", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Honeoye Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "Hoosick Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Horseheads", "countryCode": "US", "stateCode": "NY"}, {"name": "Horseheads North", "countryCode": "US", "stateCode": "NY"}, {"name": "Houghton", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Hudson Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Huntington", "countryCode": "US", "stateCode": "NY"}, {"name": "Huntington Bay", "countryCode": "US", "stateCode": "NY"}, {"name": "Huntington Station", "countryCode": "US", "stateCode": "NY"}, {"name": "Hunts Point", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Hyde Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Ilion", "countryCode": "US", "stateCode": "NY"}, {"name": "Inwood", "countryCode": "US", "stateCode": "NY"}, {"name": "Irondequoit", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Island Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Islandia", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Islip Terrace", "countryCode": "US", "stateCode": "NY"}, {"name": "Ithaca", "countryCode": "US", "stateCode": "NY"}, {"name": "Jackson Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Jamaica", "countryCode": "US", "stateCode": "NY"}, {"name": "Jamesport", "countryCode": "US", "stateCode": "NY"}, {"name": "Jamestown", "countryCode": "US", "stateCode": "NY"}, {"name": "Jamestown West", "countryCode": "US", "stateCode": "NY"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "NY"}, {"name": "Jefferson Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Jefferson Valley-Yorktown", "countryCode": "US", "stateCode": "NY"}, {"name": "Jericho", "countryCode": "US", "stateCode": "NY"}, {"name": "Johnson City", "countryCode": "US", "stateCode": "NY"}, {"name": "Johnstown", "countryCode": "US", "stateCode": "NY"}, {"name": "Jordan", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Keeseville", "countryCode": "US", "stateCode": "NY"}, {"name": "Kenmore", "countryCode": "US", "stateCode": "NY"}, {"name": "Kensington", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Keuka Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Kew Gardens", "countryCode": "US", "stateCode": "NY"}, {"name": "Kew Gardens Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "Kiantone", "countryCode": "US", "stateCode": "NY"}, {"name": "Kinderhook", "countryCode": "US", "stateCode": "NY"}, {"name": "Kings Bridge", "countryCode": "US", "stateCode": "NY"}, {"name": "Kings County", "countryCode": "US", "stateCode": "NY"}, {"name": "Kings Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Kings Point", "countryCode": "US", "stateCode": "NY"}, {"name": "Kingston", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Lackawanna", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Carmel", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Erie Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Grove", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Katrine", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Luzerne", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Mohegan", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Placid", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Pleasant", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Ronkonkoma", "countryCode": "US", "stateCode": "NY"}, {"name": "Lake Success", "countryCode": "US", "stateCode": "NY"}, {"name": "Lakeland", "countryCode": "US", "stateCode": "NY"}, {"name": "Lakeview", "countryCode": "US", "stateCode": "NY"}, {"name": "Lakewood", "countryCode": "US", "stateCode": "NY"}, {"name": "Lancaster", "countryCode": "US", "stateCode": "NY"}, {"name": "Lansing", "countryCode": "US", "stateCode": "NY"}, {"name": "Larchmont", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Lattingtown", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Laurel Hollow", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Levittown", "countryCode": "US", "stateCode": "NY"}, {"name": "Lewis County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Liberty", "countryCode": "US", "stateCode": "NY"}, {"name": "Lido Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "Lima", "countryCode": "US", "stateCode": "NY"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Lincolndale", "countryCode": "US", "stateCode": "NY"}, {"name": "Lindenhurst", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Little Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "Little Neck", "countryCode": "US", "stateCode": "NY"}, {"name": "Little Valley", "countryCode": "US", "stateCode": "NY"}, {"name": "Liverpool", "countryCode": "US", "stateCode": "NY"}, {"name": "Livingston County", "countryCode": "US", "stateCode": "NY"}, {"name": "Livingston Manor", "countryCode": "US", "stateCode": "NY"}, {"name": "Livonia", "countryCode": "US", "stateCode": "NY"}, {"name": "Lloyd Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "Lockport", "countryCode": "US", "stateCode": "NY"}, {"name": "Locust Valley", "countryCode": "US", "stateCode": "NY"}, {"name": "Long Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "Long Island City", "countryCode": "US", "stateCode": "NY"}, {"name": "Lorenz Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Lowville", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Lyons", "countryCode": "US", "stateCode": "NY"}, {"name": "Macedon", "countryCode": "US", "stateCode": "NY"}, {"name": "Madison County", "countryCode": "US", "stateCode": "NY"}, {"name": "Mahopac", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Malverne", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Manchester", "countryCode": "US", "stateCode": "NY"}, {"name": "Man<PERSON>set", "countryCode": "US", "stateCode": "NY"}, {"name": "Manhasset Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "Manhattan", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Manorhaven", "countryCode": "US", "stateCode": "NY"}, {"name": "Manorville", "countryCode": "US", "stateCode": "NY"}, {"name": "Marbletown", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Mariners Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Marlboro", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Massapequa", "countryCode": "US", "stateCode": "NY"}, {"name": "Massapequa Park", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Mastic Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "Mattituck", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Mayville", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "McKownville", "countryCode": "US", "stateCode": "NY"}, {"name": "Mechanicstown", "countryCode": "US", "stateCode": "NY"}, {"name": "Mechanicville", "countryCode": "US", "stateCode": "NY"}, {"name": "Medford", "countryCode": "US", "stateCode": "NY"}, {"name": "Medina", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Melrose Park", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Men<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Merritt Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Mexico", "countryCode": "US", "stateCode": "NY"}, {"name": "Middle Island", "countryCode": "US", "stateCode": "NY"}, {"name": "Middle Village", "countryCode": "US", "stateCode": "NY"}, {"name": "Middleburgh", "countryCode": "US", "stateCode": "NY"}, {"name": "Middleport", "countryCode": "US", "stateCode": "NY"}, {"name": "Middletown", "countryCode": "US", "stateCode": "NY"}, {"name": "Midland Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "Mill Neck", "countryCode": "US", "stateCode": "NY"}, {"name": "Millbrook", "countryCode": "US", "stateCode": "NY"}, {"name": "Miller <PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Mineola", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Mineville", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Mohawk", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>au<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Montebello", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Montour Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Moravia", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Morningside Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Morris Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Morris Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Morrisania", "countryCode": "US", "stateCode": "NY"}, {"name": "Morrisonville", "countryCode": "US", "stateCode": "NY"}, {"name": "Morrisville", "countryCode": "US", "stateCode": "NY"}, {"name": "Mott Haven", "countryCode": "US", "stateCode": "NY"}, {"name": "Mount Ivy", "countryCode": "US", "stateCode": "NY"}, {"name": "Mount Kisco", "countryCode": "US", "stateCode": "NY"}, {"name": "Mount Morris", "countryCode": "US", "stateCode": "NY"}, {"name": "Mount Sinai", "countryCode": "US", "stateCode": "NY"}, {"name": "Mount Vernon", "countryCode": "US", "stateCode": "NY"}, {"name": "Mountain Lodge Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Munsey Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Munsons Corners", "countryCode": "US", "stateCode": "NY"}, {"name": "Muttontown", "countryCode": "US", "stateCode": "NY"}, {"name": "Myers <PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Napanoch", "countryCode": "US", "stateCode": "NY"}, {"name": "Naples", "countryCode": "US", "stateCode": "NY"}, {"name": "Nassau", "countryCode": "US", "stateCode": "NY"}, {"name": "Nassau County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Neponsit", "countryCode": "US", "stateCode": "NY"}, {"name": "Nesconset", "countryCode": "US", "stateCode": "NY"}, {"name": "New Brighton", "countryCode": "US", "stateCode": "NY"}, {"name": "New Cassel", "countryCode": "US", "stateCode": "NY"}, {"name": "New City", "countryCode": "US", "stateCode": "NY"}, {"name": "New Dorp", "countryCode": "US", "stateCode": "NY"}, {"name": "New Dorp Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "New Hartford", "countryCode": "US", "stateCode": "NY"}, {"name": "New Hempstead", "countryCode": "US", "stateCode": "NY"}, {"name": "New Hyde Park", "countryCode": "US", "stateCode": "NY"}, {"name": "New Paltz", "countryCode": "US", "stateCode": "NY"}, {"name": "New Rochelle", "countryCode": "US", "stateCode": "NY"}, {"name": "New Springville", "countryCode": "US", "stateCode": "NY"}, {"name": "New Square", "countryCode": "US", "stateCode": "NY"}, {"name": "New Windsor", "countryCode": "US", "stateCode": "NY"}, {"name": "New York City", "countryCode": "US", "stateCode": "NY"}, {"name": "New York County", "countryCode": "US", "stateCode": "NY"}, {"name": "New York Mills", "countryCode": "US", "stateCode": "NY"}, {"name": "Newark", "countryCode": "US", "stateCode": "NY"}, {"name": "Newburgh", "countryCode": "US", "stateCode": "NY"}, {"name": "Newfane", "countryCode": "US", "stateCode": "NY"}, {"name": "Niagara County", "countryCode": "US", "stateCode": "NY"}, {"name": "Niagara Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "Niskayuna", "countryCode": "US", "stateCode": "NY"}, {"name": "Nissequogue", "countryCode": "US", "stateCode": "NY"}, {"name": "Niverville", "countryCode": "US", "stateCode": "NY"}, {"name": "Norfolk", "countryCode": "US", "stateCode": "NY"}, {"name": "North Amityville", "countryCode": "US", "stateCode": "NY"}, {"name": "North Babylon", "countryCode": "US", "stateCode": "NY"}, {"name": "North Ballston Spa", "countryCode": "US", "stateCode": "NY"}, {"name": "North Bay Shore", "countryCode": "US", "stateCode": "NY"}, {"name": "North Bellmore", "countryCode": "US", "stateCode": "NY"}, {"name": "North Bellport", "countryCode": "US", "stateCode": "NY"}, {"name": "North Boston", "countryCode": "US", "stateCode": "NY"}, {"name": "North Castle", "countryCode": "US", "stateCode": "NY"}, {"name": "North Collins", "countryCode": "US", "stateCode": "NY"}, {"name": "North Elba", "countryCode": "US", "stateCode": "NY"}, {"name": "North Gates", "countryCode": "US", "stateCode": "NY"}, {"name": "North Great River", "countryCode": "US", "stateCode": "NY"}, {"name": "North Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "North Lindenhurst", "countryCode": "US", "stateCode": "NY"}, {"name": "North Massapequa", "countryCode": "US", "stateCode": "NY"}, {"name": "North Merrick", "countryCode": "US", "stateCode": "NY"}, {"name": "North New Hyde Park", "countryCode": "US", "stateCode": "NY"}, {"name": "North Patchogue", "countryCode": "US", "stateCode": "NY"}, {"name": "North Sea", "countryCode": "US", "stateCode": "NY"}, {"name": "North Syracuse", "countryCode": "US", "stateCode": "NY"}, {"name": "North Tonawanda", "countryCode": "US", "stateCode": "NY"}, {"name": "North Valley Stream", "countryCode": "US", "stateCode": "NY"}, {"name": "North Wantagh", "countryCode": "US", "stateCode": "NY"}, {"name": "Northeast Ithaca", "countryCode": "US", "stateCode": "NY"}, {"name": "Northport", "countryCode": "US", "stateCode": "NY"}, {"name": "Northumberland", "countryCode": "US", "stateCode": "NY"}, {"name": "Northville", "countryCode": "US", "stateCode": "NY"}, {"name": "Northwest Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "Northwest Ithaca", "countryCode": "US", "stateCode": "NY"}, {"name": "Norwich", "countryCode": "US", "stateCode": "NY"}, {"name": "Norwood", "countryCode": "US", "stateCode": "NY"}, {"name": "Noyack", "countryCode": "US", "stateCode": "NY"}, {"name": "Nunda", "countryCode": "US", "stateCode": "NY"}, {"name": "N<PERSON>ck", "countryCode": "US", "stateCode": "NY"}, {"name": "Oakdale", "countryCode": "US", "stateCode": "NY"}, {"name": "Oakfield", "countryCode": "US", "stateCode": "NY"}, {"name": "Oakwood", "countryCode": "US", "stateCode": "NY"}, {"name": "Oceanside", "countryCode": "US", "stateCode": "NY"}, {"name": "Ogdensburg", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Old Bethpage", "countryCode": "US", "stateCode": "NY"}, {"name": "Old Brookville", "countryCode": "US", "stateCode": "NY"}, {"name": "Old Westbury", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Oneida", "countryCode": "US", "stateCode": "NY"}, {"name": "Oneida County", "countryCode": "US", "stateCode": "NY"}, {"name": "Oneonta", "countryCode": "US", "stateCode": "NY"}, {"name": "Onondaga County", "countryCode": "US", "stateCode": "NY"}, {"name": "Ontario", "countryCode": "US", "stateCode": "NY"}, {"name": "Ontario County", "countryCode": "US", "stateCode": "NY"}, {"name": "Orange County", "countryCode": "US", "stateCode": "NY"}, {"name": "Orange Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "Orangeburg", "countryCode": "US", "stateCode": "NY"}, {"name": "Orchard Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Oriskany", "countryCode": "US", "stateCode": "NY"}, {"name": "Orleans County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Oswego", "countryCode": "US", "stateCode": "NY"}, {"name": "Oswego County", "countryCode": "US", "stateCode": "NY"}, {"name": "Otisville", "countryCode": "US", "stateCode": "NY"}, {"name": "Otsego County", "countryCode": "US", "stateCode": "NY"}, {"name": "Owego", "countryCode": "US", "stateCode": "NY"}, {"name": "Oxford", "countryCode": "US", "stateCode": "NY"}, {"name": "Oyster Bay", "countryCode": "US", "stateCode": "NY"}, {"name": "Oyster Bay Cove", "countryCode": "US", "stateCode": "NY"}, {"name": "Ozone Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Painted Post", "countryCode": "US", "stateCode": "NY"}, {"name": "Palenville", "countryCode": "US", "stateCode": "NY"}, {"name": "Palmyra", "countryCode": "US", "stateCode": "NY"}, {"name": "Park Slope", "countryCode": "US", "stateCode": "NY"}, {"name": "Parkchester", "countryCode": "US", "stateCode": "NY"}, {"name": "Patchogue", "countryCode": "US", "stateCode": "NY"}, {"name": "Pawling", "countryCode": "US", "stateCode": "NY"}, {"name": "Peach Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "Pearl River", "countryCode": "US", "stateCode": "NY"}, {"name": "Peekskill", "countryCode": "US", "stateCode": "NY"}, {"name": "Pelham", "countryCode": "US", "stateCode": "NY"}, {"name": "Pelham Manor", "countryCode": "US", "stateCode": "NY"}, {"name": "Penn Yan", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Perth", "countryCode": "US", "stateCode": "NY"}, {"name": "Peru", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Philadelphia", "countryCode": "US", "stateCode": "NY"}, {"name": "Philmont", "countryCode": "US", "stateCode": "NY"}, {"name": "Phoenix", "countryCode": "US", "stateCode": "NY"}, {"name": "Piermont", "countryCode": "US", "stateCode": "NY"}, {"name": "Pine Bush", "countryCode": "US", "stateCode": "NY"}, {"name": "Pine Plains", "countryCode": "US", "stateCode": "NY"}, {"name": "Pittsford", "countryCode": "US", "stateCode": "NY"}, {"name": "Plainedge", "countryCode": "US", "stateCode": "NY"}, {"name": "Plainview", "countryCode": "US", "stateCode": "NY"}, {"name": "Plandome", "countryCode": "US", "stateCode": "NY"}, {"name": "Plandome Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Plattekill", "countryCode": "US", "stateCode": "NY"}, {"name": "Plattsburgh", "countryCode": "US", "stateCode": "NY"}, {"name": "Plattsburgh West", "countryCode": "US", "stateCode": "NY"}, {"name": "Pleasant Valley", "countryCode": "US", "stateCode": "NY"}, {"name": "Pleasantville", "countryCode": "US", "stateCode": "NY"}, {"name": "Poestenkill", "countryCode": "US", "stateCode": "NY"}, {"name": "Point Lookout", "countryCode": "US", "stateCode": "NY"}, {"name": "Pomona", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Byron", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Chester", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Dickinson", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Ewen", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Henry", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Jefferson", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Jefferson Station", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Jervis", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Morris", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Richmond", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Washington", "countryCode": "US", "stateCode": "NY"}, {"name": "Port Washington North", "countryCode": "US", "stateCode": "NY"}, {"name": "Portland", "countryCode": "US", "stateCode": "NY"}, {"name": "Potsdam", "countryCode": "US", "stateCode": "NY"}, {"name": "Poughkeepsie", "countryCode": "US", "stateCode": "NY"}, {"name": "Pound Ridge", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Purchase", "countryCode": "US", "stateCode": "NY"}, {"name": "Putnam County", "countryCode": "US", "stateCode": "NY"}, {"name": "Putnam Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "Queens", "countryCode": "US", "stateCode": "NY"}, {"name": "Queens County", "countryCode": "US", "stateCode": "NY"}, {"name": "Queens Village", "countryCode": "US", "stateCode": "NY"}, {"name": "Queensbury", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Ransomville", "countryCode": "US", "stateCode": "NY"}, {"name": "Rapids", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Red Hook", "countryCode": "US", "stateCode": "NY"}, {"name": "Red Oaks Mill", "countryCode": "US", "stateCode": "NY"}, {"name": "Rego Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Remsenburg-Speonk", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Rensselaer County", "countryCode": "US", "stateCode": "NY"}, {"name": "Rhinebeck", "countryCode": "US", "stateCode": "NY"}, {"name": "Richfield Springs", "countryCode": "US", "stateCode": "NY"}, {"name": "Richland", "countryCode": "US", "stateCode": "NY"}, {"name": "Richmond County", "countryCode": "US", "stateCode": "NY"}, {"name": "Richmond Hill", "countryCode": "US", "stateCode": "NY"}, {"name": "Ridge", "countryCode": "US", "stateCode": "NY"}, {"name": "Ridgewood", "countryCode": "US", "stateCode": "NY"}, {"name": "Riverdale", "countryCode": "US", "stateCode": "NY"}, {"name": "Riverhead", "countryCode": "US", "stateCode": "NY"}, {"name": "Riverside", "countryCode": "US", "stateCode": "NY"}, {"name": "Rochester", "countryCode": "US", "stateCode": "NY"}, {"name": "Rock Hill", "countryCode": "US", "stateCode": "NY"}, {"name": "Rockaway Point", "countryCode": "US", "stateCode": "NY"}, {"name": "Rockland County", "countryCode": "US", "stateCode": "NY"}, {"name": "Rockville Centre", "countryCode": "US", "stateCode": "NY"}, {"name": "Rocky Point", "countryCode": "US", "stateCode": "NY"}, {"name": "Roessleville", "countryCode": "US", "stateCode": "NY"}, {"name": "Rome", "countryCode": "US", "stateCode": "NY"}, {"name": "Ronkonko<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Rosebank", "countryCode": "US", "stateCode": "NY"}, {"name": "Rosedale", "countryCode": "US", "stateCode": "NY"}, {"name": "Rosendale Village", "countryCode": "US", "stateCode": "NY"}, {"name": "Roslyn", "countryCode": "US", "stateCode": "NY"}, {"name": "Roslyn Estates", "countryCode": "US", "stateCode": "NY"}, {"name": "Roslyn Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "Roslyn Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Rossville", "countryCode": "US", "stateCode": "NY"}, {"name": "Rotterdam", "countryCode": "US", "stateCode": "NY"}, {"name": "Rouses Point", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Rye Brook", "countryCode": "US", "stateCode": "NY"}, {"name": "Sackets Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "Sag Harbor", "countryCode": "US", "stateCode": "NY"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Saint <PERSON>ville", "countryCode": "US", "stateCode": "NY"}, {"name": "Salamanca", "countryCode": "US", "stateCode": "NY"}, {"name": "Salisbury", "countryCode": "US", "stateCode": "NY"}, {"name": "Sanborn", "countryCode": "US", "stateCode": "NY"}, {"name": "Sands Point", "countryCode": "US", "stateCode": "NY"}, {"name": "Saranac Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "Saratoga County", "countryCode": "US", "stateCode": "NY"}, {"name": "Saratoga Springs", "countryCode": "US", "stateCode": "NY"}, {"name": "Saugerties", "countryCode": "US", "stateCode": "NY"}, {"name": "Saugerties South", "countryCode": "US", "stateCode": "NY"}, {"name": "Sayville", "countryCode": "US", "stateCode": "NY"}, {"name": "Scarsdale", "countryCode": "US", "stateCode": "NY"}, {"name": "Schenectady", "countryCode": "US", "stateCode": "NY"}, {"name": "Schenectady County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Schoharie County", "countryCode": "US", "stateCode": "NY"}, {"name": "Schuyler County", "countryCode": "US", "stateCode": "NY"}, {"name": "Sc<PERSON>ylerville", "countryCode": "US", "stateCode": "NY"}, {"name": "Scotchtown", "countryCode": "US", "stateCode": "NY"}, {"name": "Scotia", "countryCode": "US", "stateCode": "NY"}, {"name": "Scottsville", "countryCode": "US", "stateCode": "NY"}, {"name": "Sea Cliff", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>ford", "countryCode": "US", "stateCode": "NY"}, {"name": "Searingtown", "countryCode": "US", "stateCode": "NY"}, {"name": "Seaside", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Seneca County", "countryCode": "US", "stateCode": "NY"}, {"name": "Seneca Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>lls", "countryCode": "US", "stateCode": "NY"}, {"name": "Setauket-East Setauket", "countryCode": "US", "stateCode": "NY"}, {"name": "Sheepshead Bay", "countryCode": "US", "stateCode": "NY"}, {"name": "Shelter Island", "countryCode": "US", "stateCode": "NY"}, {"name": "Shelter Island Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Shenorock", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Shinnecock Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Shokan", "countryCode": "US", "stateCode": "NY"}, {"name": "Shortsville", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Silver Creek", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Sleepy Hollow", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Sloatsburg", "countryCode": "US", "stateCode": "NY"}, {"name": "Smithtown", "countryCode": "US", "stateCode": "NY"}, {"name": "Sodus", "countryCode": "US", "stateCode": "NY"}, {"name": "Solvay", "countryCode": "US", "stateCode": "NY"}, {"name": "Sound Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "South Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "South Blooming Grove", "countryCode": "US", "stateCode": "NY"}, {"name": "South Corning", "countryCode": "US", "stateCode": "NY"}, {"name": "South Fallsburg", "countryCode": "US", "stateCode": "NY"}, {"name": "South Farmingdale", "countryCode": "US", "stateCode": "NY"}, {"name": "South Floral Park", "countryCode": "US", "stateCode": "NY"}, {"name": "South Glens Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "South Hempstead", "countryCode": "US", "stateCode": "NY"}, {"name": "South Hill", "countryCode": "US", "stateCode": "NY"}, {"name": "South Huntington", "countryCode": "US", "stateCode": "NY"}, {"name": "South Lockport", "countryCode": "US", "stateCode": "NY"}, {"name": "South Nyack", "countryCode": "US", "stateCode": "NY"}, {"name": "South Valley Stream", "countryCode": "US", "stateCode": "NY"}, {"name": "Southampton", "countryCode": "US", "stateCode": "NY"}, {"name": "Southold", "countryCode": "US", "stateCode": "NY"}, {"name": "Southport", "countryCode": "US", "stateCode": "NY"}, {"name": "Spackenkill", "countryCode": "US", "stateCode": "NY"}, {"name": "Sparkill", "countryCode": "US", "stateCode": "NY"}, {"name": "Spencerport", "countryCode": "US", "stateCode": "NY"}, {"name": "Spring Valley", "countryCode": "US", "stateCode": "NY"}, {"name": "Springfield", "countryCode": "US", "stateCode": "NY"}, {"name": "Springfield Gardens", "countryCode": "US", "stateCode": "NY"}, {"name": "Springs", "countryCode": "US", "stateCode": "NY"}, {"name": "Springville", "countryCode": "US", "stateCode": "NY"}, {"name": "Spuyten Duyvil", "countryCode": "US", "stateCode": "NY"}, {"name": "St. Lawrence County", "countryCode": "US", "stateCode": "NY"}, {"name": "Stamford", "countryCode": "US", "stateCode": "NY"}, {"name": "Stapleton", "countryCode": "US", "stateCode": "NY"}, {"name": "Staten Island", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Steuben County", "countryCode": "US", "stateCode": "NY"}, {"name": "Stewart Manor", "countryCode": "US", "stateCode": "NY"}, {"name": "Stillwater", "countryCode": "US", "stateCode": "NY"}, {"name": "Stone Ridge", "countryCode": "US", "stateCode": "NY"}, {"name": "Stony Brook", "countryCode": "US", "stateCode": "NY"}, {"name": "Stony Point", "countryCode": "US", "stateCode": "NY"}, {"name": "Stottville", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Suffolk County", "countryCode": "US", "stateCode": "NY"}, {"name": "Sullivan County", "countryCode": "US", "stateCode": "NY"}, {"name": "Sunnyside", "countryCode": "US", "stateCode": "NY"}, {"name": "Sunset Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Sylvan Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "S<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Syracuse", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Tarrytown", "countryCode": "US", "stateCode": "NY"}, {"name": "Terrace Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Terryville", "countryCode": "US", "stateCode": "NY"}, {"name": "The Bronx", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Throgs Neck", "countryCode": "US", "stateCode": "NY"}, {"name": "Ticonderoga", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Tioga County", "countryCode": "US", "stateCode": "NY"}, {"name": "Tivoli", "countryCode": "US", "stateCode": "NY"}, {"name": "Tompkins County", "countryCode": "US", "stateCode": "NY"}, {"name": "Tompkinsville", "countryCode": "US", "stateCode": "NY"}, {"name": "Tonawanda", "countryCode": "US", "stateCode": "NY"}, {"name": "Town Line", "countryCode": "US", "stateCode": "NY"}, {"name": "Tremont", "countryCode": "US", "stateCode": "NY"}, {"name": "Tribes Hill", "countryCode": "US", "stateCode": "NY"}, {"name": "Troy", "countryCode": "US", "stateCode": "NY"}, {"name": "Trumansburg", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>ah<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Tupper Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "Ulster County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Union Springs", "countryCode": "US", "stateCode": "NY"}, {"name": "Uniondale", "countryCode": "US", "stateCode": "NY"}, {"name": "Unionport", "countryCode": "US", "stateCode": "NY"}, {"name": "University Gardens", "countryCode": "US", "stateCode": "NY"}, {"name": "University Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Upper Brookville", "countryCode": "US", "stateCode": "NY"}, {"name": "Upper Nyack", "countryCode": "US", "stateCode": "NY"}, {"name": "Utica", "countryCode": "US", "stateCode": "NY"}, {"name": "Vails Gate", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Valhalla", "countryCode": "US", "stateCode": "NY"}, {"name": "Valley Cottage", "countryCode": "US", "stateCode": "NY"}, {"name": "Valley Stream", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Victor", "countryCode": "US", "stateCode": "NY"}, {"name": "Village Green", "countryCode": "US", "stateCode": "NY"}, {"name": "Village of the Branch", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Voorheesville", "countryCode": "US", "stateCode": "NY"}, {"name": "Wading River", "countryCode": "US", "stateCode": "NY"}, {"name": "Wakefield", "countryCode": "US", "stateCode": "NY"}, {"name": "Walden", "countryCode": "US", "stateCode": "NY"}, {"name": "Wallkill", "countryCode": "US", "stateCode": "NY"}, {"name": "Walton", "countryCode": "US", "stateCode": "NY"}, {"name": "Walton Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Wampsville", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Wantagh", "countryCode": "US", "stateCode": "NY"}, {"name": "Wappingers Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "Warren County", "countryCode": "US", "stateCode": "NY"}, {"name": "Warrensburg", "countryCode": "US", "stateCode": "NY"}, {"name": "Warsaw", "countryCode": "US", "stateCode": "NY"}, {"name": "Warwick", "countryCode": "US", "stateCode": "NY"}, {"name": "Washington County", "countryCode": "US", "stateCode": "NY"}, {"name": "Washington Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Washington Mills", "countryCode": "US", "stateCode": "NY"}, {"name": "Washingtonville", "countryCode": "US", "stateCode": "NY"}, {"name": "Watchtower", "countryCode": "US", "stateCode": "NY"}, {"name": "Water Mill", "countryCode": "US", "stateCode": "NY"}, {"name": "Waterford", "countryCode": "US", "stateCode": "NY"}, {"name": "Waterloo", "countryCode": "US", "stateCode": "NY"}, {"name": "Watertown", "countryCode": "US", "stateCode": "NY"}, {"name": "Waterville", "countryCode": "US", "stateCode": "NY"}, {"name": "Watervliet", "countryCode": "US", "stateCode": "NY"}, {"name": "Watkins Glen", "countryCode": "US", "stateCode": "NY"}, {"name": "Waverly", "countryCode": "US", "stateCode": "NY"}, {"name": "Wawarsing", "countryCode": "US", "stateCode": "NY"}, {"name": "Wayland", "countryCode": "US", "stateCode": "NY"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Weedsport", "countryCode": "US", "stateCode": "NY"}, {"name": "Wellsville", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "West Albany", "countryCode": "US", "stateCode": "NY"}, {"name": "West Babylon", "countryCode": "US", "stateCode": "NY"}, {"name": "West Bay Shore", "countryCode": "US", "stateCode": "NY"}, {"name": "West Carthage", "countryCode": "US", "stateCode": "NY"}, {"name": "West Elmira", "countryCode": "US", "stateCode": "NY"}, {"name": "West End", "countryCode": "US", "stateCode": "NY"}, {"name": "West Glens Falls", "countryCode": "US", "stateCode": "NY"}, {"name": "West Haverstraw", "countryCode": "US", "stateCode": "NY"}, {"name": "West Hempstead", "countryCode": "US", "stateCode": "NY"}, {"name": "West Hills", "countryCode": "US", "stateCode": "NY"}, {"name": "West Hurley", "countryCode": "US", "stateCode": "NY"}, {"name": "West Islip", "countryCode": "US", "stateCode": "NY"}, {"name": "West Nyack", "countryCode": "US", "stateCode": "NY"}, {"name": "West Point", "countryCode": "US", "stateCode": "NY"}, {"name": "West Sand Lake", "countryCode": "US", "stateCode": "NY"}, {"name": "West Sayville", "countryCode": "US", "stateCode": "NY"}, {"name": "West Seneca", "countryCode": "US", "stateCode": "NY"}, {"name": "Westbury", "countryCode": "US", "stateCode": "NY"}, {"name": "Westchester County", "countryCode": "US", "stateCode": "NY"}, {"name": "Westerleigh", "countryCode": "US", "stateCode": "NY"}, {"name": "Westfield", "countryCode": "US", "stateCode": "NY"}, {"name": "Westhampton", "countryCode": "US", "stateCode": "NY"}, {"name": "Westhampton Beach", "countryCode": "US", "stateCode": "NY"}, {"name": "Westmere", "countryCode": "US", "stateCode": "NY"}, {"name": "Weston Mills", "countryCode": "US", "stateCode": "NY"}, {"name": "Westvale", "countryCode": "US", "stateCode": "NY"}, {"name": "Wheatley Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "White Plains", "countryCode": "US", "stateCode": "NY"}, {"name": "Whitehall", "countryCode": "US", "stateCode": "NY"}, {"name": "Whitesboro", "countryCode": "US", "stateCode": "NY"}, {"name": "Whitestone", "countryCode": "US", "stateCode": "NY"}, {"name": "Williamsburg", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Williamsville", "countryCode": "US", "stateCode": "NY"}, {"name": "Williston Park", "countryCode": "US", "stateCode": "NY"}, {"name": "Willowbrook", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Woodbury", "countryCode": "US", "stateCode": "NY"}, {"name": "Woodhaven", "countryCode": "US", "stateCode": "NY"}, {"name": "Woodlawn", "countryCode": "US", "stateCode": "NY"}, {"name": "Woodmere", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Woodside", "countryCode": "US", "stateCode": "NY"}, {"name": "Woodstock", "countryCode": "US", "stateCode": "NY"}, {"name": "Worcester", "countryCode": "US", "stateCode": "NY"}, {"name": "Wurtsboro", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Wynantskill", "countryCode": "US", "stateCode": "NY"}, {"name": "Wyoming County", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}, {"name": "Yates County", "countryCode": "US", "stateCode": "NY"}, {"name": "Yonkers", "countryCode": "US", "stateCode": "NY"}, {"name": "Yorkshire", "countryCode": "US", "stateCode": "NY"}, {"name": "Yorktown Heights", "countryCode": "US", "stateCode": "NY"}, {"name": "Yorkville", "countryCode": "US", "stateCode": "NY"}, {"name": "Youngstown", "countryCode": "US", "stateCode": "NY"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NY"}]