[{"name": "Abbottstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Adams County", "countryCode": "US", "stateCode": "PA"}, {"name": "Adamstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Akron", "countryCode": "US", "stateCode": "PA"}, {"name": "Albion", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Aldan", "countryCode": "US", "stateCode": "PA"}, {"name": "Aliquippa", "countryCode": "US", "stateCode": "PA"}, {"name": "Allegheny County", "countryCode": "US", "stateCode": "PA"}, {"name": "Alleghenyville", "countryCode": "US", "stateCode": "PA"}, {"name": "Allentown", "countryCode": "US", "stateCode": "PA"}, {"name": "Allison <PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Almedia", "countryCode": "US", "stateCode": "PA"}, {"name": "Altoona", "countryCode": "US", "stateCode": "PA"}, {"name": "Ambler", "countryCode": "US", "stateCode": "PA"}, {"name": "Ambridge", "countryCode": "US", "stateCode": "PA"}, {"name": "Amity Gardens", "countryCode": "US", "stateCode": "PA"}, {"name": "Ancient Oaks", "countryCode": "US", "stateCode": "PA"}, {"name": "Annville", "countryCode": "US", "stateCode": "PA"}, {"name": "Apollo", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Arlington Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "Armstrong County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Ashland", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Aspinwall", "countryCode": "US", "stateCode": "PA"}, {"name": "Atglen", "countryCode": "US", "stateCode": "PA"}, {"name": "Athens", "countryCode": "US", "stateCode": "PA"}, {"name": "Audubon", "countryCode": "US", "stateCode": "PA"}, {"name": "Avalon", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Avoca", "countryCode": "US", "stateCode": "PA"}, {"name": "Avon", "countryCode": "US", "stateCode": "PA"}, {"name": "Avondale", "countryCode": "US", "stateCode": "PA"}, {"name": "Avonia", "countryCode": "US", "stateCode": "PA"}, {"name": "Back Mountain", "countryCode": "US", "stateCode": "PA"}, {"name": "Baden", "countryCode": "US", "stateCode": "PA"}, {"name": "Baidland", "countryCode": "US", "stateCode": "PA"}, {"name": "Bainbridge", "countryCode": "US", "stateCode": "PA"}, {"name": "Bakerstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Bala-Cynwyd", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>y", "countryCode": "US", "stateCode": "PA"}, {"name": "Bangor", "countryCode": "US", "stateCode": "PA"}, {"name": "Barnesboro", "countryCode": "US", "stateCode": "PA"}, {"name": "Bath", "countryCode": "US", "stateCode": "PA"}, {"name": "Bear Rocks", "countryCode": "US", "stateCode": "PA"}, {"name": "Beaver", "countryCode": "US", "stateCode": "PA"}, {"name": "Beaver County", "countryCode": "US", "stateCode": "PA"}, {"name": "Beaver Falls", "countryCode": "US", "stateCode": "PA"}, {"name": "Beaverdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Bedford", "countryCode": "US", "stateCode": "PA"}, {"name": "Bedford County", "countryCode": "US", "stateCode": "PA"}, {"name": "Bedminster", "countryCode": "US", "stateCode": "PA"}, {"name": "Beech Mountain Lakes", "countryCode": "US", "stateCode": "PA"}, {"name": "Belfast", "countryCode": "US", "stateCode": "PA"}, {"name": "Bell Acres", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Bellefonte", "countryCode": "US", "stateCode": "PA"}, {"name": "Belleville", "countryCode": "US", "stateCode": "PA"}, {"name": "Bellevue", "countryCode": "US", "stateCode": "PA"}, {"name": "Bellwood", "countryCode": "US", "stateCode": "PA"}, {"name": "Belmont", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Bentleyville", "countryCode": "US", "stateCode": "PA"}, {"name": "Berks County", "countryCode": "US", "stateCode": "PA"}, {"name": "Berlin", "countryCode": "US", "stateCode": "PA"}, {"name": "Berwick", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Bessemer", "countryCode": "US", "stateCode": "PA"}, {"name": "Bethel Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Bethlehem", "countryCode": "US", "stateCode": "PA"}, {"name": "Big Bass Lake", "countryCode": "US", "stateCode": "PA"}, {"name": "Big Beaver", "countryCode": "US", "stateCode": "PA"}, {"name": "Biglerville", "countryCode": "US", "stateCode": "PA"}, {"name": "Birchwood Lakes", "countryCode": "US", "stateCode": "PA"}, {"name": "Birdsboro", "countryCode": "US", "stateCode": "PA"}, {"name": "Black Lick", "countryCode": "US", "stateCode": "PA"}, {"name": "Blair County", "countryCode": "US", "stateCode": "PA"}, {"name": "Blairsville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Blawnox", "countryCode": "US", "stateCode": "PA"}, {"name": "Bloomfield", "countryCode": "US", "stateCode": "PA"}, {"name": "Bloomsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Blossburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Blue Ball", "countryCode": "US", "stateCode": "PA"}, {"name": "Blue Bell", "countryCode": "US", "stateCode": "PA"}, {"name": "Boalsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Boiling Springs", "countryCode": "US", "stateCode": "PA"}, {"name": "Bonneauville", "countryCode": "US", "stateCode": "PA"}, {"name": "Boothwyn", "countryCode": "US", "stateCode": "PA"}, {"name": "Boswell", "countryCode": "US", "stateCode": "PA"}, {"name": "Bowmansville", "countryCode": "US", "stateCode": "PA"}, {"name": "Boyertown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Braddock", "countryCode": "US", "stateCode": "PA"}, {"name": "Braddock Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "Bradford", "countryCode": "US", "stateCode": "PA"}, {"name": "Bradford County", "countryCode": "US", "stateCode": "PA"}, {"name": "Bradford Woods", "countryCode": "US", "stateCode": "PA"}, {"name": "Breinigsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Brentwood", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Brickerville", "countryCode": "US", "stateCode": "PA"}, {"name": "Bridgeport", "countryCode": "US", "stateCode": "PA"}, {"name": "Bridgeville", "countryCode": "US", "stateCode": "PA"}, {"name": "Bristol", "countryCode": "US", "stateCode": "PA"}, {"name": "Brittany Farms-Highlands", "countryCode": "US", "stateCode": "PA"}, {"name": "Brockway", "countryCode": "US", "stateCode": "PA"}, {"name": "Brodheadsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Brookhaven", "countryCode": "US", "stateCode": "PA"}, {"name": "Brookville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>all", "countryCode": "US", "stateCode": "PA"}, {"name": "Brownstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Brownsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Browntown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Bucks County", "countryCode": "US", "stateCode": "PA"}, {"name": "Burgettstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Burnham", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Butler County", "countryCode": "US", "stateCode": "PA"}, {"name": "California", "countryCode": "US", "stateCode": "PA"}, {"name": "Caln", "countryCode": "US", "stateCode": "PA"}, {"name": "Calumet", "countryCode": "US", "stateCode": "PA"}, {"name": "Cambria County", "countryCode": "US", "stateCode": "PA"}, {"name": "Cambridge Springs", "countryCode": "US", "stateCode": "PA"}, {"name": "Cameron County", "countryCode": "US", "stateCode": "PA"}, {"name": "Camp Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "Campbelltown", "countryCode": "US", "stateCode": "PA"}, {"name": "Canonsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Canton", "countryCode": "US", "stateCode": "PA"}, {"name": "Carbon County", "countryCode": "US", "stateCode": "PA"}, {"name": "Carbondale", "countryCode": "US", "stateCode": "PA"}, {"name": "Carlisle", "countryCode": "US", "stateCode": "PA"}, {"name": "Carnegie", "countryCode": "US", "stateCode": "PA"}, {"name": "Carnot<PERSON>Moon", "countryCode": "US", "stateCode": "PA"}, {"name": "Carroll Valley", "countryCode": "US", "stateCode": "PA"}, {"name": "Castanea", "countryCode": "US", "stateCode": "PA"}, {"name": "Castle Shannon", "countryCode": "US", "stateCode": "PA"}, {"name": "Catasauqua", "countryCode": "US", "stateCode": "PA"}, {"name": "Catawissa", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Cementon", "countryCode": "US", "stateCode": "PA"}, {"name": "Center City", "countryCode": "US", "stateCode": "PA"}, {"name": "Centerville", "countryCode": "US", "stateCode": "PA"}, {"name": "Central City", "countryCode": "US", "stateCode": "PA"}, {"name": "Centre County", "countryCode": "US", "stateCode": "PA"}, {"name": "Centre Hall", "countryCode": "US", "stateCode": "PA"}, {"name": "Cetronia", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Chambersburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Charleroi", "countryCode": "US", "stateCode": "PA"}, {"name": "Cherryville", "countryCode": "US", "stateCode": "PA"}, {"name": "Chester", "countryCode": "US", "stateCode": "PA"}, {"name": "Chester County", "countryCode": "US", "stateCode": "PA"}, {"name": "Chester Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "Chester Springs", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Cheswick", "countryCode": "US", "stateCode": "PA"}, {"name": "Chevy Chase Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "Chicora", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Church Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Churchville", "countryCode": "US", "stateCode": "PA"}, {"name": "Clairton", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Clarion County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Clarks Summit", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Claysburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Clearfield", "countryCode": "US", "stateCode": "PA"}, {"name": "Clearfield County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Clifton Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Coaldale", "countryCode": "US", "stateCode": "PA"}, {"name": "Coatesville", "countryCode": "US", "stateCode": "PA"}, {"name": "Cochranton", "countryCode": "US", "stateCode": "PA"}, {"name": "Collegeville", "countryCode": "US", "stateCode": "PA"}, {"name": "Collingdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Collinsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Colonial Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Colony Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Columbia", "countryCode": "US", "stateCode": "PA"}, {"name": "Columbia County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Conashaugh Lakes", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Conestoga", "countryCode": "US", "stateCode": "PA"}, {"name": "Conneaut Lakeshore", "countryCode": "US", "stateCode": "PA"}, {"name": "Connellsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Conshohocken", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Coopersburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Coplay", "countryCode": "US", "stateCode": "PA"}, {"name": "Coraopolis", "countryCode": "US", "stateCode": "PA"}, {"name": "Cornwall", "countryCode": "US", "stateCode": "PA"}, {"name": "Cornwells Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "Corry", "countryCode": "US", "stateCode": "PA"}, {"name": "Coudersport", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Cranberry Township", "countryCode": "US", "stateCode": "PA"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Cressona", "countryCode": "US", "stateCode": "PA"}, {"name": "Croydon", "countryCode": "US", "stateCode": "PA"}, {"name": "Cumberland County", "countryCode": "US", "stateCode": "PA"}, {"name": "Curtisville", "countryCode": "US", "stateCode": "PA"}, {"name": "Curwensville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Dallas", "countryCode": "US", "stateCode": "PA"}, {"name": "Dallastown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Danville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Dauphin County", "countryCode": "US", "stateCode": "PA"}, {"name": "Davidsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Delaware County", "countryCode": "US", "stateCode": "PA"}, {"name": "Delmont", "countryCode": "US", "stateCode": "PA"}, {"name": "Denver", "countryCode": "US", "stateCode": "PA"}, {"name": "Derry", "countryCode": "US", "stateCode": "PA"}, {"name": "Devon", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Dickson City", "countryCode": "US", "stateCode": "PA"}, {"name": "Dillsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Dormont", "countryCode": "US", "stateCode": "PA"}, {"name": "Dorneyville", "countryCode": "US", "stateCode": "PA"}, {"name": "Dover", "countryCode": "US", "stateCode": "PA"}, {"name": "Downingtown", "countryCode": "US", "stateCode": "PA"}, {"name": "Doylestown", "countryCode": "US", "stateCode": "PA"}, {"name": "Dravosburg", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Drexel Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Dublin", "countryCode": "US", "stateCode": "PA"}, {"name": "Duboistown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Duncansville", "countryCode": "US", "stateCode": "PA"}, {"name": "Dunmore", "countryCode": "US", "stateCode": "PA"}, {"name": "Dunnstown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Duquesne", "countryCode": "US", "stateCode": "PA"}, {"name": "Dury<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Eagleview", "countryCode": "US", "stateCode": "PA"}, {"name": "Eagleville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "East Bangor", "countryCode": "US", "stateCode": "PA"}, {"name": "East Berlin", "countryCode": "US", "stateCode": "PA"}, {"name": "East Berwick", "countryCode": "US", "stateCode": "PA"}, {"name": "East Conemaugh", "countryCode": "US", "stateCode": "PA"}, {"name": "East Earl", "countryCode": "US", "stateCode": "PA"}, {"name": "East Greenville", "countryCode": "US", "stateCode": "PA"}, {"name": "East Lansdowne", "countryCode": "US", "stateCode": "PA"}, {"name": "East McKeesport", "countryCode": "US", "stateCode": "PA"}, {"name": "East Petersburg", "countryCode": "US", "stateCode": "PA"}, {"name": "East Pittsburgh", "countryCode": "US", "stateCode": "PA"}, {"name": "East Stroudsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "East Uniontown", "countryCode": "US", "stateCode": "PA"}, {"name": "East Washington", "countryCode": "US", "stateCode": "PA"}, {"name": "East York", "countryCode": "US", "stateCode": "PA"}, {"name": "Eastlawn Gardens", "countryCode": "US", "stateCode": "PA"}, {"name": "Easton", "countryCode": "US", "stateCode": "PA"}, {"name": "Ebensburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Economy", "countryCode": "US", "stateCode": "PA"}, {"name": "Eddington", "countryCode": "US", "stateCode": "PA"}, {"name": "Eddystone", "countryCode": "US", "stateCode": "PA"}, {"name": "Edgewood", "countryCode": "US", "stateCode": "PA"}, {"name": "Edgeworth", "countryCode": "US", "stateCode": "PA"}, {"name": "Edinboro", "countryCode": "US", "stateCode": "PA"}, {"name": "Edwardsville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Egypt", "countryCode": "US", "stateCode": "PA"}, {"name": "Elim", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Elizabethtown", "countryCode": "US", "stateCode": "PA"}, {"name": "Elizabethville", "countryCode": "US", "stateCode": "PA"}, {"name": "Elk County", "countryCode": "US", "stateCode": "PA"}, {"name": "Elkland", "countryCode": "US", "stateCode": "PA"}, {"name": "Ellport", "countryCode": "US", "stateCode": "PA"}, {"name": "Ellwood City", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Elysburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Emerald Lakes", "countryCode": "US", "stateCode": "PA"}, {"name": "Emigsville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Emporium", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Erie", "countryCode": "US", "stateCode": "PA"}, {"name": "Erie County", "countryCode": "US", "stateCode": "PA"}, {"name": "Espy", "countryCode": "US", "stateCode": "PA"}, {"name": "Etna", "countryCode": "US", "stateCode": "PA"}, {"name": "Evans City", "countryCode": "US", "stateCode": "PA"}, {"name": "Evansburg", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Exeter", "countryCode": "US", "stateCode": "PA"}, {"name": "Exton", "countryCode": "US", "stateCode": "PA"}, {"name": "Factoryville", "countryCode": "US", "stateCode": "PA"}, {"name": "Fairchance", "countryCode": "US", "stateCode": "PA"}, {"name": "Fairdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Fairhope", "countryCode": "US", "stateCode": "PA"}, {"name": "Fairless Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "Fairview", "countryCode": "US", "stateCode": "PA"}, {"name": "Fairview-Ferndale", "countryCode": "US", "stateCode": "PA"}, {"name": "Falls Creek", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Faxon", "countryCode": "US", "stateCode": "PA"}, {"name": "Fayette County", "countryCode": "US", "stateCode": "PA"}, {"name": "Fayetteville", "countryCode": "US", "stateCode": "PA"}, {"name": "Feasterville", "countryCode": "US", "stateCode": "PA"}, {"name": "Fellsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Ferndale", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Fivepointville", "countryCode": "US", "stateCode": "PA"}, {"name": "Fleetwood", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Flourtown", "countryCode": "US", "stateCode": "PA"}, {"name": "Flying Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Ford City", "countryCode": "US", "stateCode": "PA"}, {"name": "Forest City", "countryCode": "US", "stateCode": "PA"}, {"name": "Forest County", "countryCode": "US", "stateCode": "PA"}, {"name": "Forest Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "Fort Washington", "countryCode": "US", "stateCode": "PA"}, {"name": "Forty Fort", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Fountain Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "Fox Chapel", "countryCode": "US", "stateCode": "PA"}, {"name": "Fox Chase", "countryCode": "US", "stateCode": "PA"}, {"name": "Fox Run", "countryCode": "US", "stateCode": "PA"}, {"name": "Frackville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "PA"}, {"name": "Franklin Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Fredericksburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Freedom", "countryCode": "US", "stateCode": "PA"}, {"name": "Freeland", "countryCode": "US", "stateCode": "PA"}, {"name": "Freemansburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Freeport", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Fulton County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Gap", "countryCode": "US", "stateCode": "PA"}, {"name": "Garden View", "countryCode": "US", "stateCode": "PA"}, {"name": "Gastonville", "countryCode": "US", "stateCode": "PA"}, {"name": "Geistown", "countryCode": "US", "stateCode": "PA"}, {"name": "Georgetown", "countryCode": "US", "stateCode": "PA"}, {"name": "Gettysburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Gibsonia", "countryCode": "US", "stateCode": "PA"}, {"name": "Gilbertsville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Girardville", "countryCode": "US", "stateCode": "PA"}, {"name": "Glassport", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Glen Rock", "countryCode": "US", "stateCode": "PA"}, {"name": "Glenolden", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Glenside", "countryCode": "US", "stateCode": "PA"}, {"name": "Gold Key Lake", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Green Tree", "countryCode": "US", "stateCode": "PA"}, {"name": "Greencastle", "countryCode": "US", "stateCode": "PA"}, {"name": "Greene County", "countryCode": "US", "stateCode": "PA"}, {"name": "Greenfields", "countryCode": "US", "stateCode": "PA"}, {"name": "Greenock", "countryCode": "US", "stateCode": "PA"}, {"name": "Greensburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Greenville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Grill", "countryCode": "US", "stateCode": "PA"}, {"name": "Grove City", "countryCode": "US", "stateCode": "PA"}, {"name": "Guilford", "countryCode": "US", "stateCode": "PA"}, {"name": "Guilford Siding", "countryCode": "US", "stateCode": "PA"}, {"name": "Halfway House", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Hallstead", "countryCode": "US", "stateCode": "PA"}, {"name": "Hamburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Hanover", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Harleysville", "countryCode": "US", "stateCode": "PA"}, {"name": "Harrisburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Harveys Lake", "countryCode": "US", "stateCode": "PA"}, {"name": "Hasson Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "Hastings", "countryCode": "US", "stateCode": "PA"}, {"name": "Hatboro", "countryCode": "US", "stateCode": "PA"}, {"name": "Hatfield", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Hebron", "countryCode": "US", "stateCode": "PA"}, {"name": "Heidelberg", "countryCode": "US", "stateCode": "PA"}, {"name": "Hellertown", "countryCode": "US", "stateCode": "PA"}, {"name": "Hemlock Farms", "countryCode": "US", "stateCode": "PA"}, {"name": "Hermitage", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Highland Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Highspire", "countryCode": "US", "stateCode": "PA"}, {"name": "Hilldale", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Hokendauqua", "countryCode": "US", "stateCode": "PA"}, {"name": "Hollidaysburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Homeacre-Lyndora", "countryCode": "US", "stateCode": "PA"}, {"name": "Homer City", "countryCode": "US", "stateCode": "PA"}, {"name": "Homestead", "countryCode": "US", "stateCode": "PA"}, {"name": "Hometown", "countryCode": "US", "stateCode": "PA"}, {"name": "Honesdale", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Houserville", "countryCode": "US", "stateCode": "PA"}, {"name": "Houston", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Hughestown", "countryCode": "US", "stateCode": "PA"}, {"name": "Hughesville", "countryCode": "US", "stateCode": "PA"}, {"name": "Hummels Wharf", "countryCode": "US", "stateCode": "PA"}, {"name": "Hummelstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Huntingdon", "countryCode": "US", "stateCode": "PA"}, {"name": "Huntingdon County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Hyde Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Imperial", "countryCode": "US", "stateCode": "PA"}, {"name": "Indian Mountain Lake", "countryCode": "US", "stateCode": "PA"}, {"name": "Indiana", "countryCode": "US", "stateCode": "PA"}, {"name": "Indiana County", "countryCode": "US", "stateCode": "PA"}, {"name": "Industry", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Inkerman", "countryCode": "US", "stateCode": "PA"}, {"name": "Intercourse", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Ivyland", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "PA"}, {"name": "Jefferson Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "Jenkintown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Jersey Shore", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Johnsonburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Johnstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Jonestown", "countryCode": "US", "stateCode": "PA"}, {"name": "Juniata County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Kennett Square", "countryCode": "US", "stateCode": "PA"}, {"name": "King of Prussia", "countryCode": "US", "stateCode": "PA"}, {"name": "Kingston", "countryCode": "US", "stateCode": "PA"}, {"name": "Kittanning", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Ku<PERSON>psville", "countryCode": "US", "stateCode": "PA"}, {"name": "Kutztown", "countryCode": "US", "stateCode": "PA"}, {"name": "Lackawanna County", "countryCode": "US", "stateCode": "PA"}, {"name": "Lafayette Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Lake City", "countryCode": "US", "stateCode": "PA"}, {"name": "Lake Heritage", "countryCode": "US", "stateCode": "PA"}, {"name": "Lake Latonka", "countryCode": "US", "stateCode": "PA"}, {"name": "Lake Meade", "countryCode": "US", "stateCode": "PA"}, {"name": "Lake Wynonah", "countryCode": "US", "stateCode": "PA"}, {"name": "Lakemont", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Lancaster", "countryCode": "US", "stateCode": "PA"}, {"name": "Lancaster County", "countryCode": "US", "stateCode": "PA"}, {"name": "Landisville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Langhorne Manor", "countryCode": "US", "stateCode": "PA"}, {"name": "Lansdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Lansdowne", "countryCode": "US", "stateCode": "PA"}, {"name": "Lansford", "countryCode": "US", "stateCode": "PA"}, {"name": "Laporte", "countryCode": "US", "stateCode": "PA"}, {"name": "Larksville", "countryCode": "US", "stateCode": "PA"}, {"name": "Latrobe", "countryCode": "US", "stateCode": "PA"}, {"name": "Laureldale", "countryCode": "US", "stateCode": "PA"}, {"name": "Laurys Station", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "PA"}, {"name": "Lawrence Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Lawson Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "PA"}, {"name": "Lebanon County", "countryCode": "US", "stateCode": "PA"}, {"name": "Lebanon South", "countryCode": "US", "stateCode": "PA"}, {"name": "Leechburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Leesport", "countryCode": "US", "stateCode": "PA"}, {"name": "Leetsdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Lehigh County", "countryCode": "US", "stateCode": "PA"}, {"name": "Lehighton", "countryCode": "US", "stateCode": "PA"}, {"name": "Leith-Hatfield", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Lenape Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Level Green", "countryCode": "US", "stateCode": "PA"}, {"name": "Levittown", "countryCode": "US", "stateCode": "PA"}, {"name": "Lewisburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Lewistown", "countryCode": "US", "stateCode": "PA"}, {"name": "Liberty", "countryCode": "US", "stateCode": "PA"}, {"name": "Light Street", "countryCode": "US", "stateCode": "PA"}, {"name": "Ligonier", "countryCode": "US", "stateCode": "PA"}, {"name": "Lima", "countryCode": "US", "stateCode": "PA"}, {"name": "Limerick", "countryCode": "US", "stateCode": "PA"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "PA"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Linglestown", "countryCode": "US", "stateCode": "PA"}, {"name": "Linntown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Lionville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Littlestown", "countryCode": "US", "stateCode": "PA"}, {"name": "Lock Haven", "countryCode": "US", "stateCode": "PA"}, {"name": "Loganville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Lower Allen", "countryCode": "US", "stateCode": "PA"}, {"name": "Lower Burrell", "countryCode": "US", "stateCode": "PA"}, {"name": "Loyalhanna", "countryCode": "US", "stateCode": "PA"}, {"name": "Luzerne", "countryCode": "US", "stateCode": "PA"}, {"name": "Luzerne County", "countryCode": "US", "stateCode": "PA"}, {"name": "Lycoming County", "countryCode": "US", "stateCode": "PA"}, {"name": "Lykens", "countryCode": "US", "stateCode": "PA"}, {"name": "Lynnwood-Pricedale", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Mahanoy City", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Manchester", "countryCode": "US", "stateCode": "PA"}, {"name": "Manheim", "countryCode": "US", "stateCode": "PA"}, {"name": "Manor", "countryCode": "US", "stateCode": "PA"}, {"name": "Mansfield", "countryCode": "US", "stateCode": "PA"}, {"name": "Maple Glen", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Marienville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Mars", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Martinsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Marysville", "countryCode": "US", "stateCode": "PA"}, {"name": "Masontown", "countryCode": "US", "stateCode": "PA"}, {"name": "Matamoras", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Maytown", "countryCode": "US", "stateCode": "PA"}, {"name": "McAdoo", "countryCode": "US", "stateCode": "PA"}, {"name": "McConnellsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "McConnellstown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "McKean County", "countryCode": "US", "stateCode": "PA"}, {"name": "McKees Rocks", "countryCode": "US", "stateCode": "PA"}, {"name": "McKeesport", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "McSherrystown", "countryCode": "US", "stateCode": "PA"}, {"name": "Meadowood", "countryCode": "US", "stateCode": "PA"}, {"name": "Meadville", "countryCode": "US", "stateCode": "PA"}, {"name": "Mechanicsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Mechanicsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Media", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Mercer County", "countryCode": "US", "stateCode": "PA"}, {"name": "Mercersburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Meridian", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Middleburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Middletown", "countryCode": "US", "stateCode": "PA"}, {"name": "Midland", "countryCode": "US", "stateCode": "PA"}, {"name": "Midway", "countryCode": "US", "stateCode": "PA"}, {"name": "Mifflin County", "countryCode": "US", "stateCode": "PA"}, {"name": "Mifflinburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Mifflintown", "countryCode": "US", "stateCode": "PA"}, {"name": "Mifflinville", "countryCode": "US", "stateCode": "PA"}, {"name": "Milesburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Milford", "countryCode": "US", "stateCode": "PA"}, {"name": "Mill Hall", "countryCode": "US", "stateCode": "PA"}, {"name": "Millbourne", "countryCode": "US", "stateCode": "PA"}, {"name": "Millersburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Millersville", "countryCode": "US", "stateCode": "PA"}, {"name": "Millvale", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Minersville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Monaca", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Monongahela", "countryCode": "US", "stateCode": "PA"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "PA"}, {"name": "Monroeville", "countryCode": "US", "stateCode": "PA"}, {"name": "Mont Alto", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "PA"}, {"name": "Montgomeryville", "countryCode": "US", "stateCode": "PA"}, {"name": "Montour County", "countryCode": "US", "stateCode": "PA"}, {"name": "Montoursville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Morningside", "countryCode": "US", "stateCode": "PA"}, {"name": "Morrisville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Moscow", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Carmel", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Cobb", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Holly Springs", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Joy", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Lebanon", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Oliver", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Penn", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Pleasant", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Pocono", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Union", "countryCode": "US", "stateCode": "PA"}, {"name": "Mount Wolf", "countryCode": "US", "stateCode": "PA"}, {"name": "Mountain Top", "countryCode": "US", "stateCode": "PA"}, {"name": "Mountainhome", "countryCode": "US", "stateCode": "PA"}, {"name": "Mountville", "countryCode": "US", "stateCode": "PA"}, {"name": "Muhlenberg Park", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Mundys Corner", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Murrysville", "countryCode": "US", "stateCode": "PA"}, {"name": "Muse", "countryCode": "US", "stateCode": "PA"}, {"name": "Myerstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Nanticoke", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Nazareth", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Nesquehoning", "countryCode": "US", "stateCode": "PA"}, {"name": "New Beaver", "countryCode": "US", "stateCode": "PA"}, {"name": "New Berlinville", "countryCode": "US", "stateCode": "PA"}, {"name": "New Bloomfield", "countryCode": "US", "stateCode": "PA"}, {"name": "New Brighton", "countryCode": "US", "stateCode": "PA"}, {"name": "New Britain", "countryCode": "US", "stateCode": "PA"}, {"name": "New Castle", "countryCode": "US", "stateCode": "PA"}, {"name": "New Castle Northwest", "countryCode": "US", "stateCode": "PA"}, {"name": "New Columbia", "countryCode": "US", "stateCode": "PA"}, {"name": "New Cumberland", "countryCode": "US", "stateCode": "PA"}, {"name": "New Eagle", "countryCode": "US", "stateCode": "PA"}, {"name": "New Freedom", "countryCode": "US", "stateCode": "PA"}, {"name": "New Holland", "countryCode": "US", "stateCode": "PA"}, {"name": "New Hope", "countryCode": "US", "stateCode": "PA"}, {"name": "New Kensington", "countryCode": "US", "stateCode": "PA"}, {"name": "New Oxford", "countryCode": "US", "stateCode": "PA"}, {"name": "New Philadelphia", "countryCode": "US", "stateCode": "PA"}, {"name": "New Stanton", "countryCode": "US", "stateCode": "PA"}, {"name": "New Wilmington", "countryCode": "US", "stateCode": "PA"}, {"name": "Newmanstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Newport", "countryCode": "US", "stateCode": "PA"}, {"name": "Newtown", "countryCode": "US", "stateCode": "PA"}, {"name": "Newtown Grant", "countryCode": "US", "stateCode": "PA"}, {"name": "Newville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Norristown", "countryCode": "US", "stateCode": "PA"}, {"name": "North Apollo", "countryCode": "US", "stateCode": "PA"}, {"name": "North Belle Vernon", "countryCode": "US", "stateCode": "PA"}, {"name": "North Braddock", "countryCode": "US", "stateCode": "PA"}, {"name": "North Catasauqua", "countryCode": "US", "stateCode": "PA"}, {"name": "North Charleroi", "countryCode": "US", "stateCode": "PA"}, {"name": "North East", "countryCode": "US", "stateCode": "PA"}, {"name": "North Versailles", "countryCode": "US", "stateCode": "PA"}, {"name": "North Wales", "countryCode": "US", "stateCode": "PA"}, {"name": "North Warren", "countryCode": "US", "stateCode": "PA"}, {"name": "North York", "countryCode": "US", "stateCode": "PA"}, {"name": "Northampton", "countryCode": "US", "stateCode": "PA"}, {"name": "Northampton County", "countryCode": "US", "stateCode": "PA"}, {"name": "Northern Cambria", "countryCode": "US", "stateCode": "PA"}, {"name": "Northumberland", "countryCode": "US", "stateCode": "PA"}, {"name": "Northumberland County", "countryCode": "US", "stateCode": "PA"}, {"name": "Northwest Harborcreek", "countryCode": "US", "stateCode": "PA"}, {"name": "Norwood", "countryCode": "US", "stateCode": "PA"}, {"name": "Oak Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "Oakdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Oakland", "countryCode": "US", "stateCode": "PA"}, {"name": "Oakmont", "countryCode": "US", "stateCode": "PA"}, {"name": "Oakwood", "countryCode": "US", "stateCode": "PA"}, {"name": "Ohioville", "countryCode": "US", "stateCode": "PA"}, {"name": "Oil City", "countryCode": "US", "stateCode": "PA"}, {"name": "Old Forge", "countryCode": "US", "stateCode": "PA"}, {"name": "Old Orchard", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Olyphant", "countryCode": "US", "stateCode": "PA"}, {"name": "Orchard Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "Oreland", "countryCode": "US", "stateCode": "PA"}, {"name": "Orwigsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Osceola Mills", "countryCode": "US", "stateCode": "PA"}, {"name": "Oxford", "countryCode": "US", "stateCode": "PA"}, {"name": "Palmdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Palmer Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Palmyra", "countryCode": "US", "stateCode": "PA"}, {"name": "Palo Alto", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Paradise", "countryCode": "US", "stateCode": "PA"}, {"name": "Park Forest Village", "countryCode": "US", "stateCode": "PA"}, {"name": "Parkesburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Parkside", "countryCode": "US", "stateCode": "PA"}, {"name": "Parkville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Paxtang", "countryCode": "US", "stateCode": "PA"}, {"name": "Paxtonia", "countryCode": "US", "stateCode": "PA"}, {"name": "Pen Argyl", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Penn Estates", "countryCode": "US", "stateCode": "PA"}, {"name": "Penn Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>e", "countryCode": "US", "stateCode": "PA"}, {"name": "Penndel", "countryCode": "US", "stateCode": "PA"}, {"name": "Pennsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Pennside", "countryCode": "US", "stateCode": "PA"}, {"name": "Pennsport", "countryCode": "US", "stateCode": "PA"}, {"name": "Pennville", "countryCode": "US", "stateCode": "PA"}, {"name": "Penryn", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Perry County", "countryCode": "US", "stateCode": "PA"}, {"name": "Perryopolis", "countryCode": "US", "stateCode": "PA"}, {"name": "Philadelphia", "countryCode": "US", "stateCode": "PA"}, {"name": "Philadelphia County", "countryCode": "US", "stateCode": "PA"}, {"name": "Philipsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Phoenixville", "countryCode": "US", "stateCode": "PA"}, {"name": "Pike County", "countryCode": "US", "stateCode": "PA"}, {"name": "Pine Grove", "countryCode": "US", "stateCode": "PA"}, {"name": "Pine Grove Mills", "countryCode": "US", "stateCode": "PA"}, {"name": "Pine Ridge", "countryCode": "US", "stateCode": "PA"}, {"name": "Pitcairn", "countryCode": "US", "stateCode": "PA"}, {"name": "Pittsburgh", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>ston", "countryCode": "US", "stateCode": "PA"}, {"name": "Plains", "countryCode": "US", "stateCode": "PA"}, {"name": "Pleasant Gap", "countryCode": "US", "stateCode": "PA"}, {"name": "Pleasant Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "Pleasant Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Plumsteadville", "countryCode": "US", "stateCode": "PA"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "PA"}, {"name": "Plymouth Meeting", "countryCode": "US", "stateCode": "PA"}, {"name": "Pocono Pines", "countryCode": "US", "stateCode": "PA"}, {"name": "Pocono Ranch Lands", "countryCode": "US", "stateCode": "PA"}, {"name": "Point Marion", "countryCode": "US", "stateCode": "PA"}, {"name": "Port Allegany", "countryCode": "US", "stateCode": "PA"}, {"name": "Port Carbon", "countryCode": "US", "stateCode": "PA"}, {"name": "Port Vue", "countryCode": "US", "stateCode": "PA"}, {"name": "Portage", "countryCode": "US", "stateCode": "PA"}, {"name": "Potter County", "countryCode": "US", "stateCode": "PA"}, {"name": "Pottsgrove", "countryCode": "US", "stateCode": "PA"}, {"name": "Pottstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Pottsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Progress", "countryCode": "US", "stateCode": "PA"}, {"name": "Prospect", "countryCode": "US", "stateCode": "PA"}, {"name": "Prospect Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Punx<PERSON>ta<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Pymatuning Central", "countryCode": "US", "stateCode": "PA"}, {"name": "Quakertown", "countryCode": "US", "stateCode": "PA"}, {"name": "Quarryville", "countryCode": "US", "stateCode": "PA"}, {"name": "Radnor", "countryCode": "US", "stateCode": "PA"}, {"name": "Rankin", "countryCode": "US", "stateCode": "PA"}, {"name": "Raubsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Reading", "countryCode": "US", "stateCode": "PA"}, {"name": "Reamstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Red Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "Red Lion", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Reinholds", "countryCode": "US", "stateCode": "PA"}, {"name": "Rennerdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Renovo", "countryCode": "US", "stateCode": "PA"}, {"name": "Republic", "countryCode": "US", "stateCode": "PA"}, {"name": "Reynolds Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "Reynoldsville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Richboro", "countryCode": "US", "stateCode": "PA"}, {"name": "Richland", "countryCode": "US", "stateCode": "PA"}, {"name": "Richlandtown", "countryCode": "US", "stateCode": "PA"}, {"name": "Ridgway", "countryCode": "US", "stateCode": "PA"}, {"name": "Ridley Park", "countryCode": "US", "stateCode": "PA"}, {"name": "River View Park", "countryCode": "US", "stateCode": "PA"}, {"name": "Riverside", "countryCode": "US", "stateCode": "PA"}, {"name": "Roaring Spring", "countryCode": "US", "stateCode": "PA"}, {"name": "Robesonia", "countryCode": "US", "stateCode": "PA"}, {"name": "Rochester", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Rothsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Royalton", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Saint Clair", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Saint Marys", "countryCode": "US", "stateCode": "PA"}, {"name": "Salix", "countryCode": "US", "stateCode": "PA"}, {"name": "Salunga", "countryCode": "US", "stateCode": "PA"}, {"name": "Sanatoga", "countryCode": "US", "stateCode": "PA"}, {"name": "Sand Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Saw Creek", "countryCode": "US", "stateCode": "PA"}, {"name": "Saxonburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Saylorsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Schnecksville", "countryCode": "US", "stateCode": "PA"}, {"name": "Schoe<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Schuylkill County", "countryCode": "US", "stateCode": "PA"}, {"name": "Schuylkill Haven", "countryCode": "US", "stateCode": "PA"}, {"name": "Schwenksville", "countryCode": "US", "stateCode": "PA"}, {"name": "Scotland", "countryCode": "US", "stateCode": "PA"}, {"name": "Scottdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Scranton", "countryCode": "US", "stateCode": "PA"}, {"name": "Selinsgrove", "countryCode": "US", "stateCode": "PA"}, {"name": "Sellersville", "countryCode": "US", "stateCode": "PA"}, {"name": "Seneca", "countryCode": "US", "stateCode": "PA"}, {"name": "Seven Fields", "countryCode": "US", "stateCode": "PA"}, {"name": "Sewickley", "countryCode": "US", "stateCode": "PA"}, {"name": "Shamokin", "countryCode": "US", "stateCode": "PA"}, {"name": "Shamokin Dam", "countryCode": "US", "stateCode": "PA"}, {"name": "Shanor-Northvue", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Sharpsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Sharpsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Shavertown", "countryCode": "US", "stateCode": "PA"}, {"name": "Sheffield", "countryCode": "US", "stateCode": "PA"}, {"name": "Shenandoah", "countryCode": "US", "stateCode": "PA"}, {"name": "Shenandoah Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Shiloh", "countryCode": "US", "stateCode": "PA"}, {"name": "Shinglehouse", "countryCode": "US", "stateCode": "PA"}, {"name": "Shippensburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Shiremanstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Shoemakersville", "countryCode": "US", "stateCode": "PA"}, {"name": "Shrewsbury", "countryCode": "US", "stateCode": "PA"}, {"name": "Sierra View", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Sinking Spring", "countryCode": "US", "stateCode": "PA"}, {"name": "Skippack", "countryCode": "US", "stateCode": "PA"}, {"name": "Skyline View", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Slippery Rock", "countryCode": "US", "stateCode": "PA"}, {"name": "Smethport", "countryCode": "US", "stateCode": "PA"}, {"name": "Snyder County", "countryCode": "US", "stateCode": "PA"}, {"name": "Somerset", "countryCode": "US", "stateCode": "PA"}, {"name": "Somerset County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "South Coatesville", "countryCode": "US", "stateCode": "PA"}, {"name": "South Connellsville", "countryCode": "US", "stateCode": "PA"}, {"name": "South Greensburg", "countryCode": "US", "stateCode": "PA"}, {"name": "South Park Township", "countryCode": "US", "stateCode": "PA"}, {"name": "South Pottstown", "countryCode": "US", "stateCode": "PA"}, {"name": "South Temple", "countryCode": "US", "stateCode": "PA"}, {"name": "South Uniontown", "countryCode": "US", "stateCode": "PA"}, {"name": "South Waverly", "countryCode": "US", "stateCode": "PA"}, {"name": "South Williamsport", "countryCode": "US", "stateCode": "PA"}, {"name": "Southmont", "countryCode": "US", "stateCode": "PA"}, {"name": "Southwest Greensburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Spangler", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Spinnerstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Spring City", "countryCode": "US", "stateCode": "PA"}, {"name": "Spring Grove", "countryCode": "US", "stateCode": "PA"}, {"name": "Spring House", "countryCode": "US", "stateCode": "PA"}, {"name": "Spring Mount", "countryCode": "US", "stateCode": "PA"}, {"name": "Spring Ridge", "countryCode": "US", "stateCode": "PA"}, {"name": "Springdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Springfield", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "State College", "countryCode": "US", "stateCode": "PA"}, {"name": "State Line", "countryCode": "US", "stateCode": "PA"}, {"name": "Steelton", "countryCode": "US", "stateCode": "PA"}, {"name": "Stewartstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Stiles", "countryCode": "US", "stateCode": "PA"}, {"name": "Stoneboro", "countryCode": "US", "stateCode": "PA"}, {"name": "Stony Creek Mills", "countryCode": "US", "stateCode": "PA"}, {"name": "Stonybrook", "countryCode": "US", "stateCode": "PA"}, {"name": "Stormstown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Strasburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Stroudsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Sturgeon", "countryCode": "US", "stateCode": "PA"}, {"name": "Sugarcreek", "countryCode": "US", "stateCode": "PA"}, {"name": "Sullivan County", "countryCode": "US", "stateCode": "PA"}, {"name": "Summit Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "Sun Valley", "countryCode": "US", "stateCode": "PA"}, {"name": "Sunbury", "countryCode": "US", "stateCode": "PA"}, {"name": "Sunrise Lake", "countryCode": "US", "stateCode": "PA"}, {"name": "Susquehanna", "countryCode": "US", "stateCode": "PA"}, {"name": "Susquehanna County", "countryCode": "US", "stateCode": "PA"}, {"name": "Susquehanna Trails", "countryCode": "US", "stateCode": "PA"}, {"name": "Swart<PERSON>ore", "countryCode": "US", "stateCode": "PA"}, {"name": "Swartzville", "countryCode": "US", "stateCode": "PA"}, {"name": "Swissvale", "countryCode": "US", "stateCode": "PA"}, {"name": "Swoyersville", "countryCode": "US", "stateCode": "PA"}, {"name": "Sykesville", "countryCode": "US", "stateCode": "PA"}, {"name": "Tacony", "countryCode": "US", "stateCode": "PA"}, {"name": "Tamaqua", "countryCode": "US", "stateCode": "PA"}, {"name": "Tannersville", "countryCode": "US", "stateCode": "PA"}, {"name": "Tarentum", "countryCode": "US", "stateCode": "PA"}, {"name": "Ta<PERSON>y", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Telford", "countryCode": "US", "stateCode": "PA"}, {"name": "Temple", "countryCode": "US", "stateCode": "PA"}, {"name": "Terre Hill", "countryCode": "US", "stateCode": "PA"}, {"name": "The Hideout", "countryCode": "US", "stateCode": "PA"}, {"name": "Thompsonville", "countryCode": "US", "stateCode": "PA"}, {"name": "Thorndale", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Tinicum", "countryCode": "US", "stateCode": "PA"}, {"name": "Tioga County", "countryCode": "US", "stateCode": "PA"}, {"name": "Tionesta", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Titusville", "countryCode": "US", "stateCode": "PA"}, {"name": "Toftrees", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Toughkenamon", "countryCode": "US", "stateCode": "PA"}, {"name": "Towamensing Trails", "countryCode": "US", "stateCode": "PA"}, {"name": "Towanda", "countryCode": "US", "stateCode": "PA"}, {"name": "Tower City", "countryCode": "US", "stateCode": "PA"}, {"name": "Trafford", "countryCode": "US", "stateCode": "PA"}, {"name": "Trainer", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Treasure Lake", "countryCode": "US", "stateCode": "PA"}, {"name": "Tremont", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Trexlertown", "countryCode": "US", "stateCode": "PA"}, {"name": "Trooper", "countryCode": "US", "stateCode": "PA"}, {"name": "Troy", "countryCode": "US", "stateCode": "PA"}, {"name": "Trucksville", "countryCode": "US", "stateCode": "PA"}, {"name": "Tullytown", "countryCode": "US", "stateCode": "PA"}, {"name": "Tunkhannock", "countryCode": "US", "stateCode": "PA"}, {"name": "Turtle Creek", "countryCode": "US", "stateCode": "PA"}, {"name": "Tyrone", "countryCode": "US", "stateCode": "PA"}, {"name": "Union City", "countryCode": "US", "stateCode": "PA"}, {"name": "Union County", "countryCode": "US", "stateCode": "PA"}, {"name": "Uniontown", "countryCode": "US", "stateCode": "PA"}, {"name": "Upland", "countryCode": "US", "stateCode": "PA"}, {"name": "Upper Saint Clair", "countryCode": "US", "stateCode": "PA"}, {"name": "Valley Green", "countryCode": "US", "stateCode": "PA"}, {"name": "Valley View", "countryCode": "US", "stateCode": "PA"}, {"name": "Vandergrift", "countryCode": "US", "stateCode": "PA"}, {"name": "Venango County", "countryCode": "US", "stateCode": "PA"}, {"name": "Verona", "countryCode": "US", "stateCode": "PA"}, {"name": "Versailles", "countryCode": "US", "stateCode": "PA"}, {"name": "Village Green-Green Ridge", "countryCode": "US", "stateCode": "PA"}, {"name": "Village Shires", "countryCode": "US", "stateCode": "PA"}, {"name": "Vinco", "countryCode": "US", "stateCode": "PA"}, {"name": "Wallenpaupack Lake Estates", "countryCode": "US", "stateCode": "PA"}, {"name": "Walnutport", "countryCode": "US", "stateCode": "PA"}, {"name": "Warminster Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Warren County", "countryCode": "US", "stateCode": "PA"}, {"name": "Washington", "countryCode": "US", "stateCode": "PA"}, {"name": "Washington County", "countryCode": "US", "stateCode": "PA"}, {"name": "Waterford", "countryCode": "US", "stateCode": "PA"}, {"name": "Watsontown", "countryCode": "US", "stateCode": "PA"}, {"name": "Waymart", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "PA"}, {"name": "Wayne Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "Waynesboro", "countryCode": "US", "stateCode": "PA"}, {"name": "Waynesburg", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>ly", "countryCode": "US", "stateCode": "PA"}, {"name": "Weigelstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Weissport East", "countryCode": "US", "stateCode": "PA"}, {"name": "Wellsboro", "countryCode": "US", "stateCode": "PA"}, {"name": "Wernersville", "countryCode": "US", "stateCode": "PA"}, {"name": "Wescosville", "countryCode": "US", "stateCode": "PA"}, {"name": "Wesleyville", "countryCode": "US", "stateCode": "PA"}, {"name": "West Chester", "countryCode": "US", "stateCode": "PA"}, {"name": "West Conshohocken", "countryCode": "US", "stateCode": "PA"}, {"name": "West Easton", "countryCode": "US", "stateCode": "PA"}, {"name": "West Fairview", "countryCode": "US", "stateCode": "PA"}, {"name": "West Grove", "countryCode": "US", "stateCode": "PA"}, {"name": "West Hamburg", "countryCode": "US", "stateCode": "PA"}, {"name": "West Hazleton", "countryCode": "US", "stateCode": "PA"}, {"name": "West Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "West Homestead", "countryCode": "US", "stateCode": "PA"}, {"name": "West Kittanning", "countryCode": "US", "stateCode": "PA"}, {"name": "West Lawn", "countryCode": "US", "stateCode": "PA"}, {"name": "West Leechburg", "countryCode": "US", "stateCode": "PA"}, {"name": "West Mayfield", "countryCode": "US", "stateCode": "PA"}, {"name": "West Mifflin", "countryCode": "US", "stateCode": "PA"}, {"name": "West Newton", "countryCode": "US", "stateCode": "PA"}, {"name": "West Norriton", "countryCode": "US", "stateCode": "PA"}, {"name": "West Pittston", "countryCode": "US", "stateCode": "PA"}, {"name": "West Reading", "countryCode": "US", "stateCode": "PA"}, {"name": "West View", "countryCode": "US", "stateCode": "PA"}, {"name": "West Wyoming", "countryCode": "US", "stateCode": "PA"}, {"name": "West Wyomissing", "countryCode": "US", "stateCode": "PA"}, {"name": "West York", "countryCode": "US", "stateCode": "PA"}, {"name": "Westfield", "countryCode": "US", "stateCode": "PA"}, {"name": "Westmont", "countryCode": "US", "stateCode": "PA"}, {"name": "Westmoreland County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "White Haven", "countryCode": "US", "stateCode": "PA"}, {"name": "White Oak", "countryCode": "US", "stateCode": "PA"}, {"name": "Whitehall", "countryCode": "US", "stateCode": "PA"}, {"name": "Whitehall Township", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Wickerham Manor-Fisher", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Wilkinsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Williamsburg", "countryCode": "US", "stateCode": "PA"}, {"name": "Williamsport", "countryCode": "US", "stateCode": "PA"}, {"name": "Williamstown", "countryCode": "US", "stateCode": "PA"}, {"name": "Willow Grove", "countryCode": "US", "stateCode": "PA"}, {"name": "Willow Street", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Wind Gap", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Windsor", "countryCode": "US", "stateCode": "PA"}, {"name": "Wolfdale", "countryCode": "US", "stateCode": "PA"}, {"name": "Womelsdorf", "countryCode": "US", "stateCode": "PA"}, {"name": "Woodbourne", "countryCode": "US", "stateCode": "PA"}, {"name": "Woodland Heights", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Woodside", "countryCode": "US", "stateCode": "PA"}, {"name": "Wormleysburg", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Wrightsville", "countryCode": "US", "stateCode": "PA"}, {"name": "Wyncote", "countryCode": "US", "stateCode": "PA"}, {"name": "Wyndmoor", "countryCode": "US", "stateCode": "PA"}, {"name": "Wyoming", "countryCode": "US", "stateCode": "PA"}, {"name": "Wyoming County", "countryCode": "US", "stateCode": "PA"}, {"name": "Wyomissing", "countryCode": "US", "stateCode": "PA"}, {"name": "Wyomissing Hills", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Yeadon", "countryCode": "US", "stateCode": "PA"}, {"name": "Yeagertown", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>e", "countryCode": "US", "stateCode": "PA"}, {"name": "York", "countryCode": "US", "stateCode": "PA"}, {"name": "York County", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON>lyn", "countryCode": "US", "stateCode": "PA"}, {"name": "Youngsville", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA"}, {"name": "Zion", "countryCode": "US", "stateCode": "PA"}]