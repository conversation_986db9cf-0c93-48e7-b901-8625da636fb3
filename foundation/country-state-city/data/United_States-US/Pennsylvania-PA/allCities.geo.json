[{"name": "Abbottstown", "countryCode": "US", "stateCode": "PA", "latitude": "39.88649000", "longitude": "-76.98470000"}, {"name": "Adams County", "countryCode": "US", "stateCode": "PA", "latitude": "39.87145000", "longitude": "-77.21789000"}, {"name": "Adamstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.24120000", "longitude": "-76.05633000"}, {"name": "Akron", "countryCode": "US", "stateCode": "PA", "latitude": "40.15676000", "longitude": "-76.20217000"}, {"name": "Albion", "countryCode": "US", "stateCode": "PA", "latitude": "41.89061000", "longitude": "-80.36645000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.51093000", "longitude": "-75.60297000"}, {"name": "Aldan", "countryCode": "US", "stateCode": "PA", "latitude": "39.92150000", "longitude": "-75.28796000"}, {"name": "Aliquippa", "countryCode": "US", "stateCode": "PA", "latitude": "40.63673000", "longitude": "-80.24006000"}, {"name": "Allegheny County", "countryCode": "US", "stateCode": "PA", "latitude": "40.46883000", "longitude": "-79.98119000"}, {"name": "Alleghenyville", "countryCode": "US", "stateCode": "PA", "latitude": "40.23426000", "longitude": "-75.98855000"}, {"name": "Allentown", "countryCode": "US", "stateCode": "PA", "latitude": "40.60843000", "longitude": "-75.49018000"}, {"name": "Allison <PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.55951000", "longitude": "-79.95867000"}, {"name": "Almedia", "countryCode": "US", "stateCode": "PA", "latitude": "41.01453000", "longitude": "-76.38105000"}, {"name": "Altoona", "countryCode": "US", "stateCode": "PA", "latitude": "40.51868000", "longitude": "-78.39474000"}, {"name": "Ambler", "countryCode": "US", "stateCode": "PA", "latitude": "40.15455000", "longitude": "-75.22157000"}, {"name": "Ambridge", "countryCode": "US", "stateCode": "PA", "latitude": "40.58923000", "longitude": "-80.22506000"}, {"name": "Amity Gardens", "countryCode": "US", "stateCode": "PA", "latitude": "40.27426000", "longitude": "-75.73519000"}, {"name": "Ancient Oaks", "countryCode": "US", "stateCode": "PA", "latitude": "40.54732000", "longitude": "-75.58935000"}, {"name": "Annville", "countryCode": "US", "stateCode": "PA", "latitude": "40.32954000", "longitude": "-76.51524000"}, {"name": "Apollo", "countryCode": "US", "stateCode": "PA", "latitude": "40.58145000", "longitude": "-79.56643000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.49480000", "longitude": "-75.53685000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.00678000", "longitude": "-75.28546000"}, {"name": "Arlington Heights", "countryCode": "US", "stateCode": "PA", "latitude": "40.99009000", "longitude": "-75.21629000"}, {"name": "Armstrong County", "countryCode": "US", "stateCode": "PA", "latitude": "40.81229000", "longitude": "-79.46454000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.58007000", "longitude": "-79.76672000"}, {"name": "Ashland", "countryCode": "US", "stateCode": "PA", "latitude": "40.78175000", "longitude": "-76.34578000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.21036000", "longitude": "-75.89659000"}, {"name": "Aspinwall", "countryCode": "US", "stateCode": "PA", "latitude": "40.49146000", "longitude": "-79.90477000"}, {"name": "Atglen", "countryCode": "US", "stateCode": "PA", "latitude": "39.94927000", "longitude": "-75.97356000"}, {"name": "Athens", "countryCode": "US", "stateCode": "PA", "latitude": "41.95730000", "longitude": "-76.51800000"}, {"name": "Audubon", "countryCode": "US", "stateCode": "PA", "latitude": "40.12788000", "longitude": "-75.43185000"}, {"name": "Avalon", "countryCode": "US", "stateCode": "PA", "latitude": "40.50090000", "longitude": "-80.06756000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.18479000", "longitude": "-77.31386000"}, {"name": "Avoca", "countryCode": "US", "stateCode": "PA", "latitude": "41.33980000", "longitude": "-75.73630000"}, {"name": "Avon", "countryCode": "US", "stateCode": "PA", "latitude": "40.34565000", "longitude": "-76.38996000"}, {"name": "Avondale", "countryCode": "US", "stateCode": "PA", "latitude": "39.82344000", "longitude": "-75.78327000"}, {"name": "Avonia", "countryCode": "US", "stateCode": "PA", "latitude": "42.04561000", "longitude": "-80.26979000"}, {"name": "Back Mountain", "countryCode": "US", "stateCode": "PA", "latitude": "41.33591000", "longitude": "-75.99631000"}, {"name": "Baden", "countryCode": "US", "stateCode": "PA", "latitude": "40.63507000", "longitude": "-80.22812000"}, {"name": "Baidland", "countryCode": "US", "stateCode": "PA", "latitude": "40.19479000", "longitude": "-79.97088000"}, {"name": "Bainbridge", "countryCode": "US", "stateCode": "PA", "latitude": "40.09093000", "longitude": "-76.66747000"}, {"name": "Bakerstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.65090000", "longitude": "-79.93644000"}, {"name": "Bala-Cynwyd", "countryCode": "US", "stateCode": "PA", "latitude": "40.00761000", "longitude": "-75.23407000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.33813000", "longitude": "-79.97894000"}, {"name": "<PERSON>y", "countryCode": "US", "stateCode": "PA", "latitude": "40.40232000", "longitude": "-75.58713000"}, {"name": "Bangor", "countryCode": "US", "stateCode": "PA", "latitude": "40.86565000", "longitude": "-75.20657000"}, {"name": "Barnesboro", "countryCode": "US", "stateCode": "PA", "latitude": "40.66257000", "longitude": "-78.78003000"}, {"name": "Bath", "countryCode": "US", "stateCode": "PA", "latitude": "40.72565000", "longitude": "-75.39407000"}, {"name": "Bear Rocks", "countryCode": "US", "stateCode": "PA", "latitude": "40.12285000", "longitude": "-79.46170000"}, {"name": "Beaver", "countryCode": "US", "stateCode": "PA", "latitude": "40.69534000", "longitude": "-80.30478000"}, {"name": "Beaver County", "countryCode": "US", "stateCode": "PA", "latitude": "40.68226000", "longitude": "-80.34929000"}, {"name": "Beaver Falls", "countryCode": "US", "stateCode": "PA", "latitude": "40.75201000", "longitude": "-80.31923000"}, {"name": "Beaverdale", "countryCode": "US", "stateCode": "PA", "latitude": "40.32202000", "longitude": "-78.69696000"}, {"name": "Bedford", "countryCode": "US", "stateCode": "PA", "latitude": "40.01869000", "longitude": "-78.50391000"}, {"name": "Bedford County", "countryCode": "US", "stateCode": "PA", "latitude": "40.00656000", "longitude": "-78.49032000"}, {"name": "Bedminster", "countryCode": "US", "stateCode": "PA", "latitude": "40.42594000", "longitude": "-75.17906000"}, {"name": "Beech Mountain Lakes", "countryCode": "US", "stateCode": "PA", "latitude": "41.04158000", "longitude": "-75.93545000"}, {"name": "Belfast", "countryCode": "US", "stateCode": "PA", "latitude": "40.78065000", "longitude": "-75.27796000"}, {"name": "Bell Acres", "countryCode": "US", "stateCode": "PA", "latitude": "40.59007000", "longitude": "-80.16645000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.12507000", "longitude": "-79.86644000"}, {"name": "Bellefonte", "countryCode": "US", "stateCode": "PA", "latitude": "40.91339000", "longitude": "-77.77833000"}, {"name": "Belleville", "countryCode": "US", "stateCode": "PA", "latitude": "40.60507000", "longitude": "-77.72555000"}, {"name": "Bellevue", "countryCode": "US", "stateCode": "PA", "latitude": "40.49396000", "longitude": "-80.05172000"}, {"name": "Bellwood", "countryCode": "US", "stateCode": "PA", "latitude": "40.60340000", "longitude": "-78.32474000"}, {"name": "Belmont", "countryCode": "US", "stateCode": "PA", "latitude": "40.28730000", "longitude": "-78.88947000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.50812000", "longitude": "-80.08311000"}, {"name": "Bentleyville", "countryCode": "US", "stateCode": "PA", "latitude": "40.11674000", "longitude": "-80.00839000"}, {"name": "Berks County", "countryCode": "US", "stateCode": "PA", "latitude": "40.41630000", "longitude": "-75.92600000"}, {"name": "Berlin", "countryCode": "US", "stateCode": "PA", "latitude": "39.92064000", "longitude": "-78.95780000"}, {"name": "Berwick", "countryCode": "US", "stateCode": "PA", "latitude": "41.05453000", "longitude": "-76.23327000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.04483000", "longitude": "-75.43881000"}, {"name": "Bessemer", "countryCode": "US", "stateCode": "PA", "latitude": "40.97478000", "longitude": "-80.49368000"}, {"name": "Bethel Park", "countryCode": "US", "stateCode": "PA", "latitude": "40.32757000", "longitude": "-80.03950000"}, {"name": "Bethlehem", "countryCode": "US", "stateCode": "PA", "latitude": "40.62593000", "longitude": "-75.37046000"}, {"name": "Big Bass Lake", "countryCode": "US", "stateCode": "PA", "latitude": "41.25383000", "longitude": "-75.47644000"}, {"name": "Big Beaver", "countryCode": "US", "stateCode": "PA", "latitude": "40.82451000", "longitude": "-80.36284000"}, {"name": "Biglerville", "countryCode": "US", "stateCode": "PA", "latitude": "39.93037000", "longitude": "-77.24804000"}, {"name": "Birchwood Lakes", "countryCode": "US", "stateCode": "PA", "latitude": "41.25454000", "longitude": "-74.91850000"}, {"name": "Birdsboro", "countryCode": "US", "stateCode": "PA", "latitude": "40.26454000", "longitude": "-75.80409000"}, {"name": "Black Lick", "countryCode": "US", "stateCode": "PA", "latitude": "40.47250000", "longitude": "-79.18688000"}, {"name": "Blair County", "countryCode": "US", "stateCode": "PA", "latitude": "40.48100000", "longitude": "-78.34860000"}, {"name": "Blairsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.43118000", "longitude": "-79.26087000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.48091000", "longitude": "-75.59463000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.44120000", "longitude": "-75.88687000"}, {"name": "Blawnox", "countryCode": "US", "stateCode": "PA", "latitude": "40.49340000", "longitude": "-79.86061000"}, {"name": "Bloomfield", "countryCode": "US", "stateCode": "PA", "latitude": "40.46090000", "longitude": "-79.95089000"}, {"name": "Bloomsburg", "countryCode": "US", "stateCode": "PA", "latitude": "41.00370000", "longitude": "-76.45495000"}, {"name": "Blossburg", "countryCode": "US", "stateCode": "PA", "latitude": "41.67952000", "longitude": "-77.06386000"}, {"name": "Blue Ball", "countryCode": "US", "stateCode": "PA", "latitude": "40.11871000", "longitude": "-76.04717000"}, {"name": "Blue Bell", "countryCode": "US", "stateCode": "PA", "latitude": "40.15233000", "longitude": "-75.26629000"}, {"name": "Boalsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.77562000", "longitude": "-77.79250000"}, {"name": "Boiling Springs", "countryCode": "US", "stateCode": "PA", "latitude": "40.14981000", "longitude": "-77.12831000"}, {"name": "Bonneauville", "countryCode": "US", "stateCode": "PA", "latitude": "39.81204000", "longitude": "-77.13721000"}, {"name": "Boothwyn", "countryCode": "US", "stateCode": "PA", "latitude": "39.83011000", "longitude": "-75.44158000"}, {"name": "Boswell", "countryCode": "US", "stateCode": "PA", "latitude": "40.16147000", "longitude": "-79.02892000"}, {"name": "Bowmansville", "countryCode": "US", "stateCode": "PA", "latitude": "40.19676000", "longitude": "-76.01744000"}, {"name": "Boyertown", "countryCode": "US", "stateCode": "PA", "latitude": "40.33371000", "longitude": "-75.63741000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.60812000", "longitude": "-79.74116000"}, {"name": "Braddock", "countryCode": "US", "stateCode": "PA", "latitude": "40.40340000", "longitude": "-79.86838000"}, {"name": "Braddock Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.41729000", "longitude": "-79.86505000"}, {"name": "Bradford", "countryCode": "US", "stateCode": "PA", "latitude": "41.95590000", "longitude": "-78.64392000"}, {"name": "Bradford County", "countryCode": "US", "stateCode": "PA", "latitude": "41.78867000", "longitude": "-76.51545000"}, {"name": "Bradford Woods", "countryCode": "US", "stateCode": "PA", "latitude": "40.63757000", "longitude": "-80.08172000"}, {"name": "Breinigsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.53676000", "longitude": "-75.63130000"}, {"name": "Brentwood", "countryCode": "US", "stateCode": "PA", "latitude": "40.37063000", "longitude": "-79.97477000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.22953000", "longitude": "-76.81997000"}, {"name": "Brickerville", "countryCode": "US", "stateCode": "PA", "latitude": "40.22593000", "longitude": "-76.30246000"}, {"name": "Bridgeport", "countryCode": "US", "stateCode": "PA", "latitude": "40.10511000", "longitude": "-75.34518000"}, {"name": "Bridgeville", "countryCode": "US", "stateCode": "PA", "latitude": "40.35618000", "longitude": "-80.11006000"}, {"name": "Bristol", "countryCode": "US", "stateCode": "PA", "latitude": "40.10067000", "longitude": "-74.85183000"}, {"name": "Brittany Farms-Highlands", "countryCode": "US", "stateCode": "PA", "latitude": "40.26901000", "longitude": "-75.21401000"}, {"name": "Brockway", "countryCode": "US", "stateCode": "PA", "latitude": "41.24923000", "longitude": "-78.79947000"}, {"name": "Brodheadsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.92454000", "longitude": "-75.39379000"}, {"name": "Brookhaven", "countryCode": "US", "stateCode": "PA", "latitude": "39.86928000", "longitude": "-75.38241000"}, {"name": "Brookville", "countryCode": "US", "stateCode": "PA", "latitude": "41.16117000", "longitude": "-79.08309000"}, {"name": "<PERSON><PERSON>all", "countryCode": "US", "stateCode": "PA", "latitude": "39.98150000", "longitude": "-75.35658000"}, {"name": "Brownstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.12371000", "longitude": "-76.21384000"}, {"name": "Brownsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.02369000", "longitude": "-79.88394000"}, {"name": "Browntown", "countryCode": "US", "stateCode": "PA", "latitude": "41.30980000", "longitude": "-75.78742000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.13150000", "longitude": "-75.06739000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.01983000", "longitude": "-75.30463000"}, {"name": "Bucks County", "countryCode": "US", "stateCode": "PA", "latitude": "40.33694000", "longitude": "-75.10687000"}, {"name": "Burgettstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.38201000", "longitude": "-80.39284000"}, {"name": "Burnham", "countryCode": "US", "stateCode": "PA", "latitude": "40.63868000", "longitude": "-77.56861000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.86118000", "longitude": "-79.89533000"}, {"name": "Butler County", "countryCode": "US", "stateCode": "PA", "latitude": "40.91172000", "longitude": "-79.91299000"}, {"name": "California", "countryCode": "US", "stateCode": "PA", "latitude": "40.06563000", "longitude": "-79.89171000"}, {"name": "Caln", "countryCode": "US", "stateCode": "PA", "latitude": "39.99094000", "longitude": "-75.78022000"}, {"name": "Calumet", "countryCode": "US", "stateCode": "PA", "latitude": "40.21090000", "longitude": "-79.48532000"}, {"name": "Cambria County", "countryCode": "US", "stateCode": "PA", "latitude": "40.49529000", "longitude": "-78.71370000"}, {"name": "Cambridge Springs", "countryCode": "US", "stateCode": "PA", "latitude": "41.80367000", "longitude": "-80.05644000"}, {"name": "Cameron County", "countryCode": "US", "stateCode": "PA", "latitude": "41.43680000", "longitude": "-78.20378000"}, {"name": "Camp Hill", "countryCode": "US", "stateCode": "PA", "latitude": "40.23981000", "longitude": "-76.91997000"}, {"name": "Campbelltown", "countryCode": "US", "stateCode": "PA", "latitude": "40.27759000", "longitude": "-76.58525000"}, {"name": "Canonsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.26257000", "longitude": "-80.18728000"}, {"name": "Canton", "countryCode": "US", "stateCode": "PA", "latitude": "41.65646000", "longitude": "-76.85329000"}, {"name": "Carbon County", "countryCode": "US", "stateCode": "PA", "latitude": "40.91818000", "longitude": "-75.70882000"}, {"name": "Carbondale", "countryCode": "US", "stateCode": "PA", "latitude": "41.57369000", "longitude": "-75.50185000"}, {"name": "Carlisle", "countryCode": "US", "stateCode": "PA", "latitude": "40.20148000", "longitude": "-77.18887000"}, {"name": "Carnegie", "countryCode": "US", "stateCode": "PA", "latitude": "40.40868000", "longitude": "-80.08339000"}, {"name": "Carnot<PERSON>Moon", "countryCode": "US", "stateCode": "PA", "latitude": "40.51856000", "longitude": "-80.21736000"}, {"name": "Carroll Valley", "countryCode": "US", "stateCode": "PA", "latitude": "39.74926000", "longitude": "-77.38304000"}, {"name": "Castanea", "countryCode": "US", "stateCode": "PA", "latitude": "41.12479000", "longitude": "-77.42970000"}, {"name": "Castle Shannon", "countryCode": "US", "stateCode": "PA", "latitude": "40.36479000", "longitude": "-80.02228000"}, {"name": "Catasauqua", "countryCode": "US", "stateCode": "PA", "latitude": "40.65482000", "longitude": "-75.47463000"}, {"name": "Catawissa", "countryCode": "US", "stateCode": "PA", "latitude": "40.95203000", "longitude": "-76.45967000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.31819000", "longitude": "-80.19331000"}, {"name": "Cementon", "countryCode": "US", "stateCode": "PA", "latitude": "40.68926000", "longitude": "-75.50768000"}, {"name": "Center City", "countryCode": "US", "stateCode": "PA", "latitude": "39.95120000", "longitude": "-75.15923000"}, {"name": "Centerville", "countryCode": "US", "stateCode": "PA", "latitude": "40.04535000", "longitude": "-79.97561000"}, {"name": "Central City", "countryCode": "US", "stateCode": "PA", "latitude": "40.11063000", "longitude": "-78.80197000"}, {"name": "Centre County", "countryCode": "US", "stateCode": "PA", "latitude": "40.91934000", "longitude": "-77.81995000"}, {"name": "Centre Hall", "countryCode": "US", "stateCode": "PA", "latitude": "40.84756000", "longitude": "-77.68611000"}, {"name": "Cetronia", "countryCode": "US", "stateCode": "PA", "latitude": "40.58676000", "longitude": "-75.52963000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.28844000", "longitude": "-75.20906000"}, {"name": "Chambersburg", "countryCode": "US", "stateCode": "PA", "latitude": "39.93759000", "longitude": "-77.66110000"}, {"name": "Charleroi", "countryCode": "US", "stateCode": "PA", "latitude": "40.13785000", "longitude": "-79.89810000"}, {"name": "Cherryville", "countryCode": "US", "stateCode": "PA", "latitude": "40.75398000", "longitude": "-75.53852000"}, {"name": "Chester", "countryCode": "US", "stateCode": "PA", "latitude": "39.84967000", "longitude": "-75.35707000"}, {"name": "Chester County", "countryCode": "US", "stateCode": "PA", "latitude": "39.97314000", "longitude": "-75.74845000"}, {"name": "Chester Heights", "countryCode": "US", "stateCode": "PA", "latitude": "39.89011000", "longitude": "-75.47548000"}, {"name": "Chester Springs", "countryCode": "US", "stateCode": "PA", "latitude": "40.09510000", "longitude": "-75.61687000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.07566000", "longitude": "-75.45908000"}, {"name": "Cheswick", "countryCode": "US", "stateCode": "PA", "latitude": "40.54173000", "longitude": "-79.79922000"}, {"name": "Chevy Chase Heights", "countryCode": "US", "stateCode": "PA", "latitude": "40.63673000", "longitude": "-79.14420000"}, {"name": "Chicora", "countryCode": "US", "stateCode": "PA", "latitude": "40.94812000", "longitude": "-79.74283000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.47508000", "longitude": "-75.67713000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.95483000", "longitude": "-75.99689000"}, {"name": "Church Hill", "countryCode": "US", "stateCode": "PA", "latitude": "40.68201000", "longitude": "-77.59861000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.43840000", "longitude": "-79.84310000"}, {"name": "Churchville", "countryCode": "US", "stateCode": "PA", "latitude": "40.18622000", "longitude": "-75.01878000"}, {"name": "Clairton", "countryCode": "US", "stateCode": "PA", "latitude": "40.29229000", "longitude": "-79.88171000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.21479000", "longitude": "-79.38532000"}, {"name": "Clarion County", "countryCode": "US", "stateCode": "PA", "latitude": "41.19239000", "longitude": "-79.42096000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.49286000", "longitude": "-75.69964000"}, {"name": "Clarks Summit", "countryCode": "US", "stateCode": "PA", "latitude": "41.48869000", "longitude": "-75.70852000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.21843000", "longitude": "-76.25551000"}, {"name": "Claysburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.29674000", "longitude": "-78.44974000"}, {"name": "Clearfield", "countryCode": "US", "stateCode": "PA", "latitude": "41.02728000", "longitude": "-78.43919000"}, {"name": "Clearfield County", "countryCode": "US", "stateCode": "PA", "latitude": "41.00019000", "longitude": "-78.47411000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.33731000", "longitude": "-76.47552000"}, {"name": "Clifton Heights", "countryCode": "US", "stateCode": "PA", "latitude": "39.92928000", "longitude": "-75.29630000"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "PA", "latitude": "41.23405000", "longitude": "-77.63811000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.66812000", "longitude": "-79.01170000"}, {"name": "Coaldale", "countryCode": "US", "stateCode": "PA", "latitude": "40.82287000", "longitude": "-75.90687000"}, {"name": "Coatesville", "countryCode": "US", "stateCode": "PA", "latitude": "39.98316000", "longitude": "-75.82384000"}, {"name": "Cochranton", "countryCode": "US", "stateCode": "PA", "latitude": "41.52005000", "longitude": "-80.04839000"}, {"name": "Collegeville", "countryCode": "US", "stateCode": "PA", "latitude": "40.18566000", "longitude": "-75.45157000"}, {"name": "Collingdale", "countryCode": "US", "stateCode": "PA", "latitude": "39.91178000", "longitude": "-75.27713000"}, {"name": "Collinsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.22424000", "longitude": "-79.76838000"}, {"name": "Colonial Park", "countryCode": "US", "stateCode": "PA", "latitude": "40.30064000", "longitude": "-76.80969000"}, {"name": "Colony Park", "countryCode": "US", "stateCode": "PA", "latitude": "40.34683000", "longitude": "-75.98240000"}, {"name": "Columbia", "countryCode": "US", "stateCode": "PA", "latitude": "40.03371000", "longitude": "-76.50441000"}, {"name": "Columbia County", "countryCode": "US", "stateCode": "PA", "latitude": "41.04865000", "longitude": "-76.40515000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.91234000", "longitude": "-75.25379000"}, {"name": "Conashaugh Lakes", "countryCode": "US", "stateCode": "PA", "latitude": "41.30593000", "longitude": "-74.98962000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.32674000", "longitude": "-78.90808000"}, {"name": "Conestoga", "countryCode": "US", "stateCode": "PA", "latitude": "39.94066000", "longitude": "-76.34635000"}, {"name": "Conneaut Lakeshore", "countryCode": "US", "stateCode": "PA", "latitude": "41.62711000", "longitude": "-80.31008000"}, {"name": "Connellsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.01785000", "longitude": "-79.58948000"}, {"name": "Conshohocken", "countryCode": "US", "stateCode": "PA", "latitude": "40.07928000", "longitude": "-75.30157000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.65979000", "longitude": "-80.23923000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.99203000", "longitude": "-76.05659000"}, {"name": "Coopersburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.51149000", "longitude": "-75.39046000"}, {"name": "Coplay", "countryCode": "US", "stateCode": "PA", "latitude": "40.67010000", "longitude": "-75.49546000"}, {"name": "Coraopolis", "countryCode": "US", "stateCode": "PA", "latitude": "40.51840000", "longitude": "-80.16672000"}, {"name": "Cornwall", "countryCode": "US", "stateCode": "PA", "latitude": "40.27370000", "longitude": "-76.40607000"}, {"name": "Cornwells Heights", "countryCode": "US", "stateCode": "PA", "latitude": "40.07678000", "longitude": "-74.94878000"}, {"name": "Corry", "countryCode": "US", "stateCode": "PA", "latitude": "41.92033000", "longitude": "-79.64033000"}, {"name": "Coudersport", "countryCode": "US", "stateCode": "PA", "latitude": "41.77479000", "longitude": "-78.02056000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.43507000", "longitude": "-80.06617000"}, {"name": "Cranberry Township", "countryCode": "US", "stateCode": "PA", "latitude": "40.68496000", "longitude": "-80.10714000"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "PA", "latitude": "41.68470000", "longitude": "-80.10628000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.45979000", "longitude": "-78.59168000"}, {"name": "Cressona", "countryCode": "US", "stateCode": "PA", "latitude": "40.62676000", "longitude": "-76.19272000"}, {"name": "Croydon", "countryCode": "US", "stateCode": "PA", "latitude": "40.08733000", "longitude": "-74.90350000"}, {"name": "Cumberland County", "countryCode": "US", "stateCode": "PA", "latitude": "40.16363000", "longitude": "-77.26555000"}, {"name": "Curtisville", "countryCode": "US", "stateCode": "PA", "latitude": "40.64229000", "longitude": "-79.85089000"}, {"name": "Curwensville", "countryCode": "US", "stateCode": "PA", "latitude": "40.97561000", "longitude": "-78.52502000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.31285000", "longitude": "-78.90419000"}, {"name": "Dallas", "countryCode": "US", "stateCode": "PA", "latitude": "41.33619000", "longitude": "-75.96325000"}, {"name": "Dallastown", "countryCode": "US", "stateCode": "PA", "latitude": "39.89954000", "longitude": "-76.64025000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.53424000", "longitude": "-75.73603000"}, {"name": "Danville", "countryCode": "US", "stateCode": "PA", "latitude": "40.96342000", "longitude": "-76.61273000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.91845000", "longitude": "-75.25907000"}, {"name": "Dauphin County", "countryCode": "US", "stateCode": "PA", "latitude": "40.41544000", "longitude": "-76.77947000"}, {"name": "Davidsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.22702000", "longitude": "-78.93641000"}, {"name": "Delaware County", "countryCode": "US", "stateCode": "PA", "latitude": "39.91681000", "longitude": "-75.39890000"}, {"name": "Delmont", "countryCode": "US", "stateCode": "PA", "latitude": "40.41312000", "longitude": "-79.57032000"}, {"name": "Denver", "countryCode": "US", "stateCode": "PA", "latitude": "40.23315000", "longitude": "-76.13717000"}, {"name": "Derry", "countryCode": "US", "stateCode": "PA", "latitude": "40.33396000", "longitude": "-79.29976000"}, {"name": "Devon", "countryCode": "US", "stateCode": "PA", "latitude": "40.04928000", "longitude": "-75.42908000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.10925000", "longitude": "-76.87663000"}, {"name": "Dickson City", "countryCode": "US", "stateCode": "PA", "latitude": "41.47147000", "longitude": "-75.60769000"}, {"name": "Dillsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.11093000", "longitude": "-77.03498000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.17340000", "longitude": "-79.85755000"}, {"name": "Dormont", "countryCode": "US", "stateCode": "PA", "latitude": "40.39590000", "longitude": "-80.03311000"}, {"name": "Dorneyville", "countryCode": "US", "stateCode": "PA", "latitude": "40.57510000", "longitude": "-75.51963000"}, {"name": "Dover", "countryCode": "US", "stateCode": "PA", "latitude": "40.00176000", "longitude": "-76.85025000"}, {"name": "Downingtown", "countryCode": "US", "stateCode": "PA", "latitude": "40.00650000", "longitude": "-75.70327000"}, {"name": "Doylestown", "countryCode": "US", "stateCode": "PA", "latitude": "40.31011000", "longitude": "-75.12989000"}, {"name": "Dravosburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.35063000", "longitude": "-79.88616000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.14094000", "longitude": "-75.16684000"}, {"name": "Drexel Hill", "countryCode": "US", "stateCode": "PA", "latitude": "39.94706000", "longitude": "-75.29213000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.11923000", "longitude": "-78.76003000"}, {"name": "Dublin", "countryCode": "US", "stateCode": "PA", "latitude": "40.37177000", "longitude": "-75.20156000"}, {"name": "Duboistown", "countryCode": "US", "stateCode": "PA", "latitude": "41.22258000", "longitude": "-77.03691000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.97785000", "longitude": "-79.61448000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.39814000", "longitude": "-77.02303000"}, {"name": "Duncansville", "countryCode": "US", "stateCode": "PA", "latitude": "40.42341000", "longitude": "-78.43390000"}, {"name": "Dunmore", "countryCode": "US", "stateCode": "PA", "latitude": "41.41980000", "longitude": "-75.63241000"}, {"name": "Dunnstown", "countryCode": "US", "stateCode": "PA", "latitude": "41.14590000", "longitude": "-77.42137000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.32508000", "longitude": "-75.74547000"}, {"name": "Duquesne", "countryCode": "US", "stateCode": "PA", "latitude": "40.38146000", "longitude": "-79.85977000"}, {"name": "Dury<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.34397000", "longitude": "-75.73853000"}, {"name": "Eagleview", "countryCode": "US", "stateCode": "PA", "latitude": "40.05938000", "longitude": "-75.68076000"}, {"name": "Eagleville", "countryCode": "US", "stateCode": "PA", "latitude": "40.15955000", "longitude": "-75.40824000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.00619000", "longitude": "-78.37001000"}, {"name": "East Bangor", "countryCode": "US", "stateCode": "PA", "latitude": "40.87954000", "longitude": "-75.18379000"}, {"name": "East Berlin", "countryCode": "US", "stateCode": "PA", "latitude": "39.93760000", "longitude": "-76.97859000"}, {"name": "East Berwick", "countryCode": "US", "stateCode": "PA", "latitude": "41.06203000", "longitude": "-76.22243000"}, {"name": "East Conemaugh", "countryCode": "US", "stateCode": "PA", "latitude": "40.34868000", "longitude": "-78.88364000"}, {"name": "East Earl", "countryCode": "US", "stateCode": "PA", "latitude": "40.11010000", "longitude": "-76.03272000"}, {"name": "East Greenville", "countryCode": "US", "stateCode": "PA", "latitude": "40.40649000", "longitude": "-75.50185000"}, {"name": "East Lansdowne", "countryCode": "US", "stateCode": "PA", "latitude": "39.94567000", "longitude": "-75.26129000"}, {"name": "East McKeesport", "countryCode": "US", "stateCode": "PA", "latitude": "40.38312000", "longitude": "-79.80644000"}, {"name": "East Petersburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.10010000", "longitude": "-76.35413000"}, {"name": "East Pittsburgh", "countryCode": "US", "stateCode": "PA", "latitude": "40.39562000", "longitude": "-79.83866000"}, {"name": "East Stroudsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.99954000", "longitude": "-75.18129000"}, {"name": "East Uniontown", "countryCode": "US", "stateCode": "PA", "latitude": "39.89980000", "longitude": "-79.69782000"}, {"name": "East Washington", "countryCode": "US", "stateCode": "PA", "latitude": "40.17368000", "longitude": "-80.23756000"}, {"name": "East York", "countryCode": "US", "stateCode": "PA", "latitude": "39.97371000", "longitude": "-76.68636000"}, {"name": "Eastlawn Gardens", "countryCode": "US", "stateCode": "PA", "latitude": "40.75065000", "longitude": "-75.29573000"}, {"name": "Easton", "countryCode": "US", "stateCode": "PA", "latitude": "40.68843000", "longitude": "-75.22073000"}, {"name": "Ebensburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.48507000", "longitude": "-78.72474000"}, {"name": "Economy", "countryCode": "US", "stateCode": "PA", "latitude": "40.60007000", "longitude": "-80.22478000"}, {"name": "Eddington", "countryCode": "US", "stateCode": "PA", "latitude": "40.08456000", "longitude": "-74.94489000"}, {"name": "Eddystone", "countryCode": "US", "stateCode": "PA", "latitude": "39.86011000", "longitude": "-75.34436000"}, {"name": "Edgewood", "countryCode": "US", "stateCode": "PA", "latitude": "40.43201000", "longitude": "-79.88144000"}, {"name": "Edgeworth", "countryCode": "US", "stateCode": "PA", "latitude": "40.55118000", "longitude": "-80.19284000"}, {"name": "Edinboro", "countryCode": "US", "stateCode": "PA", "latitude": "41.87422000", "longitude": "-80.13172000"}, {"name": "Edwardsville", "countryCode": "US", "stateCode": "PA", "latitude": "41.26953000", "longitude": "-75.91631000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.93926000", "longitude": "-75.43491000"}, {"name": "Egypt", "countryCode": "US", "stateCode": "PA", "latitude": "40.68010000", "longitude": "-75.52991000"}, {"name": "Elim", "countryCode": "US", "stateCode": "PA", "latitude": "40.29785000", "longitude": "-78.94253000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.26924000", "longitude": "-79.88977000"}, {"name": "Elizabethtown", "countryCode": "US", "stateCode": "PA", "latitude": "40.15287000", "longitude": "-76.60275000"}, {"name": "Elizabethville", "countryCode": "US", "stateCode": "PA", "latitude": "40.54897000", "longitude": "-76.81192000"}, {"name": "Elk County", "countryCode": "US", "stateCode": "PA", "latitude": "41.42523000", "longitude": "-78.64909000"}, {"name": "Elkland", "countryCode": "US", "stateCode": "PA", "latitude": "41.98618000", "longitude": "-77.31081000"}, {"name": "Ellport", "countryCode": "US", "stateCode": "PA", "latitude": "40.86395000", "longitude": "-80.25895000"}, {"name": "Ellwood City", "countryCode": "US", "stateCode": "PA", "latitude": "40.86173000", "longitude": "-80.28645000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.15676000", "longitude": "-75.83271000"}, {"name": "Elysburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.86453000", "longitude": "-76.55246000"}, {"name": "Emerald Lakes", "countryCode": "US", "stateCode": "PA", "latitude": "41.08842000", "longitude": "-75.41963000"}, {"name": "Emigsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.02176000", "longitude": "-76.72802000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.53954000", "longitude": "-75.49685000"}, {"name": "Emporium", "countryCode": "US", "stateCode": "PA", "latitude": "41.51145000", "longitude": "-78.23529000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.51007000", "longitude": "-80.09450000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.23176000", "longitude": "-76.82692000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.45423000", "longitude": "-80.23311000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.29009000", "longitude": "-76.93386000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.17982000", "longitude": "-76.17884000"}, {"name": "Erie", "countryCode": "US", "stateCode": "PA", "latitude": "42.12922000", "longitude": "-80.08506000"}, {"name": "Erie County", "countryCode": "US", "stateCode": "PA", "latitude": "42.11748000", "longitude": "-80.09811000"}, {"name": "Espy", "countryCode": "US", "stateCode": "PA", "latitude": "41.00620000", "longitude": "-76.40994000"}, {"name": "Etna", "countryCode": "US", "stateCode": "PA", "latitude": "40.50424000", "longitude": "-79.94894000"}, {"name": "Evans City", "countryCode": "US", "stateCode": "PA", "latitude": "40.76923000", "longitude": "-80.06284000"}, {"name": "Evansburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.18094000", "longitude": "-75.42907000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.01147000", "longitude": "-78.37335000"}, {"name": "Exeter", "countryCode": "US", "stateCode": "PA", "latitude": "41.32064000", "longitude": "-75.81908000"}, {"name": "Exton", "countryCode": "US", "stateCode": "PA", "latitude": "40.02900000", "longitude": "-75.62077000"}, {"name": "Factoryville", "countryCode": "US", "stateCode": "PA", "latitude": "41.56313000", "longitude": "-75.78269000"}, {"name": "Fairchance", "countryCode": "US", "stateCode": "PA", "latitude": "39.82480000", "longitude": "-79.75449000"}, {"name": "Fairdale", "countryCode": "US", "stateCode": "PA", "latitude": "39.88702000", "longitude": "-79.96811000"}, {"name": "Fairhope", "countryCode": "US", "stateCode": "PA", "latitude": "40.11368000", "longitude": "-79.83977000"}, {"name": "Fairless Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.17955000", "longitude": "-74.85516000"}, {"name": "Fairview", "countryCode": "US", "stateCode": "PA", "latitude": "42.03145000", "longitude": "-80.25534000"}, {"name": "Fairview-Ferndale", "countryCode": "US", "stateCode": "PA", "latitude": "40.78037000", "longitude": "-76.57528000"}, {"name": "Falls Creek", "countryCode": "US", "stateCode": "PA", "latitude": "41.14506000", "longitude": "-78.80447000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.21228000", "longitude": "-80.49674000"}, {"name": "Faxon", "countryCode": "US", "stateCode": "PA", "latitude": "41.24841000", "longitude": "-76.97719000"}, {"name": "Fayette County", "countryCode": "US", "stateCode": "PA", "latitude": "39.91990000", "longitude": "-79.64737000"}, {"name": "Fayetteville", "countryCode": "US", "stateCode": "PA", "latitude": "39.91120000", "longitude": "-77.54999000"}, {"name": "Feasterville", "countryCode": "US", "stateCode": "PA", "latitude": "40.14400000", "longitude": "-75.00517000"}, {"name": "Fellsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.18313000", "longitude": "-79.82421000"}, {"name": "Ferndale", "countryCode": "US", "stateCode": "PA", "latitude": "40.28896000", "longitude": "-78.91475000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.69479000", "longitude": "-80.13089000"}, {"name": "Fivepointville", "countryCode": "US", "stateCode": "PA", "latitude": "40.18287000", "longitude": "-76.05106000"}, {"name": "Fleetwood", "countryCode": "US", "stateCode": "PA", "latitude": "40.45398000", "longitude": "-75.81798000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.12646000", "longitude": "-77.47165000"}, {"name": "Flourtown", "countryCode": "US", "stateCode": "PA", "latitude": "40.10344000", "longitude": "-75.21240000"}, {"name": "Flying Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.27676000", "longitude": "-75.91410000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.89095000", "longitude": "-75.28380000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.88983000", "longitude": "-75.32519000"}, {"name": "Ford City", "countryCode": "US", "stateCode": "PA", "latitude": "40.77229000", "longitude": "-79.52977000"}, {"name": "Forest City", "countryCode": "US", "stateCode": "PA", "latitude": "41.65147000", "longitude": "-75.46657000"}, {"name": "Forest County", "countryCode": "US", "stateCode": "PA", "latitude": "41.51307000", "longitude": "-79.23601000"}, {"name": "Forest Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.41979000", "longitude": "-79.85005000"}, {"name": "Fort Washington", "countryCode": "US", "stateCode": "PA", "latitude": "40.14178000", "longitude": "-75.20906000"}, {"name": "Forty Fort", "countryCode": "US", "stateCode": "PA", "latitude": "41.27897000", "longitude": "-75.87825000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.97506000", "longitude": "-78.61725000"}, {"name": "Fountain Hill", "countryCode": "US", "stateCode": "PA", "latitude": "40.60149000", "longitude": "-75.39518000"}, {"name": "Fox Chapel", "countryCode": "US", "stateCode": "PA", "latitude": "40.51340000", "longitude": "-79.87977000"}, {"name": "Fox Chase", "countryCode": "US", "stateCode": "PA", "latitude": "40.39557000", "longitude": "-75.96216000"}, {"name": "Fox Run", "countryCode": "US", "stateCode": "PA", "latitude": "40.70229000", "longitude": "-80.08284000"}, {"name": "Frackville", "countryCode": "US", "stateCode": "PA", "latitude": "40.78398000", "longitude": "-76.23022000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.39784000", "longitude": "-79.83144000"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "PA", "latitude": "39.92742000", "longitude": "-77.72127000"}, {"name": "Franklin Park", "countryCode": "US", "stateCode": "PA", "latitude": "40.58340000", "longitude": "-80.08784000"}, {"name": "Fredericksburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.44370000", "longitude": "-76.42829000"}, {"name": "Freedom", "countryCode": "US", "stateCode": "PA", "latitude": "40.68562000", "longitude": "-80.25173000"}, {"name": "Freeland", "countryCode": "US", "stateCode": "PA", "latitude": "41.01675000", "longitude": "-75.89714000"}, {"name": "Freemansburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.62649000", "longitude": "-75.34574000"}, {"name": "Freeport", "countryCode": "US", "stateCode": "PA", "latitude": "40.67395000", "longitude": "-79.68477000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.05008000", "longitude": "-78.99836000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.63176000", "longitude": "-75.47324000"}, {"name": "Fulton County", "countryCode": "US", "stateCode": "PA", "latitude": "39.92534000", "longitude": "-78.11268000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.73312000", "longitude": "-77.64193000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.48229000", "longitude": "-78.55168000"}, {"name": "Gap", "countryCode": "US", "stateCode": "PA", "latitude": "39.98732000", "longitude": "-76.02051000"}, {"name": "Garden View", "countryCode": "US", "stateCode": "PA", "latitude": "41.25424000", "longitude": "-77.04608000"}, {"name": "Gastonville", "countryCode": "US", "stateCode": "PA", "latitude": "40.25729000", "longitude": "-79.99588000"}, {"name": "Geistown", "countryCode": "US", "stateCode": "PA", "latitude": "40.29091000", "longitude": "-78.86891000"}, {"name": "Georgetown", "countryCode": "US", "stateCode": "PA", "latitude": "39.93760000", "longitude": "-76.08329000"}, {"name": "Gettysburg", "countryCode": "US", "stateCode": "PA", "latitude": "39.83093000", "longitude": "-77.23110000"}, {"name": "Gibsonia", "countryCode": "US", "stateCode": "PA", "latitude": "40.63007000", "longitude": "-79.96950000"}, {"name": "Gilbertsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.32010000", "longitude": "-75.61018000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "42.00033000", "longitude": "-80.31812000"}, {"name": "Girardville", "countryCode": "US", "stateCode": "PA", "latitude": "40.79148000", "longitude": "-76.28356000"}, {"name": "Glassport", "countryCode": "US", "stateCode": "PA", "latitude": "40.32479000", "longitude": "-79.89227000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.17508000", "longitude": "-76.07465000"}, {"name": "Glen Rock", "countryCode": "US", "stateCode": "PA", "latitude": "39.79316000", "longitude": "-76.73025000"}, {"name": "Glenolden", "countryCode": "US", "stateCode": "PA", "latitude": "39.90011000", "longitude": "-75.28907000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.53285000", "longitude": "-79.96755000"}, {"name": "Glenside", "countryCode": "US", "stateCode": "PA", "latitude": "40.10233000", "longitude": "-75.15212000"}, {"name": "Gold Key Lake", "countryCode": "US", "stateCode": "PA", "latitude": "41.30593000", "longitude": "-74.93850000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.94038000", "longitude": "-76.72913000"}, {"name": "Green Tree", "countryCode": "US", "stateCode": "PA", "latitude": "40.41174000", "longitude": "-80.04561000"}, {"name": "Greencastle", "countryCode": "US", "stateCode": "PA", "latitude": "39.79037000", "longitude": "-77.72777000"}, {"name": "Greene County", "countryCode": "US", "stateCode": "PA", "latitude": "39.85380000", "longitude": "-80.22287000"}, {"name": "Greenfields", "countryCode": "US", "stateCode": "PA", "latitude": "40.35990000", "longitude": "-75.95199000"}, {"name": "Greenock", "countryCode": "US", "stateCode": "PA", "latitude": "40.31229000", "longitude": "-79.80671000"}, {"name": "Greensburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.30146000", "longitude": "-79.53893000"}, {"name": "Greenville", "countryCode": "US", "stateCode": "PA", "latitude": "41.40450000", "longitude": "-80.39118000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.53590000", "longitude": "-78.35751000"}, {"name": "Grill", "countryCode": "US", "stateCode": "PA", "latitude": "40.29870000", "longitude": "-75.94049000"}, {"name": "Grove City", "countryCode": "US", "stateCode": "PA", "latitude": "41.15784000", "longitude": "-80.08867000"}, {"name": "Guilford", "countryCode": "US", "stateCode": "PA", "latitude": "39.91541000", "longitude": "-77.60105000"}, {"name": "Guilford Siding", "countryCode": "US", "stateCode": "PA", "latitude": "39.86537000", "longitude": "-77.61249000"}, {"name": "Halfway House", "countryCode": "US", "stateCode": "PA", "latitude": "40.28204000", "longitude": "-75.64324000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.00482000", "longitude": "-76.60413000"}, {"name": "Hallstead", "countryCode": "US", "stateCode": "PA", "latitude": "41.96119000", "longitude": "-75.74324000"}, {"name": "Hamburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.55565000", "longitude": "-75.98188000"}, {"name": "Hanover", "countryCode": "US", "stateCode": "PA", "latitude": "39.80066000", "longitude": "-76.98304000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.98064000", "longitude": "-75.97131000"}, {"name": "Harleysville", "countryCode": "US", "stateCode": "PA", "latitude": "40.27955000", "longitude": "-75.38712000"}, {"name": "Harrisburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.27370000", "longitude": "-76.88442000"}, {"name": "Harveys Lake", "countryCode": "US", "stateCode": "PA", "latitude": "41.38341000", "longitude": "-76.02465000"}, {"name": "Hasson Heights", "countryCode": "US", "stateCode": "PA", "latitude": "41.44895000", "longitude": "-79.67700000"}, {"name": "Hastings", "countryCode": "US", "stateCode": "PA", "latitude": "40.66507000", "longitude": "-78.71225000"}, {"name": "Hatboro", "countryCode": "US", "stateCode": "PA", "latitude": "40.17428000", "longitude": "-75.10684000"}, {"name": "Hatfield", "countryCode": "US", "stateCode": "PA", "latitude": "40.27983000", "longitude": "-75.29934000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.47592000", "longitude": "-75.18212000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.95842000", "longitude": "-75.97465000"}, {"name": "Hebron", "countryCode": "US", "stateCode": "PA", "latitude": "40.33898000", "longitude": "-76.39940000"}, {"name": "Heidelberg", "countryCode": "US", "stateCode": "PA", "latitude": "40.39229000", "longitude": "-80.09089000"}, {"name": "Hellertown", "countryCode": "US", "stateCode": "PA", "latitude": "40.57954000", "longitude": "-75.34073000"}, {"name": "Hemlock Farms", "countryCode": "US", "stateCode": "PA", "latitude": "41.32676000", "longitude": "-75.03656000"}, {"name": "Hermitage", "countryCode": "US", "stateCode": "PA", "latitude": "41.23339000", "longitude": "-80.44868000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.28592000", "longitude": "-76.65025000"}, {"name": "Highland Park", "countryCode": "US", "stateCode": "PA", "latitude": "40.62091000", "longitude": "-77.56805000"}, {"name": "Highspire", "countryCode": "US", "stateCode": "PA", "latitude": "40.21092000", "longitude": "-76.79108000"}, {"name": "Hilldale", "countryCode": "US", "stateCode": "PA", "latitude": "41.28925000", "longitude": "-75.83631000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.01035000", "longitude": "-79.90088000"}, {"name": "Hokendauqua", "countryCode": "US", "stateCode": "PA", "latitude": "40.66204000", "longitude": "-75.49102000"}, {"name": "Hollidaysburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.42729000", "longitude": "-78.38890000"}, {"name": "Homeacre-Lyndora", "countryCode": "US", "stateCode": "PA", "latitude": "40.87206000", "longitude": "-79.92060000"}, {"name": "Homer City", "countryCode": "US", "stateCode": "PA", "latitude": "40.54340000", "longitude": "-79.16226000"}, {"name": "Homestead", "countryCode": "US", "stateCode": "PA", "latitude": "40.40590000", "longitude": "-79.91199000"}, {"name": "Hometown", "countryCode": "US", "stateCode": "PA", "latitude": "40.82370000", "longitude": "-75.98020000"}, {"name": "Honesdale", "countryCode": "US", "stateCode": "PA", "latitude": "41.57676000", "longitude": "-75.25879000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.09427000", "longitude": "-75.91133000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.87702000", "longitude": "-79.70199000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.17844000", "longitude": "-75.12851000"}, {"name": "Houserville", "countryCode": "US", "stateCode": "PA", "latitude": "40.82395000", "longitude": "-77.82889000"}, {"name": "Houston", "countryCode": "US", "stateCode": "PA", "latitude": "40.24646000", "longitude": "-80.21145000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.27480000", "longitude": "-75.83603000"}, {"name": "Hughestown", "countryCode": "US", "stateCode": "PA", "latitude": "41.32702000", "longitude": "-75.77325000"}, {"name": "Hughesville", "countryCode": "US", "stateCode": "PA", "latitude": "41.24119000", "longitude": "-76.72385000"}, {"name": "Hummels Wharf", "countryCode": "US", "stateCode": "PA", "latitude": "40.83175000", "longitude": "-76.83580000"}, {"name": "Hummelstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.26537000", "longitude": "-76.70830000"}, {"name": "Huntingdon", "countryCode": "US", "stateCode": "PA", "latitude": "40.48480000", "longitude": "-78.01028000"}, {"name": "Huntingdon County", "countryCode": "US", "stateCode": "PA", "latitude": "40.41693000", "longitude": "-77.98121000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.00256000", "longitude": "-78.46252000"}, {"name": "Hyde Park", "countryCode": "US", "stateCode": "PA", "latitude": "40.37732000", "longitude": "-75.92521000"}, {"name": "Imperial", "countryCode": "US", "stateCode": "PA", "latitude": "40.44951000", "longitude": "-80.24450000"}, {"name": "Indian Mountain Lake", "countryCode": "US", "stateCode": "PA", "latitude": "41.00314000", "longitude": "-75.50824000"}, {"name": "Indiana", "countryCode": "US", "stateCode": "PA", "latitude": "40.62146000", "longitude": "-79.15253000"}, {"name": "Indiana County", "countryCode": "US", "stateCode": "PA", "latitude": "40.65205000", "longitude": "-79.08755000"}, {"name": "Industry", "countryCode": "US", "stateCode": "PA", "latitude": "40.64451000", "longitude": "-80.41618000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.44618000", "longitude": "-80.06755000"}, {"name": "Inkerman", "countryCode": "US", "stateCode": "PA", "latitude": "41.29897000", "longitude": "-75.81269000"}, {"name": "Intercourse", "countryCode": "US", "stateCode": "PA", "latitude": "40.03760000", "longitude": "-76.10495000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.32451000", "longitude": "-79.70115000"}, {"name": "Ivyland", "countryCode": "US", "stateCode": "PA", "latitude": "40.20789000", "longitude": "-75.07267000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.32454000", "longitude": "-75.84965000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.88316000", "longitude": "-76.71052000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.32812000", "longitude": "-79.61532000"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "PA", "latitude": "41.12815000", "longitude": "-78.99942000"}, {"name": "Jefferson Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.29118000", "longitude": "-79.93199000"}, {"name": "Jenkintown", "countryCode": "US", "stateCode": "PA", "latitude": "40.09594000", "longitude": "-75.12517000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.53091000", "longitude": "-75.54546000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.20896000", "longitude": "-78.98364000"}, {"name": "Jersey Shore", "countryCode": "US", "stateCode": "PA", "latitude": "41.20202000", "longitude": "-77.26442000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.46869000", "longitude": "-75.56213000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.87592000", "longitude": "-75.73241000"}, {"name": "Johnsonburg", "countryCode": "US", "stateCode": "PA", "latitude": "41.49062000", "longitude": "-78.67503000"}, {"name": "Johnstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.32674000", "longitude": "-78.92197000"}, {"name": "Jonestown", "countryCode": "US", "stateCode": "PA", "latitude": "40.41370000", "longitude": "-76.47830000"}, {"name": "Juniata County", "countryCode": "US", "stateCode": "PA", "latitude": "40.53106000", "longitude": "-77.40216000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.66284000", "longitude": "-78.81114000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.31065000", "longitude": "-75.93938000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.23149000", "longitude": "-75.63408000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.25341000", "longitude": "-76.95941000"}, {"name": "Kennett Square", "countryCode": "US", "stateCode": "PA", "latitude": "39.84678000", "longitude": "-75.71160000"}, {"name": "King of Prussia", "countryCode": "US", "stateCode": "PA", "latitude": "40.08927000", "longitude": "-75.39602000"}, {"name": "Kingston", "countryCode": "US", "stateCode": "PA", "latitude": "41.26175000", "longitude": "-75.89686000"}, {"name": "Kittanning", "countryCode": "US", "stateCode": "PA", "latitude": "40.81645000", "longitude": "-79.52199000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.23451000", "longitude": "-79.53727000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.79342000", "longitude": "-76.47245000"}, {"name": "Ku<PERSON>psville", "countryCode": "US", "stateCode": "PA", "latitude": "40.24288000", "longitude": "-75.33656000"}, {"name": "Kutztown", "countryCode": "US", "stateCode": "PA", "latitude": "40.51732000", "longitude": "-75.77742000"}, {"name": "Lackawanna County", "countryCode": "US", "stateCode": "PA", "latitude": "41.43679000", "longitude": "-75.60920000"}, {"name": "Lafayette Hill", "countryCode": "US", "stateCode": "PA", "latitude": "40.09245000", "longitude": "-75.25330000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.28897000", "longitude": "-75.80547000"}, {"name": "Lake City", "countryCode": "US", "stateCode": "PA", "latitude": "42.01422000", "longitude": "-80.34534000"}, {"name": "Lake Heritage", "countryCode": "US", "stateCode": "PA", "latitude": "39.80954000", "longitude": "-77.18387000"}, {"name": "Lake Latonka", "countryCode": "US", "stateCode": "PA", "latitude": "41.29039000", "longitude": "-80.18129000"}, {"name": "Lake Meade", "countryCode": "US", "stateCode": "PA", "latitude": "39.98510000", "longitude": "-77.03720000"}, {"name": "Lake Wynonah", "countryCode": "US", "stateCode": "PA", "latitude": "40.59926000", "longitude": "-76.16383000"}, {"name": "Lakemont", "countryCode": "US", "stateCode": "PA", "latitude": "40.47285000", "longitude": "-78.38835000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.99010000", "longitude": "-76.23968000"}, {"name": "Lancaster", "countryCode": "US", "stateCode": "PA", "latitude": "40.03788000", "longitude": "-76.30551000"}, {"name": "Lancaster County", "countryCode": "US", "stateCode": "PA", "latitude": "40.04244000", "longitude": "-76.24770000"}, {"name": "Landisville", "countryCode": "US", "stateCode": "PA", "latitude": "40.09537000", "longitude": "-76.40996000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.17455000", "longitude": "-74.92267000"}, {"name": "Langhorne Manor", "countryCode": "US", "stateCode": "PA", "latitude": "40.16705000", "longitude": "-74.91767000"}, {"name": "Lansdale", "countryCode": "US", "stateCode": "PA", "latitude": "40.24150000", "longitude": "-75.28379000"}, {"name": "Lansdowne", "countryCode": "US", "stateCode": "PA", "latitude": "39.93817000", "longitude": "-75.27185000"}, {"name": "Lansford", "countryCode": "US", "stateCode": "PA", "latitude": "40.83176000", "longitude": "-75.88242000"}, {"name": "Laporte", "countryCode": "US", "stateCode": "PA", "latitude": "41.42397000", "longitude": "-76.49411000"}, {"name": "Larksville", "countryCode": "US", "stateCode": "PA", "latitude": "41.24508000", "longitude": "-75.93075000"}, {"name": "Latrobe", "countryCode": "US", "stateCode": "PA", "latitude": "40.32118000", "longitude": "-79.37948000"}, {"name": "Laureldale", "countryCode": "US", "stateCode": "PA", "latitude": "40.38815000", "longitude": "-75.91798000"}, {"name": "Laurys Station", "countryCode": "US", "stateCode": "PA", "latitude": "40.72315000", "longitude": "-75.53018000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.25842000", "longitude": "-76.80386000"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "PA", "latitude": "40.99127000", "longitude": "-80.33419000"}, {"name": "Lawrence Park", "countryCode": "US", "stateCode": "PA", "latitude": "42.15228000", "longitude": "-80.02311000"}, {"name": "Lawson Heights", "countryCode": "US", "stateCode": "PA", "latitude": "40.29174000", "longitude": "-79.38920000"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "PA", "latitude": "40.34093000", "longitude": "-76.41135000"}, {"name": "Lebanon County", "countryCode": "US", "stateCode": "PA", "latitude": "40.36723000", "longitude": "-76.45771000"}, {"name": "Lebanon South", "countryCode": "US", "stateCode": "PA", "latitude": "40.32804000", "longitude": "-76.40644000"}, {"name": "Leechburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.62701000", "longitude": "-79.60560000"}, {"name": "Leesport", "countryCode": "US", "stateCode": "PA", "latitude": "40.44704000", "longitude": "-75.96632000"}, {"name": "Leetsdale", "countryCode": "US", "stateCode": "PA", "latitude": "40.56312000", "longitude": "-80.20839000"}, {"name": "Lehigh County", "countryCode": "US", "stateCode": "PA", "latitude": "40.61271000", "longitude": "-75.59237000"}, {"name": "Lehighton", "countryCode": "US", "stateCode": "PA", "latitude": "40.83370000", "longitude": "-75.71380000"}, {"name": "Leith-Hatfield", "countryCode": "US", "stateCode": "PA", "latitude": "39.87744000", "longitude": "-79.73133000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.81062000", "longitude": "-77.81833000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.24120000", "longitude": "-76.89414000"}, {"name": "Lenape Heights", "countryCode": "US", "stateCode": "PA", "latitude": "40.76423000", "longitude": "-79.52060000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.08787000", "longitude": "-76.18495000"}, {"name": "Level Green", "countryCode": "US", "stateCode": "PA", "latitude": "40.39340000", "longitude": "-79.72032000"}, {"name": "Levittown", "countryCode": "US", "stateCode": "PA", "latitude": "40.15511000", "longitude": "-74.82877000"}, {"name": "Lewisburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.96453000", "longitude": "-76.88441000"}, {"name": "Lewistown", "countryCode": "US", "stateCode": "PA", "latitude": "40.59924000", "longitude": "-77.57138000"}, {"name": "Liberty", "countryCode": "US", "stateCode": "PA", "latitude": "40.32535000", "longitude": "-79.85616000"}, {"name": "Light Street", "countryCode": "US", "stateCode": "PA", "latitude": "41.03620000", "longitude": "-76.42356000"}, {"name": "Ligonier", "countryCode": "US", "stateCode": "PA", "latitude": "40.24313000", "longitude": "-79.23753000"}, {"name": "Lima", "countryCode": "US", "stateCode": "PA", "latitude": "39.91733000", "longitude": "-75.44047000"}, {"name": "Limerick", "countryCode": "US", "stateCode": "PA", "latitude": "40.23093000", "longitude": "-75.52212000"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "PA", "latitude": "40.31896000", "longitude": "-79.85477000"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "PA", "latitude": "40.31481000", "longitude": "-75.98549000"}, {"name": "Linglestown", "countryCode": "US", "stateCode": "PA", "latitude": "40.33398000", "longitude": "-76.78914000"}, {"name": "Linntown", "countryCode": "US", "stateCode": "PA", "latitude": "40.95897000", "longitude": "-76.89913000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.82650000", "longitude": "-75.42547000"}, {"name": "Lionville", "countryCode": "US", "stateCode": "PA", "latitude": "40.05372000", "longitude": "-75.65993000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.15732000", "longitude": "-76.30690000"}, {"name": "Littlestown", "countryCode": "US", "stateCode": "PA", "latitude": "39.74454000", "longitude": "-77.08804000"}, {"name": "Lock Haven", "countryCode": "US", "stateCode": "PA", "latitude": "41.13701000", "longitude": "-77.44693000"}, {"name": "Loganville", "countryCode": "US", "stateCode": "PA", "latitude": "39.85566000", "longitude": "-76.70747000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.28843000", "longitude": "-75.85465000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.50313000", "longitude": "-78.63030000"}, {"name": "Lower Allen", "countryCode": "US", "stateCode": "PA", "latitude": "40.22648000", "longitude": "-76.90053000"}, {"name": "Lower Burrell", "countryCode": "US", "stateCode": "PA", "latitude": "40.55312000", "longitude": "-79.75727000"}, {"name": "Loyalhanna", "countryCode": "US", "stateCode": "PA", "latitude": "40.32257000", "longitude": "-79.36226000"}, {"name": "Luzerne", "countryCode": "US", "stateCode": "PA", "latitude": "41.28564000", "longitude": "-75.90103000"}, {"name": "Luzerne County", "countryCode": "US", "stateCode": "PA", "latitude": "41.17701000", "longitude": "-75.98903000"}, {"name": "Lycoming County", "countryCode": "US", "stateCode": "PA", "latitude": "41.34338000", "longitude": "-77.06451000"}, {"name": "Lykens", "countryCode": "US", "stateCode": "PA", "latitude": "40.56675000", "longitude": "-76.70052000"}, {"name": "Lynnwood-Pricedale", "countryCode": "US", "stateCode": "PA", "latitude": "40.13071000", "longitude": "-79.85135000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.51593000", "longitude": "-75.55519000"}, {"name": "Mahanoy City", "countryCode": "US", "stateCode": "PA", "latitude": "40.81259000", "longitude": "-76.14160000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.03622000", "longitude": "-75.51381000"}, {"name": "Manchester", "countryCode": "US", "stateCode": "PA", "latitude": "40.06315000", "longitude": "-76.71830000"}, {"name": "Manheim", "countryCode": "US", "stateCode": "PA", "latitude": "40.16343000", "longitude": "-76.39496000"}, {"name": "Manor", "countryCode": "US", "stateCode": "PA", "latitude": "40.33396000", "longitude": "-79.67004000"}, {"name": "Mansfield", "countryCode": "US", "stateCode": "PA", "latitude": "41.80730000", "longitude": "-77.07747000"}, {"name": "Maple Glen", "countryCode": "US", "stateCode": "PA", "latitude": "40.17928000", "longitude": "-75.18045000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.81928000", "longitude": "-75.41853000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.24645000", "longitude": "-79.42893000"}, {"name": "Marienville", "countryCode": "US", "stateCode": "PA", "latitude": "41.46895000", "longitude": "-79.12310000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.05704000", "longitude": "-76.55219000"}, {"name": "Mars", "countryCode": "US", "stateCode": "PA", "latitude": "40.69590000", "longitude": "-80.01173000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.78675000", "longitude": "-76.53940000"}, {"name": "Martinsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.31119000", "longitude": "-78.32418000"}, {"name": "Marysville", "countryCode": "US", "stateCode": "PA", "latitude": "40.34259000", "longitude": "-76.92997000"}, {"name": "Masontown", "countryCode": "US", "stateCode": "PA", "latitude": "39.84674000", "longitude": "-79.89978000"}, {"name": "Matamoras", "countryCode": "US", "stateCode": "PA", "latitude": "41.36870000", "longitude": "-74.70016000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.53814000", "longitude": "-75.53602000"}, {"name": "Maytown", "countryCode": "US", "stateCode": "PA", "latitude": "40.07537000", "longitude": "-76.58219000"}, {"name": "McAdoo", "countryCode": "US", "stateCode": "PA", "latitude": "40.90127000", "longitude": "-75.99106000"}, {"name": "McConnellsburg", "countryCode": "US", "stateCode": "PA", "latitude": "39.93259000", "longitude": "-77.99889000"}, {"name": "McConnellstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.45257000", "longitude": "-78.08167000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.37090000", "longitude": "-80.23478000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.22896000", "longitude": "-80.21645000"}, {"name": "McKean County", "countryCode": "US", "stateCode": "PA", "latitude": "41.80775000", "longitude": "-78.56903000"}, {"name": "McKees Rocks", "countryCode": "US", "stateCode": "PA", "latitude": "40.46562000", "longitude": "-80.06561000"}, {"name": "McKeesport", "countryCode": "US", "stateCode": "PA", "latitude": "40.34785000", "longitude": "-79.86422000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.27785000", "longitude": "-80.08394000"}, {"name": "McSherrystown", "countryCode": "US", "stateCode": "PA", "latitude": "39.80732000", "longitude": "-77.01137000"}, {"name": "Meadowood", "countryCode": "US", "stateCode": "PA", "latitude": "40.84201000", "longitude": "-79.89394000"}, {"name": "Meadville", "countryCode": "US", "stateCode": "PA", "latitude": "41.64144000", "longitude": "-80.15145000"}, {"name": "Mechanicsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.21426000", "longitude": "-77.00859000"}, {"name": "Mechanicsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.96648000", "longitude": "-76.58662000"}, {"name": "Media", "countryCode": "US", "stateCode": "PA", "latitude": "39.91678000", "longitude": "-75.38769000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.22700000", "longitude": "-80.23979000"}, {"name": "Mercer County", "countryCode": "US", "stateCode": "PA", "latitude": "41.30218000", "longitude": "-80.25770000"}, {"name": "Mercersburg", "countryCode": "US", "stateCode": "PA", "latitude": "39.82787000", "longitude": "-77.90333000"}, {"name": "Meridian", "countryCode": "US", "stateCode": "PA", "latitude": "40.84840000", "longitude": "-79.96200000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.81369000", "longitude": "-79.02475000"}, {"name": "Middleburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.78592000", "longitude": "-77.04720000"}, {"name": "Middletown", "countryCode": "US", "stateCode": "PA", "latitude": "40.19981000", "longitude": "-76.73108000"}, {"name": "Midland", "countryCode": "US", "stateCode": "PA", "latitude": "40.63257000", "longitude": "-80.44645000"}, {"name": "Midway", "countryCode": "US", "stateCode": "PA", "latitude": "39.80843000", "longitude": "-77.00276000"}, {"name": "Mifflin County", "countryCode": "US", "stateCode": "PA", "latitude": "40.61041000", "longitude": "-77.61704000"}, {"name": "Mifflinburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.91758000", "longitude": "-77.04775000"}, {"name": "Mifflintown", "countryCode": "US", "stateCode": "PA", "latitude": "40.56980000", "longitude": "-77.39693000"}, {"name": "Mifflinville", "countryCode": "US", "stateCode": "PA", "latitude": "41.03231000", "longitude": "-76.30799000"}, {"name": "Milesburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.94173000", "longitude": "-77.78500000"}, {"name": "Milford", "countryCode": "US", "stateCode": "PA", "latitude": "41.32232000", "longitude": "-74.80239000"}, {"name": "Mill Hall", "countryCode": "US", "stateCode": "PA", "latitude": "41.10729000", "longitude": "-77.48443000"}, {"name": "Millbourne", "countryCode": "US", "stateCode": "PA", "latitude": "39.96345000", "longitude": "-75.25018000"}, {"name": "Millersburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.53953000", "longitude": "-76.96081000"}, {"name": "Millersville", "countryCode": "US", "stateCode": "PA", "latitude": "39.99788000", "longitude": "-76.35413000"}, {"name": "Millvale", "countryCode": "US", "stateCode": "PA", "latitude": "40.48007000", "longitude": "-79.97839000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.71396000", "longitude": "-77.59055000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.01203000", "longitude": "-76.84774000"}, {"name": "Minersville", "countryCode": "US", "stateCode": "PA", "latitude": "40.69065000", "longitude": "-76.26217000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.28593000", "longitude": "-75.98438000"}, {"name": "Monaca", "countryCode": "US", "stateCode": "PA", "latitude": "40.68729000", "longitude": "-80.27145000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.14841000", "longitude": "-79.88783000"}, {"name": "Monongahela", "countryCode": "US", "stateCode": "PA", "latitude": "40.20313000", "longitude": "-79.92616000"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "PA", "latitude": "41.05807000", "longitude": "-75.33948000"}, {"name": "Monroeville", "countryCode": "US", "stateCode": "PA", "latitude": "40.42118000", "longitude": "-79.78810000"}, {"name": "Mont Alto", "countryCode": "US", "stateCode": "PA", "latitude": "39.84426000", "longitude": "-77.55832000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.17036000", "longitude": "-76.87691000"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "PA", "latitude": "40.21083000", "longitude": "-75.36730000"}, {"name": "Montgomeryville", "countryCode": "US", "stateCode": "PA", "latitude": "40.24733000", "longitude": "-75.24379000"}, {"name": "Montour County", "countryCode": "US", "stateCode": "PA", "latitude": "41.02792000", "longitude": "-76.65856000"}, {"name": "Montoursville", "countryCode": "US", "stateCode": "PA", "latitude": "41.25425000", "longitude": "-76.92052000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.83397000", "longitude": "-75.87714000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.35341000", "longitude": "-75.73825000"}, {"name": "Morningside", "countryCode": "US", "stateCode": "PA", "latitude": "40.48140510", "longitude": "-79.93442840"}, {"name": "Morrisville", "countryCode": "US", "stateCode": "PA", "latitude": "40.21150000", "longitude": "-74.78794000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.90983000", "longitude": "-75.32352000"}, {"name": "Moscow", "countryCode": "US", "stateCode": "PA", "latitude": "41.33675000", "longitude": "-75.51852000"}, {"name": "Mount Carmel", "countryCode": "US", "stateCode": "PA", "latitude": "40.79703000", "longitude": "-76.41190000"}, {"name": "Mount Cobb", "countryCode": "US", "stateCode": "PA", "latitude": "41.41342000", "longitude": "-75.49324000"}, {"name": "Mount Holly Springs", "countryCode": "US", "stateCode": "PA", "latitude": "40.11842000", "longitude": "-77.18998000"}, {"name": "Mount Joy", "countryCode": "US", "stateCode": "PA", "latitude": "40.10982000", "longitude": "-76.50330000"}, {"name": "Mount Lebanon", "countryCode": "US", "stateCode": "PA", "latitude": "40.35535000", "longitude": "-80.04950000"}, {"name": "Mount Oliver", "countryCode": "US", "stateCode": "PA", "latitude": "40.41424000", "longitude": "-79.98783000"}, {"name": "Mount Penn", "countryCode": "US", "stateCode": "PA", "latitude": "40.32815000", "longitude": "-75.89076000"}, {"name": "Mount Pleasant", "countryCode": "US", "stateCode": "PA", "latitude": "40.14896000", "longitude": "-79.54115000"}, {"name": "Mount Pocono", "countryCode": "US", "stateCode": "PA", "latitude": "41.12203000", "longitude": "-75.36463000"}, {"name": "Mount Union", "countryCode": "US", "stateCode": "PA", "latitude": "40.38452000", "longitude": "-77.88222000"}, {"name": "Mount Wolf", "countryCode": "US", "stateCode": "PA", "latitude": "40.06315000", "longitude": "-76.70386000"}, {"name": "Mountain Top", "countryCode": "US", "stateCode": "PA", "latitude": "41.16953000", "longitude": "-75.87742000"}, {"name": "Mountainhome", "countryCode": "US", "stateCode": "PA", "latitude": "41.17370000", "longitude": "-75.27102000"}, {"name": "Mountville", "countryCode": "US", "stateCode": "PA", "latitude": "40.03926000", "longitude": "-76.43080000"}, {"name": "Muhlenberg Park", "countryCode": "US", "stateCode": "PA", "latitude": "40.38482000", "longitude": "-75.94132000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.20564000", "longitude": "-76.78552000"}, {"name": "Mundys Corner", "countryCode": "US", "stateCode": "PA", "latitude": "40.44479000", "longitude": "-78.84113000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.39229000", "longitude": "-79.90005000"}, {"name": "Murrysville", "countryCode": "US", "stateCode": "PA", "latitude": "40.42840000", "longitude": "-79.69755000"}, {"name": "Muse", "countryCode": "US", "stateCode": "PA", "latitude": "40.29285000", "longitude": "-80.20034000"}, {"name": "Myerstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.37454000", "longitude": "-76.30273000"}, {"name": "Nanticoke", "countryCode": "US", "stateCode": "PA", "latitude": "41.20536000", "longitude": "-76.00492000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.47229000", "longitude": "-78.83336000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.00845000", "longitude": "-75.26046000"}, {"name": "Nazareth", "countryCode": "US", "stateCode": "PA", "latitude": "40.74038000", "longitude": "-75.30962000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.05203000", "longitude": "-76.22077000"}, {"name": "Nesquehoning", "countryCode": "US", "stateCode": "PA", "latitude": "40.86453000", "longitude": "-75.81103000"}, {"name": "New Beaver", "countryCode": "US", "stateCode": "PA", "latitude": "40.87645000", "longitude": "-80.37062000"}, {"name": "New Berlinville", "countryCode": "US", "stateCode": "PA", "latitude": "40.34537000", "longitude": "-75.63296000"}, {"name": "New Bloomfield", "countryCode": "US", "stateCode": "PA", "latitude": "40.41981000", "longitude": "-77.18637000"}, {"name": "New Brighton", "countryCode": "US", "stateCode": "PA", "latitude": "40.73034000", "longitude": "-80.31006000"}, {"name": "New Britain", "countryCode": "US", "stateCode": "PA", "latitude": "40.29900000", "longitude": "-75.18101000"}, {"name": "New Castle", "countryCode": "US", "stateCode": "PA", "latitude": "41.00367000", "longitude": "-80.34701000"}, {"name": "New Castle Northwest", "countryCode": "US", "stateCode": "PA", "latitude": "41.02208000", "longitude": "-80.35682000"}, {"name": "New Columbia", "countryCode": "US", "stateCode": "PA", "latitude": "41.04092000", "longitude": "-76.86691000"}, {"name": "New Cumberland", "countryCode": "US", "stateCode": "PA", "latitude": "40.23231000", "longitude": "-76.88470000"}, {"name": "New Eagle", "countryCode": "US", "stateCode": "PA", "latitude": "40.20785000", "longitude": "-79.94699000"}, {"name": "New Freedom", "countryCode": "US", "stateCode": "PA", "latitude": "39.73788000", "longitude": "-76.70136000"}, {"name": "New Holland", "countryCode": "US", "stateCode": "PA", "latitude": "40.10176000", "longitude": "-76.08523000"}, {"name": "New Hope", "countryCode": "US", "stateCode": "PA", "latitude": "40.36427000", "longitude": "-74.95128000"}, {"name": "New Kensington", "countryCode": "US", "stateCode": "PA", "latitude": "40.56979000", "longitude": "-79.76477000"}, {"name": "New Oxford", "countryCode": "US", "stateCode": "PA", "latitude": "39.86371000", "longitude": "-77.05581000"}, {"name": "New Philadelphia", "countryCode": "US", "stateCode": "PA", "latitude": "40.71953000", "longitude": "-76.11577000"}, {"name": "New Stanton", "countryCode": "US", "stateCode": "PA", "latitude": "40.21924000", "longitude": "-79.60948000"}, {"name": "New Wilmington", "countryCode": "US", "stateCode": "PA", "latitude": "41.12228000", "longitude": "-80.33284000"}, {"name": "Newmanstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.34954000", "longitude": "-76.21328000"}, {"name": "Newport", "countryCode": "US", "stateCode": "PA", "latitude": "40.47786000", "longitude": "-77.13054000"}, {"name": "Newtown", "countryCode": "US", "stateCode": "PA", "latitude": "40.22928000", "longitude": "-74.93683000"}, {"name": "Newtown Grant", "countryCode": "US", "stateCode": "PA", "latitude": "40.26011000", "longitude": "-74.95489000"}, {"name": "Newville", "countryCode": "US", "stateCode": "PA", "latitude": "40.17314000", "longitude": "-77.39860000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.78340000", "longitude": "-79.92950000"}, {"name": "Norristown", "countryCode": "US", "stateCode": "PA", "latitude": "40.12150000", "longitude": "-75.33990000"}, {"name": "North Apollo", "countryCode": "US", "stateCode": "PA", "latitude": "40.59618000", "longitude": "-79.55560000"}, {"name": "North Belle Vernon", "countryCode": "US", "stateCode": "PA", "latitude": "40.12924000", "longitude": "-79.86810000"}, {"name": "North Braddock", "countryCode": "US", "stateCode": "PA", "latitude": "40.39896000", "longitude": "-79.84088000"}, {"name": "North Catasauqua", "countryCode": "US", "stateCode": "PA", "latitude": "40.65982000", "longitude": "-75.47685000"}, {"name": "North Charleroi", "countryCode": "US", "stateCode": "PA", "latitude": "40.15118000", "longitude": "-79.90755000"}, {"name": "North East", "countryCode": "US", "stateCode": "PA", "latitude": "42.21561000", "longitude": "-79.83422000"}, {"name": "North Versailles", "countryCode": "US", "stateCode": "PA", "latitude": "40.37979000", "longitude": "-79.80949000"}, {"name": "North Wales", "countryCode": "US", "stateCode": "PA", "latitude": "40.21094000", "longitude": "-75.27823000"}, {"name": "North Warren", "countryCode": "US", "stateCode": "PA", "latitude": "41.87423000", "longitude": "-79.15227000"}, {"name": "North York", "countryCode": "US", "stateCode": "PA", "latitude": "39.97815000", "longitude": "-76.73302000"}, {"name": "Northampton", "countryCode": "US", "stateCode": "PA", "latitude": "40.68621000", "longitude": "-75.49685000"}, {"name": "Northampton County", "countryCode": "US", "stateCode": "PA", "latitude": "40.75423000", "longitude": "-75.30742000"}, {"name": "Northern Cambria", "countryCode": "US", "stateCode": "PA", "latitude": "40.65923000", "longitude": "-78.78169000"}, {"name": "Northumberland", "countryCode": "US", "stateCode": "PA", "latitude": "40.89175000", "longitude": "-76.79747000"}, {"name": "Northumberland County", "countryCode": "US", "stateCode": "PA", "latitude": "40.85198000", "longitude": "-76.70932000"}, {"name": "Northwest Harborcreek", "countryCode": "US", "stateCode": "PA", "latitude": "42.14944000", "longitude": "-79.99463000"}, {"name": "Norwood", "countryCode": "US", "stateCode": "PA", "latitude": "39.89178000", "longitude": "-75.29963000"}, {"name": "Oak Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.82479000", "longitude": "-79.91311000"}, {"name": "Oakdale", "countryCode": "US", "stateCode": "PA", "latitude": "40.39812000", "longitude": "-80.18561000"}, {"name": "Oakland", "countryCode": "US", "stateCode": "PA", "latitude": "40.30646000", "longitude": "-78.88752000"}, {"name": "Oakmont", "countryCode": "US", "stateCode": "PA", "latitude": "40.52173000", "longitude": "-79.84227000"}, {"name": "Oakwood", "countryCode": "US", "stateCode": "PA", "latitude": "41.01062000", "longitude": "-80.37951000"}, {"name": "Ohioville", "countryCode": "US", "stateCode": "PA", "latitude": "40.67923000", "longitude": "-80.49479000"}, {"name": "Oil City", "countryCode": "US", "stateCode": "PA", "latitude": "41.43395000", "longitude": "-79.70644000"}, {"name": "Old Forge", "countryCode": "US", "stateCode": "PA", "latitude": "41.37119000", "longitude": "-75.73491000"}, {"name": "Old Orchard", "countryCode": "US", "stateCode": "PA", "latitude": "40.65788000", "longitude": "-75.26212000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.38759000", "longitude": "-75.78964000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.91869000", "longitude": "-79.71782000"}, {"name": "Olyphant", "countryCode": "US", "stateCode": "PA", "latitude": "41.46841000", "longitude": "-75.60297000"}, {"name": "Orchard Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.58618000", "longitude": "-79.53143000"}, {"name": "Oreland", "countryCode": "US", "stateCode": "PA", "latitude": "40.11844000", "longitude": "-75.17768000"}, {"name": "Orwigsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.65481000", "longitude": "-76.10077000"}, {"name": "Osceola Mills", "countryCode": "US", "stateCode": "PA", "latitude": "40.85006000", "longitude": "-78.27057000"}, {"name": "Oxford", "countryCode": "US", "stateCode": "PA", "latitude": "39.78539000", "longitude": "-75.97883000"}, {"name": "Palmdale", "countryCode": "US", "stateCode": "PA", "latitude": "40.29787000", "longitude": "-76.61858000"}, {"name": "Palmer Heights", "countryCode": "US", "stateCode": "PA", "latitude": "40.68732000", "longitude": "-75.26240000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.80140000", "longitude": "-75.61190000"}, {"name": "Palmyra", "countryCode": "US", "stateCode": "PA", "latitude": "40.30898000", "longitude": "-76.59330000"}, {"name": "Palo Alto", "countryCode": "US", "stateCode": "PA", "latitude": "40.68731000", "longitude": "-76.17216000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.04205000", "longitude": "-75.47631000"}, {"name": "Paradise", "countryCode": "US", "stateCode": "PA", "latitude": "40.00982000", "longitude": "-76.12857000"}, {"name": "Park Forest Village", "countryCode": "US", "stateCode": "PA", "latitude": "40.80673000", "longitude": "-77.91695000"}, {"name": "Parkesburg", "countryCode": "US", "stateCode": "PA", "latitude": "39.95872000", "longitude": "-75.91939000"}, {"name": "Parkside", "countryCode": "US", "stateCode": "PA", "latitude": "39.86428000", "longitude": "-75.37853000"}, {"name": "Parkville", "countryCode": "US", "stateCode": "PA", "latitude": "39.78121000", "longitude": "-76.96331000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.63396000", "longitude": "-78.65030000"}, {"name": "Paxtang", "countryCode": "US", "stateCode": "PA", "latitude": "40.25898000", "longitude": "-76.83192000"}, {"name": "Paxtonia", "countryCode": "US", "stateCode": "PA", "latitude": "40.31731000", "longitude": "-76.79442000"}, {"name": "Pen Argyl", "countryCode": "US", "stateCode": "PA", "latitude": "40.86871000", "longitude": "-75.25490000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.27537000", "longitude": "-76.84803000"}, {"name": "Penn Estates", "countryCode": "US", "stateCode": "PA", "latitude": "41.03750000", "longitude": "-75.23956000"}, {"name": "Penn Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.50118000", "longitude": "-79.83922000"}, {"name": "<PERSON>e", "countryCode": "US", "stateCode": "PA", "latitude": "39.98622000", "longitude": "-75.27546000"}, {"name": "Penndel", "countryCode": "US", "stateCode": "PA", "latitude": "40.15205000", "longitude": "-74.91656000"}, {"name": "Pennsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.39093000", "longitude": "-75.49212000"}, {"name": "Pennside", "countryCode": "US", "stateCode": "PA", "latitude": "40.33732000", "longitude": "-75.87854000"}, {"name": "Pennsport", "countryCode": "US", "stateCode": "PA", "latitude": "39.92761000", "longitude": "-75.15045000"}, {"name": "Pennville", "countryCode": "US", "stateCode": "PA", "latitude": "39.78954000", "longitude": "-76.99804000"}, {"name": "Penryn", "countryCode": "US", "stateCode": "PA", "latitude": "40.20509000", "longitude": "-76.36829000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.37205000", "longitude": "-75.29268000"}, {"name": "Perry County", "countryCode": "US", "stateCode": "PA", "latitude": "40.39841000", "longitude": "-77.26230000"}, {"name": "Perryopolis", "countryCode": "US", "stateCode": "PA", "latitude": "40.08702000", "longitude": "-79.75060000"}, {"name": "Philadelphia", "countryCode": "US", "stateCode": "PA", "latitude": "39.95233000", "longitude": "-75.16379000"}, {"name": "Philadelphia County", "countryCode": "US", "stateCode": "PA", "latitude": "40.00764000", "longitude": "-75.13396000"}, {"name": "Philipsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.89645000", "longitude": "-78.22057000"}, {"name": "Phoenixville", "countryCode": "US", "stateCode": "PA", "latitude": "40.13038000", "longitude": "-75.51491000"}, {"name": "Pike County", "countryCode": "US", "stateCode": "PA", "latitude": "41.33199000", "longitude": "-75.03383000"}, {"name": "Pine Grove", "countryCode": "US", "stateCode": "PA", "latitude": "40.54842000", "longitude": "-76.38468000"}, {"name": "Pine Grove Mills", "countryCode": "US", "stateCode": "PA", "latitude": "40.73367000", "longitude": "-77.88556000"}, {"name": "Pine Ridge", "countryCode": "US", "stateCode": "PA", "latitude": "41.14598000", "longitude": "-74.99116000"}, {"name": "Pitcairn", "countryCode": "US", "stateCode": "PA", "latitude": "40.40312000", "longitude": "-79.77810000"}, {"name": "Pittsburgh", "countryCode": "US", "stateCode": "PA", "latitude": "40.44062000", "longitude": "-79.99589000"}, {"name": "<PERSON>ston", "countryCode": "US", "stateCode": "PA", "latitude": "41.32591000", "longitude": "-75.78936000"}, {"name": "Plains", "countryCode": "US", "stateCode": "PA", "latitude": "41.27536000", "longitude": "-75.85020000"}, {"name": "Pleasant Gap", "countryCode": "US", "stateCode": "PA", "latitude": "40.86812000", "longitude": "-77.74667000"}, {"name": "Pleasant Hill", "countryCode": "US", "stateCode": "PA", "latitude": "40.33620000", "longitude": "-76.44163000"}, {"name": "Pleasant Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.33563000", "longitude": "-79.96061000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.50035000", "longitude": "-79.74949000"}, {"name": "Plumsteadville", "countryCode": "US", "stateCode": "PA", "latitude": "40.38733000", "longitude": "-75.14656000"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "PA", "latitude": "41.24036000", "longitude": "-75.94464000"}, {"name": "Plymouth Meeting", "countryCode": "US", "stateCode": "PA", "latitude": "40.10233000", "longitude": "-75.27435000"}, {"name": "Pocono Pines", "countryCode": "US", "stateCode": "PA", "latitude": "41.10675000", "longitude": "-75.45435000"}, {"name": "Pocono Ranch Lands", "countryCode": "US", "stateCode": "PA", "latitude": "41.16454000", "longitude": "-74.95212000"}, {"name": "Point Marion", "countryCode": "US", "stateCode": "PA", "latitude": "39.73897000", "longitude": "-79.89867000"}, {"name": "Port Allegany", "countryCode": "US", "stateCode": "PA", "latitude": "41.81090000", "longitude": "-78.27974000"}, {"name": "Port Carbon", "countryCode": "US", "stateCode": "PA", "latitude": "40.69648000", "longitude": "-76.16883000"}, {"name": "Port Vue", "countryCode": "US", "stateCode": "PA", "latitude": "40.33590000", "longitude": "-79.86977000"}, {"name": "Portage", "countryCode": "US", "stateCode": "PA", "latitude": "40.38868000", "longitude": "-78.67224000"}, {"name": "Potter County", "countryCode": "US", "stateCode": "PA", "latitude": "41.74492000", "longitude": "-77.89581000"}, {"name": "Pottsgrove", "countryCode": "US", "stateCode": "PA", "latitude": "40.26482000", "longitude": "-75.61185000"}, {"name": "Pottstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.24537000", "longitude": "-75.64963000"}, {"name": "Pottsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.68565000", "longitude": "-76.19550000"}, {"name": "Progress", "countryCode": "US", "stateCode": "PA", "latitude": "40.28509000", "longitude": "-76.83136000"}, {"name": "Prospect", "countryCode": "US", "stateCode": "PA", "latitude": "40.90451000", "longitude": "-80.04645000"}, {"name": "Prospect Park", "countryCode": "US", "stateCode": "PA", "latitude": "39.88789000", "longitude": "-75.30824000"}, {"name": "Punx<PERSON>ta<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.94368000", "longitude": "-78.97087000"}, {"name": "Pymatuning Central", "countryCode": "US", "stateCode": "PA", "latitude": "41.58546000", "longitude": "-80.47960000"}, {"name": "Quakertown", "countryCode": "US", "stateCode": "PA", "latitude": "40.44177000", "longitude": "-75.34157000"}, {"name": "Quarryville", "countryCode": "US", "stateCode": "PA", "latitude": "39.89705000", "longitude": "-76.16357000"}, {"name": "Radnor", "countryCode": "US", "stateCode": "PA", "latitude": "40.04622000", "longitude": "-75.35991000"}, {"name": "Rankin", "countryCode": "US", "stateCode": "PA", "latitude": "40.41257000", "longitude": "-79.87922000"}, {"name": "Raubsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.63566000", "longitude": "-75.19295000"}, {"name": "Reading", "countryCode": "US", "stateCode": "PA", "latitude": "40.33565000", "longitude": "-75.92687000"}, {"name": "Reamstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.21148000", "longitude": "-76.12328000"}, {"name": "Red Hill", "countryCode": "US", "stateCode": "PA", "latitude": "40.37288000", "longitude": "-75.48101000"}, {"name": "Red Lion", "countryCode": "US", "stateCode": "PA", "latitude": "39.90093000", "longitude": "-76.60580000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.31981000", "longitude": "-75.87354000"}, {"name": "Reinholds", "countryCode": "US", "stateCode": "PA", "latitude": "40.26676000", "longitude": "-76.11550000"}, {"name": "Rennerdale", "countryCode": "US", "stateCode": "PA", "latitude": "40.39840000", "longitude": "-80.14145000"}, {"name": "Renovo", "countryCode": "US", "stateCode": "PA", "latitude": "41.32646000", "longitude": "-77.75082000"}, {"name": "Republic", "countryCode": "US", "stateCode": "PA", "latitude": "39.96258000", "longitude": "-79.87671000"}, {"name": "Reynolds Heights", "countryCode": "US", "stateCode": "PA", "latitude": "41.34506000", "longitude": "-80.39423000"}, {"name": "Reynoldsville", "countryCode": "US", "stateCode": "PA", "latitude": "41.09701000", "longitude": "-78.88864000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.13009000", "longitude": "-76.57052000"}, {"name": "Richboro", "countryCode": "US", "stateCode": "PA", "latitude": "40.21511000", "longitude": "-75.01072000"}, {"name": "Richland", "countryCode": "US", "stateCode": "PA", "latitude": "40.35926000", "longitude": "-76.25828000"}, {"name": "Richlandtown", "countryCode": "US", "stateCode": "PA", "latitude": "40.47010000", "longitude": "-75.32046000"}, {"name": "Ridgway", "countryCode": "US", "stateCode": "PA", "latitude": "41.42034000", "longitude": "-78.72864000"}, {"name": "Ridley Park", "countryCode": "US", "stateCode": "PA", "latitude": "39.88122000", "longitude": "-75.32380000"}, {"name": "River View Park", "countryCode": "US", "stateCode": "PA", "latitude": "40.39259000", "longitude": "-75.95882000"}, {"name": "Riverside", "countryCode": "US", "stateCode": "PA", "latitude": "40.95536000", "longitude": "-76.62885000"}, {"name": "Roaring Spring", "countryCode": "US", "stateCode": "PA", "latitude": "40.33591000", "longitude": "-78.39085000"}, {"name": "Robesonia", "countryCode": "US", "stateCode": "PA", "latitude": "40.35176000", "longitude": "-76.13439000"}, {"name": "Rochester", "countryCode": "US", "stateCode": "PA", "latitude": "40.70229000", "longitude": "-80.28645000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.08122000", "longitude": "-75.08962000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.88065000", "longitude": "-75.21462000"}, {"name": "Rothsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.15121000", "longitude": "-76.25107000"}, {"name": "Royalton", "countryCode": "US", "stateCode": "PA", "latitude": "40.18731000", "longitude": "-76.72997000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.18427000", "longitude": "-75.53796000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.94145000", "longitude": "-79.13505000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.61146000", "longitude": "-79.83700000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.26898000", "longitude": "-76.76803000"}, {"name": "Saint Clair", "countryCode": "US", "stateCode": "PA", "latitude": "40.72065000", "longitude": "-76.19105000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.32704000", "longitude": "-75.87187000"}, {"name": "Saint Marys", "countryCode": "US", "stateCode": "PA", "latitude": "41.42784000", "longitude": "-78.56086000"}, {"name": "Salix", "countryCode": "US", "stateCode": "PA", "latitude": "40.30008000", "longitude": "-78.76530000"}, {"name": "Salunga", "countryCode": "US", "stateCode": "PA", "latitude": "40.10093000", "longitude": "-76.42469000"}, {"name": "Sanatoga", "countryCode": "US", "stateCode": "PA", "latitude": "40.24510000", "longitude": "-75.59518000"}, {"name": "Sand Hill", "countryCode": "US", "stateCode": "PA", "latitude": "40.35954000", "longitude": "-76.43163000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.10784000", "longitude": "-78.77114000"}, {"name": "Saw Creek", "countryCode": "US", "stateCode": "PA", "latitude": "41.11259000", "longitude": "-75.05073000"}, {"name": "Saxonburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.75395000", "longitude": "-79.81005000"}, {"name": "Saylorsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.89565000", "longitude": "-75.32352000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.97896000", "longitude": "-76.51550000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.24175000", "longitude": "-77.17693000"}, {"name": "Schnecksville", "countryCode": "US", "stateCode": "PA", "latitude": "40.67514000", "longitude": "-75.62044000"}, {"name": "Schoe<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.24148000", "longitude": "-76.17411000"}, {"name": "Schuylkill County", "countryCode": "US", "stateCode": "PA", "latitude": "40.70580000", "longitude": "-76.21595000"}, {"name": "Schuylkill Haven", "countryCode": "US", "stateCode": "PA", "latitude": "40.63065000", "longitude": "-76.17105000"}, {"name": "Schwenksville", "countryCode": "US", "stateCode": "PA", "latitude": "40.25621000", "longitude": "-75.46379000"}, {"name": "Scotland", "countryCode": "US", "stateCode": "PA", "latitude": "39.96870000", "longitude": "-77.58721000"}, {"name": "Scottdale", "countryCode": "US", "stateCode": "PA", "latitude": "40.10035000", "longitude": "-79.58698000"}, {"name": "Scranton", "countryCode": "US", "stateCode": "PA", "latitude": "41.40916000", "longitude": "-75.66490000"}, {"name": "Selinsgrove", "countryCode": "US", "stateCode": "PA", "latitude": "40.79897000", "longitude": "-76.86219000"}, {"name": "Sellersville", "countryCode": "US", "stateCode": "PA", "latitude": "40.35399000", "longitude": "-75.30490000"}, {"name": "Seneca", "countryCode": "US", "stateCode": "PA", "latitude": "41.37867000", "longitude": "-79.70394000"}, {"name": "Seven Fields", "countryCode": "US", "stateCode": "PA", "latitude": "40.69173000", "longitude": "-80.06256000"}, {"name": "Sewickley", "countryCode": "US", "stateCode": "PA", "latitude": "40.53646000", "longitude": "-80.18450000"}, {"name": "Shamokin", "countryCode": "US", "stateCode": "PA", "latitude": "40.78897000", "longitude": "-76.55885000"}, {"name": "Shamokin Dam", "countryCode": "US", "stateCode": "PA", "latitude": "40.84870000", "longitude": "-76.81969000"}, {"name": "Shanor-Northvue", "countryCode": "US", "stateCode": "PA", "latitude": "40.91045000", "longitude": "-79.91562000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.23311000", "longitude": "-80.49340000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.90650000", "longitude": "-75.27157000"}, {"name": "Sharpsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.49451000", "longitude": "-79.92644000"}, {"name": "Sharpsville", "countryCode": "US", "stateCode": "PA", "latitude": "41.25922000", "longitude": "-80.47201000"}, {"name": "Shavertown", "countryCode": "US", "stateCode": "PA", "latitude": "41.31980000", "longitude": "-75.93798000"}, {"name": "Sheffield", "countryCode": "US", "stateCode": "PA", "latitude": "41.70395000", "longitude": "-79.03560000"}, {"name": "Shenandoah", "countryCode": "US", "stateCode": "PA", "latitude": "40.82037000", "longitude": "-76.20077000"}, {"name": "Shenandoah Heights", "countryCode": "US", "stateCode": "PA", "latitude": "40.82759000", "longitude": "-76.20688000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.30787000", "longitude": "-75.96549000"}, {"name": "Shiloh", "countryCode": "US", "stateCode": "PA", "latitude": "39.97815000", "longitude": "-76.79719000"}, {"name": "Shinglehouse", "countryCode": "US", "stateCode": "PA", "latitude": "41.96368000", "longitude": "-78.19084000"}, {"name": "Shippensburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.05065000", "longitude": "-77.52026000"}, {"name": "Shiremanstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.22342000", "longitude": "-76.95359000"}, {"name": "Shoemakersville", "countryCode": "US", "stateCode": "PA", "latitude": "40.50093000", "longitude": "-75.96993000"}, {"name": "Shrewsbury", "countryCode": "US", "stateCode": "PA", "latitude": "39.76871000", "longitude": "-76.67969000"}, {"name": "Sierra View", "countryCode": "US", "stateCode": "PA", "latitude": "41.01207000", "longitude": "-75.45900000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.59175000", "longitude": "-75.48518000"}, {"name": "Sinking Spring", "countryCode": "US", "stateCode": "PA", "latitude": "40.32731000", "longitude": "-76.01105000"}, {"name": "Skippack", "countryCode": "US", "stateCode": "PA", "latitude": "40.22288000", "longitude": "-75.39879000"}, {"name": "Skyline View", "countryCode": "US", "stateCode": "PA", "latitude": "40.33926000", "longitude": "-76.72553000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.74843000", "longitude": "-75.61185000"}, {"name": "Slippery Rock", "countryCode": "US", "stateCode": "PA", "latitude": "41.06395000", "longitude": "-80.05645000"}, {"name": "Smethport", "countryCode": "US", "stateCode": "PA", "latitude": "41.81117000", "longitude": "-78.44474000"}, {"name": "Snyder County", "countryCode": "US", "stateCode": "PA", "latitude": "40.76984000", "longitude": "-77.07019000"}, {"name": "Somerset", "countryCode": "US", "stateCode": "PA", "latitude": "40.00841000", "longitude": "-79.07808000"}, {"name": "Somerset County", "countryCode": "US", "stateCode": "PA", "latitude": "39.97244000", "longitude": "-79.02827000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.31177000", "longitude": "-75.32518000"}, {"name": "South Coatesville", "countryCode": "US", "stateCode": "PA", "latitude": "39.97427000", "longitude": "-75.81995000"}, {"name": "South Connellsville", "countryCode": "US", "stateCode": "PA", "latitude": "39.99674000", "longitude": "-79.58587000"}, {"name": "South Greensburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.27840000", "longitude": "-79.54476000"}, {"name": "South Park Township", "countryCode": "US", "stateCode": "PA", "latitude": "40.29864000", "longitude": "-79.99405000"}, {"name": "South Pottstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.23954000", "longitude": "-75.65102000"}, {"name": "South Temple", "countryCode": "US", "stateCode": "PA", "latitude": "40.40000000", "longitude": "-75.90000000"}, {"name": "South Uniontown", "countryCode": "US", "stateCode": "PA", "latitude": "39.89285000", "longitude": "-79.74699000"}, {"name": "South Waverly", "countryCode": "US", "stateCode": "PA", "latitude": "41.99757000", "longitude": "-76.53717000"}, {"name": "South Williamsport", "countryCode": "US", "stateCode": "PA", "latitude": "41.23202000", "longitude": "-76.99913000"}, {"name": "Southmont", "countryCode": "US", "stateCode": "PA", "latitude": "40.31063000", "longitude": "-78.93864000"}, {"name": "Southwest Greensburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.29118000", "longitude": "-79.54698000"}, {"name": "Spangler", "countryCode": "US", "stateCode": "PA", "latitude": "40.64285000", "longitude": "-78.77280000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.12452000", "longitude": "-79.87977000"}, {"name": "Spinnerstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.43899000", "longitude": "-75.43712000"}, {"name": "Spring City", "countryCode": "US", "stateCode": "PA", "latitude": "40.17677000", "longitude": "-75.54769000"}, {"name": "Spring Grove", "countryCode": "US", "stateCode": "PA", "latitude": "39.87454000", "longitude": "-76.86581000"}, {"name": "Spring House", "countryCode": "US", "stateCode": "PA", "latitude": "40.18539000", "longitude": "-75.22768000"}, {"name": "Spring Mount", "countryCode": "US", "stateCode": "PA", "latitude": "40.27566000", "longitude": "-75.45657000"}, {"name": "Spring Ridge", "countryCode": "US", "stateCode": "PA", "latitude": "40.35287000", "longitude": "-75.98994000"}, {"name": "Springdale", "countryCode": "US", "stateCode": "PA", "latitude": "40.54090000", "longitude": "-79.78394000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "PA", "latitude": "39.93067000", "longitude": "-75.32019000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.91843000", "longitude": "-76.68497000"}, {"name": "State College", "countryCode": "US", "stateCode": "PA", "latitude": "40.79339000", "longitude": "-77.86000000"}, {"name": "State Line", "countryCode": "US", "stateCode": "PA", "latitude": "39.72482000", "longitude": "-77.72444000"}, {"name": "Steelton", "countryCode": "US", "stateCode": "PA", "latitude": "40.23537000", "longitude": "-76.84136000"}, {"name": "Stewartstown", "countryCode": "US", "stateCode": "PA", "latitude": "39.75371000", "longitude": "-76.59136000"}, {"name": "Stiles", "countryCode": "US", "stateCode": "PA", "latitude": "40.66537000", "longitude": "-75.50824000"}, {"name": "Stoneboro", "countryCode": "US", "stateCode": "PA", "latitude": "41.33922000", "longitude": "-80.10506000"}, {"name": "Stony Creek Mills", "countryCode": "US", "stateCode": "PA", "latitude": "40.34565000", "longitude": "-75.86993000"}, {"name": "Stonybrook", "countryCode": "US", "stateCode": "PA", "latitude": "39.98704000", "longitude": "-76.64413000"}, {"name": "Stormstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.79339000", "longitude": "-78.01667000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.25260000", "longitude": "-75.67741000"}, {"name": "Strasburg", "countryCode": "US", "stateCode": "PA", "latitude": "39.98316000", "longitude": "-76.18412000"}, {"name": "Stroudsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.98676000", "longitude": "-75.19462000"}, {"name": "Sturgeon", "countryCode": "US", "stateCode": "PA", "latitude": "40.38479000", "longitude": "-80.21089000"}, {"name": "Sugarcreek", "countryCode": "US", "stateCode": "PA", "latitude": "41.42145000", "longitude": "-79.88117000"}, {"name": "Sullivan County", "countryCode": "US", "stateCode": "PA", "latitude": "41.44616000", "longitude": "-76.51214000"}, {"name": "Summit Hill", "countryCode": "US", "stateCode": "PA", "latitude": "40.82481000", "longitude": "-75.87103000"}, {"name": "Sun Valley", "countryCode": "US", "stateCode": "PA", "latitude": "40.98203000", "longitude": "-75.46602000"}, {"name": "Sunbury", "countryCode": "US", "stateCode": "PA", "latitude": "40.86259000", "longitude": "-76.79441000"}, {"name": "Sunrise Lake", "countryCode": "US", "stateCode": "PA", "latitude": "41.30981000", "longitude": "-74.96656000"}, {"name": "Susquehanna", "countryCode": "US", "stateCode": "PA", "latitude": "41.94341000", "longitude": "-75.59963000"}, {"name": "Susquehanna County", "countryCode": "US", "stateCode": "PA", "latitude": "41.82133000", "longitude": "-75.80068000"}, {"name": "Susquehanna Trails", "countryCode": "US", "stateCode": "PA", "latitude": "39.75872000", "longitude": "-76.36802000"}, {"name": "Swart<PERSON>ore", "countryCode": "US", "stateCode": "PA", "latitude": "39.90206000", "longitude": "-75.34991000"}, {"name": "Swartzville", "countryCode": "US", "stateCode": "PA", "latitude": "40.23315000", "longitude": "-76.07828000"}, {"name": "Swissvale", "countryCode": "US", "stateCode": "PA", "latitude": "40.42368000", "longitude": "-79.88283000"}, {"name": "Swoyersville", "countryCode": "US", "stateCode": "PA", "latitude": "41.29175000", "longitude": "-75.87464000"}, {"name": "Sykesville", "countryCode": "US", "stateCode": "PA", "latitude": "41.05034000", "longitude": "-78.82225000"}, {"name": "Tacony", "countryCode": "US", "stateCode": "PA", "latitude": "40.03122000", "longitude": "-75.04434000"}, {"name": "Tamaqua", "countryCode": "US", "stateCode": "PA", "latitude": "40.79731000", "longitude": "-75.96937000"}, {"name": "Tannersville", "countryCode": "US", "stateCode": "PA", "latitude": "41.04009000", "longitude": "-75.30574000"}, {"name": "Tarentum", "countryCode": "US", "stateCode": "PA", "latitude": "40.60146000", "longitude": "-79.75977000"}, {"name": "Ta<PERSON>y", "countryCode": "US", "stateCode": "PA", "latitude": "40.74093000", "longitude": "-75.25712000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.39480000", "longitude": "-75.70658000"}, {"name": "Telford", "countryCode": "US", "stateCode": "PA", "latitude": "40.32205000", "longitude": "-75.32795000"}, {"name": "Temple", "countryCode": "US", "stateCode": "PA", "latitude": "40.40870000", "longitude": "-75.92160000"}, {"name": "Terre Hill", "countryCode": "US", "stateCode": "PA", "latitude": "40.15732000", "longitude": "-76.05050000"}, {"name": "The Hideout", "countryCode": "US", "stateCode": "PA", "latitude": "41.42736000", "longitude": "-75.35255000"}, {"name": "Thompsonville", "countryCode": "US", "stateCode": "PA", "latitude": "40.29090000", "longitude": "-80.10811000"}, {"name": "Thorndale", "countryCode": "US", "stateCode": "PA", "latitude": "39.99288000", "longitude": "-75.74522000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.45147000", "longitude": "-75.61185000"}, {"name": "Tinicum", "countryCode": "US", "stateCode": "PA", "latitude": "40.44844000", "longitude": "-75.10767000"}, {"name": "Tioga County", "countryCode": "US", "stateCode": "PA", "latitude": "41.77216000", "longitude": "-77.25426000"}, {"name": "Tionesta", "countryCode": "US", "stateCode": "PA", "latitude": "41.49534000", "longitude": "-79.45588000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.63590000", "longitude": "-78.29585000"}, {"name": "Titusville", "countryCode": "US", "stateCode": "PA", "latitude": "41.62700000", "longitude": "-79.67366000"}, {"name": "Toftrees", "countryCode": "US", "stateCode": "PA", "latitude": "40.82604000", "longitude": "-77.88110000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.50343000", "longitude": "-75.70130000"}, {"name": "Toughkenamon", "countryCode": "US", "stateCode": "PA", "latitude": "39.83150000", "longitude": "-75.75744000"}, {"name": "Towamensing Trails", "countryCode": "US", "stateCode": "PA", "latitude": "41.00787000", "longitude": "-75.58463000"}, {"name": "Towanda", "countryCode": "US", "stateCode": "PA", "latitude": "41.76758000", "longitude": "-76.44272000"}, {"name": "Tower City", "countryCode": "US", "stateCode": "PA", "latitude": "40.58925000", "longitude": "-76.55246000"}, {"name": "Trafford", "countryCode": "US", "stateCode": "PA", "latitude": "40.38562000", "longitude": "-79.75893000"}, {"name": "Trainer", "countryCode": "US", "stateCode": "PA", "latitude": "39.82761000", "longitude": "-75.41436000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.19899000", "longitude": "-75.47629000"}, {"name": "Treasure Lake", "countryCode": "US", "stateCode": "PA", "latitude": "41.17339000", "longitude": "-78.71586000"}, {"name": "Tremont", "countryCode": "US", "stateCode": "PA", "latitude": "40.62842000", "longitude": "-76.38718000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.78120000", "longitude": "-76.67302000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.13928000", "longitude": "-74.98100000"}, {"name": "Trexlertown", "countryCode": "US", "stateCode": "PA", "latitude": "40.54815000", "longitude": "-75.60574000"}, {"name": "Trooper", "countryCode": "US", "stateCode": "PA", "latitude": "40.14983000", "longitude": "-75.40185000"}, {"name": "Troy", "countryCode": "US", "stateCode": "PA", "latitude": "41.78591000", "longitude": "-76.78801000"}, {"name": "Trucksville", "countryCode": "US", "stateCode": "PA", "latitude": "41.30397000", "longitude": "-75.93214000"}, {"name": "Tullytown", "countryCode": "US", "stateCode": "PA", "latitude": "40.13928000", "longitude": "-74.81461000"}, {"name": "Tunkhannock", "countryCode": "US", "stateCode": "PA", "latitude": "41.53869000", "longitude": "-75.94659000"}, {"name": "Turtle Creek", "countryCode": "US", "stateCode": "PA", "latitude": "40.40590000", "longitude": "-79.82505000"}, {"name": "Tyrone", "countryCode": "US", "stateCode": "PA", "latitude": "40.67062000", "longitude": "-78.23862000"}, {"name": "Union City", "countryCode": "US", "stateCode": "PA", "latitude": "41.89950000", "longitude": "-79.84533000"}, {"name": "Union County", "countryCode": "US", "stateCode": "PA", "latitude": "40.96297000", "longitude": "-77.06225000"}, {"name": "Uniontown", "countryCode": "US", "stateCode": "PA", "latitude": "39.90008000", "longitude": "-79.71643000"}, {"name": "Upland", "countryCode": "US", "stateCode": "PA", "latitude": "39.85261000", "longitude": "-75.38269000"}, {"name": "Upper Saint Clair", "countryCode": "US", "stateCode": "PA", "latitude": "40.33590000", "longitude": "-80.08339000"}, {"name": "Valley Green", "countryCode": "US", "stateCode": "PA", "latitude": "40.15731000", "longitude": "-76.79275000"}, {"name": "Valley View", "countryCode": "US", "stateCode": "PA", "latitude": "39.95010000", "longitude": "-76.70108000"}, {"name": "Vandergrift", "countryCode": "US", "stateCode": "PA", "latitude": "40.60284000", "longitude": "-79.56477000"}, {"name": "Venango County", "countryCode": "US", "stateCode": "PA", "latitude": "41.40097000", "longitude": "-79.75795000"}, {"name": "Verona", "countryCode": "US", "stateCode": "PA", "latitude": "40.50646000", "longitude": "-79.84310000"}, {"name": "Versailles", "countryCode": "US", "stateCode": "PA", "latitude": "40.31563000", "longitude": "-79.83116000"}, {"name": "Village Green-Green Ridge", "countryCode": "US", "stateCode": "PA", "latitude": "39.86363000", "longitude": "-75.42548000"}, {"name": "Village Shires", "countryCode": "US", "stateCode": "PA", "latitude": "40.20316000", "longitude": "-74.97045000"}, {"name": "Vinco", "countryCode": "US", "stateCode": "PA", "latitude": "40.40507000", "longitude": "-78.85558000"}, {"name": "Wallenpaupack Lake Estates", "countryCode": "US", "stateCode": "PA", "latitude": "41.39898000", "longitude": "-75.27402000"}, {"name": "Walnutport", "countryCode": "US", "stateCode": "PA", "latitude": "40.75426000", "longitude": "-75.59880000"}, {"name": "Warminster Heights", "countryCode": "US", "stateCode": "PA", "latitude": "40.18705000", "longitude": "-75.08156000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.84395000", "longitude": "-79.14504000"}, {"name": "Warren County", "countryCode": "US", "stateCode": "PA", "latitude": "41.81457000", "longitude": "-79.27414000"}, {"name": "Washington", "countryCode": "US", "stateCode": "PA", "latitude": "40.17396000", "longitude": "-80.24617000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "PA", "latitude": "40.18940000", "longitude": "-80.24824000"}, {"name": "Waterford", "countryCode": "US", "stateCode": "PA", "latitude": "41.94283000", "longitude": "-79.98450000"}, {"name": "Watsontown", "countryCode": "US", "stateCode": "PA", "latitude": "41.08453000", "longitude": "-76.86385000"}, {"name": "Waymart", "countryCode": "US", "stateCode": "PA", "latitude": "41.58036000", "longitude": "-75.40824000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.04400000", "longitude": "-75.38769000"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "PA", "latitude": "41.64873000", "longitude": "-75.30326000"}, {"name": "Wayne Heights", "countryCode": "US", "stateCode": "PA", "latitude": "39.74371000", "longitude": "-77.55388000"}, {"name": "Waynesboro", "countryCode": "US", "stateCode": "PA", "latitude": "39.75593000", "longitude": "-77.57777000"}, {"name": "Waynesburg", "countryCode": "US", "stateCode": "PA", "latitude": "39.89646000", "longitude": "-80.17923000"}, {"name": "<PERSON>ly", "countryCode": "US", "stateCode": "PA", "latitude": "40.94175000", "longitude": "-75.82964000"}, {"name": "Weigelstown", "countryCode": "US", "stateCode": "PA", "latitude": "39.98371000", "longitude": "-76.82247000"}, {"name": "Weissport East", "countryCode": "US", "stateCode": "PA", "latitude": "40.83697000", "longitude": "-75.68643000"}, {"name": "Wellsboro", "countryCode": "US", "stateCode": "PA", "latitude": "41.74868000", "longitude": "-77.30053000"}, {"name": "Wernersville", "countryCode": "US", "stateCode": "PA", "latitude": "40.33009000", "longitude": "-76.08050000"}, {"name": "Wescosville", "countryCode": "US", "stateCode": "PA", "latitude": "40.56676000", "longitude": "-75.55296000"}, {"name": "Wesleyville", "countryCode": "US", "stateCode": "PA", "latitude": "42.14033000", "longitude": "-80.01506000"}, {"name": "West Chester", "countryCode": "US", "stateCode": "PA", "latitude": "39.96097000", "longitude": "-75.60804000"}, {"name": "West Conshohocken", "countryCode": "US", "stateCode": "PA", "latitude": "40.06983000", "longitude": "-75.31630000"}, {"name": "West Easton", "countryCode": "US", "stateCode": "PA", "latitude": "40.67871000", "longitude": "-75.23684000"}, {"name": "West Fairview", "countryCode": "US", "stateCode": "PA", "latitude": "40.27509000", "longitude": "-76.91553000"}, {"name": "West Grove", "countryCode": "US", "stateCode": "PA", "latitude": "39.82205000", "longitude": "-75.82744000"}, {"name": "West Hamburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.54759000", "longitude": "-76.00216000"}, {"name": "West Hazleton", "countryCode": "US", "stateCode": "PA", "latitude": "40.95870000", "longitude": "-75.99604000"}, {"name": "West Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.82423000", "longitude": "-79.54310000"}, {"name": "West Homestead", "countryCode": "US", "stateCode": "PA", "latitude": "40.39396000", "longitude": "-79.91199000"}, {"name": "West Kittanning", "countryCode": "US", "stateCode": "PA", "latitude": "40.81034000", "longitude": "-79.52949000"}, {"name": "West Lawn", "countryCode": "US", "stateCode": "PA", "latitude": "40.32981000", "longitude": "-75.99438000"}, {"name": "West Leechburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.62229000", "longitude": "-79.61282000"}, {"name": "West Mayfield", "countryCode": "US", "stateCode": "PA", "latitude": "40.78006000", "longitude": "-80.33840000"}, {"name": "West Mifflin", "countryCode": "US", "stateCode": "PA", "latitude": "40.36340000", "longitude": "-79.86644000"}, {"name": "West Newton", "countryCode": "US", "stateCode": "PA", "latitude": "40.20979000", "longitude": "-79.76699000"}, {"name": "West Norriton", "countryCode": "US", "stateCode": "PA", "latitude": "40.12955000", "longitude": "-75.37852000"}, {"name": "West Pittston", "countryCode": "US", "stateCode": "PA", "latitude": "41.32758000", "longitude": "-75.79297000"}, {"name": "West Reading", "countryCode": "US", "stateCode": "PA", "latitude": "40.33370000", "longitude": "-75.94743000"}, {"name": "West View", "countryCode": "US", "stateCode": "PA", "latitude": "40.52229000", "longitude": "-80.03422000"}, {"name": "West Wyoming", "countryCode": "US", "stateCode": "PA", "latitude": "41.31980000", "longitude": "-75.84603000"}, {"name": "West Wyomissing", "countryCode": "US", "stateCode": "PA", "latitude": "40.32454000", "longitude": "-75.99077000"}, {"name": "West York", "countryCode": "US", "stateCode": "PA", "latitude": "39.95260000", "longitude": "-76.75136000"}, {"name": "Westfield", "countryCode": "US", "stateCode": "PA", "latitude": "41.91924000", "longitude": "-77.53887000"}, {"name": "Westmont", "countryCode": "US", "stateCode": "PA", "latitude": "40.31563000", "longitude": "-78.95169000"}, {"name": "Westmoreland County", "countryCode": "US", "stateCode": "PA", "latitude": "40.31073000", "longitude": "-79.46696000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.92678000", "longitude": "-75.15712000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.39840000", "longitude": "-79.88977000"}, {"name": "White Haven", "countryCode": "US", "stateCode": "PA", "latitude": "41.06064000", "longitude": "-75.77408000"}, {"name": "White Oak", "countryCode": "US", "stateCode": "PA", "latitude": "40.33757000", "longitude": "-79.80921000"}, {"name": "Whitehall", "countryCode": "US", "stateCode": "PA", "latitude": "40.36118000", "longitude": "-79.99089000"}, {"name": "Whitehall Township", "countryCode": "US", "stateCode": "PA", "latitude": "40.66676000", "longitude": "-75.49991000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.33593000", "longitude": "-76.00605000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.91678000", "longitude": "-75.15546000"}, {"name": "Wickerham Manor-Fisher", "countryCode": "US", "stateCode": "PA", "latitude": "40.17749000", "longitude": "-79.90684000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "41.24591000", "longitude": "-75.88131000"}, {"name": "Wilkinsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.44174000", "longitude": "-79.88199000"}, {"name": "Williamsburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.46202000", "longitude": "-78.19973000"}, {"name": "Williamsport", "countryCode": "US", "stateCode": "PA", "latitude": "41.24119000", "longitude": "-77.00108000"}, {"name": "Williamstown", "countryCode": "US", "stateCode": "PA", "latitude": "40.58009000", "longitude": "-76.61774000"}, {"name": "Willow Grove", "countryCode": "US", "stateCode": "PA", "latitude": "40.14400000", "longitude": "-75.11573000"}, {"name": "Willow Street", "countryCode": "US", "stateCode": "PA", "latitude": "39.97927000", "longitude": "-76.27635000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.39090000", "longitude": "-79.81005000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.68399000", "longitude": "-75.24184000"}, {"name": "Wind Gap", "countryCode": "US", "stateCode": "PA", "latitude": "40.84815000", "longitude": "-75.29157000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.23980000", "longitude": "-78.83502000"}, {"name": "Windsor", "countryCode": "US", "stateCode": "PA", "latitude": "39.91621000", "longitude": "-76.58441000"}, {"name": "Wolfdale", "countryCode": "US", "stateCode": "PA", "latitude": "40.19285000", "longitude": "-80.28784000"}, {"name": "Womelsdorf", "countryCode": "US", "stateCode": "PA", "latitude": "40.36176000", "longitude": "-76.18411000"}, {"name": "Woodbourne", "countryCode": "US", "stateCode": "PA", "latitude": "40.19233000", "longitude": "-74.88878000"}, {"name": "Woodland Heights", "countryCode": "US", "stateCode": "PA", "latitude": "41.40978000", "longitude": "-79.71172000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "39.87233000", "longitude": "-75.33713000"}, {"name": "Woodside", "countryCode": "US", "stateCode": "PA", "latitude": "40.22178000", "longitude": "-74.87544000"}, {"name": "Wormleysburg", "countryCode": "US", "stateCode": "PA", "latitude": "40.26287000", "longitude": "-76.91386000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.31066000", "longitude": "-75.44879000"}, {"name": "Wrightsville", "countryCode": "US", "stateCode": "PA", "latitude": "40.02565000", "longitude": "-76.52997000"}, {"name": "Wyncote", "countryCode": "US", "stateCode": "PA", "latitude": "40.09455000", "longitude": "-75.14879000"}, {"name": "Wyndmoor", "countryCode": "US", "stateCode": "PA", "latitude": "40.08122000", "longitude": "-75.18934000"}, {"name": "Wyoming", "countryCode": "US", "stateCode": "PA", "latitude": "41.31175000", "longitude": "-75.83742000"}, {"name": "Wyoming County", "countryCode": "US", "stateCode": "PA", "latitude": "41.51833000", "longitude": "-76.01655000"}, {"name": "Wyomissing", "countryCode": "US", "stateCode": "PA", "latitude": "40.32954000", "longitude": "-75.96521000"}, {"name": "Wyomissing Hills", "countryCode": "US", "stateCode": "PA", "latitude": "40.33759000", "longitude": "-75.97966000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.24566000", "longitude": "-74.84600000"}, {"name": "Yeadon", "countryCode": "US", "stateCode": "PA", "latitude": "39.93900000", "longitude": "-75.25546000"}, {"name": "Yeagertown", "countryCode": "US", "stateCode": "PA", "latitude": "40.64313000", "longitude": "-77.58055000"}, {"name": "<PERSON>e", "countryCode": "US", "stateCode": "PA", "latitude": "39.90899000", "longitude": "-76.63691000"}, {"name": "York", "countryCode": "US", "stateCode": "PA", "latitude": "39.96260000", "longitude": "-76.72774000"}, {"name": "York County", "countryCode": "US", "stateCode": "PA", "latitude": "39.91996000", "longitude": "-76.72651000"}, {"name": "<PERSON>lyn", "countryCode": "US", "stateCode": "PA", "latitude": "39.99232000", "longitude": "-76.64635000"}, {"name": "Youngsville", "countryCode": "US", "stateCode": "PA", "latitude": "41.85228000", "longitude": "-79.31866000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.24035000", "longitude": "-79.57671000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "PA", "latitude": "40.79451000", "longitude": "-80.13673000"}, {"name": "Zion", "countryCode": "US", "stateCode": "PA", "latitude": "40.91423000", "longitude": "-77.68472000"}]