[{"name": "Aberdeen", "countryCode": "US", "stateCode": "MD", "latitude": "39.50956000", "longitude": "-76.16412000"}, {"name": "Aberdeen Proving Ground", "countryCode": "US", "stateCode": "MD", "latitude": "39.46686000", "longitude": "-76.13066000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.66762000", "longitude": "-77.02831000"}, {"name": "Adamstown", "countryCode": "US", "stateCode": "MD", "latitude": "39.31094000", "longitude": "-77.47471000"}, {"name": "Adelphi", "countryCode": "US", "stateCode": "MD", "latitude": "39.00317000", "longitude": "-76.97192000"}, {"name": "Algonquin", "countryCode": "US", "stateCode": "MD", "latitude": "38.58290000", "longitude": "-76.10577000"}, {"name": "Allegany County", "countryCode": "US", "stateCode": "MD", "latitude": "39.62148000", "longitude": "-78.69890000"}, {"name": "Andrews AFB", "countryCode": "US", "stateCode": "MD", "latitude": "38.80531000", "longitude": "-76.87460000"}, {"name": "Annapolis", "countryCode": "US", "stateCode": "MD", "latitude": "38.97845000", "longitude": "-76.49218000"}, {"name": "Anne <PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.99416000", "longitude": "-76.56760000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.25455000", "longitude": "-76.69997000"}, {"name": "Arden on the Severn", "countryCode": "US", "stateCode": "MD", "latitude": "39.06594000", "longitude": "-76.57885000"}, {"name": "Arlington", "countryCode": "US", "stateCode": "MD", "latitude": "39.34857000", "longitude": "-76.68324000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.03206000", "longitude": "-76.50274000"}, {"name": "Ashton-Sandy Spring", "countryCode": "US", "stateCode": "MD", "latitude": "39.14976000", "longitude": "-77.00504000"}, {"name": "Aspen Hill", "countryCode": "US", "stateCode": "MD", "latitude": "39.07955000", "longitude": "-77.07303000"}, {"name": "Baden", "countryCode": "US", "stateCode": "MD", "latitude": "38.65928000", "longitude": "-76.77775000"}, {"name": "Ballenger Creek", "countryCode": "US", "stateCode": "MD", "latitude": "39.37260000", "longitude": "-77.43526000"}, {"name": "Baltimore", "countryCode": "US", "stateCode": "MD", "latitude": "39.29038000", "longitude": "-76.61219000"}, {"name": "Baltimore County", "countryCode": "US", "stateCode": "MD", "latitude": "39.44307000", "longitude": "-76.61632000"}, {"name": "Baltimore Highlands", "countryCode": "US", "stateCode": "MD", "latitude": "39.23316000", "longitude": "-76.63663000"}, {"name": "Bartonsville", "countryCode": "US", "stateCode": "MD", "latitude": "39.39260000", "longitude": "-77.35804000"}, {"name": "Bel Air", "countryCode": "US", "stateCode": "MD", "latitude": "39.53594000", "longitude": "-76.34829000"}, {"name": "Bel Air North", "countryCode": "US", "stateCode": "MD", "latitude": "39.55429000", "longitude": "-76.37309000"}, {"name": "Bel Air South", "countryCode": "US", "stateCode": "MD", "latitude": "39.50506000", "longitude": "-76.31977000"}, {"name": "Beltsville", "countryCode": "US", "stateCode": "MD", "latitude": "39.03483000", "longitude": "-76.90747000"}, {"name": "Bennsville", "countryCode": "US", "stateCode": "MD", "latitude": "38.60929000", "longitude": "-77.01220000"}, {"name": "Berlin", "countryCode": "US", "stateCode": "MD", "latitude": "38.32262000", "longitude": "-75.21769000"}, {"name": "Berwyn Heights", "countryCode": "US", "stateCode": "MD", "latitude": "38.99400000", "longitude": "-76.91053000"}, {"name": "Bethesda", "countryCode": "US", "stateCode": "MD", "latitude": "38.98067000", "longitude": "-77.10026000"}, {"name": "Bladensburg", "countryCode": "US", "stateCode": "MD", "latitude": "38.93928000", "longitude": "-76.93386000"}, {"name": "Boonsboro", "countryCode": "US", "stateCode": "MD", "latitude": "39.50621000", "longitude": "-77.65249000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.94278000", "longitude": "-76.73028000"}, {"name": "Bowleys Quarters", "countryCode": "US", "stateCode": "MD", "latitude": "39.33539000", "longitude": "-76.39024000"}, {"name": "Bowling Green", "countryCode": "US", "stateCode": "MD", "latitude": "39.62370000", "longitude": "-78.80446000"}, {"name": "Braddock Heights", "countryCode": "US", "stateCode": "MD", "latitude": "39.41871000", "longitude": "-77.50360000"}, {"name": "Brandywine", "countryCode": "US", "stateCode": "MD", "latitude": "38.69678000", "longitude": "-76.84775000"}, {"name": "Brentwood", "countryCode": "US", "stateCode": "MD", "latitude": "38.94317000", "longitude": "-76.95664000"}, {"name": "Brock Hall", "countryCode": "US", "stateCode": "MD", "latitude": "38.85011000", "longitude": "-76.76108000"}, {"name": "Brooklyn Park", "countryCode": "US", "stateCode": "MD", "latitude": "39.22844000", "longitude": "-76.61636000"}, {"name": "Brookmont", "countryCode": "US", "stateCode": "MD", "latitude": "38.94206000", "longitude": "-77.12026000"}, {"name": "Brunswick", "countryCode": "US", "stateCode": "MD", "latitude": "39.31427000", "longitude": "-77.62777000"}, {"name": "Bryans Road", "countryCode": "US", "stateCode": "MD", "latitude": "38.62706000", "longitude": "-77.07303000"}, {"name": "Buckeystown", "countryCode": "US", "stateCode": "MD", "latitude": "39.33482000", "longitude": "-77.43165000"}, {"name": "Burtonsville", "countryCode": "US", "stateCode": "MD", "latitude": "39.11122000", "longitude": "-76.93248000"}, {"name": "Butcher's Hill", "countryCode": "US", "stateCode": "MD", "latitude": "39.28955000", "longitude": "-76.58830000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.97539000", "longitude": "-77.15803000"}, {"name": "California", "countryCode": "US", "stateCode": "MD", "latitude": "38.30040000", "longitude": "-76.50745000"}, {"name": "Calvert County", "countryCode": "US", "stateCode": "MD", "latitude": "38.53471000", "longitude": "-76.53056000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.05761000", "longitude": "-76.93581000"}, {"name": "Cambridge", "countryCode": "US", "stateCode": "MD", "latitude": "38.56317000", "longitude": "-76.07883000"}, {"name": "Camp Springs", "countryCode": "US", "stateCode": "MD", "latitude": "38.80400000", "longitude": "-76.90664000"}, {"name": "Cape Saint Claire", "countryCode": "US", "stateCode": "MD", "latitude": "39.04317000", "longitude": "-76.44496000"}, {"name": "Capitol Heights", "countryCode": "US", "stateCode": "MD", "latitude": "38.88511000", "longitude": "-76.91581000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.39427000", "longitude": "-76.52358000"}, {"name": "Caroline County", "countryCode": "US", "stateCode": "MD", "latitude": "38.87173000", "longitude": "-75.83160000"}, {"name": "Carroll County", "countryCode": "US", "stateCode": "MD", "latitude": "39.56286000", "longitude": "-77.02252000"}, {"name": "Catonsville", "countryCode": "US", "stateCode": "MD", "latitude": "39.27205000", "longitude": "-76.73192000"}, {"name": "Cavetown", "countryCode": "US", "stateCode": "MD", "latitude": "39.64426000", "longitude": "-77.58582000"}, {"name": "Cecil County", "countryCode": "US", "stateCode": "MD", "latitude": "39.56242000", "longitude": "-75.94811000"}, {"name": "Centreville", "countryCode": "US", "stateCode": "MD", "latitude": "39.04178000", "longitude": "-76.06633000"}, {"name": "Charles County", "countryCode": "US", "stateCode": "MD", "latitude": "38.47368000", "longitude": "-77.01348000"}, {"name": "Charles Village", "countryCode": "US", "stateCode": "MD", "latitude": "39.32316000", "longitude": "-76.61330000"}, {"name": "Charlestown", "countryCode": "US", "stateCode": "MD", "latitude": "39.57373000", "longitude": "-75.97495000"}, {"name": "Charlotte Hall", "countryCode": "US", "stateCode": "MD", "latitude": "38.48096000", "longitude": "-76.77802000"}, {"name": "Chesapeake Beach", "countryCode": "US", "stateCode": "MD", "latitude": "38.68623000", "longitude": "-76.53468000"}, {"name": "Chesapeake Ranch Estates", "countryCode": "US", "stateCode": "MD", "latitude": "38.34624000", "longitude": "-76.41773000"}, {"name": "Chester", "countryCode": "US", "stateCode": "MD", "latitude": "38.97539000", "longitude": "-76.28940000"}, {"name": "Chestertown", "countryCode": "US", "stateCode": "MD", "latitude": "39.20900000", "longitude": "-76.06661000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.92817000", "longitude": "-76.91581000"}, {"name": "Chevy Chase", "countryCode": "US", "stateCode": "MD", "latitude": "39.00287000", "longitude": "-77.07115000"}, {"name": "Chevy Chase Village", "countryCode": "US", "stateCode": "MD", "latitude": "38.96928000", "longitude": "-77.07887000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.96372000", "longitude": "-76.99081000"}, {"name": "City of Baltimore", "countryCode": "US", "stateCode": "MD", "latitude": "39.29038000", "longitude": "-76.61219000"}, {"name": "Clarksburg", "countryCode": "US", "stateCode": "MD", "latitude": "39.23872000", "longitude": "-77.27943000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.76511000", "longitude": "-76.89831000"}, {"name": "Clover Hill", "countryCode": "US", "stateCode": "MD", "latitude": "39.45621000", "longitude": "-77.42887000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.10816000", "longitude": "-76.99775000"}, {"name": "Cobb Island", "countryCode": "US", "stateCode": "MD", "latitude": "38.25846000", "longitude": "-76.84386000"}, {"name": "Cockeysville", "countryCode": "US", "stateCode": "MD", "latitude": "39.48122000", "longitude": "-76.64386000"}, {"name": "Colesville", "countryCode": "US", "stateCode": "MD", "latitude": "39.07566000", "longitude": "-77.00192000"}, {"name": "College Park", "countryCode": "US", "stateCode": "MD", "latitude": "38.98067000", "longitude": "-76.93692000"}, {"name": "Colmar Manor", "countryCode": "US", "stateCode": "MD", "latitude": "38.93317000", "longitude": "-76.94581000"}, {"name": "Columbia", "countryCode": "US", "stateCode": "MD", "latitude": "39.24038000", "longitude": "-76.83942000"}, {"name": "Coral Hills", "countryCode": "US", "stateCode": "MD", "latitude": "38.87039000", "longitude": "-76.92108000"}, {"name": "Cottage City", "countryCode": "US", "stateCode": "MD", "latitude": "38.93817000", "longitude": "-76.94831000"}, {"name": "Cresaptown", "countryCode": "US", "stateCode": "MD", "latitude": "39.59287000", "longitude": "-78.83335000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "37.98346000", "longitude": "-75.85382000"}, {"name": "Crofton", "countryCode": "US", "stateCode": "MD", "latitude": "39.00178000", "longitude": "-76.68747000"}, {"name": "Croom", "countryCode": "US", "stateCode": "MD", "latitude": "38.75262000", "longitude": "-76.76386000"}, {"name": "Crownsville", "countryCode": "US", "stateCode": "MD", "latitude": "39.02844000", "longitude": "-76.60135000"}, {"name": "Cumberland", "countryCode": "US", "stateCode": "MD", "latitude": "39.65287000", "longitude": "-78.76252000"}, {"name": "Damascus", "countryCode": "US", "stateCode": "MD", "latitude": "39.28844000", "longitude": "-77.20387000"}, {"name": "Darnestown", "countryCode": "US", "stateCode": "MD", "latitude": "39.10344000", "longitude": "-77.29082000"}, {"name": "Davidsonville", "countryCode": "US", "stateCode": "MD", "latitude": "38.92289000", "longitude": "-76.62830000"}, {"name": "<PERSON>e", "countryCode": "US", "stateCode": "MD", "latitude": "38.77651000", "longitude": "-76.55524000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.88456000", "longitude": "-75.82716000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.11733000", "longitude": "-77.16109000"}, {"name": "District Heights", "countryCode": "US", "stateCode": "MD", "latitude": "38.85761000", "longitude": "-76.88942000"}, {"name": "Dorchester County", "countryCode": "US", "stateCode": "MD", "latitude": "38.42261000", "longitude": "-76.08332000"}, {"name": "Drum Point", "countryCode": "US", "stateCode": "MD", "latitude": "38.32679000", "longitude": "-76.42606000"}, {"name": "Dundalk", "countryCode": "US", "stateCode": "MD", "latitude": "39.25066000", "longitude": "-76.52052000"}, {"name": "Dunkirk", "countryCode": "US", "stateCode": "MD", "latitude": "38.72178000", "longitude": "-76.66052000"}, {"name": "Dunkirk Town Center", "countryCode": "US", "stateCode": "MD", "latitude": "38.72039000", "longitude": "-76.65857000"}, {"name": "East Riverdale", "countryCode": "US", "stateCode": "MD", "latitude": "38.96206000", "longitude": "-76.92192000"}, {"name": "Easton", "countryCode": "US", "stateCode": "MD", "latitude": "38.77428000", "longitude": "-76.07633000"}, {"name": "Edgemere", "countryCode": "US", "stateCode": "MD", "latitude": "39.24205000", "longitude": "-76.44802000"}, {"name": "Edgewater", "countryCode": "US", "stateCode": "MD", "latitude": "38.95706000", "longitude": "-76.54996000"}, {"name": "Edgewood", "countryCode": "US", "stateCode": "MD", "latitude": "39.41872000", "longitude": "-76.29440000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.94678000", "longitude": "-76.93109000"}, {"name": "Eldersburg", "countryCode": "US", "stateCode": "MD", "latitude": "39.40371000", "longitude": "-76.95026000"}, {"name": "Elkridge", "countryCode": "US", "stateCode": "MD", "latitude": "39.21261000", "longitude": "-76.71358000"}, {"name": "Elkton", "countryCode": "US", "stateCode": "MD", "latitude": "39.60678000", "longitude": "-75.83327000"}, {"name": "Ellicott City", "countryCode": "US", "stateCode": "MD", "latitude": "39.26733000", "longitude": "-76.79831000"}, {"name": "Emmitsburg", "countryCode": "US", "stateCode": "MD", "latitude": "39.70454000", "longitude": "-77.32693000"}, {"name": "Essex", "countryCode": "US", "stateCode": "MD", "latitude": "39.30927000", "longitude": "-76.47496000"}, {"name": "Fairland", "countryCode": "US", "stateCode": "MD", "latitude": "39.07622000", "longitude": "-76.95775000"}, {"name": "Fairmount Heights", "countryCode": "US", "stateCode": "MD", "latitude": "38.90095000", "longitude": "-76.91553000"}, {"name": "<PERSON>wood", "countryCode": "US", "stateCode": "MD", "latitude": "38.95665000", "longitude": "-76.77772000"}, {"name": "Fallston", "countryCode": "US", "stateCode": "MD", "latitude": "39.51455000", "longitude": "-76.41107000"}, {"name": "Federalsburg", "countryCode": "US", "stateCode": "MD", "latitude": "38.69428000", "longitude": "-75.77216000"}, {"name": "Ferndale", "countryCode": "US", "stateCode": "MD", "latitude": "39.18316000", "longitude": "-76.64024000"}, {"name": "Forest Glen", "countryCode": "US", "stateCode": "MD", "latitude": "39.01455000", "longitude": "-77.05470000"}, {"name": "Forest Heights", "countryCode": "US", "stateCode": "MD", "latitude": "38.80956000", "longitude": "-76.99803000"}, {"name": "Forestville", "countryCode": "US", "stateCode": "MD", "latitude": "38.84511000", "longitude": "-76.87497000"}, {"name": "Fort George G Mead Junction", "countryCode": "US", "stateCode": "MD", "latitude": "39.12594000", "longitude": "-76.78914000"}, {"name": "Fort Meade", "countryCode": "US", "stateCode": "MD", "latitude": "39.10815000", "longitude": "-76.74323000"}, {"name": "Fort Washington", "countryCode": "US", "stateCode": "MD", "latitude": "38.70734000", "longitude": "-77.02303000"}, {"name": "Fountainhead-Orchard Hills", "countryCode": "US", "stateCode": "MD", "latitude": "39.68636000", "longitude": "-77.71901000"}, {"name": "Four Corners", "countryCode": "US", "stateCode": "MD", "latitude": "39.02039000", "longitude": "-77.01275000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.41427000", "longitude": "-77.41054000"}, {"name": "Frederick County", "countryCode": "US", "stateCode": "MD", "latitude": "39.47222000", "longitude": "-77.39799000"}, {"name": "Friendly", "countryCode": "US", "stateCode": "MD", "latitude": "38.75178000", "longitude": "-76.97859000"}, {"name": "Friendship Village", "countryCode": "US", "stateCode": "MD", "latitude": "38.96289000", "longitude": "-77.08887000"}, {"name": "Frostburg", "countryCode": "US", "stateCode": "MD", "latitude": "39.65814000", "longitude": "-78.92836000"}, {"name": "Fruitland", "countryCode": "US", "stateCode": "MD", "latitude": "38.32206000", "longitude": "-75.62020000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.15094000", "longitude": "-76.92303000"}, {"name": "Gaithersburg", "countryCode": "US", "stateCode": "MD", "latitude": "39.14344000", "longitude": "-77.20137000"}, {"name": "Gambrills", "countryCode": "US", "stateCode": "MD", "latitude": "39.06705000", "longitude": "-76.66524000"}, {"name": "Garrett County", "countryCode": "US", "stateCode": "MD", "latitude": "39.52860000", "longitude": "-79.27388000"}, {"name": "Garrett Park", "countryCode": "US", "stateCode": "MD", "latitude": "39.03816000", "longitude": "-77.09303000"}, {"name": "Garrison", "countryCode": "US", "stateCode": "MD", "latitude": "39.40594000", "longitude": "-76.76053000"}, {"name": "Germantown", "countryCode": "US", "stateCode": "MD", "latitude": "39.17316000", "longitude": "-77.27165000"}, {"name": "Glassmanor", "countryCode": "US", "stateCode": "MD", "latitude": "38.81900000", "longitude": "-76.99859000"}, {"name": "<PERSON>ie", "countryCode": "US", "stateCode": "MD", "latitude": "39.16261000", "longitude": "-76.62469000"}, {"name": "Glenarden", "countryCode": "US", "stateCode": "MD", "latitude": "38.92928000", "longitude": "-76.86164000"}, {"name": "Glenmont", "countryCode": "US", "stateCode": "MD", "latitude": "39.05789000", "longitude": "-77.04970000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.98761000", "longitude": "-76.82053000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.98955000", "longitude": "-76.85331000"}, {"name": "Golden Beach", "countryCode": "US", "stateCode": "MD", "latitude": "38.48985000", "longitude": "-76.68218000"}, {"name": "Grasonville", "countryCode": "US", "stateCode": "MD", "latitude": "38.95817000", "longitude": "-76.21023000"}, {"name": "Greater Upper Marlboro", "countryCode": "US", "stateCode": "MD", "latitude": "38.83142000", "longitude": "-76.74827000"}, {"name": "Green Haven", "countryCode": "US", "stateCode": "MD", "latitude": "39.13955000", "longitude": "-76.54774000"}, {"name": "Green Valley", "countryCode": "US", "stateCode": "MD", "latitude": "39.30927000", "longitude": "-77.29721000"}, {"name": "Greenbelt", "countryCode": "US", "stateCode": "MD", "latitude": "39.00455000", "longitude": "-76.87553000"}, {"name": "Greensboro", "countryCode": "US", "stateCode": "MD", "latitude": "38.97372000", "longitude": "-75.80493000"}, {"name": "Hagerstown", "countryCode": "US", "stateCode": "MD", "latitude": "39.64176000", "longitude": "-77.71999000"}, {"name": "Halfway", "countryCode": "US", "stateCode": "MD", "latitude": "39.62065000", "longitude": "-77.75888000"}, {"name": "Hampstead", "countryCode": "US", "stateCode": "MD", "latitude": "39.60483000", "longitude": "-76.84998000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.42288000", "longitude": "-76.58469000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.69898000", "longitude": "-78.17973000"}, {"name": "Hanover", "countryCode": "US", "stateCode": "MD", "latitude": "39.19289000", "longitude": "-76.72414000"}, {"name": "Harford County", "countryCode": "US", "stateCode": "MD", "latitude": "39.53644000", "longitude": "-76.29887000"}, {"name": "<PERSON><PERSON> <PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.54928000", "longitude": "-76.09162000"}, {"name": "Hebron", "countryCode": "US", "stateCode": "MD", "latitude": "38.42012000", "longitude": "-75.68771000"}, {"name": "Herald Harbor", "countryCode": "US", "stateCode": "MD", "latitude": "39.05372000", "longitude": "-76.56913000"}, {"name": "Highfield-Cascade", "countryCode": "US", "stateCode": "MD", "latitude": "39.71616000", "longitude": "-77.48282000"}, {"name": "Highland", "countryCode": "US", "stateCode": "MD", "latitude": "39.17900000", "longitude": "-76.95748000"}, {"name": "Hill<PERSON>le", "countryCode": "US", "stateCode": "MD", "latitude": "39.02650000", "longitude": "-76.97414000"}, {"name": "Hillcrest Heights", "countryCode": "US", "stateCode": "MD", "latitude": "38.83289000", "longitude": "-76.95942000"}, {"name": "Hillsmere Shores", "countryCode": "US", "stateCode": "MD", "latitude": "38.94011000", "longitude": "-76.49496000"}, {"name": "Howard County", "countryCode": "US", "stateCode": "MD", "latitude": "39.25072000", "longitude": "-76.93119000"}, {"name": "Hughesville", "countryCode": "US", "stateCode": "MD", "latitude": "38.53262000", "longitude": "-76.78386000"}, {"name": "Hunt Valley", "countryCode": "US", "stateCode": "MD", "latitude": "39.49983000", "longitude": "-76.64108000"}, {"name": "Huntingtown", "countryCode": "US", "stateCode": "MD", "latitude": "38.61595000", "longitude": "-76.61302000"}, {"name": "Huntingtown Town Center", "countryCode": "US", "stateCode": "MD", "latitude": "38.62095000", "longitude": "-76.61607000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.62428000", "longitude": "-75.85438000"}, {"name": "Hyattsville", "countryCode": "US", "stateCode": "MD", "latitude": "38.95594000", "longitude": "-76.94553000"}, {"name": "Ilchester", "countryCode": "US", "stateCode": "MD", "latitude": "39.25094000", "longitude": "-76.76469000"}, {"name": "Indian Head", "countryCode": "US", "stateCode": "MD", "latitude": "38.60012000", "longitude": "-77.16220000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.28288000", "longitude": "-76.68608000"}, {"name": "Jarrettsville", "countryCode": "US", "stateCode": "MD", "latitude": "39.60455000", "longitude": "-76.47774000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.36205000", "longitude": "-77.53165000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.14927000", "longitude": "-76.77525000"}, {"name": "Joppatowne", "countryCode": "US", "stateCode": "MD", "latitude": "39.45789000", "longitude": "-76.35524000"}, {"name": "Keedysville", "countryCode": "US", "stateCode": "MD", "latitude": "39.48621000", "longitude": "-77.69971000"}, {"name": "Kemp Mill", "countryCode": "US", "stateCode": "MD", "latitude": "39.03900000", "longitude": "-77.01914000"}, {"name": "Kensington", "countryCode": "US", "stateCode": "MD", "latitude": "39.02567000", "longitude": "-77.07637000"}, {"name": "Kent County", "countryCode": "US", "stateCode": "MD", "latitude": "39.23560000", "longitude": "-76.09582000"}, {"name": "Kettering", "countryCode": "US", "stateCode": "MD", "latitude": "38.88456000", "longitude": "-76.81469000"}, {"name": "Kingstown", "countryCode": "US", "stateCode": "MD", "latitude": "39.20483000", "longitude": "-76.05133000"}, {"name": "Kingsville", "countryCode": "US", "stateCode": "MD", "latitude": "39.44872000", "longitude": "-76.41774000"}, {"name": "La Plata", "countryCode": "US", "stateCode": "MD", "latitude": "38.52929000", "longitude": "-76.97525000"}, {"name": "La Vale", "countryCode": "US", "stateCode": "MD", "latitude": "39.65564000", "longitude": "-78.81058000"}, {"name": "Lake Arbor", "countryCode": "US", "stateCode": "MD", "latitude": "38.90789000", "longitude": "-76.82969000"}, {"name": "Lake Shore", "countryCode": "US", "stateCode": "MD", "latitude": "39.10705000", "longitude": "-76.48496000"}, {"name": "Landover", "countryCode": "US", "stateCode": "MD", "latitude": "38.93400000", "longitude": "-76.89664000"}, {"name": "Landover Hills", "countryCode": "US", "stateCode": "MD", "latitude": "38.94317000", "longitude": "-76.89220000"}, {"name": "Langley Park", "countryCode": "US", "stateCode": "MD", "latitude": "38.98872000", "longitude": "-76.98136000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.96875000", "longitude": "-76.86340000"}, {"name": "Lanham-Seabrook", "countryCode": "US", "stateCode": "MD", "latitude": "38.96835000", "longitude": "-76.85108000"}, {"name": "Lansdowne", "countryCode": "US", "stateCode": "MD", "latitude": "39.24511000", "longitude": "-76.66052000"}, {"name": "Largo", "countryCode": "US", "stateCode": "MD", "latitude": "38.89761000", "longitude": "-76.83025000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.09928000", "longitude": "-76.84831000"}, {"name": "Layhill", "countryCode": "US", "stateCode": "MD", "latitude": "39.09233000", "longitude": "-77.04442000"}, {"name": "Leisure World", "countryCode": "US", "stateCode": "MD", "latitude": "39.10230000", "longitude": "-77.06898000"}, {"name": "Leonardtown", "countryCode": "US", "stateCode": "MD", "latitude": "38.29124000", "longitude": "-76.63579000"}, {"name": "Lexington Park", "countryCode": "US", "stateCode": "MD", "latitude": "38.26679000", "longitude": "-76.45384000"}, {"name": "Linganore", "countryCode": "US", "stateCode": "MD", "latitude": "39.44038000", "longitude": "-77.20804000"}, {"name": "Linthicum", "countryCode": "US", "stateCode": "MD", "latitude": "39.20511000", "longitude": "-76.65275000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.34066000", "longitude": "-76.72219000"}, {"name": "Lonaconing", "countryCode": "US", "stateCode": "MD", "latitude": "39.56592000", "longitude": "-78.98030000"}, {"name": "Londontowne", "countryCode": "US", "stateCode": "MD", "latitude": "38.93345000", "longitude": "-76.54941000"}, {"name": "Long Beach", "countryCode": "US", "stateCode": "MD", "latitude": "38.46096000", "longitude": "-76.46884000"}, {"name": "Lu<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.41068000", "longitude": "-76.45523000"}, {"name": "Lutherville", "countryCode": "US", "stateCode": "MD", "latitude": "39.42122000", "longitude": "-76.62608000"}, {"name": "Lutherville-Timonium", "countryCode": "US", "stateCode": "MD", "latitude": "39.43997000", "longitude": "-76.61099000"}, {"name": "Manchester", "countryCode": "US", "stateCode": "MD", "latitude": "39.66121000", "longitude": "-76.88498000"}, {"name": "Marlboro Meadows", "countryCode": "US", "stateCode": "MD", "latitude": "38.83622000", "longitude": "-76.71497000"}, {"name": "Marlboro Village", "countryCode": "US", "stateCode": "MD", "latitude": "38.83054000", "longitude": "-76.76965000"}, {"name": "Marlow Heights", "countryCode": "US", "stateCode": "MD", "latitude": "38.83345000", "longitude": "-76.95164000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.77373000", "longitude": "-76.78997000"}, {"name": "Maryland City", "countryCode": "US", "stateCode": "MD", "latitude": "39.09205000", "longitude": "-76.81775000"}, {"name": "Maugansville", "countryCode": "US", "stateCode": "MD", "latitude": "39.69287000", "longitude": "-77.74472000"}, {"name": "Mayo", "countryCode": "US", "stateCode": "MD", "latitude": "38.88761000", "longitude": "-76.51190000"}, {"name": "Mays Chapel", "countryCode": "US", "stateCode": "MD", "latitude": "39.43316000", "longitude": "-76.64941000"}, {"name": "Mechanicsville", "countryCode": "US", "stateCode": "MD", "latitude": "38.44290000", "longitude": "-76.74385000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.81039000", "longitude": "-76.82414000"}, {"name": "Middle River", "countryCode": "US", "stateCode": "MD", "latitude": "39.33427000", "longitude": "-76.43941000"}, {"name": "Middletown", "countryCode": "US", "stateCode": "MD", "latitude": "39.44371000", "longitude": "-77.54471000"}, {"name": "Milford Mill", "countryCode": "US", "stateCode": "MD", "latitude": "39.34788000", "longitude": "-76.76997000"}, {"name": "Mitchellville", "countryCode": "US", "stateCode": "MD", "latitude": "38.92511000", "longitude": "-76.74275000"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "MD", "latitude": "39.13638000", "longitude": "-77.20424000"}, {"name": "Montgomery Village", "countryCode": "US", "stateCode": "MD", "latitude": "39.17677000", "longitude": "-77.19526000"}, {"name": "Morningside", "countryCode": "US", "stateCode": "MD", "latitude": "38.83011000", "longitude": "-76.89136000"}, {"name": "Mount Airy", "countryCode": "US", "stateCode": "MD", "latitude": "39.37621000", "longitude": "-77.15470000"}, {"name": "Mount Rainier", "countryCode": "US", "stateCode": "MD", "latitude": "38.94150000", "longitude": "-76.96498000"}, {"name": "Mountain Lake Park", "countryCode": "US", "stateCode": "MD", "latitude": "39.39843000", "longitude": "-79.38171000"}, {"name": "Myersville", "countryCode": "US", "stateCode": "MD", "latitude": "39.50510000", "longitude": "-77.56638000"}, {"name": "National Harbor", "countryCode": "US", "stateCode": "MD", "latitude": "38.78264000", "longitude": "-77.01506000"}, {"name": "Naval Academy", "countryCode": "US", "stateCode": "MD", "latitude": "38.98568000", "longitude": "-76.48774000"}, {"name": "New Carrollton", "countryCode": "US", "stateCode": "MD", "latitude": "38.96983000", "longitude": "-76.87997000"}, {"name": "New Windsor", "countryCode": "US", "stateCode": "MD", "latitude": "39.54205000", "longitude": "-77.10804000"}, {"name": "North Beach", "countryCode": "US", "stateCode": "MD", "latitude": "38.70734000", "longitude": "-76.53107000"}, {"name": "North Bel Air", "countryCode": "US", "stateCode": "MD", "latitude": "39.53983000", "longitude": "-76.35496000"}, {"name": "North Bethesda", "countryCode": "US", "stateCode": "MD", "latitude": "39.04455000", "longitude": "-77.11887000"}, {"name": "North East", "countryCode": "US", "stateCode": "MD", "latitude": "39.60011000", "longitude": "-75.94133000"}, {"name": "North Kensington", "countryCode": "US", "stateCode": "MD", "latitude": "39.03039000", "longitude": "-77.07248000"}, {"name": "North Laurel", "countryCode": "US", "stateCode": "MD", "latitude": "39.13900000", "longitude": "-76.87053000"}, {"name": "North Potomac", "countryCode": "US", "stateCode": "MD", "latitude": "39.08289000", "longitude": "-77.26498000"}, {"name": "Oakland", "countryCode": "US", "stateCode": "MD", "latitude": "39.40787000", "longitude": "-79.40671000"}, {"name": "Ocean City", "countryCode": "US", "stateCode": "MD", "latitude": "38.33650000", "longitude": "-75.08491000"}, {"name": "Ocean Pines", "countryCode": "US", "stateCode": "MD", "latitude": "38.39539000", "longitude": "-75.15574000"}, {"name": "Odenton", "countryCode": "US", "stateCode": "MD", "latitude": "39.08400000", "longitude": "-76.70025000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.15316000", "longitude": "-77.06692000"}, {"name": "Overlea", "countryCode": "US", "stateCode": "MD", "latitude": "39.36344000", "longitude": "-76.52052000"}, {"name": "Owings", "countryCode": "US", "stateCode": "MD", "latitude": "38.71762000", "longitude": "-76.60135000"}, {"name": "Owings Mills", "countryCode": "US", "stateCode": "MD", "latitude": "39.41955000", "longitude": "-76.78025000"}, {"name": "Oxon Hill", "countryCode": "US", "stateCode": "MD", "latitude": "38.80345000", "longitude": "-76.98970000"}, {"name": "Oxon Hill-Glassmanor", "countryCode": "US", "stateCode": "MD", "latitude": "38.79615000", "longitude": "-76.97499000"}, {"name": "Paramount-Long Meadow", "countryCode": "US", "stateCode": "MD", "latitude": "39.68042000", "longitude": "-77.69290000"}, {"name": "Parkville", "countryCode": "US", "stateCode": "MD", "latitude": "39.37733000", "longitude": "-76.53969000"}, {"name": "Parole", "countryCode": "US", "stateCode": "MD", "latitude": "38.97956000", "longitude": "-76.53052000"}, {"name": "Pasadena", "countryCode": "US", "stateCode": "MD", "latitude": "39.10733000", "longitude": "-76.57108000"}, {"name": "Peppermill Village", "countryCode": "US", "stateCode": "MD", "latitude": "38.89472000", "longitude": "-76.88654000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.41261000", "longitude": "-76.46357000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.46955000", "longitude": "-76.20440000"}, {"name": "Perryville", "countryCode": "US", "stateCode": "MD", "latitude": "39.56011000", "longitude": "-76.07134000"}, {"name": "Pikesville", "countryCode": "US", "stateCode": "MD", "latitude": "39.37427000", "longitude": "-76.72247000"}, {"name": "Pittsville", "countryCode": "US", "stateCode": "MD", "latitude": "38.39539000", "longitude": "-75.41297000"}, {"name": "Pleasant Hills", "countryCode": "US", "stateCode": "MD", "latitude": "39.47955000", "longitude": "-76.39413000"}, {"name": "Pocomoke City", "countryCode": "US", "stateCode": "MD", "latitude": "38.07568000", "longitude": "-75.56798000"}, {"name": "Point of Rocks", "countryCode": "US", "stateCode": "MD", "latitude": "39.27594000", "longitude": "-77.53915000"}, {"name": "Poolesville", "countryCode": "US", "stateCode": "MD", "latitude": "39.14594000", "longitude": "-77.41693000"}, {"name": "Potomac", "countryCode": "US", "stateCode": "MD", "latitude": "39.01817000", "longitude": "-77.20859000"}, {"name": "Potomac Heights", "countryCode": "US", "stateCode": "MD", "latitude": "38.60873000", "longitude": "-77.14053000"}, {"name": "Potomac Park", "countryCode": "US", "stateCode": "MD", "latitude": "39.61176000", "longitude": "-78.80585000"}, {"name": "Prince <PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.54040000", "longitude": "-76.58440000"}, {"name": "Prince George's County", "countryCode": "US", "stateCode": "MD", "latitude": "38.82952000", "longitude": "-76.84729000"}, {"name": "Princess <PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.20290000", "longitude": "-75.69243000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.21733000", "longitude": "-76.63719000"}, {"name": "Queen <PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.89872000", "longitude": "-76.67830000"}, {"name": "Queen Anne's County", "countryCode": "US", "stateCode": "MD", "latitude": "39.03763000", "longitude": "-76.08504000"}, {"name": "Queenland", "countryCode": "US", "stateCode": "MD", "latitude": "38.80524000", "longitude": "-76.79126000"}, {"name": "Randallstown", "countryCode": "US", "stateCode": "MD", "latitude": "39.36733000", "longitude": "-76.79525000"}, {"name": "Redland", "countryCode": "US", "stateCode": "MD", "latitude": "39.14539000", "longitude": "-77.14415000"}, {"name": "Reisterstown", "countryCode": "US", "stateCode": "MD", "latitude": "39.46976000", "longitude": "-76.83190000"}, {"name": "<PERSON>ly", "countryCode": "US", "stateCode": "MD", "latitude": "38.94789000", "longitude": "-75.88438000"}, {"name": "Rising Sun", "countryCode": "US", "stateCode": "MD", "latitude": "39.69789000", "longitude": "-76.06273000"}, {"name": "Riva", "countryCode": "US", "stateCode": "MD", "latitude": "38.95206000", "longitude": "-76.57802000"}, {"name": "Riverdale Park", "countryCode": "US", "stateCode": "MD", "latitude": "38.96344000", "longitude": "-76.93164000"}, {"name": "Riverside", "countryCode": "US", "stateCode": "MD", "latitude": "39.47372000", "longitude": "-76.24134000"}, {"name": "Riviera Beach", "countryCode": "US", "stateCode": "MD", "latitude": "39.16678000", "longitude": "-76.50802000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.63704000", "longitude": "-77.65694000"}, {"name": "Rock Hall", "countryCode": "US", "stateCode": "MD", "latitude": "39.13817000", "longitude": "-76.23495000"}, {"name": "Rockville", "countryCode": "US", "stateCode": "MD", "latitude": "39.08400000", "longitude": "-77.15276000"}, {"name": "Rosaryville", "countryCode": "US", "stateCode": "MD", "latitude": "38.75678000", "longitude": "-76.80969000"}, {"name": "Rosedale", "countryCode": "US", "stateCode": "MD", "latitude": "39.32011000", "longitude": "-76.51552000"}, {"name": "Rossmoor", "countryCode": "US", "stateCode": "MD", "latitude": "39.10372000", "longitude": "-77.07109000"}, {"name": "Rossville", "countryCode": "US", "stateCode": "MD", "latitude": "39.33844000", "longitude": "-76.47968000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.60317000", "longitude": "-76.93858000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.56260000", "longitude": "-77.75805000"}, {"name": "Saint Mary's County", "countryCode": "US", "stateCode": "MD", "latitude": "38.21586000", "longitude": "-76.52906000"}, {"name": "Saint Michaels", "countryCode": "US", "stateCode": "MD", "latitude": "38.78512000", "longitude": "-76.22439000"}, {"name": "Salisbury", "countryCode": "US", "stateCode": "MD", "latitude": "38.36067000", "longitude": "-75.59937000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.13789000", "longitude": "-76.82386000"}, {"name": "Scaggsville", "countryCode": "US", "stateCode": "MD", "latitude": "39.14511000", "longitude": "-76.90025000"}, {"name": "Seabrook", "countryCode": "US", "stateCode": "MD", "latitude": "38.96805000", "longitude": "-76.84658000"}, {"name": "Seat Pleasant", "countryCode": "US", "stateCode": "MD", "latitude": "38.89622000", "longitude": "-76.90664000"}, {"name": "Selby-on-the-Bay", "countryCode": "US", "stateCode": "MD", "latitude": "38.91622000", "longitude": "-76.52246000"}, {"name": "Severn", "countryCode": "US", "stateCode": "MD", "latitude": "39.13705000", "longitude": "-76.69830000"}, {"name": "Severna Park", "countryCode": "US", "stateCode": "MD", "latitude": "39.07039000", "longitude": "-76.54524000"}, {"name": "Shady Side", "countryCode": "US", "stateCode": "MD", "latitude": "38.84178000", "longitude": "-76.51218000"}, {"name": "Silver Hill", "countryCode": "US", "stateCode": "MD", "latitude": "38.84178000", "longitude": "-76.94581000"}, {"name": "Silver Spring", "countryCode": "US", "stateCode": "MD", "latitude": "38.99067000", "longitude": "-77.02609000"}, {"name": "Smithsburg", "countryCode": "US", "stateCode": "MD", "latitude": "39.65482000", "longitude": "-77.57277000"}, {"name": "Snow Hill", "countryCode": "US", "stateCode": "MD", "latitude": "38.17706000", "longitude": "-75.39270000"}, {"name": "Solomons", "countryCode": "US", "stateCode": "MD", "latitude": "38.31846000", "longitude": "-76.45412000"}, {"name": "Somerset", "countryCode": "US", "stateCode": "MD", "latitude": "38.96594000", "longitude": "-77.09609000"}, {"name": "Somerset County", "countryCode": "US", "stateCode": "MD", "latitude": "38.08007000", "longitude": "-75.85347000"}, {"name": "South Bel Air", "countryCode": "US", "stateCode": "MD", "latitude": "39.53316000", "longitude": "-76.33746000"}, {"name": "South Gate", "countryCode": "US", "stateCode": "MD", "latitude": "39.12900000", "longitude": "-76.62580000"}, {"name": "South Kensington", "countryCode": "US", "stateCode": "MD", "latitude": "39.01900000", "longitude": "-77.07998000"}, {"name": "South Laurel", "countryCode": "US", "stateCode": "MD", "latitude": "39.06983000", "longitude": "-76.85025000"}, {"name": "Spencerville", "countryCode": "US", "stateCode": "MD", "latitude": "39.11427000", "longitude": "-76.97831000"}, {"name": "Spring Ridge", "countryCode": "US", "stateCode": "MD", "latitude": "39.40149000", "longitude": "-77.35248000"}, {"name": "Springdale", "countryCode": "US", "stateCode": "MD", "latitude": "38.93761000", "longitude": "-76.83886000"}, {"name": "Stevensville", "countryCode": "US", "stateCode": "MD", "latitude": "38.98067000", "longitude": "-76.31440000"}, {"name": "Suitland", "countryCode": "US", "stateCode": "MD", "latitude": "38.84872000", "longitude": "-76.92386000"}, {"name": "Suitland-Silver <PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.84685000", "longitude": "-76.92591000"}, {"name": "Summerfield", "countryCode": "US", "stateCode": "MD", "latitude": "38.90454000", "longitude": "-76.86830000"}, {"name": "Sykesville", "countryCode": "US", "stateCode": "MD", "latitude": "39.37371000", "longitude": "-76.96776000"}, {"name": "Takoma Park", "countryCode": "US", "stateCode": "MD", "latitude": "38.97789000", "longitude": "-77.00748000"}, {"name": "Talbot County", "countryCode": "US", "stateCode": "MD", "latitude": "38.74910000", "longitude": "-76.17862000"}, {"name": "Taneytown", "countryCode": "US", "stateCode": "MD", "latitude": "39.65788000", "longitude": "-77.17443000"}, {"name": "Temple Hills", "countryCode": "US", "stateCode": "MD", "latitude": "38.81400000", "longitude": "-76.94553000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.62371000", "longitude": "-77.41082000"}, {"name": "Timonium", "countryCode": "US", "stateCode": "MD", "latitude": "39.43705000", "longitude": "-76.61969000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.40150000", "longitude": "-76.60191000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.65845000", "longitude": "-76.05800000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.06900000", "longitude": "-77.26304000"}, {"name": "University Park", "countryCode": "US", "stateCode": "MD", "latitude": "38.97039000", "longitude": "-76.94192000"}, {"name": "Upper Marlboro", "countryCode": "US", "stateCode": "MD", "latitude": "38.81595000", "longitude": "-76.74969000"}, {"name": "Urbana", "countryCode": "US", "stateCode": "MD", "latitude": "39.32594000", "longitude": "-77.35137000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "38.62456000", "longitude": "-76.93914000"}, {"name": "Walker Mill", "countryCode": "US", "stateCode": "MD", "latitude": "38.87539000", "longitude": "-76.88831000"}, {"name": "Walkersville", "countryCode": "US", "stateCode": "MD", "latitude": "39.48621000", "longitude": "-77.35193000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "MD", "latitude": "39.60367000", "longitude": "-77.81398000"}, {"name": "West Elkridge", "countryCode": "US", "stateCode": "MD", "latitude": "39.20705000", "longitude": "-76.72692000"}, {"name": "West Laurel", "countryCode": "US", "stateCode": "MD", "latitude": "39.10122000", "longitude": "-76.89970000"}, {"name": "West Ocean City", "countryCode": "US", "stateCode": "MD", "latitude": "38.33150000", "longitude": "-75.10685000"}, {"name": "Westernport", "countryCode": "US", "stateCode": "MD", "latitude": "39.48537000", "longitude": "-79.04475000"}, {"name": "Westminster", "countryCode": "US", "stateCode": "MD", "latitude": "39.57538000", "longitude": "-76.99581000"}, {"name": "Westphalia", "countryCode": "US", "stateCode": "MD", "latitude": "38.84539000", "longitude": "-76.81108000"}, {"name": "Wheaton", "countryCode": "US", "stateCode": "MD", "latitude": "39.03983000", "longitude": "-77.05526000"}, {"name": "White Marsh", "countryCode": "US", "stateCode": "MD", "latitude": "39.38372000", "longitude": "-76.43218000"}, {"name": "White Oak", "countryCode": "US", "stateCode": "MD", "latitude": "39.03983000", "longitude": "-76.99303000"}, {"name": "Wicomico County", "countryCode": "US", "stateCode": "MD", "latitude": "38.36942000", "longitude": "-75.63151000"}, {"name": "Williamsport", "countryCode": "US", "stateCode": "MD", "latitude": "39.60065000", "longitude": "-77.82055000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MD", "latitude": "39.65351000", "longitude": "-77.83157000"}, {"name": "Woodlawn", "countryCode": "US", "stateCode": "MD", "latitude": "39.32288000", "longitude": "-76.72803000"}, {"name": "Woodmore", "countryCode": "US", "stateCode": "MD", "latitude": "38.92122000", "longitude": "-76.80303000"}, {"name": "Woodsboro", "countryCode": "US", "stateCode": "MD", "latitude": "39.53316000", "longitude": "-77.31471000"}, {"name": "Worcester County", "countryCode": "US", "stateCode": "MD", "latitude": "38.21650000", "longitude": "-75.29667000"}]