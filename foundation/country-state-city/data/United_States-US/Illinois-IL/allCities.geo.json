[{"name": "Abingdon", "countryCode": "US", "stateCode": "IL", "latitude": "40.80448000", "longitude": "-90.40180000"}, {"name": "Adams County", "countryCode": "US", "stateCode": "IL", "latitude": "39.98789000", "longitude": "-91.18849000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.93170000", "longitude": "-87.98896000"}, {"name": "Albany Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.96836000", "longitude": "-87.72339000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.54338000", "longitude": "-89.61231000"}, {"name": "Albion", "countryCode": "US", "stateCode": "IL", "latitude": "38.37755000", "longitude": "-88.05615000"}, {"name": "Aledo", "countryCode": "US", "stateCode": "IL", "latitude": "41.19976000", "longitude": "-90.74931000"}, {"name": "Alexander County", "countryCode": "US", "stateCode": "IL", "latitude": "37.19160000", "longitude": "-89.33764000"}, {"name": "Algonquin", "countryCode": "US", "stateCode": "IL", "latitude": "42.16558000", "longitude": "-88.29425000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.58977000", "longitude": "-90.12011000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.66892000", "longitude": "-87.73866000"}, {"name": "Altamont", "countryCode": "US", "stateCode": "IL", "latitude": "39.06199000", "longitude": "-88.74811000"}, {"name": "Alton", "countryCode": "US", "stateCode": "IL", "latitude": "38.89060000", "longitude": "-90.18428000"}, {"name": "Amboy", "countryCode": "US", "stateCode": "IL", "latitude": "41.71420000", "longitude": "-89.32871000"}, {"name": "Andalusia", "countryCode": "US", "stateCode": "IL", "latitude": "41.43920000", "longitude": "-90.71764000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "37.46033000", "longitude": "-89.24703000"}, {"name": "Antioch", "countryCode": "US", "stateCode": "IL", "latitude": "42.47724000", "longitude": "-88.09564000"}, {"name": "Arcola", "countryCode": "US", "stateCode": "IL", "latitude": "39.68476000", "longitude": "-88.30644000"}, {"name": "Arlington Heights", "countryCode": "US", "stateCode": "IL", "latitude": "42.08836000", "longitude": "-87.98063000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.71476000", "longitude": "-88.47228000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.74753000", "longitude": "-87.71116000"}, {"name": "Ashland", "countryCode": "US", "stateCode": "IL", "latitude": "39.88783000", "longitude": "-90.00789000"}, {"name": "Assumption", "countryCode": "US", "stateCode": "IL", "latitude": "39.52032000", "longitude": "-89.04897000"}, {"name": "Astoria", "countryCode": "US", "stateCode": "IL", "latitude": "40.22754000", "longitude": "-90.35957000"}, {"name": "Athens", "countryCode": "US", "stateCode": "IL", "latitude": "39.96088000", "longitude": "-89.72399000"}, {"name": "Atlanta", "countryCode": "US", "stateCode": "IL", "latitude": "40.25948000", "longitude": "-89.23342000"}, {"name": "Atwood", "countryCode": "US", "stateCode": "IL", "latitude": "39.79948000", "longitude": "-88.46228000"}, {"name": "Auburn", "countryCode": "US", "stateCode": "IL", "latitude": "39.59172000", "longitude": "-89.74649000"}, {"name": "Auburn Gresham", "countryCode": "US", "stateCode": "IL", "latitude": "41.74179000", "longitude": "-87.65322000"}, {"name": "Aurora", "countryCode": "US", "stateCode": "IL", "latitude": "41.76058000", "longitude": "-88.32007000"}, {"name": "Aviston", "countryCode": "US", "stateCode": "IL", "latitude": "38.60672000", "longitude": "-89.60759000"}, {"name": "Avondale", "countryCode": "US", "stateCode": "IL", "latitude": "41.93892000", "longitude": "-87.71117000"}, {"name": "Bannockburn", "countryCode": "US", "stateCode": "IL", "latitude": "42.19336000", "longitude": "-87.86646000"}, {"name": "Barrington", "countryCode": "US", "stateCode": "IL", "latitude": "42.15391000", "longitude": "-88.13619000"}, {"name": "Barrington Hills", "countryCode": "US", "stateCode": "IL", "latitude": "42.14475000", "longitude": "-88.15563000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.69421000", "longitude": "-91.03902000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.99503000", "longitude": "-88.18563000"}, {"name": "Bartonville", "countryCode": "US", "stateCode": "IL", "latitude": "40.65032000", "longitude": "-89.65205000"}, {"name": "Batavia", "countryCode": "US", "stateCode": "IL", "latitude": "41.85003000", "longitude": "-88.31257000"}, {"name": "Beach Park", "countryCode": "US", "stateCode": "IL", "latitude": "42.42224000", "longitude": "-87.85730000"}, {"name": "Beardstown", "countryCode": "US", "stateCode": "IL", "latitude": "40.01755000", "longitude": "-90.42429000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.60560000", "longitude": "-89.43592000"}, {"name": "Beecher", "countryCode": "US", "stateCode": "IL", "latitude": "41.34059000", "longitude": "-87.62143000"}, {"name": "Belleville", "countryCode": "US", "stateCode": "IL", "latitude": "38.52005000", "longitude": "-89.98399000"}, {"name": "Bellevue", "countryCode": "US", "stateCode": "IL", "latitude": "40.68448000", "longitude": "-89.68010000"}, {"name": "Bellwood", "countryCode": "US", "stateCode": "IL", "latitude": "41.88142000", "longitude": "-87.88312000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.93170000", "longitude": "-87.76867000"}, {"name": "Belvidere", "countryCode": "US", "stateCode": "IL", "latitude": "42.26391000", "longitude": "-88.84427000"}, {"name": "Bement", "countryCode": "US", "stateCode": "IL", "latitude": "39.92198000", "longitude": "-88.57201000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.09282000", "longitude": "-89.80398000"}, {"name": "Bensenville", "countryCode": "US", "stateCode": "IL", "latitude": "41.95503000", "longitude": "-87.94007000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "37.99672000", "longitude": "-88.92007000"}, {"name": "Berkeley", "countryCode": "US", "stateCode": "IL", "latitude": "41.88892000", "longitude": "-87.90340000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.85059000", "longitude": "-87.79367000"}, {"name": "Bethal<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.90921000", "longitude": "-90.04066000"}, {"name": "Bethany", "countryCode": "US", "stateCode": "IL", "latitude": "39.64559000", "longitude": "-88.73813000"}, {"name": "Big Rock", "countryCode": "US", "stateCode": "IL", "latitude": "41.76392000", "longitude": "-88.54702000"}, {"name": "Bloomingdale", "countryCode": "US", "stateCode": "IL", "latitude": "41.95753000", "longitude": "-88.08090000"}, {"name": "Bloomington", "countryCode": "US", "stateCode": "IL", "latitude": "40.48420000", "longitude": "-88.99369000"}, {"name": "Blue Island", "countryCode": "US", "stateCode": "IL", "latitude": "41.65726000", "longitude": "-87.68005000"}, {"name": "Blue Mound", "countryCode": "US", "stateCode": "IL", "latitude": "39.70115000", "longitude": "-89.12314000"}, {"name": "<PERSON>ling<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.69864000", "longitude": "-88.06840000"}, {"name": "Bond County", "countryCode": "US", "stateCode": "IL", "latitude": "38.88682000", "longitude": "-89.43555000"}, {"name": "Boone County", "countryCode": "US", "stateCode": "IL", "latitude": "42.32308000", "longitude": "-88.82336000"}, {"name": "Boulder Hill", "countryCode": "US", "stateCode": "IL", "latitude": "41.71253000", "longitude": "-88.33618000"}, {"name": "Bourbonnais", "countryCode": "US", "stateCode": "IL", "latitude": "41.15376000", "longitude": "-87.88754000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.14198000", "longitude": "-87.86115000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.26503000", "longitude": "-88.21228000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.61060000", "longitude": "-89.52703000"}, {"name": "Bridgeport", "countryCode": "US", "stateCode": "IL", "latitude": "41.83809000", "longitude": "-87.65116000"}, {"name": "Bridgeview", "countryCode": "US", "stateCode": "IL", "latitude": "41.75003000", "longitude": "-87.80422000"}, {"name": "Brighton", "countryCode": "US", "stateCode": "IL", "latitude": "39.03977000", "longitude": "-90.14066000"}, {"name": "Brighton Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.81892000", "longitude": "-87.69894000"}, {"name": "Broadview", "countryCode": "US", "stateCode": "IL", "latitude": "41.86392000", "longitude": "-87.85339000"}, {"name": "Brookfield", "countryCode": "US", "stateCode": "IL", "latitude": "41.82392000", "longitude": "-87.********"}, {"name": "Brown County", "countryCode": "US", "stateCode": "IL", "latitude": "39.********", "longitude": "-90.********"}, {"name": "Buffalo Grove", "countryCode": "US", "stateCode": "IL", "latitude": "42.********", "longitude": "-87.********"}, {"name": "Bull Valley", "countryCode": "US", "stateCode": "IL", "latitude": "42.********", "longitude": "-88.********"}, {"name": "Bunker Hill", "countryCode": "US", "stateCode": "IL", "latitude": "39.********", "longitude": "-89.********"}, {"name": "Burbank", "countryCode": "US", "stateCode": "IL", "latitude": "41.********", "longitude": "-87.********"}, {"name": "Bureau County", "countryCode": "US", "stateCode": "IL", "latitude": "41.********", "longitude": "-89.********"}, {"name": "Burnham", "countryCode": "US", "stateCode": "IL", "latitude": "41.********", "longitude": "-87.********"}, {"name": "Burr Ridge", "countryCode": "US", "stateCode": "IL", "latitude": "41.********", "longitude": "-87.********"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.********", "longitude": "-90.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.********", "longitude": "-89.********"}, {"name": "Cahokia", "countryCode": "US", "stateCode": "IL", "latitude": "38.********", "longitude": "-90.********"}, {"name": "Cairo", "countryCode": "US", "stateCode": "IL", "latitude": "37.********", "longitude": "-89.********"}, {"name": "Calhoun County", "countryCode": "US", "stateCode": "IL", "latitude": "39.16930000", "longitude": "-90.66753000"}, {"name": "Calumet City", "countryCode": "US", "stateCode": "IL", "latitude": "41.61559000", "longitude": "-87.52949000"}, {"name": "Calumet Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.66281000", "longitude": "-87.66060000"}, {"name": "Cambria", "countryCode": "US", "stateCode": "IL", "latitude": "37.78144000", "longitude": "-89.11925000"}, {"name": "Cambridge", "countryCode": "US", "stateCode": "IL", "latitude": "41.30365000", "longitude": "-90.19290000"}, {"name": "Camp Point", "countryCode": "US", "stateCode": "IL", "latitude": "40.03921000", "longitude": "-91.06930000"}, {"name": "Canton", "countryCode": "US", "stateCode": "IL", "latitude": "40.55809000", "longitude": "-90.03512000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.39974000", "longitude": "-88.74038000"}, {"name": "Carbon Cliff", "countryCode": "US", "stateCode": "IL", "latitude": "41.49476000", "longitude": "-90.39068000"}, {"name": "Carbondale", "countryCode": "US", "stateCode": "IL", "latitude": "37.72727000", "longitude": "-89.21675000"}, {"name": "Carlinville", "countryCode": "US", "stateCode": "IL", "latitude": "39.27977000", "longitude": "-89.88177000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.61033000", "longitude": "-89.37258000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.09088000", "longitude": "-88.15865000"}, {"name": "Carol Stream", "countryCode": "US", "stateCode": "IL", "latitude": "41.91253000", "longitude": "-88.13479000"}, {"name": "Carpentersville", "countryCode": "US", "stateCode": "IL", "latitude": "42.12114000", "longitude": "-88.25786000"}, {"name": "Carrier Mills", "countryCode": "US", "stateCode": "IL", "latitude": "37.68422000", "longitude": "-88.63283000"}, {"name": "Carroll County", "countryCode": "US", "stateCode": "IL", "latitude": "42.06861000", "longitude": "-89.93433000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.30227000", "longitude": "-90.40706000"}, {"name": "Carterville", "countryCode": "US", "stateCode": "IL", "latitude": "37.76005000", "longitude": "-89.07730000"}, {"name": "Carthage", "countryCode": "US", "stateCode": "IL", "latitude": "40.41643000", "longitude": "-91.13625000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.21197000", "longitude": "-88.23814000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.29920000", "longitude": "-87.99253000"}, {"name": "Caseyville", "countryCode": "US", "stateCode": "IL", "latitude": "38.63672000", "longitude": "-90.02566000"}, {"name": "Cass County", "countryCode": "US", "stateCode": "IL", "latitude": "39.97356000", "longitude": "-90.24738000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.06504000", "longitude": "-87.70197000"}, {"name": "Central City", "countryCode": "US", "stateCode": "IL", "latitude": "38.54894000", "longitude": "-89.12701000"}, {"name": "Centralia", "countryCode": "US", "stateCode": "IL", "latitude": "38.52505000", "longitude": "-89.13340000"}, {"name": "Centreville", "countryCode": "US", "stateCode": "IL", "latitude": "38.58338000", "longitude": "-90.12511000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.89059000", "longitude": "-88.72813000"}, {"name": "Champaign", "countryCode": "US", "stateCode": "IL", "latitude": "40.11642000", "longitude": "-88.24338000"}, {"name": "Champaign County", "countryCode": "US", "stateCode": "IL", "latitude": "40.14008000", "longitude": "-88.19919000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.42948000", "longitude": "-88.22867000"}, {"name": "Channel Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.47863000", "longitude": "-88.13759000"}, {"name": "Charleston", "countryCode": "US", "stateCode": "IL", "latitude": "39.49615000", "longitude": "-88.17615000"}, {"name": "Chatham", "countryCode": "US", "stateCode": "IL", "latitude": "41.74115000", "longitude": "-87.61255000"}, {"name": "Chatsworth", "countryCode": "US", "stateCode": "IL", "latitude": "40.75365000", "longitude": "-88.29199000"}, {"name": "Chebanse", "countryCode": "US", "stateCode": "IL", "latitude": "41.00309000", "longitude": "-87.90810000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.74170000", "longitude": "-88.71979000"}, {"name": "Cherry Valley", "countryCode": "US", "stateCode": "IL", "latitude": "42.23474000", "longitude": "-88.94899000"}, {"name": "Chester", "countryCode": "US", "stateCode": "IL", "latitude": "37.91366000", "longitude": "-89.82205000"}, {"name": "Chicago", "countryCode": "US", "stateCode": "IL", "latitude": "41.85003000", "longitude": "-87.65005000"}, {"name": "Chicago Heights", "countryCode": "US", "stateCode": "IL", "latitude": "41.50615000", "longitude": "-87.63560000"}, {"name": "Chicago Lawn", "countryCode": "US", "stateCode": "IL", "latitude": "41.77503000", "longitude": "-87.69644000"}, {"name": "Chicago Loop", "countryCode": "US", "stateCode": "IL", "latitude": "41.88407000", "longitude": "-87.63330000"}, {"name": "Chicago Ridge", "countryCode": "US", "stateCode": "IL", "latitude": "41.70142000", "longitude": "-87.77922000"}, {"name": "Chillicothe", "countryCode": "US", "stateCode": "IL", "latitude": "40.92226000", "longitude": "-89.48620000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.80365000", "longitude": "-87.67364000"}, {"name": "Christian County", "countryCode": "US", "stateCode": "IL", "latitude": "39.54579000", "longitude": "-89.27727000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "37.97255000", "longitude": "-89.05341000"}, {"name": "Cicero", "countryCode": "US", "stateCode": "IL", "latitude": "41.84559000", "longitude": "-87.75394000"}, {"name": "Clarendon Hills", "countryCode": "US", "stateCode": "IL", "latitude": "41.79753000", "longitude": "-87.95478000"}, {"name": "Clark County", "countryCode": "US", "stateCode": "IL", "latitude": "39.33357000", "longitude": "-87.78772000"}, {"name": "Clay County", "countryCode": "US", "stateCode": "IL", "latitude": "38.75416000", "longitude": "-88.49019000"}, {"name": "Clifton", "countryCode": "US", "stateCode": "IL", "latitude": "40.93531000", "longitude": "-87.93449000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.15365000", "longitude": "-88.96453000"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "IL", "latitude": "38.60645000", "longitude": "-89.42248000"}, {"name": "Coal City", "countryCode": "US", "stateCode": "IL", "latitude": "41.28781000", "longitude": "-88.28562000"}, {"name": "Coal Valley", "countryCode": "US", "stateCode": "IL", "latitude": "41.42865000", "longitude": "-90.46096000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "37.53144000", "longitude": "-89.25342000"}, {"name": "Colchester", "countryCode": "US", "stateCode": "IL", "latitude": "40.42643000", "longitude": "-90.79263000"}, {"name": "Coles County", "countryCode": "US", "stateCode": "IL", "latitude": "39.52029000", "longitude": "-88.22180000"}, {"name": "Colfax", "countryCode": "US", "stateCode": "IL", "latitude": "40.56698000", "longitude": "-88.61645000"}, {"name": "Collinsville", "countryCode": "US", "stateCode": "IL", "latitude": "38.67033000", "longitude": "-89.98455000"}, {"name": "Colona", "countryCode": "US", "stateCode": "IL", "latitude": "41.48392000", "longitude": "-90.35318000"}, {"name": "Columbia", "countryCode": "US", "stateCode": "IL", "latitude": "38.44366000", "longitude": "-90.20122000"}, {"name": "Cook County", "countryCode": "US", "stateCode": "IL", "latitude": "41.89540000", "longitude": "-87.64616000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.92003000", "longitude": "-88.68870000"}, {"name": "Country Club Hills", "countryCode": "US", "stateCode": "IL", "latitude": "41.56809000", "longitude": "-87.72033000"}, {"name": "Countryside", "countryCode": "US", "stateCode": "IL", "latitude": "41.78281000", "longitude": "-87.87811000"}, {"name": "Crainville", "countryCode": "US", "stateCode": "IL", "latitude": "37.75199000", "longitude": "-89.06785000"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "IL", "latitude": "39.00269000", "longitude": "-87.75956000"}, {"name": "Crest Hill", "countryCode": "US", "stateCode": "IL", "latitude": "41.55475000", "longitude": "-88.09867000"}, {"name": "Crestwood", "countryCode": "US", "stateCode": "IL", "latitude": "41.64463000", "longitude": "-87.74154000"}, {"name": "Crete", "countryCode": "US", "stateCode": "IL", "latitude": "41.44448000", "longitude": "-87.63143000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.64726000", "longitude": "-89.59121000"}, {"name": "Crystal Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.24113000", "longitude": "-88.31620000"}, {"name": "Crystal Lawns", "countryCode": "US", "stateCode": "IL", "latitude": "41.57031000", "longitude": "-88.15812000"}, {"name": "Cuba", "countryCode": "US", "stateCode": "IL", "latitude": "42.18391000", "longitude": "-88.19091000"}, {"name": "Cumberland County", "countryCode": "US", "stateCode": "IL", "latitude": "39.27332000", "longitude": "-88.24023000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.52948000", "longitude": "-89.17731000"}, {"name": "Danville", "countryCode": "US", "stateCode": "IL", "latitude": "40.12448000", "longitude": "-87.63002000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.75198000", "longitude": "-87.97395000"}, {"name": "Davis Junction", "countryCode": "US", "stateCode": "IL", "latitude": "42.10169000", "longitude": "-89.09316000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "37.81755000", "longitude": "-89.22786000"}, {"name": "De <PERSON> County", "countryCode": "US", "stateCode": "IL", "latitude": "40.17463000", "longitude": "-88.90409000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.92947000", "longitude": "-88.75036000"}, {"name": "DeKalb County", "countryCode": "US", "stateCode": "IL", "latitude": "41.89353000", "longitude": "-88.77031000"}, {"name": "Decatur", "countryCode": "US", "stateCode": "IL", "latitude": "39.84031000", "longitude": "-88.95480000"}, {"name": "Deer Park", "countryCode": "US", "stateCode": "IL", "latitude": "42.16086000", "longitude": "-88.08147000"}, {"name": "Deerfield", "countryCode": "US", "stateCode": "IL", "latitude": "42.17114000", "longitude": "-87.84451000"}, {"name": "Delavan", "countryCode": "US", "stateCode": "IL", "latitude": "40.37254000", "longitude": "-89.54732000"}, {"name": "De<PERSON>e", "countryCode": "US", "stateCode": "IL", "latitude": "41.32420000", "longitude": "-89.30675000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.03336000", "longitude": "-87.88340000"}, {"name": "Diamond", "countryCode": "US", "stateCode": "IL", "latitude": "41.28864000", "longitude": "-88.25173000"}, {"name": "Divern<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.56561000", "longitude": "-89.65732000"}, {"name": "Dixmoor", "countryCode": "US", "stateCode": "IL", "latitude": "41.63170000", "longitude": "-87.66088000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.83892000", "longitude": "-89.47955000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.********", "longitude": "-87.60727000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.83476000", "longitude": "-87.61811000"}, {"name": "Douglas County", "countryCode": "US", "stateCode": "IL", "latitude": "39.76946000", "longitude": "-88.21735000"}, {"name": "Downers Grove", "countryCode": "US", "stateCode": "IL", "latitude": "41.80892000", "longitude": "-88.01117000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.01144000", "longitude": "-89.23619000"}, {"name": "DuPage County", "countryCode": "US", "stateCode": "IL", "latitude": "41.85195000", "longitude": "-88.08567000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.86170000", "longitude": "-89.67871000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.51616000", "longitude": "-90.21039000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.43640000", "longitude": "-89.33206000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.09448000", "longitude": "-88.42506000"}, {"name": "Earlville", "countryCode": "US", "stateCode": "IL", "latitude": "41.58948000", "longitude": "-88.92203000"}, {"name": "East Alton", "countryCode": "US", "stateCode": "IL", "latitude": "38.88033000", "longitude": "-90.11122000"}, {"name": "East Dubuque", "countryCode": "US", "stateCode": "IL", "latitude": "42.49223000", "longitude": "-90.64291000"}, {"name": "East Dundee", "countryCode": "US", "stateCode": "IL", "latitude": "42.09891000", "longitude": "-88.27147000"}, {"name": "East Garfield Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.88087000", "longitude": "-87.70283000"}, {"name": "East Hazel Crest", "countryCode": "US", "stateCode": "IL", "latitude": "41.57365000", "longitude": "-87.64643000"}, {"name": "East Moline", "countryCode": "US", "stateCode": "IL", "latitude": "41.50087000", "longitude": "-90.44430000"}, {"name": "East Peoria", "countryCode": "US", "stateCode": "IL", "latitude": "40.66615000", "longitude": "-89.58010000"}, {"name": "East Saint Louis", "countryCode": "US", "stateCode": "IL", "latitude": "38.62450000", "longitude": "-90.15094000"}, {"name": "Edgar County", "countryCode": "US", "stateCode": "IL", "latitude": "39.67853000", "longitude": "-87.74557000"}, {"name": "Edgewater", "countryCode": "US", "stateCode": "IL", "latitude": "41.98337000", "longitude": "-87.66395000"}, {"name": "Edinburg", "countryCode": "US", "stateCode": "IL", "latitude": "39.65727000", "longitude": "-89.38953000"}, {"name": "Edwards County", "countryCode": "US", "stateCode": "IL", "latitude": "38.41653000", "longitude": "-88.05327000"}, {"name": "Edwardsville", "countryCode": "US", "stateCode": "IL", "latitude": "38.81144000", "longitude": "-89.95316000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.12004000", "longitude": "-88.54338000"}, {"name": "Effingham County", "countryCode": "US", "stateCode": "IL", "latitude": "39.05977000", "longitude": "-88.58986000"}, {"name": "El Paso", "countryCode": "US", "stateCode": "IL", "latitude": "40.73920000", "longitude": "-89.01646000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.89225000", "longitude": "-88.47230000"}, {"name": "Eldorado", "countryCode": "US", "stateCode": "IL", "latitude": "37.81366000", "longitude": "-88.43810000"}, {"name": "Elgin", "countryCode": "US", "stateCode": "IL", "latitude": "42.03725000", "longitude": "-88.28119000"}, {"name": "Elizabethtown", "countryCode": "US", "stateCode": "IL", "latitude": "37.44588000", "longitude": "-88.30504000"}, {"name": "Elk Grove Village", "countryCode": "US", "stateCode": "IL", "latitude": "42.00392000", "longitude": "-87.97035000"}, {"name": "Elmhurst", "countryCode": "US", "stateCode": "IL", "latitude": "41.89947000", "longitude": "-87.94034000"}, {"name": "Elmwood", "countryCode": "US", "stateCode": "IL", "latitude": "40.77782000", "longitude": "-89.96650000"}, {"name": "Elmwood Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.92114000", "longitude": "-87.80923000"}, {"name": "Elwood", "countryCode": "US", "stateCode": "IL", "latitude": "41.40392000", "longitude": "-88.11172000"}, {"name": "Energy", "countryCode": "US", "stateCode": "IL", "latitude": "37.77394000", "longitude": "-89.02646000"}, {"name": "Englewood", "countryCode": "US", "stateCode": "IL", "latitude": "41.77976000", "longitude": "-87.64588000"}, {"name": "Erie", "countryCode": "US", "stateCode": "IL", "latitude": "41.65642000", "longitude": "-90.07929000"}, {"name": "Eureka", "countryCode": "US", "stateCode": "IL", "latitude": "40.72143000", "longitude": "-89.27286000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.04114000", "longitude": "-87.69006000"}, {"name": "Evergreen Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.72059000", "longitude": "-87.70172000"}, {"name": "Fairbury", "countryCode": "US", "stateCode": "IL", "latitude": "40.74726000", "longitude": "-88.51478000"}, {"name": "Fairfield", "countryCode": "US", "stateCode": "IL", "latitude": "38.37894000", "longitude": "-88.35977000"}, {"name": "Fairmont", "countryCode": "US", "stateCode": "IL", "latitude": "41.55614000", "longitude": "-88.05923000"}, {"name": "Fairmont City", "countryCode": "US", "stateCode": "IL", "latitude": "38.64977000", "longitude": "-90.09316000"}, {"name": "Fairview Heights", "countryCode": "US", "stateCode": "IL", "latitude": "38.58894000", "longitude": "-89.99038000"}, {"name": "Farmer City", "countryCode": "US", "stateCode": "IL", "latitude": "40.24337000", "longitude": "-88.64257000"}, {"name": "Farmington", "countryCode": "US", "stateCode": "IL", "latitude": "40.69809000", "longitude": "-90.00595000"}, {"name": "Fayette County", "countryCode": "US", "stateCode": "IL", "latitude": "39.00019000", "longitude": "-89.02414000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.31476000", "longitude": "-88.35005000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.87809000", "longitude": "-88.86118000"}, {"name": "Flora", "countryCode": "US", "stateCode": "IL", "latitude": "38.66894000", "longitude": "-88.48560000"}, {"name": "Flossmoor", "countryCode": "US", "stateCode": "IL", "latitude": "41.54281000", "longitude": "-87.68477000"}, {"name": "Ford County", "countryCode": "US", "stateCode": "IL", "latitude": "40.59718000", "longitude": "-88.22326000"}, {"name": "Ford Heights", "countryCode": "US", "stateCode": "IL", "latitude": "41.50642000", "longitude": "-87.59171000"}, {"name": "Forest Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.20752000", "longitude": "-88.05563000"}, {"name": "Forest Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.87948000", "longitude": "-87.81367000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.75198000", "longitude": "-88.41116000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.12614000", "longitude": "-89.57928000"}, {"name": "<PERSON>sy<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.93254000", "longitude": "-88.95119000"}, {"name": "Fox Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.39669000", "longitude": "-88.18370000"}, {"name": "Fox Lake Hills", "countryCode": "US", "stateCode": "IL", "latitude": "42.40808000", "longitude": "-88.13175000"}, {"name": "Fox River Grove", "countryCode": "US", "stateCode": "IL", "latitude": "42.20086000", "longitude": "-88.21453000"}, {"name": "Frankfort", "countryCode": "US", "stateCode": "IL", "latitude": "41.49587000", "longitude": "-87.84866000"}, {"name": "Frankfort Square", "countryCode": "US", "stateCode": "IL", "latitude": "41.51892000", "longitude": "-87.80310000"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "IL", "latitude": "37.99229000", "longitude": "-88.92415000"}, {"name": "Franklin Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.93531000", "longitude": "-87.86562000"}, {"name": "Freeburg", "countryCode": "US", "stateCode": "IL", "latitude": "38.42755000", "longitude": "-89.91371000"}, {"name": "Freeport", "countryCode": "US", "stateCode": "IL", "latitude": "42.29669000", "longitude": "-89.62123000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.86725000", "longitude": "-90.15957000"}, {"name": "Fulton County", "countryCode": "US", "stateCode": "IL", "latitude": "40.47277000", "longitude": "-90.20747000"}, {"name": "Gage Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.79503000", "longitude": "-87.69616000"}, {"name": "Gages Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.35169000", "longitude": "-87.98258000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.41667000", "longitude": "-90.42902000"}, {"name": "Galesburg", "countryCode": "US", "stateCode": "IL", "latitude": "40.94782000", "longitude": "-90.37124000"}, {"name": "Gallatin County", "countryCode": "US", "stateCode": "IL", "latitude": "37.76275000", "longitude": "-88.23050000"}, {"name": "G<PERSON>va", "countryCode": "US", "stateCode": "IL", "latitude": "41.16754000", "longitude": "-90.04261000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.18559000", "longitude": "-88.30978000"}, {"name": "Geneseo", "countryCode": "US", "stateCode": "IL", "latitude": "41.44809000", "longitude": "-90.15428000"}, {"name": "Geneva", "countryCode": "US", "stateCode": "IL", "latitude": "41.88753000", "longitude": "-88.30535000"}, {"name": "Genoa", "countryCode": "US", "stateCode": "IL", "latitude": "42.09725000", "longitude": "-88.69287000"}, {"name": "Georgetown", "countryCode": "US", "stateCode": "IL", "latitude": "42.13975000", "longitude": "-89.82873000"}, {"name": "Germantown", "countryCode": "US", "stateCode": "IL", "latitude": "38.55366000", "longitude": "-89.53842000"}, {"name": "Germantown Hills", "countryCode": "US", "stateCode": "IL", "latitude": "40.76643000", "longitude": "-89.46787000"}, {"name": "Gibson City", "countryCode": "US", "stateCode": "IL", "latitude": "40.45843000", "longitude": "-88.38460000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.30587000", "longitude": "-88.02115000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.10336000", "longitude": "-88.37286000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.12977000", "longitude": "-89.81954000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.76670000", "longitude": "-87.99226000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.44644000", "longitude": "-89.78093000"}, {"name": "Glasford", "countryCode": "US", "stateCode": "IL", "latitude": "40.57254000", "longitude": "-89.81344000"}, {"name": "Glen Carbon", "countryCode": "US", "stateCode": "IL", "latitude": "38.74838000", "longitude": "-89.98316000"}, {"name": "Glen <PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.87753000", "longitude": "-88.06701000"}, {"name": "Glencoe", "countryCode": "US", "stateCode": "IL", "latitude": "42.13503000", "longitude": "-87.75812000"}, {"name": "Glendale Heights", "countryCode": "US", "stateCode": "IL", "latitude": "41.91460000", "longitude": "-88.06486000"}, {"name": "Glenview", "countryCode": "US", "stateCode": "IL", "latitude": "42.06975000", "longitude": "-87.78784000"}, {"name": "Glenwood", "countryCode": "US", "stateCode": "IL", "latitude": "41.54253000", "longitude": "-87.60227000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.95560000", "longitude": "-90.18678000"}, {"name": "Golconda", "countryCode": "US", "stateCode": "IL", "latitude": "37.36727000", "longitude": "-88.48643000"}, {"name": "Goodings Grove", "countryCode": "US", "stateCode": "IL", "latitude": "41.62920000", "longitude": "-87.93089000"}, {"name": "Goreville", "countryCode": "US", "stateCode": "IL", "latitude": "37.55450000", "longitude": "-88.97229000"}, {"name": "Grand Boulevard", "countryCode": "US", "stateCode": "IL", "latitude": "41.81392000", "longitude": "-87.61727000"}, {"name": "Grandview", "countryCode": "US", "stateCode": "IL", "latitude": "39.81644000", "longitude": "-89.61871000"}, {"name": "Grandwood Park", "countryCode": "US", "stateCode": "IL", "latitude": "42.39308000", "longitude": "-87.98674000"}, {"name": "Granite City", "countryCode": "US", "stateCode": "IL", "latitude": "38.70144000", "longitude": "-90.14872000"}, {"name": "Grant Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.24114000", "longitude": "-87.64615000"}, {"name": "Granville", "countryCode": "US", "stateCode": "IL", "latitude": "41.26115000", "longitude": "-89.22759000"}, {"name": "Grayslake", "countryCode": "US", "stateCode": "IL", "latitude": "42.34447000", "longitude": "-88.04175000"}, {"name": "Grayville", "countryCode": "US", "stateCode": "IL", "latitude": "38.25755000", "longitude": "-87.99364000"}, {"name": "Greater Grand Crossing", "countryCode": "US", "stateCode": "IL", "latitude": "41.76113000", "longitude": "-87.61485000"}, {"name": "Green Oaks", "countryCode": "US", "stateCode": "IL", "latitude": "42.29002000", "longitude": "-87.90341000"}, {"name": "Green Rock", "countryCode": "US", "stateCode": "IL", "latitude": "41.47309000", "longitude": "-90.35763000"}, {"name": "Greene County", "countryCode": "US", "stateCode": "IL", "latitude": "39.35620000", "longitude": "-90.39049000"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "IL", "latitude": "39.34366000", "longitude": "-90.21261000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.24782000", "longitude": "-88.16337000"}, {"name": "Greenville", "countryCode": "US", "stateCode": "IL", "latitude": "38.89227000", "longitude": "-89.41314000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.74337000", "longitude": "-88.88146000"}, {"name": "Griggsville", "countryCode": "US", "stateCode": "IL", "latitude": "39.70894000", "longitude": "-90.72457000"}, {"name": "Grundy County", "countryCode": "US", "stateCode": "IL", "latitude": "41.28509000", "longitude": "-88.41850000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.37030000", "longitude": "-87.90202000"}, {"name": "Hainesville", "countryCode": "US", "stateCode": "IL", "latitude": "42.34502000", "longitude": "-88.06786000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.39643000", "longitude": "-91.33904000"}, {"name": "Hamilton County", "countryCode": "US", "stateCode": "IL", "latitude": "38.08157000", "longitude": "-88.53911000"}, {"name": "Hampshire", "countryCode": "US", "stateCode": "IL", "latitude": "42.09780000", "longitude": "-88.53036000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.55587000", "longitude": "-90.40930000"}, {"name": "Hancock County", "countryCode": "US", "stateCode": "IL", "latitude": "40.40378000", "longitude": "-91.16470000"}, {"name": "Hanna City", "countryCode": "US", "stateCode": "IL", "latitude": "40.69170000", "longitude": "-89.79511000"}, {"name": "Hanover Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.99947000", "longitude": "-88.14507000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.15671000", "longitude": "-90.61790000"}, {"name": "Hardin County", "countryCode": "US", "stateCode": "IL", "latitude": "37.51820000", "longitude": "-88.26685000"}, {"name": "Harrisburg", "countryCode": "US", "stateCode": "IL", "latitude": "37.73838000", "longitude": "-88.54061000"}, {"name": "Harristown", "countryCode": "US", "stateCode": "IL", "latitude": "39.85393000", "longitude": "-89.08397000"}, {"name": "Hartford", "countryCode": "US", "stateCode": "IL", "latitude": "38.83338000", "longitude": "-90.09594000"}, {"name": "Harvard", "countryCode": "US", "stateCode": "IL", "latitude": "42.42224000", "longitude": "-88.61371000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.61003000", "longitude": "-87.64671000"}, {"name": "Harwood Heights", "countryCode": "US", "stateCode": "IL", "latitude": "41.96725000", "longitude": "-87.80756000"}, {"name": "Havana", "countryCode": "US", "stateCode": "IL", "latitude": "40.30004000", "longitude": "-90.06095000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.21697000", "longitude": "-88.04952000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.57170000", "longitude": "-87.69449000"}, {"name": "Hebron", "countryCode": "US", "stateCode": "IL", "latitude": "42.47169000", "longitude": "-88.43232000"}, {"name": "Henderson County", "countryCode": "US", "stateCode": "IL", "latitude": "40.81812000", "longitude": "-90.92511000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.25420000", "longitude": "-89.34231000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.11142000", "longitude": "-89.35648000"}, {"name": "Henry County", "countryCode": "US", "stateCode": "IL", "latitude": "41.35313000", "longitude": "-90.13142000"}, {"name": "Heritage Lake", "countryCode": "US", "stateCode": "IL", "latitude": "40.54745000", "longitude": "-89.32581000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "37.80311000", "longitude": "-89.02757000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.04920000", "longitude": "-88.09783000"}, {"name": "Heyworth", "countryCode": "US", "stateCode": "IL", "latitude": "40.31337000", "longitude": "-88.97369000"}, {"name": "Hickory Hills", "countryCode": "US", "stateCode": "IL", "latitude": "41.72559000", "longitude": "-87.82506000"}, {"name": "Highland", "countryCode": "US", "stateCode": "IL", "latitude": "38.73949000", "longitude": "-89.67120000"}, {"name": "Highland Park", "countryCode": "US", "stateCode": "IL", "latitude": "42.18169000", "longitude": "-87.80034000"}, {"name": "Highwood", "countryCode": "US", "stateCode": "IL", "latitude": "42.19975000", "longitude": "-87.80923000"}, {"name": "Hillcrest", "countryCode": "US", "stateCode": "IL", "latitude": "41.95114000", "longitude": "-89.06454000"}, {"name": "Hillsboro", "countryCode": "US", "stateCode": "IL", "latitude": "39.16128000", "longitude": "-89.49540000"}, {"name": "Hillside", "countryCode": "US", "stateCode": "IL", "latitude": "41.87781000", "longitude": "-87.90284000"}, {"name": "Hinckley", "countryCode": "US", "stateCode": "IL", "latitude": "41.76892000", "longitude": "-88.64091000"}, {"name": "Hinsdale", "countryCode": "US", "stateCode": "IL", "latitude": "41.80086000", "longitude": "-87.93701000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.76892000", "longitude": "-87.85783000"}, {"name": "Hoffman Estates", "countryCode": "US", "stateCode": "IL", "latitude": "42.04281000", "longitude": "-88.07980000"}, {"name": "Holiday Shores", "countryCode": "US", "stateCode": "IL", "latitude": "38.92199000", "longitude": "-89.94066000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.03476000", "longitude": "-87.95809000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.60003000", "longitude": "-87.93811000"}, {"name": "Hometown", "countryCode": "US", "stateCode": "IL", "latitude": "41.73448000", "longitude": "-87.73144000"}, {"name": "Homewood", "countryCode": "US", "stateCode": "IL", "latitude": "41.55726000", "longitude": "-87.66560000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.46726000", "longitude": "-87.66836000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.60587000", "longitude": "-88.98730000"}, {"name": "Huntley", "countryCode": "US", "stateCode": "IL", "latitude": "42.16808000", "longitude": "-88.42814000"}, {"name": "Hyde Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.79420000", "longitude": "-87.59394000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.15116000", "longitude": "-88.90396000"}, {"name": "Indian Head Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.77031000", "longitude": "-87.90228000"}, {"name": "Ingalls Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.52253000", "longitude": "-88.04283000"}, {"name": "Inverness", "countryCode": "US", "stateCode": "IL", "latitude": "42.11808000", "longitude": "-88.09619000"}, {"name": "Iroquois County", "countryCode": "US", "stateCode": "IL", "latitude": "40.74723000", "longitude": "-87.82430000"}, {"name": "Irving Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.95336000", "longitude": "-87.73645000"}, {"name": "Island Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.27613000", "longitude": "-88.19203000"}, {"name": "Itasca", "countryCode": "US", "stateCode": "IL", "latitude": "41.97503000", "longitude": "-88.00729000"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "IL", "latitude": "37.78514000", "longitude": "-89.38212000"}, {"name": "Jacksonville", "countryCode": "US", "stateCode": "IL", "latitude": "39.73394000", "longitude": "-90.22901000"}, {"name": "Jasper County", "countryCode": "US", "stateCode": "IL", "latitude": "39.01003000", "longitude": "-88.15381000"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "IL", "latitude": "38.30052000", "longitude": "-88.92401000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.76755000", "longitude": "-89.68066000"}, {"name": "Jersey County", "countryCode": "US", "stateCode": "IL", "latitude": "39.08566000", "longitude": "-90.35668000"}, {"name": "Jerseyville", "countryCode": "US", "stateCode": "IL", "latitude": "39.12005000", "longitude": "-90.32845000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.36571000", "longitude": "-90.21241000"}, {"name": "Johnsburg", "countryCode": "US", "stateCode": "IL", "latitude": "42.38002000", "longitude": "-88.24203000"}, {"name": "Johnson County", "countryCode": "US", "stateCode": "IL", "latitude": "37.45963000", "longitude": "-88.88089000"}, {"name": "Johnston City", "countryCode": "US", "stateCode": "IL", "latitude": "37.82061000", "longitude": "-88.92757000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.52519000", "longitude": "-88.08340000"}, {"name": "Jonesboro", "countryCode": "US", "stateCode": "IL", "latitude": "37.45172000", "longitude": "-89.26814000"}, {"name": "Justice", "countryCode": "US", "stateCode": "IL", "latitude": "41.74448000", "longitude": "-87.83783000"}, {"name": "Kane County", "countryCode": "US", "stateCode": "IL", "latitude": "41.93894000", "longitude": "-88.42866000"}, {"name": "Kankakee", "countryCode": "US", "stateCode": "IL", "latitude": "41.12003000", "longitude": "-87.86115000"}, {"name": "Kankakee County", "countryCode": "US", "stateCode": "IL", "latitude": "41.13770000", "longitude": "-87.86180000"}, {"name": "Kendall County", "countryCode": "US", "stateCode": "IL", "latitude": "41.59056000", "longitude": "-88.42885000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.08586000", "longitude": "-87.71756000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.80920000", "longitude": "-87.59755000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.24559000", "longitude": "-89.92483000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.17058000", "longitude": "-88.04785000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.58866000", "longitude": "-89.41454000"}, {"name": "Kingston", "countryCode": "US", "stateCode": "IL", "latitude": "42.09975000", "longitude": "-88.75898000"}, {"name": "Kirkland", "countryCode": "US", "stateCode": "IL", "latitude": "42.09253000", "longitude": "-88.85121000"}, {"name": "K<PERSON>llwood", "countryCode": "US", "stateCode": "IL", "latitude": "42.28613000", "longitude": "-87.88563000"}, {"name": "Knox County", "countryCode": "US", "stateCode": "IL", "latitude": "40.93182000", "longitude": "-90.21326000"}, {"name": "Knoxville", "countryCode": "US", "stateCode": "IL", "latitude": "40.90837000", "longitude": "-90.28485000"}, {"name": "La Grange", "countryCode": "US", "stateCode": "IL", "latitude": "41.80503000", "longitude": "-87.86923000"}, {"name": "La Grange Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.83475000", "longitude": "-87.86173000"}, {"name": "La Harpe", "countryCode": "US", "stateCode": "IL", "latitude": "40.58337000", "longitude": "-90.96931000"}, {"name": "La Salle", "countryCode": "US", "stateCode": "IL", "latitude": "41.33337000", "longitude": "-89.09175000"}, {"name": "LaSalle County", "countryCode": "US", "stateCode": "IL", "latitude": "41.34399000", "longitude": "-88.88595000"}, {"name": "Lacon", "countryCode": "US", "stateCode": "IL", "latitude": "41.02476000", "longitude": "-89.41120000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.38253000", "longitude": "-89.21897000"}, {"name": "Lake Barrington", "countryCode": "US", "stateCode": "IL", "latitude": "42.21252000", "longitude": "-88.15258000"}, {"name": "Lake Bluff", "countryCode": "US", "stateCode": "IL", "latitude": "42.27891000", "longitude": "-87.83424000"}, {"name": "Lake Camelot", "countryCode": "US", "stateCode": "IL", "latitude": "40.63065000", "longitude": "-89.74210000"}, {"name": "Lake Catherine", "countryCode": "US", "stateCode": "IL", "latitude": "42.47919000", "longitude": "-88.13342000"}, {"name": "Lake County", "countryCode": "US", "stateCode": "IL", "latitude": "42.34941000", "longitude": "-87.86179000"}, {"name": "Lake Forest", "countryCode": "US", "stateCode": "IL", "latitude": "42.25863000", "longitude": "-87.84063000"}, {"name": "Lake Holiday", "countryCode": "US", "stateCode": "IL", "latitude": "41.61292000", "longitude": "-88.67209000"}, {"name": "Lake Summerset", "countryCode": "US", "stateCode": "IL", "latitude": "42.45446000", "longitude": "-89.38956000"}, {"name": "Lake Villa", "countryCode": "US", "stateCode": "IL", "latitude": "42.41697000", "longitude": "-88.07397000"}, {"name": "Lake Zurich", "countryCode": "US", "stateCode": "IL", "latitude": "42.19697000", "longitude": "-88.09341000"}, {"name": "Lake in the Hills", "countryCode": "US", "stateCode": "IL", "latitude": "42.18169000", "longitude": "-88.33036000"}, {"name": "Lake of the Woods", "countryCode": "US", "stateCode": "IL", "latitude": "40.20642000", "longitude": "-88.36867000"}, {"name": "Lakemoor", "countryCode": "US", "stateCode": "IL", "latitude": "42.32863000", "longitude": "-88.19897000"}, {"name": "Lakewood", "countryCode": "US", "stateCode": "IL", "latitude": "42.22919000", "longitude": "-88.********"}, {"name": "Lakewood Shores", "countryCode": "US", "stateCode": "IL", "latitude": "41.28170000", "longitude": "-88.14478000"}, {"name": "Lanark", "countryCode": "US", "stateCode": "IL", "latitude": "42.10225000", "longitude": "-89.83345000"}, {"name": "Lansing", "countryCode": "US", "stateCode": "IL", "latitude": "41.56476000", "longitude": "-87.53893000"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "IL", "latitude": "38.71995000", "longitude": "-87.72673000"}, {"name": "Lawrenceville", "countryCode": "US", "stateCode": "IL", "latitude": "38.72921000", "longitude": "-87.68169000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.35198000", "longitude": "-88.76424000"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "IL", "latitude": "38.60394000", "longitude": "-89.80732000"}, {"name": "Lee County", "countryCode": "US", "stateCode": "IL", "latitude": "41.74619000", "longitude": "-89.30039000"}, {"name": "Leland Grove", "countryCode": "US", "stateCode": "IL", "latitude": "39.77700000", "longitude": "-89.67927000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.67364000", "longitude": "-88.00173000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.37946000", "longitude": "-89.82234000"}, {"name": "Lewistown", "countryCode": "US", "stateCode": "IL", "latitude": "40.39310000", "longitude": "-90.15484000"}, {"name": "Lexington", "countryCode": "US", "stateCode": "IL", "latitude": "40.64142000", "longitude": "-88.78340000"}, {"name": "Libertyville", "countryCode": "US", "stateCode": "IL", "latitude": "42.28308000", "longitude": "-87.95313000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.94892000", "longitude": "-88.47786000"}, {"name": "Limestone", "countryCode": "US", "stateCode": "IL", "latitude": "41.13237000", "longitude": "-87.96840000"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "IL", "latitude": "40.14838000", "longitude": "-89.36482000"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.92170000", "longitude": "-87.64783000"}, {"name": "Lincoln Square", "countryCode": "US", "stateCode": "IL", "latitude": "41.97587000", "longitude": "-87.68922000"}, {"name": "Lincolnshire", "countryCode": "US", "stateCode": "IL", "latitude": "42.19002000", "longitude": "-87.90840000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.00448000", "longitude": "-87.73006000"}, {"name": "Lindenhurst", "countryCode": "US", "stateCode": "IL", "latitude": "42.41058000", "longitude": "-88.02619000"}, {"name": "Lisle", "countryCode": "US", "stateCode": "IL", "latitude": "41.80114000", "longitude": "-88.07479000"}, {"name": "Litchfield", "countryCode": "US", "stateCode": "IL", "latitude": "39.17533000", "longitude": "-89.65426000"}, {"name": "Livingston County", "countryCode": "US", "stateCode": "IL", "latitude": "40.89156000", "longitude": "-88.55772000"}, {"name": "Lockport", "countryCode": "US", "stateCode": "IL", "latitude": "41.58948000", "longitude": "-88.05784000"}, {"name": "Logan County", "countryCode": "US", "stateCode": "IL", "latitude": "40.12456000", "longitude": "-89.36755000"}, {"name": "Logan Square", "countryCode": "US", "stateCode": "IL", "latitude": "41.92337000", "longitude": "-87.69922000"}, {"name": "Lombard", "countryCode": "US", "stateCode": "IL", "latitude": "41.88003000", "longitude": "-88.00784000"}, {"name": "Long Creek", "countryCode": "US", "stateCode": "IL", "latitude": "39.81198000", "longitude": "-88.84757000"}, {"name": "Long Grove", "countryCode": "US", "stateCode": "IL", "latitude": "42.17836000", "longitude": "-87.99785000"}, {"name": "Long Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.37085000", "longitude": "-88.12758000"}, {"name": "Louisville", "countryCode": "US", "stateCode": "IL", "latitude": "38.77227000", "longitude": "-88.50255000"}, {"name": "Loves Park", "countryCode": "US", "stateCode": "IL", "latitude": "42.32002000", "longitude": "-89.05816000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.71559000", "longitude": "-88.63256000"}, {"name": "Lower West Side", "countryCode": "US", "stateCode": "IL", "latitude": "41.85420000", "longitude": "-87.66561000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.52642000", "longitude": "-87.53865000"}, {"name": "Lyons", "countryCode": "US", "stateCode": "IL", "latitude": "41.81337000", "longitude": "-87.81811000"}, {"name": "Machesney Park", "countryCode": "US", "stateCode": "IL", "latitude": "42.34724000", "longitude": "-89.03900000"}, {"name": "Mackinaw", "countryCode": "US", "stateCode": "IL", "latitude": "40.53698000", "longitude": "-89.35759000"}, {"name": "Macomb", "countryCode": "US", "stateCode": "IL", "latitude": "40.45921000", "longitude": "-90.67180000"}, {"name": "Macon", "countryCode": "US", "stateCode": "IL", "latitude": "39.71282000", "longitude": "-88.99702000"}, {"name": "Macon County", "countryCode": "US", "stateCode": "IL", "latitude": "39.86000000", "longitude": "-88.96160000"}, {"name": "Macoupin County", "countryCode": "US", "stateCode": "IL", "latitude": "39.26102000", "longitude": "-89.92443000"}, {"name": "Madison", "countryCode": "US", "stateCode": "IL", "latitude": "38.68255000", "longitude": "-90.15705000"}, {"name": "Madison County", "countryCode": "US", "stateCode": "IL", "latitude": "38.82985000", "longitude": "-89.90517000"}, {"name": "Mahomet", "countryCode": "US", "stateCode": "IL", "latitude": "40.19531000", "longitude": "-88.40422000"}, {"name": "Malta", "countryCode": "US", "stateCode": "IL", "latitude": "41.92975000", "longitude": "-88.86092000"}, {"name": "Manhattan", "countryCode": "US", "stateCode": "IL", "latitude": "41.42253000", "longitude": "-87.98589000"}, {"name": "Manito", "countryCode": "US", "stateCode": "IL", "latitude": "40.42587000", "longitude": "-89.77928000"}, {"name": "Manteno", "countryCode": "US", "stateCode": "IL", "latitude": "41.25059000", "longitude": "-87.83143000"}, {"name": "Maple Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.90753000", "longitude": "-88.59925000"}, {"name": "Marengo", "countryCode": "US", "stateCode": "IL", "latitude": "42.24863000", "longitude": "-88.60843000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "37.73061000", "longitude": "-88.93313000"}, {"name": "Marion County", "countryCode": "US", "stateCode": "IL", "latitude": "38.64959000", "longitude": "-88.91897000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.25005000", "longitude": "-89.75010000"}, {"name": "Markham", "countryCode": "US", "stateCode": "IL", "latitude": "41.59365000", "longitude": "-87.69477000"}, {"name": "Maroa", "countryCode": "US", "stateCode": "IL", "latitude": "40.03643000", "longitude": "-88.95703000"}, {"name": "Marquette Heights", "countryCode": "US", "stateCode": "IL", "latitude": "40.61754000", "longitude": "-89.60038000"}, {"name": "Marseilles", "countryCode": "US", "stateCode": "IL", "latitude": "41.33087000", "longitude": "-88.70813000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.39143000", "longitude": "-87.69364000"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "IL", "latitude": "41.03317000", "longitude": "-89.34478000"}, {"name": "Martinsville", "countryCode": "US", "stateCode": "IL", "latitude": "39.33559000", "longitude": "-87.88198000"}, {"name": "Maryville", "countryCode": "US", "stateCode": "IL", "latitude": "38.72366000", "longitude": "-89.95593000"}, {"name": "Mascoutah", "countryCode": "US", "stateCode": "IL", "latitude": "38.49033000", "longitude": "-89.79315000"}, {"name": "Mason City", "countryCode": "US", "stateCode": "IL", "latitude": "40.20227000", "longitude": "-89.69816000"}, {"name": "Mason County", "countryCode": "US", "stateCode": "IL", "latitude": "40.23965000", "longitude": "-89.91678000"}, {"name": "Massac County", "countryCode": "US", "stateCode": "IL", "latitude": "37.21903000", "longitude": "-88.70774000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.50392000", "longitude": "-87.71310000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.48309000", "longitude": "-88.37283000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.87920000", "longitude": "-87.84312000"}, {"name": "McCullom Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.36835000", "longitude": "-88.29259000"}, {"name": "McDonough County", "countryCode": "US", "stateCode": "IL", "latitude": "40.45621000", "longitude": "-90.67791000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.33335000", "longitude": "-88.26675000"}, {"name": "McHenry County", "countryCode": "US", "stateCode": "IL", "latitude": "42.32439000", "longitude": "-88.45245000"}, {"name": "McKinley Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.83170000", "longitude": "-87.67366000"}, {"name": "McLean County", "countryCode": "US", "stateCode": "IL", "latitude": "40.49089000", "longitude": "-88.84732000"}, {"name": "McLeansboro", "countryCode": "US", "stateCode": "IL", "latitude": "38.09338000", "longitude": "-88.53561000"}, {"name": "Melrose Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.90059000", "longitude": "-87.85673000"}, {"name": "Menard County", "countryCode": "US", "stateCode": "IL", "latitude": "40.02740000", "longitude": "-89.80217000"}, {"name": "Mendota", "countryCode": "US", "stateCode": "IL", "latitude": "41.54725000", "longitude": "-89.11759000"}, {"name": "Mercer County", "countryCode": "US", "stateCode": "IL", "latitude": "41.20534000", "longitude": "-90.74141000"}, {"name": "Meredosia", "countryCode": "US", "stateCode": "IL", "latitude": "39.83116000", "longitude": "-90.55957000"}, {"name": "Merrionette Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.68420000", "longitude": "-87.70033000"}, {"name": "Metamora", "countryCode": "US", "stateCode": "IL", "latitude": "40.79059000", "longitude": "-89.36064000"}, {"name": "Metropolis", "countryCode": "US", "stateCode": "IL", "latitude": "37.15117000", "longitude": "-88.73200000"}, {"name": "Midlothian", "countryCode": "US", "stateCode": "IL", "latitude": "41.62531000", "longitude": "-87.71755000"}, {"name": "Milan", "countryCode": "US", "stateCode": "IL", "latitude": "41.45309000", "longitude": "-90.57208000"}, {"name": "Milford", "countryCode": "US", "stateCode": "IL", "latitude": "40.62837000", "longitude": "-87.69614000"}, {"name": "Millstadt", "countryCode": "US", "stateCode": "IL", "latitude": "38.46144000", "longitude": "-90.09178000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.43365000", "longitude": "-89.31315000"}, {"name": "Minonk", "countryCode": "US", "stateCode": "IL", "latitude": "40.90448000", "longitude": "-89.03452000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.45531000", "longitude": "-88.26173000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.76199000", "longitude": "-90.08538000"}, {"name": "Moken<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.52614000", "longitude": "-87.88922000"}, {"name": "Moline", "countryCode": "US", "stateCode": "IL", "latitude": "41.50670000", "longitude": "-90.51513000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.16670000", "longitude": "-87.66281000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.42003000", "longitude": "-87.74171000"}, {"name": "Monmouth", "countryCode": "US", "stateCode": "IL", "latitude": "40.91143000", "longitude": "-90.64736000"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "IL", "latitude": "38.27865000", "longitude": "-90.17738000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.73058000", "longitude": "-88.34590000"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "IL", "latitude": "39.23104000", "longitude": "-89.47887000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.02781000", "longitude": "-88.57340000"}, {"name": "Morgan County", "countryCode": "US", "stateCode": "IL", "latitude": "39.71556000", "longitude": "-90.20150000"}, {"name": "Morgan Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.69031000", "longitude": "-87.66672000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.35725000", "longitude": "-88.42118000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.80975000", "longitude": "-89.96512000"}, {"name": "Morrisonville", "countryCode": "US", "stateCode": "IL", "latitude": "39.42005000", "longitude": "-89.45565000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.61282000", "longitude": "-89.45926000"}, {"name": "<PERSON> Grove", "countryCode": "US", "stateCode": "IL", "latitude": "42.04059000", "longitude": "-87.78256000"}, {"name": "Moultrie County", "countryCode": "US", "stateCode": "IL", "latitude": "39.64148000", "longitude": "-88.61930000"}, {"name": "Mound City", "countryCode": "US", "stateCode": "IL", "latitude": "37.08533000", "longitude": "-89.16257000"}, {"name": "Mount Carmel", "countryCode": "US", "stateCode": "IL", "latitude": "38.41088000", "longitude": "-87.76142000"}, {"name": "Mount Carroll", "countryCode": "US", "stateCode": "IL", "latitude": "42.09502000", "longitude": "-89.97818000"}, {"name": "Mount Greenwood", "countryCode": "US", "stateCode": "IL", "latitude": "41.69809000", "longitude": "-87.70866000"}, {"name": "Mount Morris", "countryCode": "US", "stateCode": "IL", "latitude": "42.05031000", "longitude": "-89.43122000"}, {"name": "Mount Olive", "countryCode": "US", "stateCode": "IL", "latitude": "39.07227000", "longitude": "-89.72731000"}, {"name": "Mount Prospect", "countryCode": "US", "stateCode": "IL", "latitude": "42.06642000", "longitude": "-87.93729000"}, {"name": "Mount Pulaski", "countryCode": "US", "stateCode": "IL", "latitude": "40.01088000", "longitude": "-89.28231000"}, {"name": "Mount Sterling", "countryCode": "US", "stateCode": "IL", "latitude": "39.98727000", "longitude": "-90.76346000"}, {"name": "Mount Vernon", "countryCode": "US", "stateCode": "IL", "latitude": "38.31727000", "longitude": "-88.90312000"}, {"name": "Mount Zion", "countryCode": "US", "stateCode": "IL", "latitude": "39.77143000", "longitude": "-88.87424000"}, {"name": "Moweaqua", "countryCode": "US", "stateCode": "IL", "latitude": "39.62476000", "longitude": "-89.01897000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.26308000", "longitude": "-88.00397000"}, {"name": "Murphysboro", "countryCode": "US", "stateCode": "IL", "latitude": "37.76450000", "longitude": "-89.33509000"}, {"name": "Naperville", "countryCode": "US", "stateCode": "IL", "latitude": "41.78586000", "longitude": "-88.14729000"}, {"name": "Nashville", "countryCode": "US", "stateCode": "IL", "latitude": "38.34366000", "longitude": "-89.38064000"}, {"name": "Nauvoo", "countryCode": "US", "stateCode": "IL", "latitude": "40.55004000", "longitude": "-91.38487000"}, {"name": "Near North Side", "countryCode": "US", "stateCode": "IL", "latitude": "41.90003000", "longitude": "-87.63450000"}, {"name": "Near South Side", "countryCode": "US", "stateCode": "IL", "latitude": "41.85670000", "longitude": "-87.62477000"}, {"name": "Neoga", "countryCode": "US", "stateCode": "IL", "latitude": "39.31948000", "longitude": "-88.45283000"}, {"name": "New Athens", "countryCode": "US", "stateCode": "IL", "latitude": "38.32644000", "longitude": "-89.87705000"}, {"name": "New Baden", "countryCode": "US", "stateCode": "IL", "latitude": "38.53505000", "longitude": "-89.70065000"}, {"name": "New Berlin", "countryCode": "US", "stateCode": "IL", "latitude": "39.72533000", "longitude": "-89.91066000"}, {"name": "New City", "countryCode": "US", "stateCode": "IL", "latitude": "41.80753000", "longitude": "-87.65644000"}, {"name": "New Lenox", "countryCode": "US", "stateCode": "IL", "latitude": "41.51198000", "longitude": "-87.96561000"}, {"name": "Newark", "countryCode": "US", "stateCode": "IL", "latitude": "41.53697000", "longitude": "-88.58341000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.99088000", "longitude": "-88.16254000"}, {"name": "Niles", "countryCode": "US", "stateCode": "IL", "latitude": "42.01892000", "longitude": "-87.80284000"}, {"name": "Nokomis", "countryCode": "US", "stateCode": "IL", "latitude": "39.30116000", "longitude": "-89.28508000"}, {"name": "Normal", "countryCode": "US", "stateCode": "IL", "latitude": "40.51420000", "longitude": "-88.99063000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.96336000", "longitude": "-87.82728000"}, {"name": "Norris City", "countryCode": "US", "stateCode": "IL", "latitude": "37.98116000", "longitude": "-88.32921000"}, {"name": "North Aurora", "countryCode": "US", "stateCode": "IL", "latitude": "41.80614000", "longitude": "-88.32730000"}, {"name": "North Barrington", "countryCode": "US", "stateCode": "IL", "latitude": "42.20780000", "longitude": "-88.14063000"}, {"name": "North Center", "countryCode": "US", "stateCode": "IL", "latitude": "41.95392000", "longitude": "-87.67895000"}, {"name": "North Chicago", "countryCode": "US", "stateCode": "IL", "latitude": "42.32558000", "longitude": "-87.84118000"}, {"name": "North Lawndale", "countryCode": "US", "stateCode": "IL", "latitude": "41.86003000", "longitude": "-87.71839000"}, {"name": "North Pekin", "countryCode": "US", "stateCode": "IL", "latitude": "40.61504000", "longitude": "-89.62232000"}, {"name": "North Peoria", "countryCode": "US", "stateCode": "IL", "latitude": "40.71754000", "longitude": "-89.58426000"}, {"name": "North Riverside", "countryCode": "US", "stateCode": "IL", "latitude": "41.84281000", "longitude": "-87.82311000"}, {"name": "Northbrook", "countryCode": "US", "stateCode": "IL", "latitude": "42.12753000", "longitude": "-87.82895000"}, {"name": "Northfield", "countryCode": "US", "stateCode": "IL", "latitude": "42.09975000", "longitude": "-87.78090000"}, {"name": "Northlake", "countryCode": "US", "stateCode": "IL", "latitude": "41.91725000", "longitude": "-87.89562000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.59227000", "longitude": "-89.91121000"}, {"name": "Oak Brook", "countryCode": "US", "stateCode": "IL", "latitude": "41.83281000", "longitude": "-87.92895000"}, {"name": "Oak Forest", "countryCode": "US", "stateCode": "IL", "latitude": "41.60281000", "longitude": "-87.74394000"}, {"name": "Oak Lawn", "countryCode": "US", "stateCode": "IL", "latitude": "41.71087000", "longitude": "-87.75811000"}, {"name": "Oak Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.88503000", "longitude": "-87.78450000"}, {"name": "Oakbrook Terrace", "countryCode": "US", "stateCode": "IL", "latitude": "41.85003000", "longitude": "-87.96451000"}, {"name": "Oakwood", "countryCode": "US", "stateCode": "IL", "latitude": "40.11615000", "longitude": "-87.77836000"}, {"name": "Oakwood Hills", "countryCode": "US", "stateCode": "IL", "latitude": "42.24641000", "longitude": "-88.24286000"}, {"name": "Oblong", "countryCode": "US", "stateCode": "IL", "latitude": "39.00199000", "longitude": "-87.90892000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.61727000", "longitude": "-89.05229000"}, {"name": "Ogle County", "countryCode": "US", "stateCode": "IL", "latitude": "42.04264000", "longitude": "-89.32065000"}, {"name": "Oglesby", "countryCode": "US", "stateCode": "IL", "latitude": "41.29531000", "longitude": "-89.05953000"}, {"name": "Okawville", "countryCode": "US", "stateCode": "IL", "latitude": "38.43422000", "longitude": "-89.55037000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.73088000", "longitude": "-88.08532000"}, {"name": "Olympia Fields", "countryCode": "US", "stateCode": "IL", "latitude": "41.51337000", "longitude": "-87.67421000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.71504000", "longitude": "-88.00615000"}, {"name": "Oquawka", "countryCode": "US", "stateCode": "IL", "latitude": "40.93198000", "longitude": "-90.94709000"}, {"name": "Oregon", "countryCode": "US", "stateCode": "IL", "latitude": "42.01475000", "longitude": "-89.33233000"}, {"name": "Orion", "countryCode": "US", "stateCode": "IL", "latitude": "41.35476000", "longitude": "-90.38152000"}, {"name": "Orland Hills", "countryCode": "US", "stateCode": "IL", "latitude": "41.58531000", "longitude": "-87.84311000"}, {"name": "Orland Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.63031000", "longitude": "-87.85394000"}, {"name": "Oswego", "countryCode": "US", "stateCode": "IL", "latitude": "41.68281000", "longitude": "-88.35146000"}, {"name": "Ottawa", "countryCode": "US", "stateCode": "IL", "latitude": "41.34559000", "longitude": "-88.84258000"}, {"name": "Palatine", "countryCode": "US", "stateCode": "IL", "latitude": "42.11030000", "longitude": "-88.03424000"}, {"name": "Palestine", "countryCode": "US", "stateCode": "IL", "latitude": "39.00365000", "longitude": "-87.61280000"}, {"name": "Palos Heights", "countryCode": "US", "stateCode": "IL", "latitude": "41.66809000", "longitude": "-87.79644000"}, {"name": "Palos Hills", "countryCode": "US", "stateCode": "IL", "latitude": "41.69670000", "longitude": "-87.81700000"}, {"name": "Palos Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.66725000", "longitude": "-87.83033000"}, {"name": "Pan<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.38893000", "longitude": "-89.08008000"}, {"name": "Paris", "countryCode": "US", "stateCode": "IL", "latitude": "39.61115000", "longitude": "-87.69614000"}, {"name": "Park City", "countryCode": "US", "stateCode": "IL", "latitude": "42.34836000", "longitude": "-87.88424000"}, {"name": "Park Forest", "countryCode": "US", "stateCode": "IL", "latitude": "41.49142000", "longitude": "-87.67449000"}, {"name": "Park Ridge", "countryCode": "US", "stateCode": "IL", "latitude": "42.01114000", "longitude": "-87.84062000"}, {"name": "Pawnee", "countryCode": "US", "stateCode": "IL", "latitude": "39.59172000", "longitude": "-89.58037000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.46031000", "longitude": "-88.09532000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.81699000", "longitude": "-91.24237000"}, {"name": "Pecatonica", "countryCode": "US", "stateCode": "IL", "latitude": "42.31391000", "longitude": "-89.35928000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.56754000", "longitude": "-89.64066000"}, {"name": "Peoria", "countryCode": "US", "stateCode": "IL", "latitude": "40.69365000", "longitude": "-89.58899000"}, {"name": "Peoria County", "countryCode": "US", "stateCode": "IL", "latitude": "40.78808000", "longitude": "-89.75999000"}, {"name": "Peoria Heights", "countryCode": "US", "stateCode": "IL", "latitude": "40.74726000", "longitude": "-89.57398000"}, {"name": "Peotone", "countryCode": "US", "stateCode": "IL", "latitude": "41.33226000", "longitude": "-87.78532000"}, {"name": "Perry County", "countryCode": "US", "stateCode": "IL", "latitude": "38.08376000", "longitude": "-89.36702000"}, {"name": "Peru", "countryCode": "US", "stateCode": "IL", "latitude": "41.32753000", "longitude": "-89.12897000"}, {"name": "Petersburg", "countryCode": "US", "stateCode": "IL", "latitude": "40.01172000", "longitude": "-89.84817000"}, {"name": "Philo", "countryCode": "US", "stateCode": "IL", "latitude": "40.00698000", "longitude": "-88.15810000"}, {"name": "Phoenix", "countryCode": "US", "stateCode": "IL", "latitude": "41.61115000", "longitude": "-87.63477000"}, {"name": "Piatt County", "countryCode": "US", "stateCode": "IL", "latitude": "40.01037000", "longitude": "-88.59109000"}, {"name": "Pike County", "countryCode": "US", "stateCode": "IL", "latitude": "39.62250000", "longitude": "-90.88629000"}, {"name": "Pinckneyville", "countryCode": "US", "stateCode": "IL", "latitude": "38.08033000", "longitude": "-89.38203000"}, {"name": "Pingree Grove", "countryCode": "US", "stateCode": "IL", "latitude": "42.06864000", "longitude": "-88.41342000"}, {"name": "Pistakee Highlands", "countryCode": "US", "stateCode": "IL", "latitude": "42.40863000", "longitude": "-88.20648000"}, {"name": "Pittsfield", "countryCode": "US", "stateCode": "IL", "latitude": "39.60783000", "longitude": "-90.80513000"}, {"name": "Plainfield", "countryCode": "US", "stateCode": "IL", "latitude": "41.62697000", "longitude": "-88.20395000"}, {"name": "Plano", "countryCode": "US", "stateCode": "IL", "latitude": "41.66281000", "longitude": "-88.53702000"}, {"name": "Polo", "countryCode": "US", "stateCode": "IL", "latitude": "41.98614000", "longitude": "-89.57928000"}, {"name": "Pontiac", "countryCode": "US", "stateCode": "IL", "latitude": "40.88087000", "longitude": "-88.62978000"}, {"name": "Pontoon Beach", "countryCode": "US", "stateCode": "IL", "latitude": "38.73172000", "longitude": "-90.08038000"}, {"name": "Pope County", "countryCode": "US", "stateCode": "IL", "latitude": "37.41276000", "longitude": "-88.56158000"}, {"name": "Poplar Grove", "countryCode": "US", "stateCode": "IL", "latitude": "42.36835000", "longitude": "-88.82205000"}, {"name": "Port Barrington", "countryCode": "US", "stateCode": "IL", "latitude": "42.24252000", "longitude": "-88.20203000"}, {"name": "Port Byron", "countryCode": "US", "stateCode": "IL", "latitude": "41.60642000", "longitude": "-90.33541000"}, {"name": "Portage Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.95781000", "longitude": "-87.76506000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.63170000", "longitude": "-87.68144000"}, {"name": "Prairie Grove", "countryCode": "US", "stateCode": "IL", "latitude": "42.27863000", "longitude": "-88.26092000"}, {"name": "Prestbury", "countryCode": "US", "stateCode": "IL", "latitude": "41.78329000", "longitude": "-88.41764000"}, {"name": "Preston Heights", "countryCode": "US", "stateCode": "IL", "latitude": "41.49170000", "longitude": "-88.08172000"}, {"name": "Princeton", "countryCode": "US", "stateCode": "IL", "latitude": "41.36809000", "longitude": "-89.46481000"}, {"name": "Princeville", "countryCode": "US", "stateCode": "IL", "latitude": "40.92976000", "longitude": "-89.75760000"}, {"name": "Prophetstown", "countryCode": "US", "stateCode": "IL", "latitude": "41.67142000", "longitude": "-89.93622000"}, {"name": "Prospect Heights", "countryCode": "US", "stateCode": "IL", "latitude": "42.09530000", "longitude": "-87.93757000"}, {"name": "Pulaski County", "countryCode": "US", "stateCode": "IL", "latitude": "37.22291000", "longitude": "-89.12657000"}, {"name": "Putnam County", "countryCode": "US", "stateCode": "IL", "latitude": "41.20447000", "longitude": "-89.28583000"}, {"name": "Quincy", "countryCode": "US", "stateCode": "IL", "latitude": "39.93560000", "longitude": "-91.40987000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.14449000", "longitude": "-89.10868000"}, {"name": "Randolph County", "countryCode": "US", "stateCode": "IL", "latitude": "38.05212000", "longitude": "-89.82531000"}, {"name": "Rantoul", "countryCode": "US", "stateCode": "IL", "latitude": "40.30837000", "longitude": "-88.15588000"}, {"name": "Red Bud", "countryCode": "US", "stateCode": "IL", "latitude": "38.21172000", "longitude": "-89.99427000"}, {"name": "Richland County", "countryCode": "US", "stateCode": "IL", "latitude": "38.71236000", "longitude": "-88.08510000"}, {"name": "Richmond", "countryCode": "US", "stateCode": "IL", "latitude": "42.47585000", "longitude": "-88.30593000"}, {"name": "Richton Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.48448000", "longitude": "-87.70338000"}, {"name": "River Forest", "countryCode": "US", "stateCode": "IL", "latitude": "41.89781000", "longitude": "-87.81395000"}, {"name": "River Grove", "countryCode": "US", "stateCode": "IL", "latitude": "41.92586000", "longitude": "-87.83589000"}, {"name": "Riverdale", "countryCode": "US", "stateCode": "IL", "latitude": "41.63337000", "longitude": "-87.63310000"}, {"name": "Riverside", "countryCode": "US", "stateCode": "IL", "latitude": "41.83503000", "longitude": "-87.82284000"}, {"name": "Riverton", "countryCode": "US", "stateCode": "IL", "latitude": "39.84422000", "longitude": "-89.53954000"}, {"name": "Riverwoods", "countryCode": "US", "stateCode": "IL", "latitude": "42.16753000", "longitude": "-87.89701000"}, {"name": "Roanoke", "countryCode": "US", "stateCode": "IL", "latitude": "40.79615000", "longitude": "-89.19730000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.64392000", "longitude": "-87.70366000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.00532000", "longitude": "-87.73919000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.92392000", "longitude": "-89.06871000"}, {"name": "Rochester", "countryCode": "US", "stateCode": "IL", "latitude": "39.74949000", "longitude": "-89.53176000"}, {"name": "Rock Falls", "countryCode": "US", "stateCode": "IL", "latitude": "41.77975000", "longitude": "-89.68900000"}, {"name": "Rock Island", "countryCode": "US", "stateCode": "IL", "latitude": "41.50948000", "longitude": "-90.57875000"}, {"name": "Rock Island County", "countryCode": "US", "stateCode": "IL", "latitude": "41.46733000", "longitude": "-90.56743000"}, {"name": "Rockdale", "countryCode": "US", "stateCode": "IL", "latitude": "41.50614000", "longitude": "-88.11450000"}, {"name": "Rockford", "countryCode": "US", "stateCode": "IL", "latitude": "42.27113000", "longitude": "-89.09400000"}, {"name": "<PERSON>ton", "countryCode": "US", "stateCode": "IL", "latitude": "42.45252000", "longitude": "-89.07233000"}, {"name": "Rogers Park", "countryCode": "US", "stateCode": "IL", "latitude": "42.00864000", "longitude": "-87.66672000"}, {"name": "Rolling Meadows", "countryCode": "US", "stateCode": "IL", "latitude": "42.08419000", "longitude": "-88.01313000"}, {"name": "Rome", "countryCode": "US", "stateCode": "IL", "latitude": "40.88309000", "longitude": "-89.50259000"}, {"name": "Romeoville", "countryCode": "US", "stateCode": "IL", "latitude": "41.64753000", "longitude": "-88.08951000"}, {"name": "Roodhouse", "countryCode": "US", "stateCode": "IL", "latitude": "39.48394000", "longitude": "-90.37151000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.41335000", "longitude": "-89.00927000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.98475000", "longitude": "-88.07979000"}, {"name": "Rosemont", "countryCode": "US", "stateCode": "IL", "latitude": "41.99531000", "longitude": "-87.88451000"}, {"name": "Rosewood Heights", "countryCode": "US", "stateCode": "IL", "latitude": "38.88783000", "longitude": "-90.08483000"}, {"name": "R<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "37.42366000", "longitude": "-88.34615000"}, {"name": "Rossville", "countryCode": "US", "stateCode": "IL", "latitude": "40.37920000", "longitude": "-87.66863000"}, {"name": "Round Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.35336000", "longitude": "-88.09341000"}, {"name": "Round Lake Beach", "countryCode": "US", "stateCode": "IL", "latitude": "42.37169000", "longitude": "-88.09008000"}, {"name": "Round Lake Heights", "countryCode": "US", "stateCode": "IL", "latitude": "42.38002000", "longitude": "-88.10425000"}, {"name": "Round Lake Park", "countryCode": "US", "stateCode": "IL", "latitude": "42.35697000", "longitude": "-88.07675000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.84838000", "longitude": "-90.07622000"}, {"name": "Royalton", "countryCode": "US", "stateCode": "IL", "latitude": "37.87699000", "longitude": "-89.11452000"}, {"name": "Rushville", "countryCode": "US", "stateCode": "IL", "latitude": "40.12116000", "longitude": "-90.56318000"}, {"name": "Saint Anne", "countryCode": "US", "stateCode": "IL", "latitude": "41.02503000", "longitude": "-87.71392000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.91419000", "longitude": "-88.30869000"}, {"name": "Saint Clair County", "countryCode": "US", "stateCode": "IL", "latitude": "38.47031000", "longitude": "-89.92841000"}, {"name": "Saint Elmo", "countryCode": "US", "stateCode": "IL", "latitude": "39.02727000", "longitude": "-88.84811000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.71394000", "longitude": "-89.76815000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.11170000", "longitude": "-88.04170000"}, {"name": "Salem", "countryCode": "US", "stateCode": "IL", "latitude": "38.62699000", "longitude": "-88.94562000"}, {"name": "Saline County", "countryCode": "US", "stateCode": "IL", "latitude": "37.75318000", "longitude": "-88.54080000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.61560000", "longitude": "-89.11423000"}, {"name": "Sandwich", "countryCode": "US", "stateCode": "IL", "latitude": "41.64586000", "longitude": "-88.62174000"}, {"name": "Sangamon County", "countryCode": "US", "stateCode": "IL", "latitude": "39.75817000", "longitude": "-89.65890000"}, {"name": "Sauk Village", "countryCode": "US", "stateCode": "IL", "latitude": "41.48837000", "longitude": "-87.56754000"}, {"name": "Savanna", "countryCode": "US", "stateCode": "IL", "latitude": "42.09447000", "longitude": "-90.15679000"}, {"name": "Savoy", "countryCode": "US", "stateCode": "IL", "latitude": "40.05475000", "longitude": "-88.25172000"}, {"name": "Schaumburg", "countryCode": "US", "stateCode": "IL", "latitude": "42.03336000", "longitude": "-88.08341000"}, {"name": "Schiller Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.95586000", "longitude": "-87.87090000"}, {"name": "Schuyler County", "countryCode": "US", "stateCode": "IL", "latitude": "40.15803000", "longitude": "-90.61507000"}, {"name": "Scott Air Force Base", "countryCode": "US", "stateCode": "IL", "latitude": "38.54270000", "longitude": "-89.85035000"}, {"name": "Scott County", "countryCode": "US", "stateCode": "IL", "latitude": "39.64414000", "longitude": "-90.47470000"}, {"name": "Seneca", "countryCode": "US", "stateCode": "IL", "latitude": "41.31114000", "longitude": "-88.60979000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.09172000", "longitude": "-89.05035000"}, {"name": "Shawneetown", "countryCode": "US", "stateCode": "IL", "latitude": "37.71310000", "longitude": "-88.18670000"}, {"name": "Shelby County", "countryCode": "US", "stateCode": "IL", "latitude": "39.39116000", "longitude": "-88.80554000"}, {"name": "Shelbyville", "countryCode": "US", "stateCode": "IL", "latitude": "39.40643000", "longitude": "-88.79007000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.76920000", "longitude": "-87.56392000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.53003000", "longitude": "-88.67980000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.89366000", "longitude": "-89.60482000"}, {"name": "Shiloh", "countryCode": "US", "stateCode": "IL", "latitude": "38.56144000", "longitude": "-89.89732000"}, {"name": "<PERSON>wood", "countryCode": "US", "stateCode": "IL", "latitude": "41.52003000", "longitude": "-88.20173000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.02503000", "longitude": "-88.07337000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.51226000", "longitude": "-90.41513000"}, {"name": "Skokie", "countryCode": "US", "stateCode": "IL", "latitude": "42.03336000", "longitude": "-87.73339000"}, {"name": "Sleepy Hollow", "countryCode": "US", "stateCode": "IL", "latitude": "42.09419000", "longitude": "-88.30258000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.40866000", "longitude": "-89.99205000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.63364000", "longitude": "-88.68119000"}, {"name": "South Barrington", "countryCode": "US", "stateCode": "IL", "latitude": "42.09142000", "longitude": "-88.12174000"}, {"name": "South Beloit", "countryCode": "US", "stateCode": "IL", "latitude": "42.49307000", "longitude": "-89.03678000"}, {"name": "South Chicago", "countryCode": "US", "stateCode": "IL", "latitude": "41.73977000", "longitude": "-87.55425000"}, {"name": "South Chicago Heights", "countryCode": "US", "stateCode": "IL", "latitude": "41.48087000", "longitude": "-87.63782000"}, {"name": "South Elgin", "countryCode": "US", "stateCode": "IL", "latitude": "41.99419000", "longitude": "-88.29230000"}, {"name": "South Holland", "countryCode": "US", "stateCode": "IL", "latitude": "41.60087000", "longitude": "-87.60699000"}, {"name": "South Jacksonville", "countryCode": "US", "stateCode": "IL", "latitude": "39.70866000", "longitude": "-90.22818000"}, {"name": "South Lawndale", "countryCode": "US", "stateCode": "IL", "latitude": "41.84364000", "longitude": "-87.71255000"}, {"name": "South Pekin", "countryCode": "US", "stateCode": "IL", "latitude": "40.49448000", "longitude": "-89.65177000"}, {"name": "South Roxana", "countryCode": "US", "stateCode": "IL", "latitude": "38.82949000", "longitude": "-90.06288000"}, {"name": "South Shore", "countryCode": "US", "stateCode": "IL", "latitude": "41.76198000", "longitude": "-87.57783000"}, {"name": "Southern View", "countryCode": "US", "stateCode": "IL", "latitude": "39.75727000", "longitude": "-89.65371000"}, {"name": "Sparta", "countryCode": "US", "stateCode": "IL", "latitude": "38.12311000", "longitude": "-89.70177000"}, {"name": "Spring Grove", "countryCode": "US", "stateCode": "IL", "latitude": "42.44363000", "longitude": "-88.23648000"}, {"name": "Spring Valley", "countryCode": "US", "stateCode": "IL", "latitude": "41.32754000", "longitude": "-89.19981000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "IL", "latitude": "39.80172000", "longitude": "-89.64371000"}, {"name": "Stark County", "countryCode": "US", "stateCode": "IL", "latitude": "41.09336000", "longitude": "-89.79749000"}, {"name": "Staunton", "countryCode": "US", "stateCode": "IL", "latitude": "39.01227000", "longitude": "-89.79121000"}, {"name": "Steeleville", "countryCode": "US", "stateCode": "IL", "latitude": "38.00727000", "longitude": "-89.65843000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.47003000", "longitude": "-87.63643000"}, {"name": "Stephenson County", "countryCode": "US", "stateCode": "IL", "latitude": "42.35175000", "longitude": "-89.66235000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.78864000", "longitude": "-89.69622000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.82142000", "longitude": "-87.78283000"}, {"name": "Stillman Valley", "countryCode": "US", "stateCode": "IL", "latitude": "42.10725000", "longitude": "-89.17927000"}, {"name": "Stockton", "countryCode": "US", "stateCode": "IL", "latitude": "42.34974000", "longitude": "-90.00679000"}, {"name": "Stone Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.90559000", "longitude": "-87.88367000"}, {"name": "Streamwood", "countryCode": "US", "stateCode": "IL", "latitude": "42.02558000", "longitude": "-88.17841000"}, {"name": "Streator", "countryCode": "US", "stateCode": "IL", "latitude": "41.12087000", "longitude": "-88.83535000"}, {"name": "Sugar Grove", "countryCode": "US", "stateCode": "IL", "latitude": "41.76142000", "longitude": "-88.44369000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "39.59948000", "longitude": "-88.60784000"}, {"name": "Summit", "countryCode": "US", "stateCode": "IL", "latitude": "41.78809000", "longitude": "-87.81033000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.71699000", "longitude": "-87.86142000"}, {"name": "Swansea", "countryCode": "US", "stateCode": "IL", "latitude": "38.53394000", "longitude": "-89.98899000"}, {"name": "Sycamore", "countryCode": "US", "stateCode": "IL", "latitude": "41.98892000", "longitude": "-88.68675000"}, {"name": "Taylorville", "countryCode": "US", "stateCode": "IL", "latitude": "39.54894000", "longitude": "-89.29453000"}, {"name": "Tazewell County", "countryCode": "US", "stateCode": "IL", "latitude": "40.50752000", "longitude": "-89.51342000"}, {"name": "Teutopolis", "countryCode": "US", "stateCode": "IL", "latitude": "39.13310000", "longitude": "-88.47199000"}, {"name": "The Galena Territory", "countryCode": "US", "stateCode": "IL", "latitude": "42.39343000", "longitude": "-90.32582000"}, {"name": "Third Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.37391000", "longitude": "-88.01091000"}, {"name": "Thomasboro", "countryCode": "US", "stateCode": "IL", "latitude": "40.24170000", "longitude": "-88.18421000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.56809000", "longitude": "-87.60810000"}, {"name": "T<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.09531000", "longitude": "-87.64752000"}, {"name": "Tinley Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.57337000", "longitude": "-87.78449000"}, {"name": "Toledo", "countryCode": "US", "stateCode": "IL", "latitude": "39.27365000", "longitude": "-88.24365000"}, {"name": "Tolono", "countryCode": "US", "stateCode": "IL", "latitude": "39.98614000", "longitude": "-88.25894000"}, {"name": "Toluca", "countryCode": "US", "stateCode": "IL", "latitude": "41.00226000", "longitude": "-89.13342000"}, {"name": "Toulon", "countryCode": "US", "stateCode": "IL", "latitude": "41.09365000", "longitude": "-89.86483000"}, {"name": "Tower Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.23197000", "longitude": "-88.15202000"}, {"name": "Tremont", "countryCode": "US", "stateCode": "IL", "latitude": "40.52754000", "longitude": "-89.49260000"}, {"name": "Trenton", "countryCode": "US", "stateCode": "IL", "latitude": "38.60560000", "longitude": "-89.68204000"}, {"name": "Troy", "countryCode": "US", "stateCode": "IL", "latitude": "38.72921000", "longitude": "-89.88315000"}, {"name": "Tuscola", "countryCode": "US", "stateCode": "IL", "latitude": "39.79920000", "longitude": "-88.28310000"}, {"name": "Twin Grove", "countryCode": "US", "stateCode": "IL", "latitude": "40.49337000", "longitude": "-89.07980000"}, {"name": "Union County", "countryCode": "US", "stateCode": "IL", "latitude": "37.47123000", "longitude": "-89.25509000"}, {"name": "University Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.44298000", "longitude": "-87.68360000"}, {"name": "Upper Alton", "countryCode": "US", "stateCode": "IL", "latitude": "38.91144000", "longitude": "-90.15066000"}, {"name": "Uptown", "countryCode": "US", "stateCode": "IL", "latitude": "41.96590000", "longitude": "-87.65262000"}, {"name": "Urbana", "countryCode": "US", "stateCode": "IL", "latitude": "40.11059000", "longitude": "-88.20727000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "38.30561000", "longitude": "-90.27651000"}, {"name": "Vandalia", "countryCode": "US", "stateCode": "IL", "latitude": "38.96060000", "longitude": "-89.09368000"}, {"name": "Venetian Village", "countryCode": "US", "stateCode": "IL", "latitude": "42.39863000", "longitude": "-88.05258000"}, {"name": "Venice", "countryCode": "US", "stateCode": "IL", "latitude": "38.67227000", "longitude": "-90.16983000"}, {"name": "Vermilion County", "countryCode": "US", "stateCode": "IL", "latitude": "40.18342000", "longitude": "-87.73283000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.21947000", "longitude": "-87.97952000"}, {"name": "Vienna", "countryCode": "US", "stateCode": "IL", "latitude": "37.41533000", "longitude": "-88.89784000"}, {"name": "Villa Grove", "countryCode": "US", "stateCode": "IL", "latitude": "39.86281000", "longitude": "-88.16227000"}, {"name": "Villa Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.88975000", "longitude": "-87.98895000"}, {"name": "Village of Campton Hills", "countryCode": "US", "stateCode": "IL", "latitude": "41.93660000", "longitude": "-88.39750000"}, {"name": "Virden", "countryCode": "US", "stateCode": "IL", "latitude": "39.50089000", "longitude": "-89.76787000"}, {"name": "Virginia", "countryCode": "US", "stateCode": "IL", "latitude": "39.95116000", "longitude": "-90.21234000"}, {"name": "Volo", "countryCode": "US", "stateCode": "IL", "latitude": "42.32613000", "longitude": "-88.16786000"}, {"name": "Wabash County", "countryCode": "US", "stateCode": "IL", "latitude": "38.44607000", "longitude": "-87.84425000"}, {"name": "Wadsworth", "countryCode": "US", "stateCode": "IL", "latitude": "42.42863000", "longitude": "-87.92397000"}, {"name": "Walnut", "countryCode": "US", "stateCode": "IL", "latitude": "41.55670000", "longitude": "-89.59343000"}, {"name": "Wamac", "countryCode": "US", "stateCode": "IL", "latitude": "38.50894000", "longitude": "-89.14063000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.49640000", "longitude": "-89.98957000"}, {"name": "Warren County", "countryCode": "US", "stateCode": "IL", "latitude": "40.84883000", "longitude": "-90.61503000"}, {"name": "Warrensburg", "countryCode": "US", "stateCode": "IL", "latitude": "39.93282000", "longitude": "-89.06203000"}, {"name": "Warrenville", "countryCode": "US", "stateCode": "IL", "latitude": "41.81781000", "longitude": "-88.17340000"}, {"name": "Warsaw", "countryCode": "US", "stateCode": "IL", "latitude": "40.35921000", "longitude": "-91.43460000"}, {"name": "Wasco", "countryCode": "US", "stateCode": "IL", "latitude": "41.93808000", "longitude": "-88.40452000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "40.91920000", "longitude": "-89.29120000"}, {"name": "Washington", "countryCode": "US", "stateCode": "IL", "latitude": "40.70365000", "longitude": "-89.40731000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "IL", "latitude": "38.35217000", "longitude": "-89.41045000"}, {"name": "Washington Park", "countryCode": "US", "stateCode": "IL", "latitude": "38.63505000", "longitude": "-90.09289000"}, {"name": "Waterloo", "countryCode": "US", "stateCode": "IL", "latitude": "38.33589000", "longitude": "-90.14983000"}, {"name": "Waterman", "countryCode": "US", "stateCode": "IL", "latitude": "41.77170000", "longitude": "-88.77369000"}, {"name": "Watseka", "countryCode": "US", "stateCode": "IL", "latitude": "40.77615000", "longitude": "-87.73642000"}, {"name": "Wauconda", "countryCode": "US", "stateCode": "IL", "latitude": "42.25891000", "longitude": "-88.13925000"}, {"name": "Waukegan", "countryCode": "US", "stateCode": "IL", "latitude": "42.36363000", "longitude": "-87.84479000"}, {"name": "Waverly", "countryCode": "US", "stateCode": "IL", "latitude": "39.59172000", "longitude": "-89.95288000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.95086000", "longitude": "-88.24230000"}, {"name": "Wayne City", "countryCode": "US", "stateCode": "IL", "latitude": "38.34533000", "longitude": "-88.58783000"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "IL", "latitude": "38.42956000", "longitude": "-88.42561000"}, {"name": "West Chicago", "countryCode": "US", "stateCode": "IL", "latitude": "41.88475000", "longitude": "-88.20396000"}, {"name": "West Dundee", "countryCode": "US", "stateCode": "IL", "latitude": "42.09808000", "longitude": "-88.28286000"}, {"name": "West Elsdon", "countryCode": "US", "stateCode": "IL", "latitude": "41.79392000", "longitude": "-87.72450000"}, {"name": "West Englewood", "countryCode": "US", "stateCode": "IL", "latitude": "41.77809000", "longitude": "-87.66672000"}, {"name": "West Frankfort", "countryCode": "US", "stateCode": "IL", "latitude": "37.89783000", "longitude": "-88.93146000"}, {"name": "West Garfield Park", "countryCode": "US", "stateCode": "IL", "latitude": "41.88059000", "longitude": "-87.72922000"}, {"name": "West Lawn", "countryCode": "US", "stateCode": "IL", "latitude": "41.77281000", "longitude": "-87.72227000"}, {"name": "West Peoria", "countryCode": "US", "stateCode": "IL", "latitude": "40.69254000", "longitude": "-89.62788000"}, {"name": "West Ridge", "countryCode": "US", "stateCode": "IL", "latitude": "41.99975000", "longitude": "-87.69284000"}, {"name": "West Town", "countryCode": "US", "stateCode": "IL", "latitude": "41.89381000", "longitude": "-87.67493000"}, {"name": "Westchester", "countryCode": "US", "stateCode": "IL", "latitude": "41.85059000", "longitude": "-87.88200000"}, {"name": "Western Springs", "countryCode": "US", "stateCode": "IL", "latitude": "41.80975000", "longitude": "-87.90062000"}, {"name": "Westmont", "countryCode": "US", "stateCode": "IL", "latitude": "41.79586000", "longitude": "-87.97562000"}, {"name": "Westville", "countryCode": "US", "stateCode": "IL", "latitude": "40.04226000", "longitude": "-87.63863000"}, {"name": "Wheaton", "countryCode": "US", "stateCode": "IL", "latitude": "41.86614000", "longitude": "-88.10701000"}, {"name": "Wheeling", "countryCode": "US", "stateCode": "IL", "latitude": "42.13919000", "longitude": "-87.92896000"}, {"name": "White County", "countryCode": "US", "stateCode": "IL", "latitude": "38.08748000", "longitude": "-88.17957000"}, {"name": "White Hall", "countryCode": "US", "stateCode": "IL", "latitude": "39.43699000", "longitude": "-90.40318000"}, {"name": "Whiteside County", "countryCode": "US", "stateCode": "IL", "latitude": "41.75626000", "longitude": "-89.91409000"}, {"name": "Will County", "countryCode": "US", "stateCode": "IL", "latitude": "41.44503000", "longitude": "-87.97866000"}, {"name": "Williamson County", "countryCode": "US", "stateCode": "IL", "latitude": "37.73025000", "longitude": "-88.92994000"}, {"name": "Williamsville", "countryCode": "US", "stateCode": "IL", "latitude": "39.95422000", "longitude": "-89.54871000"}, {"name": "Willow Springs", "countryCode": "US", "stateCode": "IL", "latitude": "41.74087000", "longitude": "-87.86033000"}, {"name": "Willowbrook", "countryCode": "US", "stateCode": "IL", "latitude": "41.76975000", "longitude": "-87.93589000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "42.07225000", "longitude": "-87.72284000"}, {"name": "Wilmington", "countryCode": "US", "stateCode": "IL", "latitude": "41.30781000", "longitude": "-88.14672000"}, {"name": "Winchester", "countryCode": "US", "stateCode": "IL", "latitude": "39.62977000", "longitude": "-90.45624000"}, {"name": "Windsor", "countryCode": "US", "stateCode": "IL", "latitude": "39.44087000", "longitude": "-88.59478000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.86170000", "longitude": "-88.16090000"}, {"name": "Winnebago", "countryCode": "US", "stateCode": "IL", "latitude": "42.26613000", "longitude": "-89.24122000"}, {"name": "Winnebago County", "countryCode": "US", "stateCode": "IL", "latitude": "42.33626000", "longitude": "-89.16085000"}, {"name": "Winnetka", "countryCode": "US", "stateCode": "IL", "latitude": "42.10808000", "longitude": "-87.73590000"}, {"name": "Winthrop Harbor", "countryCode": "US", "stateCode": "IL", "latitude": "42.47891000", "longitude": "-87.82368000"}, {"name": "Wonder Lake", "countryCode": "US", "stateCode": "IL", "latitude": "42.38530000", "longitude": "-88.34731000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.96336000", "longitude": "-87.97896000"}, {"name": "Wood River", "countryCode": "US", "stateCode": "IL", "latitude": "38.86116000", "longitude": "-90.09761000"}, {"name": "Woodford County", "countryCode": "US", "stateCode": "IL", "latitude": "40.78823000", "longitude": "-89.21114000"}, {"name": "Woodlawn", "countryCode": "US", "stateCode": "IL", "latitude": "41.77948000", "longitude": "-87.59949000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "41.74697000", "longitude": "-88.05034000"}, {"name": "Woodstock", "countryCode": "US", "stateCode": "IL", "latitude": "42.31474000", "longitude": "-88.44870000"}, {"name": "Worden", "countryCode": "US", "stateCode": "IL", "latitude": "38.93144000", "longitude": "-89.83899000"}, {"name": "Worth", "countryCode": "US", "stateCode": "IL", "latitude": "41.68975000", "longitude": "-87.79728000"}, {"name": "Wyoming", "countryCode": "US", "stateCode": "IL", "latitude": "41.06170000", "longitude": "-89.77316000"}, {"name": "Yorkville", "countryCode": "US", "stateCode": "IL", "latitude": "41.64114000", "longitude": "-88.44729000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IL", "latitude": "37.89949000", "longitude": "-89.05202000"}, {"name": "Zion", "countryCode": "US", "stateCode": "IL", "latitude": "42.44613000", "longitude": "-87.83285000"}]