[{"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.53425000", "longitude": "-70.90978000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.61841000", "longitude": "-67.74416000"}, {"name": "Albion", "countryCode": "US", "stateCode": "ME", "latitude": "44.53229000", "longitude": "-69.44254000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.47647000", "longitude": "-70.71839000"}, {"name": "Androscoggin County", "countryCode": "US", "stateCode": "ME", "latitude": "44.16585000", "longitude": "-70.20645000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.28924000", "longitude": "-69.25088000"}, {"name": "Aroostook County", "countryCode": "US", "stateCode": "ME", "latitude": "46.65881000", "longitude": "-68.59889000"}, {"name": "Arundel", "countryCode": "US", "stateCode": "ME", "latitude": "43.38259000", "longitude": "-70.47783000"}, {"name": "Auburn", "countryCode": "US", "stateCode": "ME", "latitude": "44.09785000", "longitude": "-70.23117000"}, {"name": "Augusta", "countryCode": "US", "stateCode": "ME", "latitude": "44.31062000", "longitude": "-69.77949000"}, {"name": "Bangor", "countryCode": "US", "stateCode": "ME", "latitude": "44.80118000", "longitude": "-68.77781000"}, {"name": "Bar Harbor", "countryCode": "US", "stateCode": "ME", "latitude": "44.38758000", "longitude": "-68.20390000"}, {"name": "Bath", "countryCode": "US", "stateCode": "ME", "latitude": "43.91064000", "longitude": "-69.82060000"}, {"name": "Belfast", "countryCode": "US", "stateCode": "ME", "latitude": "44.42591000", "longitude": "-69.00642000"}, {"name": "Belgrade", "countryCode": "US", "stateCode": "ME", "latitude": "44.44729000", "longitude": "-69.83255000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.58618000", "longitude": "-69.55088000"}, {"name": "Berwick", "countryCode": "US", "stateCode": "ME", "latitude": "43.26592000", "longitude": "-70.86450000"}, {"name": "Bethel", "countryCode": "US", "stateCode": "ME", "latitude": "44.40423000", "longitude": "-70.79062000"}, {"name": "Biddeford", "countryCode": "US", "stateCode": "ME", "latitude": "43.49258000", "longitude": "-70.45338000"}, {"name": "Boothbay", "countryCode": "US", "stateCode": "ME", "latitude": "43.87647000", "longitude": "-69.63366000"}, {"name": "Boothbay Harbor", "countryCode": "US", "stateCode": "ME", "latitude": "43.85230000", "longitude": "-69.62810000"}, {"name": "Bradford", "countryCode": "US", "stateCode": "ME", "latitude": "45.06673000", "longitude": "-68.93781000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.92090000", "longitude": "-68.62809000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.79674000", "longitude": "-68.76142000"}, {"name": "Bridgton", "countryCode": "US", "stateCode": "ME", "latitude": "44.05479000", "longitude": "-70.71284000"}, {"name": "Bristol", "countryCode": "US", "stateCode": "ME", "latitude": "43.95758000", "longitude": "-69.50921000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.55035000", "longitude": "-69.12087000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.93813000", "longitude": "-70.90868000"}, {"name": "Brownville", "countryCode": "US", "stateCode": "ME", "latitude": "45.30700000", "longitude": "-69.03337000"}, {"name": "Brunswick", "countryCode": "US", "stateCode": "ME", "latitude": "43.91452000", "longitude": "-69.96533000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.28951000", "longitude": "-70.36534000"}, {"name": "Bucksport", "countryCode": "US", "stateCode": "ME", "latitude": "44.57369000", "longitude": "-68.79559000"}, {"name": "Burnham", "countryCode": "US", "stateCode": "ME", "latitude": "44.69284000", "longitude": "-69.42755000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.63786000", "longitude": "-70.51894000"}, {"name": "Calais", "countryCode": "US", "stateCode": "ME", "latitude": "45.18376000", "longitude": "-67.27662000"}, {"name": "Camden", "countryCode": "US", "stateCode": "ME", "latitude": "44.20980000", "longitude": "-69.06476000"}, {"name": "Canaan", "countryCode": "US", "stateCode": "ME", "latitude": "44.76173000", "longitude": "-69.56144000"}, {"name": "Canton", "countryCode": "US", "stateCode": "ME", "latitude": "44.44080000", "longitude": "-70.31649000"}, {"name": "Cape Neddick", "countryCode": "US", "stateCode": "ME", "latitude": "43.19370000", "longitude": "-70.62089000"}, {"name": "Caribou", "countryCode": "US", "stateCode": "ME", "latitude": "46.86060000", "longitude": "-68.01197000"}, {"name": "Carmel", "countryCode": "US", "stateCode": "ME", "latitude": "44.79757000", "longitude": "-69.05115000"}, {"name": "Castine", "countryCode": "US", "stateCode": "ME", "latitude": "44.38785000", "longitude": "-68.79975000"}, {"name": "Charleston", "countryCode": "US", "stateCode": "ME", "latitude": "45.08506000", "longitude": "-69.04059000"}, {"name": "Chelsea", "countryCode": "US", "stateCode": "ME", "latitude": "44.25035000", "longitude": "-69.71727000"}, {"name": "Cherryfield", "countryCode": "US", "stateCode": "ME", "latitude": "44.60730000", "longitude": "-67.92584000"}, {"name": "Chesterville", "countryCode": "US", "stateCode": "ME", "latitude": "44.55117000", "longitude": "-70.08617000"}, {"name": "China", "countryCode": "US", "stateCode": "ME", "latitude": "44.47868000", "longitude": "-69.51726000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.48145000", "longitude": "-70.19950000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.63784000", "longitude": "-69.50310000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.92117000", "longitude": "-69.26171000"}, {"name": "Cornish", "countryCode": "US", "stateCode": "ME", "latitude": "43.80480000", "longitude": "-70.80117000"}, {"name": "Cornville", "countryCode": "US", "stateCode": "ME", "latitude": "44.83673000", "longitude": "-69.67311000"}, {"name": "Cumberland Center", "countryCode": "US", "stateCode": "ME", "latitude": "43.79647000", "longitude": "-70.25894000"}, {"name": "Cumberland County", "countryCode": "US", "stateCode": "ME", "latitude": "43.80608000", "longitude": "-70.33020000"}, {"name": "Cushing", "countryCode": "US", "stateCode": "ME", "latitude": "44.01925000", "longitude": "-69.23977000"}, {"name": "Damariscotta", "countryCode": "US", "stateCode": "ME", "latitude": "44.03286000", "longitude": "-69.51866000"}, {"name": "Dayton", "countryCode": "US", "stateCode": "ME", "latitude": "43.54972000", "longitude": "-70.57555000"}, {"name": "Dedham", "countryCode": "US", "stateCode": "ME", "latitude": "44.69174000", "longitude": "-68.66198000"}, {"name": "Deer Isle", "countryCode": "US", "stateCode": "ME", "latitude": "44.22397000", "longitude": "-68.67753000"}, {"name": "Denmark", "countryCode": "US", "stateCode": "ME", "latitude": "43.97035000", "longitude": "-70.80340000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "45.02395000", "longitude": "-69.28977000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.53395000", "longitude": "-70.45590000"}, {"name": "Dixmont", "countryCode": "US", "stateCode": "ME", "latitude": "44.68035000", "longitude": "-69.16282000"}, {"name": "Dover-<PERSON>croft", "countryCode": "US", "stateCode": "ME", "latitude": "45.18339000", "longitude": "-69.22699000"}, {"name": "East Machias", "countryCode": "US", "stateCode": "ME", "latitude": "44.73924000", "longitude": "-67.38999000"}, {"name": "East Millinocket", "countryCode": "US", "stateCode": "ME", "latitude": "45.62755000", "longitude": "-68.57448000"}, {"name": "Easton", "countryCode": "US", "stateCode": "ME", "latitude": "46.64115000", "longitude": "-67.90947000"}, {"name": "Eastport", "countryCode": "US", "stateCode": "ME", "latitude": "44.90619000", "longitude": "-66.98998000"}, {"name": "Eddington", "countryCode": "US", "stateCode": "ME", "latitude": "44.82618000", "longitude": "-68.69337000"}, {"name": "Edgecomb", "countryCode": "US", "stateCode": "ME", "latitude": "43.95841000", "longitude": "-69.63060000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.15314000", "longitude": "-70.80006000"}, {"name": "Ellsworth", "countryCode": "US", "stateCode": "ME", "latitude": "44.54341000", "longitude": "-68.41946000"}, {"name": "Enfield", "countryCode": "US", "stateCode": "ME", "latitude": "45.24894000", "longitude": "-68.56836000"}, {"name": "Etna", "countryCode": "US", "stateCode": "ME", "latitude": "44.82090000", "longitude": "-69.11115000"}, {"name": "Fairfield", "countryCode": "US", "stateCode": "ME", "latitude": "44.58840000", "longitude": "-69.59866000"}, {"name": "Falmouth", "countryCode": "US", "stateCode": "ME", "latitude": "43.72953000", "longitude": "-70.24199000"}, {"name": "Falmouth Foreside", "countryCode": "US", "stateCode": "ME", "latitude": "43.73480000", "longitude": "-70.20783000"}, {"name": "Farmingdale", "countryCode": "US", "stateCode": "ME", "latitude": "44.24451000", "longitude": "-69.77143000"}, {"name": "Farmington", "countryCode": "US", "stateCode": "ME", "latitude": "44.67062000", "longitude": "-70.15117000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.40896000", "longitude": "-70.03367000"}, {"name": "Fort Fairfield", "countryCode": "US", "stateCode": "ME", "latitude": "46.77227000", "longitude": "-67.83391000"}, {"name": "Fort Kent", "countryCode": "US", "stateCode": "ME", "latitude": "47.25865000", "longitude": "-68.58949000"}, {"name": "Frankfort", "countryCode": "US", "stateCode": "ME", "latitude": "44.60980000", "longitude": "-68.87670000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.58702000", "longitude": "-68.23224000"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "ME", "latitude": "44.97417000", "longitude": "-70.44410000"}, {"name": "Freeport", "countryCode": "US", "stateCode": "ME", "latitude": "43.85702000", "longitude": "-70.10311000"}, {"name": "Frenchville", "countryCode": "US", "stateCode": "ME", "latitude": "47.28087000", "longitude": "-68.37976000"}, {"name": "Friendship", "countryCode": "US", "stateCode": "ME", "latitude": "43.98369000", "longitude": "-69.33394000"}, {"name": "Fryeburg", "countryCode": "US", "stateCode": "ME", "latitude": "44.01646000", "longitude": "-70.98062000"}, {"name": "Gardiner", "countryCode": "US", "stateCode": "ME", "latitude": "44.23007000", "longitude": "-69.77532000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "45.03840000", "longitude": "-69.16032000"}, {"name": "Gorham", "countryCode": "US", "stateCode": "ME", "latitude": "43.67952000", "longitude": "-70.44422000"}, {"name": "Gouldsboro", "countryCode": "US", "stateCode": "ME", "latitude": "44.47841000", "longitude": "-68.03834000"}, {"name": "Greenbush", "countryCode": "US", "stateCode": "ME", "latitude": "45.08034000", "longitude": "-68.65086000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.18979000", "longitude": "-70.14033000"}, {"name": "Greenville", "countryCode": "US", "stateCode": "ME", "latitude": "45.45949000", "longitude": "-69.59061000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.28590000", "longitude": "-69.79088000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.74452000", "longitude": "-68.83698000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.52924000", "longitude": "-68.25363000"}, {"name": "Hancock County", "countryCode": "US", "stateCode": "ME", "latitude": "44.56289000", "longitude": "-68.36821000"}, {"name": "Harpswell Center", "countryCode": "US", "stateCode": "ME", "latitude": "43.80175000", "longitude": "-69.98421000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.11035000", "longitude": "-70.67923000"}, {"name": "Hartford", "countryCode": "US", "stateCode": "ME", "latitude": "44.37284000", "longitude": "-70.34673000"}, {"name": "Hebron", "countryCode": "US", "stateCode": "ME", "latitude": "44.19813000", "longitude": "-70.40645000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.81007000", "longitude": "-68.91337000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.87868000", "longitude": "-70.80340000"}, {"name": "<PERSON><PERSON>g<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "46.05394000", "longitude": "-67.86668000"}, {"name": "Holden", "countryCode": "US", "stateCode": "ME", "latitude": "44.75285000", "longitude": "-68.67892000"}, {"name": "Hollis Center", "countryCode": "US", "stateCode": "ME", "latitude": "43.60508000", "longitude": "-70.59311000"}, {"name": "Hope", "countryCode": "US", "stateCode": "ME", "latitude": "44.26508000", "longitude": "-69.15893000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "46.12616000", "longitude": "-67.84030000"}, {"name": "Howland", "countryCode": "US", "stateCode": "ME", "latitude": "45.23867000", "longitude": "-68.66364000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "45.00118000", "longitude": "-68.88059000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.50395000", "longitude": "-70.21617000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.20674000", "longitude": "-69.45254000"}, {"name": "Jonesport", "countryCode": "US", "stateCode": "ME", "latitude": "44.53286000", "longitude": "-67.59833000"}, {"name": "Kenduskeag", "countryCode": "US", "stateCode": "ME", "latitude": "44.91951000", "longitude": "-68.93170000"}, {"name": "Kennebec County", "countryCode": "US", "stateCode": "ME", "latitude": "44.40916000", "longitude": "-69.76726000"}, {"name": "Kennebunk", "countryCode": "US", "stateCode": "ME", "latitude": "43.38397000", "longitude": "-70.54478000"}, {"name": "Kennebunkport", "countryCode": "US", "stateCode": "ME", "latitude": "43.36175000", "longitude": "-70.47672000"}, {"name": "Kingfield", "countryCode": "US", "stateCode": "ME", "latitude": "44.95922000", "longitude": "-70.15395000"}, {"name": "Kittery", "countryCode": "US", "stateCode": "ME", "latitude": "43.08814000", "longitude": "-70.73616000"}, {"name": "Kittery Point", "countryCode": "US", "stateCode": "ME", "latitude": "43.08342000", "longitude": "-70.70783000"}, {"name": "Knox County", "countryCode": "US", "stateCode": "ME", "latitude": "44.07575000", "longitude": "-69.12598000"}, {"name": "Lake Arrowhead", "countryCode": "US", "stateCode": "ME", "latitude": "43.66369000", "longitude": "-70.73478000"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "ME", "latitude": "43.39453000", "longitude": "-70.85089000"}, {"name": "Leeds", "countryCode": "US", "stateCode": "ME", "latitude": "44.30340000", "longitude": "-70.11950000"}, {"name": "Levant", "countryCode": "US", "stateCode": "ME", "latitude": "44.86924000", "longitude": "-68.93476000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.10035000", "longitude": "-70.21478000"}, {"name": "Limerick", "countryCode": "US", "stateCode": "ME", "latitude": "43.68841000", "longitude": "-70.79367000"}, {"name": "Limestone", "countryCode": "US", "stateCode": "ME", "latitude": "46.90866000", "longitude": "-67.82585000"}, {"name": "Limington", "countryCode": "US", "stateCode": "ME", "latitude": "43.73174000", "longitude": "-70.71089000"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "ME", "latitude": "45.36228000", "longitude": "-68.50502000"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "ME", "latitude": "43.99779000", "longitude": "-69.52576000"}, {"name": "Lincolnville", "countryCode": "US", "stateCode": "ME", "latitude": "44.28119000", "longitude": "-69.00865000"}, {"name": "Lisbon", "countryCode": "US", "stateCode": "ME", "latitude": "44.03146000", "longitude": "-70.10450000"}, {"name": "Lisbon Falls", "countryCode": "US", "stateCode": "ME", "latitude": "43.99619000", "longitude": "-70.06061000"}, {"name": "Livermore", "countryCode": "US", "stateCode": "ME", "latitude": "44.38396000", "longitude": "-70.24922000"}, {"name": "Livermore Falls", "countryCode": "US", "stateCode": "ME", "latitude": "44.47534000", "longitude": "-70.18811000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.12674000", "longitude": "-70.89173000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.71508000", "longitude": "-67.46138000"}, {"name": "Machiasport", "countryCode": "US", "stateCode": "ME", "latitude": "44.69869000", "longitude": "-67.39471000"}, {"name": "Madawaska", "countryCode": "US", "stateCode": "ME", "latitude": "46.88421000", "longitude": "-67.94725000"}, {"name": "Madison", "countryCode": "US", "stateCode": "ME", "latitude": "44.79756000", "longitude": "-69.87978000"}, {"name": "Manchester", "countryCode": "US", "stateCode": "ME", "latitude": "44.32451000", "longitude": "-69.86033000"}, {"name": "Mechanic Falls", "countryCode": "US", "stateCode": "ME", "latitude": "44.11174000", "longitude": "-70.39172000"}, {"name": "Medway", "countryCode": "US", "stateCode": "ME", "latitude": "45.60894000", "longitude": "-68.53086000"}, {"name": "Mexico", "countryCode": "US", "stateCode": "ME", "latitude": "44.56090000", "longitude": "-70.54534000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.53536000", "longitude": "-67.88083000"}, {"name": "Milford", "countryCode": "US", "stateCode": "ME", "latitude": "44.94618000", "longitude": "-68.64392000"}, {"name": "Millinocket", "countryCode": "US", "stateCode": "ME", "latitude": "45.65727000", "longitude": "-68.70976000"}, {"name": "Milo", "countryCode": "US", "stateCode": "ME", "latitude": "45.25366000", "longitude": "-68.98587000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.08563000", "longitude": "-70.32006000"}, {"name": "Monmouth", "countryCode": "US", "stateCode": "ME", "latitude": "44.23868000", "longitude": "-70.03561000"}, {"name": "Mount Vernon", "countryCode": "US", "stateCode": "ME", "latitude": "44.50118000", "longitude": "-69.98756000"}, {"name": "New Gloucester", "countryCode": "US", "stateCode": "ME", "latitude": "43.96285000", "longitude": "-70.28255000"}, {"name": "New Sharon", "countryCode": "US", "stateCode": "ME", "latitude": "44.63895000", "longitude": "-70.01561000"}, {"name": "Newfield", "countryCode": "US", "stateCode": "ME", "latitude": "43.64813000", "longitude": "-70.84701000"}, {"name": "Newport", "countryCode": "US", "stateCode": "ME", "latitude": "44.83534000", "longitude": "-69.27394000"}, {"name": "Nobleboro", "countryCode": "US", "stateCode": "ME", "latitude": "44.07952000", "longitude": "-69.48505000"}, {"name": "Norridgewock", "countryCode": "US", "stateCode": "ME", "latitude": "44.71312000", "longitude": "-69.79061000"}, {"name": "North Bath", "countryCode": "US", "stateCode": "ME", "latitude": "43.93480000", "longitude": "-69.81588000"}, {"name": "North Berwick", "countryCode": "US", "stateCode": "ME", "latitude": "43.30370000", "longitude": "-70.73339000"}, {"name": "North Windham", "countryCode": "US", "stateCode": "ME", "latitude": "43.83424000", "longitude": "-70.43839000"}, {"name": "Northport", "countryCode": "US", "stateCode": "ME", "latitude": "44.33786000", "longitude": "-68.96142000"}, {"name": "Norway", "countryCode": "US", "stateCode": "ME", "latitude": "44.21396000", "longitude": "-70.54478000"}, {"name": "Oakland", "countryCode": "US", "stateCode": "ME", "latitude": "44.54034000", "longitude": "-69.72199000"}, {"name": "Ogunquit", "countryCode": "US", "stateCode": "ME", "latitude": "43.24898000", "longitude": "-70.59922000"}, {"name": "Old Orchard Beach", "countryCode": "US", "stateCode": "ME", "latitude": "43.51731000", "longitude": "-70.37755000"}, {"name": "Old Town", "countryCode": "US", "stateCode": "ME", "latitude": "44.93423000", "longitude": "-68.64531000"}, {"name": "Orland", "countryCode": "US", "stateCode": "ME", "latitude": "44.57035000", "longitude": "-68.73586000"}, {"name": "Orono", "countryCode": "US", "stateCode": "ME", "latitude": "44.88312000", "longitude": "-68.67198000"}, {"name": "Orrington", "countryCode": "US", "stateCode": "ME", "latitude": "44.73118000", "longitude": "-68.82643000"}, {"name": "<PERSON>wl<PERSON> Head", "countryCode": "US", "stateCode": "ME", "latitude": "44.08230000", "longitude": "-69.05726000"}, {"name": "Oxford", "countryCode": "US", "stateCode": "ME", "latitude": "44.13174000", "longitude": "-70.49311000"}, {"name": "Oxford County", "countryCode": "US", "stateCode": "ME", "latitude": "44.49977000", "longitude": "-70.75657000"}, {"name": "Palermo", "countryCode": "US", "stateCode": "ME", "latitude": "44.40785000", "longitude": "-69.47393000"}, {"name": "Palmyra", "countryCode": "US", "stateCode": "ME", "latitude": "44.84645000", "longitude": "-69.35866000"}, {"name": "Paris", "countryCode": "US", "stateCode": "ME", "latitude": "44.25979000", "longitude": "-70.50062000"}, {"name": "Parsonsfield", "countryCode": "US", "stateCode": "ME", "latitude": "43.72702000", "longitude": "-70.92868000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "45.99644000", "longitude": "-68.44614000"}, {"name": "Penobscot", "countryCode": "US", "stateCode": "ME", "latitude": "44.46452000", "longitude": "-68.71114000"}, {"name": "Penobscot County", "countryCode": "US", "stateCode": "ME", "latitude": "45.40051000", "longitude": "-68.64943000"}, {"name": "Peru", "countryCode": "US", "stateCode": "ME", "latitude": "44.50673000", "longitude": "-70.40534000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.82311000", "longitude": "-70.33951000"}, {"name": "Phippsburg", "countryCode": "US", "stateCode": "ME", "latitude": "43.82064000", "longitude": "-69.81477000"}, {"name": "Piscataquis County", "countryCode": "US", "stateCode": "ME", "latitude": "45.83736000", "longitude": "-69.28452000"}, {"name": "Pittsfield", "countryCode": "US", "stateCode": "ME", "latitude": "44.78256000", "longitude": "-69.38338000"}, {"name": "<PERSON>ston", "countryCode": "US", "stateCode": "ME", "latitude": "44.22174000", "longitude": "-69.75560000"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "ME", "latitude": "44.76729000", "longitude": "-69.21033000"}, {"name": "Poland", "countryCode": "US", "stateCode": "ME", "latitude": "44.06063000", "longitude": "-70.39367000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.79591000", "longitude": "-70.93256000"}, {"name": "Portland", "countryCode": "US", "stateCode": "ME", "latitude": "43.65737000", "longitude": "-70.25890000"}, {"name": "Presque Isle", "countryCode": "US", "stateCode": "ME", "latitude": "46.68115000", "longitude": "-68.01586000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.23035000", "longitude": "-69.76671000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.90146000", "longitude": "-70.47033000"}, {"name": "Readfield", "countryCode": "US", "stateCode": "ME", "latitude": "44.38785000", "longitude": "-69.96672000"}, {"name": "Richmond", "countryCode": "US", "stateCode": "ME", "latitude": "44.08730000", "longitude": "-69.79893000"}, {"name": "Rockland", "countryCode": "US", "stateCode": "ME", "latitude": "44.10369000", "longitude": "-69.10893000"}, {"name": "Rockport", "countryCode": "US", "stateCode": "ME", "latitude": "44.18452000", "longitude": "-69.07615000"}, {"name": "Rome", "countryCode": "US", "stateCode": "ME", "latitude": "44.58506000", "longitude": "-69.86922000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.55367000", "longitude": "-70.55090000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.11980000", "longitude": "-70.10755000"}, {"name": "Saco", "countryCode": "US", "stateCode": "ME", "latitude": "43.50092000", "longitude": "-70.44283000"}, {"name": "Sagadahoc County", "countryCode": "US", "stateCode": "ME", "latitude": "43.91173000", "longitude": "-69.83931000"}, {"name": "Saint Albans", "countryCode": "US", "stateCode": "ME", "latitude": "44.91006000", "longitude": "-69.41005000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.01647000", "longitude": "-69.19893000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.43925000", "longitude": "-70.77422000"}, {"name": "Sangerville", "countryCode": "US", "stateCode": "ME", "latitude": "45.16478000", "longitude": "-69.35644000"}, {"name": "Scarborough", "countryCode": "US", "stateCode": "ME", "latitude": "43.57814000", "longitude": "-70.32172000"}, {"name": "Searsmont", "countryCode": "US", "stateCode": "ME", "latitude": "44.36174000", "longitude": "-69.19504000"}, {"name": "Sedgwick", "countryCode": "US", "stateCode": "ME", "latitude": "44.30369000", "longitude": "-68.61614000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "43.54064000", "longitude": "-70.84812000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.41312000", "longitude": "-69.72893000"}, {"name": "Skowhegan", "countryCode": "US", "stateCode": "ME", "latitude": "44.76506000", "longitude": "-69.71922000"}, {"name": "Somerset County", "countryCode": "US", "stateCode": "ME", "latitude": "45.51385000", "longitude": "-69.95882000"}, {"name": "South Berwick", "countryCode": "US", "stateCode": "ME", "latitude": "43.23453000", "longitude": "-70.80950000"}, {"name": "South Eliot", "countryCode": "US", "stateCode": "ME", "latitude": "43.10814000", "longitude": "-70.77755000"}, {"name": "South Paris", "countryCode": "US", "stateCode": "ME", "latitude": "44.22368000", "longitude": "-70.51339000"}, {"name": "South Portland", "countryCode": "US", "stateCode": "ME", "latitude": "43.64147000", "longitude": "-70.24088000"}, {"name": "South Portland Gardens", "countryCode": "US", "stateCode": "ME", "latitude": "43.63897000", "longitude": "-70.31533000"}, {"name": "South Sanford", "countryCode": "US", "stateCode": "ME", "latitude": "43.41119000", "longitude": "-70.74256000"}, {"name": "South Thomaston", "countryCode": "US", "stateCode": "ME", "latitude": "44.05147000", "longitude": "-69.12782000"}, {"name": "South Windham", "countryCode": "US", "stateCode": "ME", "latitude": "43.73619000", "longitude": "-70.42366000"}, {"name": "Springvale", "countryCode": "US", "stateCode": "ME", "latitude": "43.46675000", "longitude": "-70.79367000"}, {"name": "Steep Falls", "countryCode": "US", "stateCode": "ME", "latitude": "43.79397000", "longitude": "-70.65256000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.89173000", "longitude": "-69.14282000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.51098000", "longitude": "-67.96662000"}, {"name": "Stockton Springs", "countryCode": "US", "stateCode": "ME", "latitude": "44.48952000", "longitude": "-68.85698000"}, {"name": "Stonington", "countryCode": "US", "stateCode": "ME", "latitude": "44.15619000", "longitude": "-68.66669000"}, {"name": "Strong", "countryCode": "US", "stateCode": "ME", "latitude": "44.80756000", "longitude": "-70.22090000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.52036000", "longitude": "-68.19668000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.49591000", "longitude": "-68.50169000"}, {"name": "Swanville", "countryCode": "US", "stateCode": "ME", "latitude": "44.52119000", "longitude": "-68.99781000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.07897000", "longitude": "-69.18171000"}, {"name": "Topsham", "countryCode": "US", "stateCode": "ME", "latitude": "43.92758000", "longitude": "-69.97588000"}, {"name": "Tremont", "countryCode": "US", "stateCode": "ME", "latitude": "44.25369000", "longitude": "-68.35141000"}, {"name": "Trenton", "countryCode": "US", "stateCode": "ME", "latitude": "44.43897000", "longitude": "-68.37002000"}, {"name": "Troy", "countryCode": "US", "stateCode": "ME", "latitude": "44.66479000", "longitude": "-69.24088000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.25646000", "longitude": "-70.25617000"}, {"name": "Union", "countryCode": "US", "stateCode": "ME", "latitude": "44.21147000", "longitude": "-69.27421000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "47.15727000", "longitude": "-67.93530000"}, {"name": "Vassalboro", "countryCode": "US", "stateCode": "ME", "latitude": "44.45923000", "longitude": "-69.67755000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.83868000", "longitude": "-68.70531000"}, {"name": "Vinalhaven", "countryCode": "US", "stateCode": "ME", "latitude": "44.04814000", "longitude": "-68.83170000"}, {"name": "Waldo County", "countryCode": "US", "stateCode": "ME", "latitude": "44.48525000", "longitude": "-69.12188000"}, {"name": "Waldoboro", "countryCode": "US", "stateCode": "ME", "latitude": "44.09536000", "longitude": "-69.37560000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.12036000", "longitude": "-69.24005000"}, {"name": "Washington", "countryCode": "US", "stateCode": "ME", "latitude": "44.27369000", "longitude": "-69.36727000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "ME", "latitude": "44.96946000", "longitude": "-67.60906000"}, {"name": "Waterboro", "countryCode": "US", "stateCode": "ME", "latitude": "43.53564000", "longitude": "-70.71506000"}, {"name": "Waterville", "countryCode": "US", "stateCode": "ME", "latitude": "44.55201000", "longitude": "-69.63171000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.34868000", "longitude": "-70.06616000"}, {"name": "Wells Beach Station", "countryCode": "US", "stateCode": "ME", "latitude": "43.32397000", "longitude": "-70.59144000"}, {"name": "West Kennebunk", "countryCode": "US", "stateCode": "ME", "latitude": "43.40870000", "longitude": "-70.58144000"}, {"name": "West Paris", "countryCode": "US", "stateCode": "ME", "latitude": "44.32423000", "longitude": "-70.57395000"}, {"name": "West Scarborough", "countryCode": "US", "stateCode": "ME", "latitude": "43.57036000", "longitude": "-70.38783000"}, {"name": "Westbrook", "countryCode": "US", "stateCode": "ME", "latitude": "43.67703000", "longitude": "-70.37116000"}, {"name": "Whitefield", "countryCode": "US", "stateCode": "ME", "latitude": "44.17007000", "longitude": "-69.62532000"}, {"name": "Wilton", "countryCode": "US", "stateCode": "ME", "latitude": "44.59284000", "longitude": "-70.22812000"}, {"name": "Windsor", "countryCode": "US", "stateCode": "ME", "latitude": "44.31063000", "longitude": "-69.58060000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.54701000", "longitude": "-69.62116000"}, {"name": "Winterport", "countryCode": "US", "stateCode": "ME", "latitude": "44.63785000", "longitude": "-68.84504000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "ME", "latitude": "44.30507000", "longitude": "-69.97700000"}, {"name": "Wiscasset", "countryCode": "US", "stateCode": "ME", "latitude": "44.00286000", "longitude": "-69.66560000"}, {"name": "Woodstock", "countryCode": "US", "stateCode": "ME", "latitude": "44.37494000", "longitude": "-70.60849000"}, {"name": "Woolwich", "countryCode": "US", "stateCode": "ME", "latitude": "43.91869000", "longitude": "-69.80116000"}, {"name": "Yarmouth", "countryCode": "US", "stateCode": "ME", "latitude": "43.80064000", "longitude": "-70.18672000"}, {"name": "York Beach", "countryCode": "US", "stateCode": "ME", "latitude": "43.17148000", "longitude": "-70.60894000"}, {"name": "York County", "countryCode": "US", "stateCode": "ME", "latitude": "43.42923000", "longitude": "-70.67015000"}, {"name": "York Harbor", "countryCode": "US", "stateCode": "ME", "latitude": "43.13676000", "longitude": "-70.64561000"}]