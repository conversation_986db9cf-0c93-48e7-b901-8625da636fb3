[{"name": "Alexandria", "countryCode": "US", "stateCode": "NH", "latitude": "43.61146000", "longitude": "-71.79286000"}, {"name": "Alstead", "countryCode": "US", "stateCode": "NH", "latitude": "43.14897000", "longitude": "-72.36064000"}, {"name": "Andover", "countryCode": "US", "stateCode": "NH", "latitude": "43.43702000", "longitude": "-71.82341000"}, {"name": "Antrim", "countryCode": "US", "stateCode": "NH", "latitude": "43.03091000", "longitude": "-71.93897000"}, {"name": "Ashland", "countryCode": "US", "stateCode": "NH", "latitude": "43.69535000", "longitude": "-71.63063000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.83842000", "longitude": "-71.14700000"}, {"name": "Auburn", "countryCode": "US", "stateCode": "NH", "latitude": "43.00453000", "longitude": "-71.34840000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.33397000", "longitude": "-71.29284000"}, {"name": "Barrington", "countryCode": "US", "stateCode": "NH", "latitude": "43.22286000", "longitude": "-71.04701000"}, {"name": "Bedford", "countryCode": "US", "stateCode": "NH", "latitude": "42.94647000", "longitude": "-71.51590000"}, {"name": "Belknap County", "countryCode": "US", "stateCode": "NH", "latitude": "43.51869000", "longitude": "-71.42336000"}, {"name": "Belmont", "countryCode": "US", "stateCode": "NH", "latitude": "43.44536000", "longitude": "-71.47785000"}, {"name": "Berlin", "countryCode": "US", "stateCode": "NH", "latitude": "44.46867000", "longitude": "-71.18508000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.31508000", "longitude": "-71.62091000"}, {"name": "Bow Bog", "countryCode": "US", "stateCode": "NH", "latitude": "43.12064000", "longitude": "-71.51146000"}, {"name": "Brentwood", "countryCode": "US", "stateCode": "NH", "latitude": "42.97870000", "longitude": "-71.07284000"}, {"name": "Bridgewater", "countryCode": "US", "stateCode": "NH", "latitude": "43.63841000", "longitude": "-71.73647000"}, {"name": "Bristol", "countryCode": "US", "stateCode": "NH", "latitude": "43.59119000", "longitude": "-71.73675000"}, {"name": "Brookline", "countryCode": "US", "stateCode": "NH", "latitude": "42.73481000", "longitude": "-71.65813000"}, {"name": "Candia", "countryCode": "US", "stateCode": "NH", "latitude": "43.07786000", "longitude": "-71.27673000"}, {"name": "Canterbury", "countryCode": "US", "stateCode": "NH", "latitude": "43.33702000", "longitude": "-71.56535000"}, {"name": "Carroll County", "countryCode": "US", "stateCode": "NH", "latitude": "43.87391000", "longitude": "-71.20277000"}, {"name": "Center Harbor", "countryCode": "US", "stateCode": "NH", "latitude": "43.70980000", "longitude": "-71.46035000"}, {"name": "Charlestown", "countryCode": "US", "stateCode": "NH", "latitude": "43.23869000", "longitude": "-72.42453000"}, {"name": "Cheshire County", "countryCode": "US", "stateCode": "NH", "latitude": "42.91932000", "longitude": "-72.25118000"}, {"name": "Chester", "countryCode": "US", "stateCode": "NH", "latitude": "42.95675000", "longitude": "-71.25728000"}, {"name": "Chesterfield", "countryCode": "US", "stateCode": "NH", "latitude": "42.88730000", "longitude": "-72.47037000"}, {"name": "Chichester", "countryCode": "US", "stateCode": "NH", "latitude": "43.24925000", "longitude": "-71.39979000"}, {"name": "Claremont", "countryCode": "US", "stateCode": "NH", "latitude": "43.37674000", "longitude": "-72.34676000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "44.89449000", "longitude": "-71.49592000"}, {"name": "Concord", "countryCode": "US", "stateCode": "NH", "latitude": "43.20814000", "longitude": "-71.53757000"}, {"name": "Contoocook", "countryCode": "US", "stateCode": "NH", "latitude": "43.22202000", "longitude": "-71.71397000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.97924000", "longitude": "-71.12035000"}, {"name": "Coos County", "countryCode": "US", "stateCode": "NH", "latitude": "44.68960000", "longitude": "-71.30542000"}, {"name": "Danbury", "countryCode": "US", "stateCode": "NH", "latitude": "43.52563000", "longitude": "-71.86175000"}, {"name": "Danville", "countryCode": "US", "stateCode": "NH", "latitude": "42.91259000", "longitude": "-71.12450000"}, {"name": "Deerfield", "countryCode": "US", "stateCode": "NH", "latitude": "44.23062000", "longitude": "-71.61703000"}, {"name": "Deering", "countryCode": "US", "stateCode": "NH", "latitude": "43.07314000", "longitude": "-71.84452000"}, {"name": "Derry", "countryCode": "US", "stateCode": "NH", "latitude": "42.88064000", "longitude": "-71.32729000"}, {"name": "Derry Village", "countryCode": "US", "stateCode": "NH", "latitude": "42.89175000", "longitude": "-71.31201000"}, {"name": "Dover", "countryCode": "US", "stateCode": "NH", "latitude": "43.19786000", "longitude": "-70.87367000"}, {"name": "Dublin", "countryCode": "US", "stateCode": "NH", "latitude": "42.90758000", "longitude": "-72.06258000"}, {"name": "Durham", "countryCode": "US", "stateCode": "NH", "latitude": "43.13397000", "longitude": "-70.92645000"}, {"name": "East Concord", "countryCode": "US", "stateCode": "NH", "latitude": "43.24202000", "longitude": "-71.53813000"}, {"name": "East Kingston", "countryCode": "US", "stateCode": "NH", "latitude": "42.92564000", "longitude": "-71.01672000"}, {"name": "East Merrimack", "countryCode": "US", "stateCode": "NH", "latitude": "42.86814000", "longitude": "-71.48340000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.76119000", "longitude": "-70.99645000"}, {"name": "Enfield", "countryCode": "US", "stateCode": "NH", "latitude": "43.64063000", "longitude": "-72.14398000"}, {"name": "Epping", "countryCode": "US", "stateCode": "NH", "latitude": "43.03342000", "longitude": "-71.07423000"}, {"name": "Epsom", "countryCode": "US", "stateCode": "NH", "latitude": "43.22286000", "longitude": "-71.33201000"}, {"name": "Exeter", "countryCode": "US", "stateCode": "NH", "latitude": "42.98148000", "longitude": "-70.94783000"}, {"name": "Farmington", "countryCode": "US", "stateCode": "NH", "latitude": "43.38980000", "longitude": "-71.06506000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.78064000", "longitude": "-72.14175000"}, {"name": "Francestown", "countryCode": "US", "stateCode": "NH", "latitude": "42.98758000", "longitude": "-71.81258000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.44424000", "longitude": "-71.64730000"}, {"name": "Freedom", "countryCode": "US", "stateCode": "NH", "latitude": "43.81230000", "longitude": "-71.03562000"}, {"name": "Fremont", "countryCode": "US", "stateCode": "NH", "latitude": "42.99092000", "longitude": "-71.14256000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.54758000", "longitude": "-71.40674000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.42425000", "longitude": "-71.41452000"}, {"name": "Goffstown", "countryCode": "US", "stateCode": "NH", "latitude": "43.02036000", "longitude": "-71.60035000"}, {"name": "Gorham", "countryCode": "US", "stateCode": "NH", "latitude": "44.38784000", "longitude": "-71.17313000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.55868000", "longitude": "-71.94397000"}, {"name": "Grafton County", "countryCode": "US", "stateCode": "NH", "latitude": "43.94074000", "longitude": "-71.82055000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.48952000", "longitude": "-72.13759000"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "NH", "latitude": "42.95064000", "longitude": "-71.87230000"}, {"name": "Greenland", "countryCode": "US", "stateCode": "NH", "latitude": "43.03620000", "longitude": "-70.83283000"}, {"name": "Greenville", "countryCode": "US", "stateCode": "NH", "latitude": "42.76731000", "longitude": "-71.81230000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "44.59867000", "longitude": "-71.51120000"}, {"name": "Hampstead", "countryCode": "US", "stateCode": "NH", "latitude": "42.87453000", "longitude": "-71.18117000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.93759000", "longitude": "-70.83894000"}, {"name": "Hampton Beach", "countryCode": "US", "stateCode": "NH", "latitude": "42.90731000", "longitude": "-70.81200000"}, {"name": "Hampton Falls", "countryCode": "US", "stateCode": "NH", "latitude": "42.91620000", "longitude": "-70.86366000"}, {"name": "Hanover", "countryCode": "US", "stateCode": "NH", "latitude": "43.70229000", "longitude": "-72.28954000"}, {"name": "Harrisville", "countryCode": "US", "stateCode": "NH", "latitude": "42.94508000", "longitude": "-72.09647000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "44.03451000", "longitude": "-72.06398000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.17980000", "longitude": "-71.82230000"}, {"name": "Hill", "countryCode": "US", "stateCode": "NH", "latitude": "43.52424000", "longitude": "-71.70091000"}, {"name": "Hillsborough", "countryCode": "US", "stateCode": "NH", "latitude": "43.11410000", "longitude": "-71.89920000"}, {"name": "Hillsborough County", "countryCode": "US", "stateCode": "NH", "latitude": "42.91531000", "longitude": "-71.71601000"}, {"name": "Hinsdale", "countryCode": "US", "stateCode": "NH", "latitude": "42.78619000", "longitude": "-72.48648000"}, {"name": "Holderness", "countryCode": "US", "stateCode": "NH", "latitude": "43.73202000", "longitude": "-71.58841000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.74314000", "longitude": "-71.59174000"}, {"name": "Hooksett", "countryCode": "US", "stateCode": "NH", "latitude": "43.09675000", "longitude": "-71.46507000"}, {"name": "<PERSON>kin<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.19147000", "longitude": "-71.67535000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.76481000", "longitude": "-71.43979000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.81397000", "longitude": "-72.02314000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "44.41895000", "longitude": "-71.47453000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.93369000", "longitude": "-72.27814000"}, {"name": "Kensington", "countryCode": "US", "stateCode": "NH", "latitude": "42.92703000", "longitude": "-70.94394000"}, {"name": "Kingston", "countryCode": "US", "stateCode": "NH", "latitude": "42.93648000", "longitude": "-71.05339000"}, {"name": "Laconia", "countryCode": "US", "stateCode": "NH", "latitude": "43.52785000", "longitude": "-71.47035000"}, {"name": "Lancaster", "countryCode": "US", "stateCode": "NH", "latitude": "44.48895000", "longitude": "-71.56925000"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "NH", "latitude": "43.64229000", "longitude": "-72.25176000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.12314000", "longitude": "-71.01145000"}, {"name": "Lempster", "countryCode": "US", "stateCode": "NH", "latitude": "43.23841000", "longitude": "-72.21064000"}, {"name": "Litchfield", "countryCode": "US", "stateCode": "NH", "latitude": "42.84425000", "longitude": "-71.47979000"}, {"name": "Littleton", "countryCode": "US", "stateCode": "NH", "latitude": "44.30617000", "longitude": "-71.77009000"}, {"name": "Londonderry", "countryCode": "US", "stateCode": "NH", "latitude": "42.86509000", "longitude": "-71.37395000"}, {"name": "Lyme", "countryCode": "US", "stateCode": "NH", "latitude": "43.80951000", "longitude": "-72.15592000"}, {"name": "Lyndeborough", "countryCode": "US", "stateCode": "NH", "latitude": "42.90758000", "longitude": "-71.76646000"}, {"name": "Madbury", "countryCode": "US", "stateCode": "NH", "latitude": "43.16925000", "longitude": "-70.92395000"}, {"name": "Madison", "countryCode": "US", "stateCode": "NH", "latitude": "43.89924000", "longitude": "-71.14840000"}, {"name": "Manchester", "countryCode": "US", "stateCode": "NH", "latitude": "42.99564000", "longitude": "-71.45479000"}, {"name": "Marlborough", "countryCode": "US", "stateCode": "NH", "latitude": "42.90425000", "longitude": "-72.20786000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.74370000", "longitude": "-71.76896000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.65757000", "longitude": "-71.50035000"}, {"name": "Me<PERSON>mack", "countryCode": "US", "stateCode": "NH", "latitude": "42.86509000", "longitude": "-71.49340000"}, {"name": "Merrimack County", "countryCode": "US", "stateCode": "NH", "latitude": "43.29765000", "longitude": "-71.68019000"}, {"name": "Milan", "countryCode": "US", "stateCode": "NH", "latitude": "44.57339000", "longitude": "-71.18508000"}, {"name": "Milford", "countryCode": "US", "stateCode": "NH", "latitude": "42.83536000", "longitude": "-71.64896000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.89453000", "longitude": "-71.67424000"}, {"name": "Moultonborough", "countryCode": "US", "stateCode": "NH", "latitude": "43.75480000", "longitude": "-71.39674000"}, {"name": "Nashua", "countryCode": "US", "stateCode": "NH", "latitude": "42.76537000", "longitude": "-71.46757000"}, {"name": "New Boston", "countryCode": "US", "stateCode": "NH", "latitude": "42.97619000", "longitude": "-71.69396000"}, {"name": "New Castle", "countryCode": "US", "stateCode": "NH", "latitude": "43.07231000", "longitude": "-70.71616000"}, {"name": "New Durham", "countryCode": "US", "stateCode": "NH", "latitude": "43.43675000", "longitude": "-71.17229000"}, {"name": "New Ipswich", "countryCode": "US", "stateCode": "NH", "latitude": "42.74814000", "longitude": "-71.85424000"}, {"name": "New London", "countryCode": "US", "stateCode": "NH", "latitude": "43.41396000", "longitude": "-71.98508000"}, {"name": "Newbury", "countryCode": "US", "stateCode": "NH", "latitude": "43.32146000", "longitude": "-72.03592000"}, {"name": "Newmarket", "countryCode": "US", "stateCode": "NH", "latitude": "43.08286000", "longitude": "-70.93506000"}, {"name": "Newport", "countryCode": "US", "stateCode": "NH", "latitude": "43.36535000", "longitude": "-72.17342000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "42.86953000", "longitude": "-71.03450000"}, {"name": "North Conway", "countryCode": "US", "stateCode": "NH", "latitude": "44.05368000", "longitude": "-71.12840000"}, {"name": "North Hampton", "countryCode": "US", "stateCode": "NH", "latitude": "42.97259000", "longitude": "-70.82978000"}, {"name": "Northfield", "countryCode": "US", "stateCode": "NH", "latitude": "43.43313000", "longitude": "-71.59230000"}, {"name": "Northumberland", "countryCode": "US", "stateCode": "NH", "latitude": "44.56339000", "longitude": "-71.55870000"}, {"name": "Northwood", "countryCode": "US", "stateCode": "NH", "latitude": "43.19425000", "longitude": "-71.15090000"}, {"name": "Nottingham", "countryCode": "US", "stateCode": "NH", "latitude": "43.11453000", "longitude": "-71.09978000"}, {"name": "Orford", "countryCode": "US", "stateCode": "NH", "latitude": "43.90535000", "longitude": "-72.14009000"}, {"name": "Ossipee", "countryCode": "US", "stateCode": "NH", "latitude": "43.68536000", "longitude": "-71.11673000"}, {"name": "Pelham", "countryCode": "US", "stateCode": "NH", "latitude": "42.73453000", "longitude": "-71.32451000"}, {"name": "Pembroke", "countryCode": "US", "stateCode": "NH", "latitude": "43.14675000", "longitude": "-71.45757000"}, {"name": "Peterborough", "countryCode": "US", "stateCode": "NH", "latitude": "42.87064000", "longitude": "-71.95175000"}, {"name": "Pinardville", "countryCode": "US", "stateCode": "NH", "latitude": "42.99425000", "longitude": "-71.50729000"}, {"name": "Pittsfield", "countryCode": "US", "stateCode": "NH", "latitude": "43.30591000", "longitude": "-71.32423000"}, {"name": "Plaistow", "countryCode": "US", "stateCode": "NH", "latitude": "42.83648000", "longitude": "-71.09478000"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "NH", "latitude": "43.75702000", "longitude": "-71.68813000"}, {"name": "Portsmouth", "countryCode": "US", "stateCode": "NH", "latitude": "43.07704000", "longitude": "-70.75766000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.03620000", "longitude": "-71.18340000"}, {"name": "Richmond", "countryCode": "US", "stateCode": "NH", "latitude": "42.75481000", "longitude": "-72.27175000"}, {"name": "Rindge", "countryCode": "US", "stateCode": "NH", "latitude": "42.75120000", "longitude": "-72.00980000"}, {"name": "Rochester", "countryCode": "US", "stateCode": "NH", "latitude": "43.30453000", "longitude": "-70.97562000"}, {"name": "Rockingham County", "countryCode": "US", "stateCode": "NH", "latitude": "42.98454000", "longitude": "-71.08897000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.23620000", "longitude": "-70.82034000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.80535000", "longitude": "-71.81258000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.01342000", "longitude": "-70.77089000"}, {"name": "Salem", "countryCode": "US", "stateCode": "NH", "latitude": "42.78842000", "longitude": "-71.20089000"}, {"name": "Salisbury", "countryCode": "US", "stateCode": "NH", "latitude": "43.38008000", "longitude": "-71.71702000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.48924000", "longitude": "-71.58230000"}, {"name": "Sanbornville", "countryCode": "US", "stateCode": "NH", "latitude": "43.55425000", "longitude": "-71.03090000"}, {"name": "Sandown", "countryCode": "US", "stateCode": "NH", "latitude": "42.92870000", "longitude": "-71.18701000"}, {"name": "Sandwich", "countryCode": "US", "stateCode": "NH", "latitude": "43.79035000", "longitude": "-71.41118000"}, {"name": "Seabrook", "countryCode": "US", "stateCode": "NH", "latitude": "42.89481000", "longitude": "-70.87116000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.26175000", "longitude": "-70.86534000"}, {"name": "South Hooksett", "countryCode": "US", "stateCode": "NH", "latitude": "43.02647000", "longitude": "-71.43534000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "NH", "latitude": "43.49507000", "longitude": "-72.03342000"}, {"name": "Strafford", "countryCode": "US", "stateCode": "NH", "latitude": "43.32703000", "longitude": "-71.18423000"}, {"name": "Strafford County", "countryCode": "US", "stateCode": "NH", "latitude": "43.29743000", "longitude": "-71.02940000"}, {"name": "Stratford", "countryCode": "US", "stateCode": "NH", "latitude": "44.65505000", "longitude": "-71.55564000"}, {"name": "Stratham Station", "countryCode": "US", "stateCode": "NH", "latitude": "43.05287000", "longitude": "-70.89533000"}, {"name": "Sullivan County", "countryCode": "US", "stateCode": "NH", "latitude": "43.36122000", "longitude": "-72.22240000"}, {"name": "Sunapee", "countryCode": "US", "stateCode": "NH", "latitude": "43.38757000", "longitude": "-72.08786000"}, {"name": "Suncook", "countryCode": "US", "stateCode": "NH", "latitude": "43.13064000", "longitude": "-71.45312000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.33424000", "longitude": "-71.95147000"}, {"name": "Swanzey", "countryCode": "US", "stateCode": "NH", "latitude": "42.86980000", "longitude": "-72.28175000"}, {"name": "Tamworth", "countryCode": "US", "stateCode": "NH", "latitude": "43.85980000", "longitude": "-71.26313000"}, {"name": "Temple", "countryCode": "US", "stateCode": "NH", "latitude": "42.81814000", "longitude": "-71.85147000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.89285000", "longitude": "-71.67591000"}, {"name": "T<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.44230000", "longitude": "-71.58896000"}, {"name": "Tilton-Northfield", "countryCode": "US", "stateCode": "NH", "latitude": "43.44300000", "longitude": "-71.59364000"}, {"name": "Troy", "countryCode": "US", "stateCode": "NH", "latitude": "42.82397000", "longitude": "-72.18119000"}, {"name": "Tuftonboro", "countryCode": "US", "stateCode": "NH", "latitude": "43.69647000", "longitude": "-71.22201000"}, {"name": "Unity", "countryCode": "US", "stateCode": "NH", "latitude": "43.29396000", "longitude": "-72.26037000"}, {"name": "Wakefield", "countryCode": "US", "stateCode": "NH", "latitude": "43.56813000", "longitude": "-71.03007000"}, {"name": "Weare", "countryCode": "US", "stateCode": "NH", "latitude": "43.09480000", "longitude": "-71.73063000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.32897000", "longitude": "-71.71786000"}, {"name": "West Swanzey", "countryCode": "US", "stateCode": "NH", "latitude": "42.87008000", "longitude": "-72.32175000"}, {"name": "Westmoreland", "countryCode": "US", "stateCode": "NH", "latitude": "42.96203000", "longitude": "-72.44231000"}, {"name": "Whitefield", "countryCode": "US", "stateCode": "NH", "latitude": "44.37312000", "longitude": "-71.61008000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "NH", "latitude": "43.45174000", "longitude": "-71.91369000"}, {"name": "Wilton", "countryCode": "US", "stateCode": "NH", "latitude": "42.84342000", "longitude": "-71.73507000"}, {"name": "Winchester", "countryCode": "US", "stateCode": "NH", "latitude": "42.77342000", "longitude": "-72.38314000"}, {"name": "Windham", "countryCode": "US", "stateCode": "NH", "latitude": "42.80064000", "longitude": "-71.30423000"}, {"name": "Wolfeboro", "countryCode": "US", "stateCode": "NH", "latitude": "43.58397000", "longitude": "-71.20729000"}, {"name": "Woodstock", "countryCode": "US", "stateCode": "NH", "latitude": "43.97757000", "longitude": "-71.68508000"}, {"name": "Woodsville", "countryCode": "US", "stateCode": "NH", "latitude": "44.15229000", "longitude": "-72.03731000"}]