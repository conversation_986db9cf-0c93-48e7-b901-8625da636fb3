[{"name": "Abbeville", "countryCode": "US", "stateCode": "AL"}, {"name": "Adamsville", "countryCode": "US", "stateCode": "AL"}, {"name": "Alabaster", "countryCode": "US", "stateCode": "AL"}, {"name": "Albertville", "countryCode": "US", "stateCode": "AL"}, {"name": "Alexander City", "countryCode": "US", "stateCode": "AL"}, {"name": "Alexandria", "countryCode": "US", "stateCode": "AL"}, {"name": "Aliceville", "countryCode": "US", "stateCode": "AL"}, {"name": "Andalusia", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Arab", "countryCode": "US", "stateCode": "AL"}, {"name": "Argo", "countryCode": "US", "stateCode": "AL"}, {"name": "Ashford", "countryCode": "US", "stateCode": "AL"}, {"name": "Ashland", "countryCode": "US", "stateCode": "AL"}, {"name": "Ashville", "countryCode": "US", "stateCode": "AL"}, {"name": "Athens", "countryCode": "US", "stateCode": "AL"}, {"name": "Atmore", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Auburn", "countryCode": "US", "stateCode": "AL"}, {"name": "Autauga County", "countryCode": "US", "stateCode": "AL"}, {"name": "Baldwin County", "countryCode": "US", "stateCode": "AL"}, {"name": "Ballplay", "countryCode": "US", "stateCode": "AL"}, {"name": "Barbour County", "countryCode": "US", "stateCode": "AL"}, {"name": "Bay Minette", "countryCode": "US", "stateCode": "AL"}, {"name": "Bayou La Batre", "countryCode": "US", "stateCode": "AL"}, {"name": "Bear Creek", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Bessemer", "countryCode": "US", "stateCode": "AL"}, {"name": "Bibb County", "countryCode": "US", "stateCode": "AL"}, {"name": "Birmingham", "countryCode": "US", "stateCode": "AL"}, {"name": "Blount County", "countryCode": "US", "stateCode": "AL"}, {"name": "Blountsville", "countryCode": "US", "stateCode": "AL"}, {"name": "Blue Ridge", "countryCode": "US", "stateCode": "AL"}, {"name": "Boaz", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "B<PERSON>ton", "countryCode": "US", "stateCode": "AL"}, {"name": "Bridgeport", "countryCode": "US", "stateCode": "AL"}, {"name": "Brighton", "countryCode": "US", "stateCode": "AL"}, {"name": "Brook Highland", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Brookwood", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Bullock County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Butler County", "countryCode": "US", "stateCode": "AL"}, {"name": "Bynum", "countryCode": "US", "stateCode": "AL"}, {"name": "Cahaba Heights", "countryCode": "US", "stateCode": "AL"}, {"name": "Calera", "countryCode": "US", "stateCode": "AL"}, {"name": "Calhoun County", "countryCode": "US", "stateCode": "AL"}, {"name": "Camden", "countryCode": "US", "stateCode": "AL"}, {"name": "Carbon Hill", "countryCode": "US", "stateCode": "AL"}, {"name": "Carlisle-<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Cedar Bluff", "countryCode": "US", "stateCode": "AL"}, {"name": "Center Point", "countryCode": "US", "stateCode": "AL"}, {"name": "Centre", "countryCode": "US", "stateCode": "AL"}, {"name": "Centreville", "countryCode": "US", "stateCode": "AL"}, {"name": "Chalkville", "countryCode": "US", "stateCode": "AL"}, {"name": "Chambers County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Chelsea", "countryCode": "US", "stateCode": "AL"}, {"name": "Cherokee", "countryCode": "US", "stateCode": "AL"}, {"name": "Cherokee County", "countryCode": "US", "stateCode": "AL"}, {"name": "Chickasaw", "countryCode": "US", "stateCode": "AL"}, {"name": "Childersburg", "countryCode": "US", "stateCode": "AL"}, {"name": "Chilton County", "countryCode": "US", "stateCode": "AL"}, {"name": "Choccolocco", "countryCode": "US", "stateCode": "AL"}, {"name": "Choctaw County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>ton", "countryCode": "US", "stateCode": "AL"}, {"name": "Clarke County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Clay County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Cleburne County", "countryCode": "US", "stateCode": "AL"}, {"name": "Cleveland", "countryCode": "US", "stateCode": "AL"}, {"name": "Clio", "countryCode": "US", "stateCode": "AL"}, {"name": "Coaling", "countryCode": "US", "stateCode": "AL"}, {"name": "Coffee County", "countryCode": "US", "stateCode": "AL"}, {"name": "Colbert County", "countryCode": "US", "stateCode": "AL"}, {"name": "Collinsville", "countryCode": "US", "stateCode": "AL"}, {"name": "Columbiana", "countryCode": "US", "stateCode": "AL"}, {"name": "Concord", "countryCode": "US", "stateCode": "AL"}, {"name": "Conecuh County", "countryCode": "US", "stateCode": "AL"}, {"name": "Coosa County", "countryCode": "US", "stateCode": "AL"}, {"name": "Coosada", "countryCode": "US", "stateCode": "AL"}, {"name": "Cordova", "countryCode": "US", "stateCode": "AL"}, {"name": "Cottonwood", "countryCode": "US", "stateCode": "AL"}, {"name": "Covington County", "countryCode": "US", "stateCode": "AL"}, {"name": "Cowarts", "countryCode": "US", "stateCode": "AL"}, {"name": "Crenshaw County", "countryCode": "US", "stateCode": "AL"}, {"name": "Creola", "countryCode": "US", "stateCode": "AL"}, {"name": "Crossville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Cullman County", "countryCode": "US", "stateCode": "AL"}, {"name": "Dadeville", "countryCode": "US", "stateCode": "AL"}, {"name": "Dale County", "countryCode": "US", "stateCode": "AL"}, {"name": "Daleville", "countryCode": "US", "stateCode": "AL"}, {"name": "Dallas County", "countryCode": "US", "stateCode": "AL"}, {"name": "Danville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Dauphin Island", "countryCode": "US", "stateCode": "AL"}, {"name": "DeKalb County", "countryCode": "US", "stateCode": "AL"}, {"name": "Deatsville", "countryCode": "US", "stateCode": "AL"}, {"name": "Decatur", "countryCode": "US", "stateCode": "AL"}, {"name": "Demopolis", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Double Springs", "countryCode": "US", "stateCode": "AL"}, {"name": "East Brewton", "countryCode": "US", "stateCode": "AL"}, {"name": "East Florence", "countryCode": "US", "stateCode": "AL"}, {"name": "Eclectic", "countryCode": "US", "stateCode": "AL"}, {"name": "Elba", "countryCode": "US", "stateCode": "AL"}, {"name": "Elberta", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Elmore County", "countryCode": "US", "stateCode": "AL"}, {"name": "Emerald Mountain", "countryCode": "US", "stateCode": "AL"}, {"name": "Enterprise", "countryCode": "US", "stateCode": "AL"}, {"name": "Escambia County", "countryCode": "US", "stateCode": "AL"}, {"name": "Etowah County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Eutaw", "countryCode": "US", "stateCode": "AL"}, {"name": "Evergreen", "countryCode": "US", "stateCode": "AL"}, {"name": "Fairfield", "countryCode": "US", "stateCode": "AL"}, {"name": "Fairhope", "countryCode": "US", "stateCode": "AL"}, {"name": "Falkville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Fayette County", "countryCode": "US", "stateCode": "AL"}, {"name": "Fayetteville", "countryCode": "US", "stateCode": "AL"}, {"name": "Flint City", "countryCode": "US", "stateCode": "AL"}, {"name": "Flomaton", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Florence", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Forestdale", "countryCode": "US", "stateCode": "AL"}, {"name": "Fort Deposit", "countryCode": "US", "stateCode": "AL"}, {"name": "Fort Payne", "countryCode": "US", "stateCode": "AL"}, {"name": "Fort Rucker", "countryCode": "US", "stateCode": "AL"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "AL"}, {"name": "Frisco City", "countryCode": "US", "stateCode": "AL"}, {"name": "Fultondale", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Gadsden", "countryCode": "US", "stateCode": "AL"}, {"name": "Gardendale", "countryCode": "US", "stateCode": "AL"}, {"name": "Geneva", "countryCode": "US", "stateCode": "AL"}, {"name": "Geneva County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Glencoe", "countryCode": "US", "stateCode": "AL"}, {"name": "Good Hope", "countryCode": "US", "stateCode": "AL"}, {"name": "Goodwater", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Grand Bay", "countryCode": "US", "stateCode": "AL"}, {"name": "Grayson Valley", "countryCode": "US", "stateCode": "AL"}, {"name": "Graysville", "countryCode": "US", "stateCode": "AL"}, {"name": "Greene County", "countryCode": "US", "stateCode": "AL"}, {"name": "Greensboro", "countryCode": "US", "stateCode": "AL"}, {"name": "Greenville", "countryCode": "US", "stateCode": "AL"}, {"name": "Grove Hill", "countryCode": "US", "stateCode": "AL"}, {"name": "Guin", "countryCode": "US", "stateCode": "AL"}, {"name": "Gulf Shores", "countryCode": "US", "stateCode": "AL"}, {"name": "Guntersville", "countryCode": "US", "stateCode": "AL"}, {"name": "Hackleburg", "countryCode": "US", "stateCode": "AL"}, {"name": "Hale County", "countryCode": "US", "stateCode": "AL"}, {"name": "Haleyville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Hanceville", "countryCode": "US", "stateCode": "AL"}, {"name": "Harpersville", "countryCode": "US", "stateCode": "AL"}, {"name": "Hartford", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Harvest", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Hayneville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Headland", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Helena", "countryCode": "US", "stateCode": "AL"}, {"name": "Henagar", "countryCode": "US", "stateCode": "AL"}, {"name": "Henry County", "countryCode": "US", "stateCode": "AL"}, {"name": "Highland Lakes", "countryCode": "US", "stateCode": "AL"}, {"name": "Hokes Bluff", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Holtville", "countryCode": "US", "stateCode": "AL"}, {"name": "Homewood", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Houston County", "countryCode": "US", "stateCode": "AL"}, {"name": "Hueytown", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Huntsville", "countryCode": "US", "stateCode": "AL"}, {"name": "Indian Springs Village", "countryCode": "US", "stateCode": "AL"}, {"name": "Inverness", "countryCode": "US", "stateCode": "AL"}, {"name": "Irondale", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "AL"}, {"name": "Jacksonville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Ladonia", "countryCode": "US", "stateCode": "AL"}, {"name": "Lafayette", "countryCode": "US", "stateCode": "AL"}, {"name": "Lake Purdy", "countryCode": "US", "stateCode": "AL"}, {"name": "Lake View", "countryCode": "US", "stateCode": "AL"}, {"name": "Lamar County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Lauderdale County", "countryCode": "US", "stateCode": "AL"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "AL"}, {"name": "Lee County", "countryCode": "US", "stateCode": "AL"}, {"name": "Leeds", "countryCode": "US", "stateCode": "AL"}, {"name": "Leesburg", "countryCode": "US", "stateCode": "AL"}, {"name": "Level Plains", "countryCode": "US", "stateCode": "AL"}, {"name": "Limestone County", "countryCode": "US", "stateCode": "AL"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Lineville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Livingston", "countryCode": "US", "stateCode": "AL"}, {"name": "Locust Fork", "countryCode": "US", "stateCode": "AL"}, {"name": "Lowndes County", "countryCode": "US", "stateCode": "AL"}, {"name": "Loxley", "countryCode": "US", "stateCode": "AL"}, {"name": "Luverne", "countryCode": "US", "stateCode": "AL"}, {"name": "Macon County", "countryCode": "US", "stateCode": "AL"}, {"name": "Madison", "countryCode": "US", "stateCode": "AL"}, {"name": "Madison County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Marbury", "countryCode": "US", "stateCode": "AL"}, {"name": "Marengo County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Marion County", "countryCode": "US", "stateCode": "AL"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "AL"}, {"name": "Meadowbrook", "countryCode": "US", "stateCode": "AL"}, {"name": "Meridianville", "countryCode": "US", "stateCode": "AL"}, {"name": "Midfield", "countryCode": "US", "stateCode": "AL"}, {"name": "Midland City", "countryCode": "US", "stateCode": "AL"}, {"name": "Mignon", "countryCode": "US", "stateCode": "AL"}, {"name": "Millbrook", "countryCode": "US", "stateCode": "AL"}, {"name": "Minor", "countryCode": "US", "stateCode": "AL"}, {"name": "Mobile", "countryCode": "US", "stateCode": "AL"}, {"name": "Mobile County", "countryCode": "US", "stateCode": "AL"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "AL"}, {"name": "Monroeville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Moores Mill", "countryCode": "US", "stateCode": "AL"}, {"name": "Morgan County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Moundville", "countryCode": "US", "stateCode": "AL"}, {"name": "Mount Olive", "countryCode": "US", "stateCode": "AL"}, {"name": "Mount Vernon", "countryCode": "US", "stateCode": "AL"}, {"name": "Mountain Brook", "countryCode": "US", "stateCode": "AL"}, {"name": "Munford", "countryCode": "US", "stateCode": "AL"}, {"name": "Muscle Shoals", "countryCode": "US", "stateCode": "AL"}, {"name": "New Brockton", "countryCode": "US", "stateCode": "AL"}, {"name": "New Hope", "countryCode": "US", "stateCode": "AL"}, {"name": "New Market", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "North Bibb", "countryCode": "US", "stateCode": "AL"}, {"name": "Northport", "countryCode": "US", "stateCode": "AL"}, {"name": "Odenville", "countryCode": "US", "stateCode": "AL"}, {"name": "Ohatchee", "countryCode": "US", "stateCode": "AL"}, {"name": "Oneonta", "countryCode": "US", "stateCode": "AL"}, {"name": "Opelika", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Orange Beach", "countryCode": "US", "stateCode": "AL"}, {"name": "Owens Cross Roads", "countryCode": "US", "stateCode": "AL"}, {"name": "Oxford", "countryCode": "US", "stateCode": "AL"}, {"name": "Ozark", "countryCode": "US", "stateCode": "AL"}, {"name": "Pelham", "countryCode": "US", "stateCode": "AL"}, {"name": "Pell City", "countryCode": "US", "stateCode": "AL"}, {"name": "Perry County", "countryCode": "US", "stateCode": "AL"}, {"name": "Phenix City", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Pickens County", "countryCode": "US", "stateCode": "AL"}, {"name": "Piedmont", "countryCode": "US", "stateCode": "AL"}, {"name": "Pike County", "countryCode": "US", "stateCode": "AL"}, {"name": "Pike Road", "countryCode": "US", "stateCode": "AL"}, {"name": "Pine Level", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Pleasant Grove", "countryCode": "US", "stateCode": "AL"}, {"name": "Point Clear", "countryCode": "US", "stateCode": "AL"}, {"name": "Prattville", "countryCode": "US", "stateCode": "AL"}, {"name": "Priceville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Ragland", "countryCode": "US", "stateCode": "AL"}, {"name": "Rainbow City", "countryCode": "US", "stateCode": "AL"}, {"name": "Rainsville", "countryCode": "US", "stateCode": "AL"}, {"name": "Randolph County", "countryCode": "US", "stateCode": "AL"}, {"name": "Red Bay", "countryCode": "US", "stateCode": "AL"}, {"name": "Redstone Arsenal", "countryCode": "US", "stateCode": "AL"}, {"name": "Reform", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Riverside", "countryCode": "US", "stateCode": "AL"}, {"name": "Roanoke", "countryCode": "US", "stateCode": "AL"}, {"name": "Robertsdale", "countryCode": "US", "stateCode": "AL"}, {"name": "Rock Creek", "countryCode": "US", "stateCode": "AL"}, {"name": "Rockford", "countryCode": "US", "stateCode": "AL"}, {"name": "Rogersville", "countryCode": "US", "stateCode": "AL"}, {"name": "Russell County", "countryCode": "US", "stateCode": "AL"}, {"name": "Russellville", "countryCode": "US", "stateCode": "AL"}, {"name": "Saint Clair County", "countryCode": "US", "stateCode": "AL"}, {"name": "Saks", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Sara<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Sardis City", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Scott<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Selmont-West Selmont", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Sheffield", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Shelby County", "countryCode": "US", "stateCode": "AL"}, {"name": "Shoal Creek", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>loc<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Smiths Station", "countryCode": "US", "stateCode": "AL"}, {"name": "Smoke Rise", "countryCode": "US", "stateCode": "AL"}, {"name": "Southside", "countryCode": "US", "stateCode": "AL"}, {"name": "Spanish Fort", "countryCode": "US", "stateCode": "AL"}, {"name": "Springville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Stewartville", "countryCode": "US", "stateCode": "AL"}, {"name": "Sulligent", "countryCode": "US", "stateCode": "AL"}, {"name": "Sumit<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Summerdale", "countryCode": "US", "stateCode": "AL"}, {"name": "Sumter County", "countryCode": "US", "stateCode": "AL"}, {"name": "Sylacauga", "countryCode": "US", "stateCode": "AL"}, {"name": "Sylvan Springs", "countryCode": "US", "stateCode": "AL"}, {"name": "Sylvania", "countryCode": "US", "stateCode": "AL"}, {"name": "Talladega", "countryCode": "US", "stateCode": "AL"}, {"name": "Talladega County", "countryCode": "US", "stateCode": "AL"}, {"name": "Tallapoosa County", "countryCode": "US", "stateCode": "AL"}, {"name": "Tallassee", "countryCode": "US", "stateCode": "AL"}, {"name": "Tarrant", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Thomasville", "countryCode": "US", "stateCode": "AL"}, {"name": "Thor<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Tillmans Corner", "countryCode": "US", "stateCode": "AL"}, {"name": "Town Creek", "countryCode": "US", "stateCode": "AL"}, {"name": "Trinity", "countryCode": "US", "stateCode": "AL"}, {"name": "Troy", "countryCode": "US", "stateCode": "AL"}, {"name": "Trussville", "countryCode": "US", "stateCode": "AL"}, {"name": "Tuscaloosa", "countryCode": "US", "stateCode": "AL"}, {"name": "Tuscaloosa County", "countryCode": "US", "stateCode": "AL"}, {"name": "Tuscumbia", "countryCode": "US", "stateCode": "AL"}, {"name": "Tuskegee", "countryCode": "US", "stateCode": "AL"}, {"name": "Underwood-Petersville", "countryCode": "US", "stateCode": "AL"}, {"name": "Union Springs", "countryCode": "US", "stateCode": "AL"}, {"name": "Uniontown", "countryCode": "US", "stateCode": "AL"}, {"name": "Valley", "countryCode": "US", "stateCode": "AL"}, {"name": "Valley Grande", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Vandiver", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Vestavia Hills", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Walker County", "countryCode": "US", "stateCode": "AL"}, {"name": "Warrior", "countryCode": "US", "stateCode": "AL"}, {"name": "Washington County", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Wedowee", "countryCode": "US", "stateCode": "AL"}, {"name": "West Blocton", "countryCode": "US", "stateCode": "AL"}, {"name": "West End-Cobb Town", "countryCode": "US", "stateCode": "AL"}, {"name": "Westover", "countryCode": "US", "stateCode": "AL"}, {"name": "Wetumpka", "countryCode": "US", "stateCode": "AL"}, {"name": "Whitesboro", "countryCode": "US", "stateCode": "AL"}, {"name": "Wilcox County", "countryCode": "US", "stateCode": "AL"}, {"name": "Wilsonville", "countryCode": "US", "stateCode": "AL"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "AL"}, {"name": "Winston County", "countryCode": "US", "stateCode": "AL"}, {"name": "Woodstock", "countryCode": "US", "stateCode": "AL"}, {"name": "York", "countryCode": "US", "stateCode": "AL"}]