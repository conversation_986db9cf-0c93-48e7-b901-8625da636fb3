[{"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Adair County", "countryCode": "US", "stateCode": "IA"}, {"name": "Adams County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Akron", "countryCode": "US", "stateCode": "IA"}, {"name": "Albia", "countryCode": "US", "stateCode": "IA"}, {"name": "Algona", "countryCode": "US", "stateCode": "IA"}, {"name": "Allamakee County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Alta", "countryCode": "US", "stateCode": "IA"}, {"name": "Alton", "countryCode": "US", "stateCode": "IA"}, {"name": "Altoona", "countryCode": "US", "stateCode": "IA"}, {"name": "Ames", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Anken<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Appanoose County", "countryCode": "US", "stateCode": "IA"}, {"name": "Arnolds Park", "countryCode": "US", "stateCode": "IA"}, {"name": "Asbury", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Atlantic", "countryCode": "US", "stateCode": "IA"}, {"name": "Audubon", "countryCode": "US", "stateCode": "IA"}, {"name": "Audubon County", "countryCode": "US", "stateCode": "IA"}, {"name": "Avoca", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Bedford", "countryCode": "US", "stateCode": "IA"}, {"name": "Belle Plaine", "countryCode": "US", "stateCode": "IA"}, {"name": "Bellevue", "countryCode": "US", "stateCode": "IA"}, {"name": "Belmond", "countryCode": "US", "stateCode": "IA"}, {"name": "Benton County", "countryCode": "US", "stateCode": "IA"}, {"name": "Bettendorf", "countryCode": "US", "stateCode": "IA"}, {"name": "Black Hawk County", "countryCode": "US", "stateCode": "IA"}, {"name": "Bloomfield", "countryCode": "US", "stateCode": "IA"}, {"name": "Blue Grass", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Boone County", "countryCode": "US", "stateCode": "IA"}, {"name": "Bremer County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Brooklyn", "countryCode": "US", "stateCode": "IA"}, {"name": "Buchanan County", "countryCode": "US", "stateCode": "IA"}, {"name": "Buena Vista County", "countryCode": "US", "stateCode": "IA"}, {"name": "Buffalo", "countryCode": "US", "stateCode": "IA"}, {"name": "Buffalo (historical)", "countryCode": "US", "stateCode": "IA"}, {"name": "Burlington", "countryCode": "US", "stateCode": "IA"}, {"name": "Butler County", "countryCode": "US", "stateCode": "IA"}, {"name": "Calhoun County", "countryCode": "US", "stateCode": "IA"}, {"name": "Camanche", "countryCode": "US", "stateCode": "IA"}, {"name": "Carlisle", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Carroll County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Cascade", "countryCode": "US", "stateCode": "IA"}, {"name": "Cass County", "countryCode": "US", "stateCode": "IA"}, {"name": "Cedar County", "countryCode": "US", "stateCode": "IA"}, {"name": "Cedar Falls", "countryCode": "US", "stateCode": "IA"}, {"name": "Cedar Rapids", "countryCode": "US", "stateCode": "IA"}, {"name": "Center Point", "countryCode": "US", "stateCode": "IA"}, {"name": "Centerville", "countryCode": "US", "stateCode": "IA"}, {"name": "Central City", "countryCode": "US", "stateCode": "IA"}, {"name": "Cerro Gordo County", "countryCode": "US", "stateCode": "IA"}, {"name": "Chariton", "countryCode": "US", "stateCode": "IA"}, {"name": "Charles City", "countryCode": "US", "stateCode": "IA"}, {"name": "Cherokee", "countryCode": "US", "stateCode": "IA"}, {"name": "Cherokee County", "countryCode": "US", "stateCode": "IA"}, {"name": "Chickasaw County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Clarke County", "countryCode": "US", "stateCode": "IA"}, {"name": "Clarksville", "countryCode": "US", "stateCode": "IA"}, {"name": "Clay County", "countryCode": "US", "stateCode": "IA"}, {"name": "Clayton County", "countryCode": "US", "stateCode": "IA"}, {"name": "Clear Lake", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Colfax", "countryCode": "US", "stateCode": "IA"}, {"name": "Columbus Junction", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Coon Rapids", "countryCode": "US", "stateCode": "IA"}, {"name": "Coralville", "countryCode": "US", "stateCode": "IA"}, {"name": "Corning", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Council Bluffs", "countryCode": "US", "stateCode": "IA"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "IA"}, {"name": "Cresco", "countryCode": "US", "stateCode": "IA"}, {"name": "Creston", "countryCode": "US", "stateCode": "IA"}, {"name": "Dakota City", "countryCode": "US", "stateCode": "IA"}, {"name": "Dallas Center", "countryCode": "US", "stateCode": "IA"}, {"name": "Dallas County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Davis County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Decatur County", "countryCode": "US", "stateCode": "IA"}, {"name": "Decorah", "countryCode": "US", "stateCode": "IA"}, {"name": "Delaware County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Denver", "countryCode": "US", "stateCode": "IA"}, {"name": "Des Moines", "countryCode": "US", "stateCode": "IA"}, {"name": "Des Moines County", "countryCode": "US", "stateCode": "IA"}, {"name": "Dickinson County", "countryCode": "US", "stateCode": "IA"}, {"name": "Dike", "countryCode": "US", "stateCode": "IA"}, {"name": "Dubuque", "countryCode": "US", "stateCode": "IA"}, {"name": "Dubuque County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Dyersville", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Eagle Grove", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Eddyville", "countryCode": "US", "stateCode": "IA"}, {"name": "Eldora", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Elk Run Heights", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Ely", "countryCode": "US", "stateCode": "IA"}, {"name": "Emmet County", "countryCode": "US", "stateCode": "IA"}, {"name": "Emmetsburg", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Estherville", "countryCode": "US", "stateCode": "IA"}, {"name": "Evansdale", "countryCode": "US", "stateCode": "IA"}, {"name": "Fairbank", "countryCode": "US", "stateCode": "IA"}, {"name": "Fairfax", "countryCode": "US", "stateCode": "IA"}, {"name": "Fairfield", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Fayette County", "countryCode": "US", "stateCode": "IA"}, {"name": "Floyd County", "countryCode": "US", "stateCode": "IA"}, {"name": "Forest City", "countryCode": "US", "stateCode": "IA"}, {"name": "Fort Dodge", "countryCode": "US", "stateCode": "IA"}, {"name": "Fort Madison", "countryCode": "US", "stateCode": "IA"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "IA"}, {"name": "Fremont County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Glenwood", "countryCode": "US", "stateCode": "IA"}, {"name": "Glidden", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Greene County", "countryCode": "US", "stateCode": "IA"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Grundy Center", "countryCode": "US", "stateCode": "IA"}, {"name": "Grundy County", "countryCode": "US", "stateCode": "IA"}, {"name": "Guthrie Center", "countryCode": "US", "stateCode": "IA"}, {"name": "Guthrie County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Hamburg", "countryCode": "US", "stateCode": "IA"}, {"name": "Hamilton County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Hancock County", "countryCode": "US", "stateCode": "IA"}, {"name": "Hardin County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Harrison County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Hawarden", "countryCode": "US", "stateCode": "IA"}, {"name": "Henry County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Holstein", "countryCode": "US", "stateCode": "IA"}, {"name": "Howard County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Hull", "countryCode": "US", "stateCode": "IA"}, {"name": "Humboldt", "countryCode": "US", "stateCode": "IA"}, {"name": "Humboldt County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Ida County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Independence", "countryCode": "US", "stateCode": "IA"}, {"name": "Indianola", "countryCode": "US", "stateCode": "IA"}, {"name": "Iowa City", "countryCode": "US", "stateCode": "IA"}, {"name": "Iowa County", "countryCode": "US", "stateCode": "IA"}, {"name": "Iowa Falls", "countryCode": "US", "stateCode": "IA"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "IA"}, {"name": "Jasper County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Jewell", "countryCode": "US", "stateCode": "IA"}, {"name": "Johnson County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Jones County", "countryCode": "US", "stateCode": "IA"}, {"name": "Kalona", "countryCode": "US", "stateCode": "IA"}, {"name": "Keokuk", "countryCode": "US", "stateCode": "IA"}, {"name": "Keokuk County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Knoxville", "countryCode": "US", "stateCode": "IA"}, {"name": "Kossuth County", "countryCode": "US", "stateCode": "IA"}, {"name": "La Porte City", "countryCode": "US", "stateCode": "IA"}, {"name": "Lake City", "countryCode": "US", "stateCode": "IA"}, {"name": "Lake Mills", "countryCode": "US", "stateCode": "IA"}, {"name": "Lake Panorama", "countryCode": "US", "stateCode": "IA"}, {"name": "Lake Park", "countryCode": "US", "stateCode": "IA"}, {"name": "Lake View", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Le Mars", "countryCode": "US", "stateCode": "IA"}, {"name": "Lee County", "countryCode": "US", "stateCode": "IA"}, {"name": "Lenox", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Linn County", "countryCode": "US", "stateCode": "IA"}, {"name": "Lisbon", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Lone Tree", "countryCode": "US", "stateCode": "IA"}, {"name": "Louisa County", "countryCode": "US", "stateCode": "IA"}, {"name": "Lucas County", "countryCode": "US", "stateCode": "IA"}, {"name": "Lyon County", "countryCode": "US", "stateCode": "IA"}, {"name": "Madison County", "countryCode": "US", "stateCode": "IA"}, {"name": "Madrid", "countryCode": "US", "stateCode": "IA"}, {"name": "Maharishi Vedic City", "countryCode": "US", "stateCode": "IA"}, {"name": "Mahaska County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Manchester", "countryCode": "US", "stateCode": "IA"}, {"name": "Manly", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>ton", "countryCode": "US", "stateCode": "IA"}, {"name": "Maquoketa", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Marengo", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Marion County", "countryCode": "US", "stateCode": "IA"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "IA"}, {"name": "Marshalltown", "countryCode": "US", "stateCode": "IA"}, {"name": "Mason City", "countryCode": "US", "stateCode": "IA"}, {"name": "Mechanicsville", "countryCode": "US", "stateCode": "IA"}, {"name": "Mediapolis", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>-<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Milford", "countryCode": "US", "stateCode": "IA"}, {"name": "Mills County", "countryCode": "US", "stateCode": "IA"}, {"name": "Missouri Valley", "countryCode": "US", "stateCode": "IA"}, {"name": "Mitchell County", "countryCode": "US", "stateCode": "IA"}, {"name": "Mitchellville", "countryCode": "US", "stateCode": "IA"}, {"name": "Monona", "countryCode": "US", "stateCode": "IA"}, {"name": "Monona County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Mount Ayr", "countryCode": "US", "stateCode": "IA"}, {"name": "Mount Pleasant", "countryCode": "US", "stateCode": "IA"}, {"name": "Mount Vernon", "countryCode": "US", "stateCode": "IA"}, {"name": "Moville", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Muscatine County", "countryCode": "US", "stateCode": "IA"}, {"name": "Nashua", "countryCode": "US", "stateCode": "IA"}, {"name": "Nevada", "countryCode": "US", "stateCode": "IA"}, {"name": "New Hampton", "countryCode": "US", "stateCode": "IA"}, {"name": "New London", "countryCode": "US", "stateCode": "IA"}, {"name": "New Sharon", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Nora Springs", "countryCode": "US", "stateCode": "IA"}, {"name": "North English", "countryCode": "US", "stateCode": "IA"}, {"name": "North Liberty", "countryCode": "US", "stateCode": "IA"}, {"name": "Northwood", "countryCode": "US", "stateCode": "IA"}, {"name": "Norwalk", "countryCode": "US", "stateCode": "IA"}, {"name": "O'Brien County", "countryCode": "US", "stateCode": "IA"}, {"name": "Oakland", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Orange City", "countryCode": "US", "stateCode": "IA"}, {"name": "Osage", "countryCode": "US", "stateCode": "IA"}, {"name": "Osceola", "countryCode": "US", "stateCode": "IA"}, {"name": "Osceola County", "countryCode": "US", "stateCode": "IA"}, {"name": "Oskaloosa", "countryCode": "US", "stateCode": "IA"}, {"name": "Ottumwa", "countryCode": "US", "stateCode": "IA"}, {"name": "Page County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Palo Alto County", "countryCode": "US", "stateCode": "IA"}, {"name": "Panora", "countryCode": "US", "stateCode": "IA"}, {"name": "Park View", "countryCode": "US", "stateCode": "IA"}, {"name": "Parkersburg", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Peosta", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Pleasant Hill", "countryCode": "US", "stateCode": "IA"}, {"name": "Pleasantville", "countryCode": "US", "stateCode": "IA"}, {"name": "Plymouth County", "countryCode": "US", "stateCode": "IA"}, {"name": "Pocahontas", "countryCode": "US", "stateCode": "IA"}, {"name": "Pocahontas County", "countryCode": "US", "stateCode": "IA"}, {"name": "Polk City", "countryCode": "US", "stateCode": "IA"}, {"name": "Polk County", "countryCode": "US", "stateCode": "IA"}, {"name": "Postville", "countryCode": "US", "stateCode": "IA"}, {"name": "Pottawattamie County", "countryCode": "US", "stateCode": "IA"}, {"name": "Poweshiek County", "countryCode": "US", "stateCode": "IA"}, {"name": "Prairie City", "countryCode": "US", "stateCode": "IA"}, {"name": "Preston", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Red Oak", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Ringgold County", "countryCode": "US", "stateCode": "IA"}, {"name": "Riverside", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Rock Rapids", "countryCode": "US", "stateCode": "IA"}, {"name": "Rock Valley", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Rockwell City", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Sac City", "countryCode": "US", "stateCode": "IA"}, {"name": "Sac County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Sanborn", "countryCode": "US", "stateCode": "IA"}, {"name": "Saylorville", "countryCode": "US", "stateCode": "IA"}, {"name": "Scott County", "countryCode": "US", "stateCode": "IA"}, {"name": "Sergeant <PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Sheffield", "countryCode": "US", "stateCode": "IA"}, {"name": "Shelby County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Shell Rock", "countryCode": "US", "stateCode": "IA"}, {"name": "Shenandoah", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Sioux Center", "countryCode": "US", "stateCode": "IA"}, {"name": "Sioux City", "countryCode": "US", "stateCode": "IA"}, {"name": "Sioux County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Solon", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Spirit Lake", "countryCode": "US", "stateCode": "IA"}, {"name": "Springville", "countryCode": "US", "stateCode": "IA"}, {"name": "State Center", "countryCode": "US", "stateCode": "IA"}, {"name": "Storm Lake", "countryCode": "US", "stateCode": "IA"}, {"name": "Story City", "countryCode": "US", "stateCode": "IA"}, {"name": "Story County", "countryCode": "US", "stateCode": "IA"}, {"name": "Strawberry Point", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Tama County", "countryCode": "US", "stateCode": "IA"}, {"name": "Taylor County", "countryCode": "US", "stateCode": "IA"}, {"name": "Tiffin", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Toledo", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Tripoli", "countryCode": "US", "stateCode": "IA"}, {"name": "Union County", "countryCode": "US", "stateCode": "IA"}, {"name": "University Heights", "countryCode": "US", "stateCode": "IA"}, {"name": "Urbana", "countryCode": "US", "stateCode": "IA"}, {"name": "Urbandale", "countryCode": "US", "stateCode": "IA"}, {"name": "Van Buren County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Villisca", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Walford", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Wapello County", "countryCode": "US", "stateCode": "IA"}, {"name": "Warren County", "countryCode": "US", "stateCode": "IA"}, {"name": "Washington", "countryCode": "US", "stateCode": "IA"}, {"name": "Washington County", "countryCode": "US", "stateCode": "IA"}, {"name": "Waterloo", "countryCode": "US", "stateCode": "IA"}, {"name": "Waukee", "countryCode": "US", "stateCode": "IA"}, {"name": "Waukon", "countryCode": "US", "stateCode": "IA"}, {"name": "Waverly", "countryCode": "US", "stateCode": "IA"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "IA"}, {"name": "Webster City", "countryCode": "US", "stateCode": "IA"}, {"name": "Webster County", "countryCode": "US", "stateCode": "IA"}, {"name": "Wellman", "countryCode": "US", "stateCode": "IA"}, {"name": "West Branch", "countryCode": "US", "stateCode": "IA"}, {"name": "West Burlington", "countryCode": "US", "stateCode": "IA"}, {"name": "West Des Moines", "countryCode": "US", "stateCode": "IA"}, {"name": "West Liberty", "countryCode": "US", "stateCode": "IA"}, {"name": "West Union", "countryCode": "US", "stateCode": "IA"}, {"name": "Williamsburg", "countryCode": "US", "stateCode": "IA"}, {"name": "Wilton", "countryCode": "US", "stateCode": "IA"}, {"name": "Windsor Heights", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Winnebago County", "countryCode": "US", "stateCode": "IA"}, {"name": "Winneshiek County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>bine", "countryCode": "US", "stateCode": "IA"}, {"name": "Woodbury County", "countryCode": "US", "stateCode": "IA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IA"}, {"name": "Worth County", "countryCode": "US", "stateCode": "IA"}, {"name": "Wright County", "countryCode": "US", "stateCode": "IA"}]