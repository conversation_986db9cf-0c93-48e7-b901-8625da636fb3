[{"name": "Aberdeen", "countryCode": "US", "stateCode": "IN", "latitude": "41.43893000", "longitude": "-87.11142000"}, {"name": "Adams County", "countryCode": "US", "stateCode": "IN", "latitude": "40.74566000", "longitude": "-84.93665000"}, {"name": "Akron", "countryCode": "US", "stateCode": "IN", "latitude": "41.03838000", "longitude": "-86.02805000"}, {"name": "Albany", "countryCode": "US", "stateCode": "IN", "latitude": "40.30088000", "longitude": "-85.24191000"}, {"name": "Albion", "countryCode": "US", "stateCode": "IN", "latitude": "41.39560000", "longitude": "-85.42442000"}, {"name": "Alexandria", "countryCode": "US", "stateCode": "IN", "latitude": "40.26282000", "longitude": "-85.67581000"}, {"name": "Allen County", "countryCode": "US", "stateCode": "IN", "latitude": "41.09087000", "longitude": "-85.06656000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.10532000", "longitude": "-85.68025000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.86254000", "longitude": "-85.60165000"}, {"name": "Angola", "countryCode": "US", "stateCode": "IN", "latitude": "41.63477000", "longitude": "-84.99941000"}, {"name": "Arcadia", "countryCode": "US", "stateCode": "IN", "latitude": "40.17587000", "longitude": "-86.02165000"}, {"name": "Argos", "countryCode": "US", "stateCode": "IN", "latitude": "41.23780000", "longitude": "-86.21465000"}, {"name": "Attica", "countryCode": "US", "stateCode": "IN", "latitude": "40.29420000", "longitude": "-87.24890000"}, {"name": "Auburn", "countryCode": "US", "stateCode": "IN", "latitude": "41.36699000", "longitude": "-85.05886000"}, {"name": "Aurora", "countryCode": "US", "stateCode": "IN", "latitude": "39.05700000", "longitude": "-84.90134000"}, {"name": "Austin", "countryCode": "US", "stateCode": "IN", "latitude": "38.75839000", "longitude": "-85.80803000"}, {"name": "Avilla", "countryCode": "US", "stateCode": "IN", "latitude": "41.36588000", "longitude": "-85.23886000"}, {"name": "Avon", "countryCode": "US", "stateCode": "IN", "latitude": "39.76282000", "longitude": "-86.39972000"}, {"name": "Bargersville", "countryCode": "US", "stateCode": "IN", "latitude": "39.52088000", "longitude": "-86.16777000"}, {"name": "Bartholomew County", "countryCode": "US", "stateCode": "IN", "latitude": "39.20597000", "longitude": "-85.89760000"}, {"name": "Bass Lake", "countryCode": "US", "stateCode": "IN", "latitude": "41.20726000", "longitude": "-86.60196000"}, {"name": "Batesville", "countryCode": "US", "stateCode": "IN", "latitude": "39.30005000", "longitude": "-85.22218000"}, {"name": "Battle Ground", "countryCode": "US", "stateCode": "IN", "latitude": "40.50837000", "longitude": "-86.84168000"}, {"name": "Bedford", "countryCode": "US", "stateCode": "IN", "latitude": "38.86116000", "longitude": "-86.48721000"}, {"name": "Beech Grove", "countryCode": "US", "stateCode": "IN", "latitude": "39.72199000", "longitude": "-86.08998000"}, {"name": "Benton County", "countryCode": "US", "stateCode": "IN", "latitude": "40.60626000", "longitude": "-87.31091000"}, {"name": "Bern<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.65782000", "longitude": "-84.95191000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.77421000", "longitude": "-87.30779000"}, {"name": "Blackford County", "countryCode": "US", "stateCode": "IN", "latitude": "40.47360000", "longitude": "-85.32482000"}, {"name": "Bloomfield", "countryCode": "US", "stateCode": "IN", "latitude": "39.02699000", "longitude": "-86.93751000"}, {"name": "Bloomington", "countryCode": "US", "stateCode": "IN", "latitude": "39.16533000", "longitude": "-86.52639000"}, {"name": "Bluffton", "countryCode": "US", "stateCode": "IN", "latitude": "40.73866000", "longitude": "-85.17164000"}, {"name": "Boone County", "countryCode": "US", "stateCode": "IN", "latitude": "40.05080000", "longitude": "-86.46870000"}, {"name": "Boonville", "countryCode": "US", "stateCode": "IN", "latitude": "38.04921000", "longitude": "-87.27417000"}, {"name": "Bourbon", "countryCode": "US", "stateCode": "IN", "latitude": "41.29560000", "longitude": "-86.11639000"}, {"name": "Brazil", "countryCode": "US", "stateCode": "IN", "latitude": "39.52365000", "longitude": "-87.12502000"}, {"name": "Bremen", "countryCode": "US", "stateCode": "IN", "latitude": "41.44644000", "longitude": "-86.14806000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.21839000", "longitude": "-84.85606000"}, {"name": "Bristol", "countryCode": "US", "stateCode": "IN", "latitude": "41.72144000", "longitude": "-85.81749000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.86671000", "longitude": "-86.14165000"}, {"name": "Brooklyn", "countryCode": "US", "stateCode": "IN", "latitude": "39.53921000", "longitude": "-86.36916000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.60281000", "longitude": "-86.86723000"}, {"name": "Brookville", "countryCode": "US", "stateCode": "IN", "latitude": "39.42311000", "longitude": "-85.01274000"}, {"name": "Brown County", "countryCode": "US", "stateCode": "IN", "latitude": "39.19621000", "longitude": "-86.22737000"}, {"name": "Brownsburg", "countryCode": "US", "stateCode": "IN", "latitude": "39.84338000", "longitude": "-86.39777000"}, {"name": "Brownstown", "countryCode": "US", "stateCode": "IN", "latitude": "38.87894000", "longitude": "-86.04192000"}, {"name": "Burns Harbor", "countryCode": "US", "stateCode": "IN", "latitude": "41.62587000", "longitude": "-87.13337000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.42977000", "longitude": "-84.87135000"}, {"name": "Cambridge City", "countryCode": "US", "stateCode": "IN", "latitude": "39.81255000", "longitude": "-85.17163000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "37.91144000", "longitude": "-86.74443000"}, {"name": "Carmel", "countryCode": "US", "stateCode": "IN", "latitude": "39.97837000", "longitude": "-86.11804000"}, {"name": "Carroll County", "countryCode": "US", "stateCode": "IN", "latitude": "40.58286000", "longitude": "-86.56348000"}, {"name": "Cass County", "countryCode": "US", "stateCode": "IN", "latitude": "40.76149000", "longitude": "-86.34595000"}, {"name": "Cayuga", "countryCode": "US", "stateCode": "IN", "latitude": "39.94865000", "longitude": "-87.45974000"}, {"name": "Cedar Lake", "countryCode": "US", "stateCode": "IN", "latitude": "41.36476000", "longitude": "-87.44115000"}, {"name": "Centerville", "countryCode": "US", "stateCode": "IN", "latitude": "39.81782000", "longitude": "-84.99635000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.04171000", "longitude": "-87.36806000"}, {"name": "Charlestown", "countryCode": "US", "stateCode": "IN", "latitude": "38.45312000", "longitude": "-85.67024000"}, {"name": "Chesterfield", "countryCode": "US", "stateCode": "IN", "latitude": "40.11254000", "longitude": "-85.59692000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.61059000", "longitude": "-87.06420000"}, {"name": "Churubusco", "countryCode": "US", "stateCode": "IN", "latitude": "41.23060000", "longitude": "-85.31942000"}, {"name": "Cicero", "countryCode": "US", "stateCode": "IN", "latitude": "40.12393000", "longitude": "-86.01332000"}, {"name": "Clark County", "countryCode": "US", "stateCode": "IN", "latitude": "38.47718000", "longitude": "-85.70728000"}, {"name": "Clarksville", "countryCode": "US", "stateCode": "IN", "latitude": "38.29674000", "longitude": "-85.75996000"}, {"name": "Clay County", "countryCode": "US", "stateCode": "IN", "latitude": "39.39273000", "longitude": "-87.11576000"}, {"name": "Clermont", "countryCode": "US", "stateCode": "IN", "latitude": "39.80977000", "longitude": "-86.32249000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.65698000", "longitude": "-87.39807000"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "IN", "latitude": "40.30169000", "longitude": "-86.47516000"}, {"name": "Cloverdale", "countryCode": "US", "stateCode": "IN", "latitude": "39.51477000", "longitude": "-86.79390000"}, {"name": "Columbia City", "countryCode": "US", "stateCode": "IN", "latitude": "41.15727000", "longitude": "-85.48831000"}, {"name": "Columbus", "countryCode": "US", "stateCode": "IN", "latitude": "39.20144000", "longitude": "-85.92138000"}, {"name": "Connersville", "countryCode": "US", "stateCode": "IN", "latitude": "39.64116000", "longitude": "-85.14107000"}, {"name": "Converse", "countryCode": "US", "stateCode": "IN", "latitude": "40.57754000", "longitude": "-85.87332000"}, {"name": "Cordry Sweetwater Lakes", "countryCode": "US", "stateCode": "IN", "latitude": "39.30464000", "longitude": "-86.11837000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.21201000", "longitude": "-86.12192000"}, {"name": "Country Squire Lakes", "countryCode": "US", "stateCode": "IN", "latitude": "39.03478000", "longitude": "-85.69858000"}, {"name": "Covington", "countryCode": "US", "stateCode": "IN", "latitude": "40.14170000", "longitude": "-87.39474000"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "IN", "latitude": "38.29241000", "longitude": "-86.45171000"}, {"name": "Crawfordsville", "countryCode": "US", "stateCode": "IN", "latitude": "40.04115000", "longitude": "-86.87445000"}, {"name": "Crothersville", "countryCode": "US", "stateCode": "IN", "latitude": "38.80061000", "longitude": "-85.84164000"}, {"name": "Crown Point", "countryCode": "US", "stateCode": "IN", "latitude": "41.41698000", "longitude": "-87.36531000"}, {"name": "Culver", "countryCode": "US", "stateCode": "IN", "latitude": "41.21893000", "longitude": "-86.42306000"}, {"name": "Cumberland", "countryCode": "US", "stateCode": "IN", "latitude": "39.77615000", "longitude": "-85.95720000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.16894000", "longitude": "-86.99000000"}, {"name": "Daleville", "countryCode": "US", "stateCode": "IN", "latitude": "40.12115000", "longitude": "-85.55803000"}, {"name": "Danville", "countryCode": "US", "stateCode": "IN", "latitude": "39.76060000", "longitude": "-86.52639000"}, {"name": "Darmstadt", "countryCode": "US", "stateCode": "IN", "latitude": "38.09921000", "longitude": "-87.57891000"}, {"name": "Daviess County", "countryCode": "US", "stateCode": "IN", "latitude": "38.70241000", "longitude": "-87.07207000"}, {"name": "Dayton", "countryCode": "US", "stateCode": "IN", "latitude": "40.37420000", "longitude": "-86.76890000"}, {"name": "DeKalb County", "countryCode": "US", "stateCode": "IN", "latitude": "41.39758000", "longitude": "-84.99909000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.19504000", "longitude": "-87.19864000"}, {"name": "Dearborn County", "countryCode": "US", "stateCode": "IN", "latitude": "39.14519000", "longitude": "-84.97326000"}, {"name": "Decatur", "countryCode": "US", "stateCode": "IN", "latitude": "40.83060000", "longitude": "-84.92913000"}, {"name": "Decatur County", "countryCode": "US", "stateCode": "IN", "latitude": "39.30700000", "longitude": "-85.50114000"}, {"name": "Delaware County", "countryCode": "US", "stateCode": "IN", "latitude": "40.22753000", "longitude": "-85.39690000"}, {"name": "Delphi", "countryCode": "US", "stateCode": "IN", "latitude": "40.58754000", "longitude": "-86.67501000"}, {"name": "Dillsboro", "countryCode": "US", "stateCode": "IN", "latitude": "39.01783000", "longitude": "-85.05884000"}, {"name": "Dubois County", "countryCode": "US", "stateCode": "IN", "latitude": "38.36428000", "longitude": "-86.87980000"}, {"name": "Dunkirk", "countryCode": "US", "stateCode": "IN", "latitude": "40.75643000", "longitude": "-86.39361000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.63783000", "longitude": "-85.92166000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.49420000", "longitude": "-87.52171000"}, {"name": "East Chicago", "countryCode": "US", "stateCode": "IN", "latitude": "41.63920000", "longitude": "-87.45476000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.34032000", "longitude": "-85.35080000"}, {"name": "Edgewood", "countryCode": "US", "stateCode": "IN", "latitude": "40.10337000", "longitude": "-85.73414000"}, {"name": "Edinburgh", "countryCode": "US", "stateCode": "IN", "latitude": "39.35422000", "longitude": "-85.96666000"}, {"name": "Elkhart", "countryCode": "US", "stateCode": "IN", "latitude": "41.68199000", "longitude": "-85.97667000"}, {"name": "Elkhart County", "countryCode": "US", "stateCode": "IN", "latitude": "41.59738000", "longitude": "-85.85876000"}, {"name": "Ellettsville", "countryCode": "US", "stateCode": "IN", "latitude": "39.23393000", "longitude": "-86.62500000"}, {"name": "Elwood", "countryCode": "US", "stateCode": "IN", "latitude": "40.27698000", "longitude": "-85.84192000"}, {"name": "English", "countryCode": "US", "stateCode": "IN", "latitude": "38.33450000", "longitude": "-86.46415000"}, {"name": "Evansville", "countryCode": "US", "stateCode": "IN", "latitude": "37.97476000", "longitude": "-87.55585000"}, {"name": "Fairfield Heights", "countryCode": "US", "stateCode": "IN", "latitude": "39.82861000", "longitude": "-86.38224000"}, {"name": "Fairmount", "countryCode": "US", "stateCode": "IN", "latitude": "40.41532000", "longitude": "-85.65053000"}, {"name": "Fairview Park", "countryCode": "US", "stateCode": "IN", "latitude": "39.68031000", "longitude": "-87.41752000"}, {"name": "Farmersburg", "countryCode": "US", "stateCode": "IN", "latitude": "39.24865000", "longitude": "-87.38196000"}, {"name": "Farmland", "countryCode": "US", "stateCode": "IN", "latitude": "40.18782000", "longitude": "-85.12747000"}, {"name": "Fayette County", "countryCode": "US", "stateCode": "IN", "latitude": "39.64006000", "longitude": "-85.17873000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.22394000", "longitude": "-86.86222000"}, {"name": "Fish Lake", "countryCode": "US", "stateCode": "IN", "latitude": "41.56671000", "longitude": "-86.55196000"}, {"name": "<PERSON>s", "countryCode": "US", "stateCode": "IN", "latitude": "39.95559000", "longitude": "-86.01387000"}, {"name": "Flora", "countryCode": "US", "stateCode": "IN", "latitude": "40.54726000", "longitude": "-86.52444000"}, {"name": "Floyd County", "countryCode": "US", "stateCode": "IN", "latitude": "38.31891000", "longitude": "-85.90687000"}, {"name": "Fort Branch", "countryCode": "US", "stateCode": "IN", "latitude": "38.25116000", "longitude": "-87.58113000"}, {"name": "Fort Wayne", "countryCode": "US", "stateCode": "IN", "latitude": "41.13060000", "longitude": "-85.12886000"}, {"name": "Fortville", "countryCode": "US", "stateCode": "IN", "latitude": "39.93226000", "longitude": "-85.84804000"}, {"name": "Fountain County", "countryCode": "US", "stateCode": "IN", "latitude": "40.12087000", "longitude": "-87.24199000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.61670000", "longitude": "-87.32085000"}, {"name": "Frankfort", "countryCode": "US", "stateCode": "IN", "latitude": "40.27948000", "longitude": "-86.51084000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.48061000", "longitude": "-86.05499000"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "IN", "latitude": "39.41486000", "longitude": "-85.06028000"}, {"name": "<PERSON>ton", "countryCode": "US", "stateCode": "IN", "latitude": "40.22282000", "longitude": "-85.77887000"}, {"name": "Fremont", "countryCode": "US", "stateCode": "IN", "latitude": "41.73088000", "longitude": "-84.93274000"}, {"name": "French Lick", "countryCode": "US", "stateCode": "IN", "latitude": "38.54894000", "longitude": "-86.61999000"}, {"name": "Fulton County", "countryCode": "US", "stateCode": "IN", "latitude": "41.04696000", "longitude": "-86.26358000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.35173000", "longitude": "-85.94164000"}, {"name": "Galveston", "countryCode": "US", "stateCode": "IN", "latitude": "40.57893000", "longitude": "-86.19027000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.34949000", "longitude": "-85.13553000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.59337000", "longitude": "-87.34643000"}, {"name": "Gas City", "countryCode": "US", "stateCode": "IN", "latitude": "40.48726000", "longitude": "-85.61303000"}, {"name": "Geneva", "countryCode": "US", "stateCode": "IN", "latitude": "40.59199000", "longitude": "-84.95719000"}, {"name": "Georgetown", "countryCode": "US", "stateCode": "IN", "latitude": "40.74060000", "longitude": "-86.50473000"}, {"name": "Gibson County", "countryCode": "US", "stateCode": "IN", "latitude": "38.31183000", "longitude": "-87.58459000"}, {"name": "Goodland", "countryCode": "US", "stateCode": "IN", "latitude": "40.76337000", "longitude": "-87.29363000"}, {"name": "Goshen", "countryCode": "US", "stateCode": "IN", "latitude": "41.58227000", "longitude": "-85.83444000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.21088000", "longitude": "-84.96691000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.75338000", "longitude": "-86.11084000"}, {"name": "Grant County", "countryCode": "US", "stateCode": "IN", "latitude": "40.51584000", "longitude": "-85.65473000"}, {"name": "Greencastle", "countryCode": "US", "stateCode": "IN", "latitude": "39.64449000", "longitude": "-86.86473000"}, {"name": "Greendale", "countryCode": "US", "stateCode": "IN", "latitude": "39.11256000", "longitude": "-84.86412000"}, {"name": "Greene County", "countryCode": "US", "stateCode": "IN", "latitude": "39.03633000", "longitude": "-86.96205000"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "IN", "latitude": "39.78504000", "longitude": "-85.76942000"}, {"name": "Greensburg", "countryCode": "US", "stateCode": "IN", "latitude": "39.33727000", "longitude": "-85.48358000"}, {"name": "Greentown", "countryCode": "US", "stateCode": "IN", "latitude": "40.47809000", "longitude": "-85.96665000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.61366000", "longitude": "-86.10665000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.52837000", "longitude": "-87.42365000"}, {"name": "Grissom Air Force Base", "countryCode": "US", "stateCode": "IN", "latitude": "40.65753000", "longitude": "-86.14755000"}, {"name": "Gulivoire Park", "countryCode": "US", "stateCode": "IN", "latitude": "41.61338000", "longitude": "-86.24528000"}, {"name": "Hagerstown", "countryCode": "US", "stateCode": "IN", "latitude": "39.91116000", "longitude": "-85.16163000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.53366000", "longitude": "-84.91274000"}, {"name": "Hamilton County", "countryCode": "US", "stateCode": "IN", "latitude": "40.07249000", "longitude": "-86.05201000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.58337000", "longitude": "-87.50004000"}, {"name": "Hancock County", "countryCode": "US", "stateCode": "IN", "latitude": "39.82355000", "longitude": "-85.77324000"}, {"name": "Hanover", "countryCode": "US", "stateCode": "IN", "latitude": "38.71423000", "longitude": "-85.47357000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.19616000", "longitude": "-84.91969000"}, {"name": "Harrison County", "countryCode": "US", "stateCode": "IN", "latitude": "38.19512000", "longitude": "-86.11131000"}, {"name": "Hartford City", "countryCode": "US", "stateCode": "IN", "latitude": "40.45115000", "longitude": "-85.36997000"}, {"name": "Haubstadt", "countryCode": "US", "stateCode": "IN", "latitude": "38.20504000", "longitude": "-87.57419000"}, {"name": "Hebron", "countryCode": "US", "stateCode": "IN", "latitude": "41.31865000", "longitude": "-87.20031000"}, {"name": "Hendricks County", "countryCode": "US", "stateCode": "IN", "latitude": "39.76952000", "longitude": "-86.50998000"}, {"name": "Henry County", "countryCode": "US", "stateCode": "IN", "latitude": "39.93104000", "longitude": "-85.39644000"}, {"name": "Henryville", "countryCode": "US", "stateCode": "IN", "latitude": "38.54173000", "longitude": "-85.76774000"}, {"name": "Heritage Lake", "countryCode": "US", "stateCode": "IN", "latitude": "39.72779000", "longitude": "-86.71022000"}, {"name": "Hidden Valley", "countryCode": "US", "stateCode": "IN", "latitude": "39.16228000", "longitude": "-84.84301000"}, {"name": "Highland", "countryCode": "US", "stateCode": "IN", "latitude": "41.55365000", "longitude": "-87.45198000"}, {"name": "Hobart", "countryCode": "US", "stateCode": "IN", "latitude": "41.53226000", "longitude": "-87.25504000"}, {"name": "Hope", "countryCode": "US", "stateCode": "IN", "latitude": "39.30394000", "longitude": "-85.77137000"}, {"name": "Howard County", "countryCode": "US", "stateCode": "IN", "latitude": "40.48359000", "longitude": "-86.11693000"}, {"name": "Hudson Lake", "countryCode": "US", "stateCode": "IN", "latitude": "41.71032000", "longitude": "-86.53419000"}, {"name": "Huntertown", "countryCode": "US", "stateCode": "IN", "latitude": "41.22838000", "longitude": "-85.17247000"}, {"name": "Huntingburg", "countryCode": "US", "stateCode": "IN", "latitude": "38.29894000", "longitude": "-86.95500000"}, {"name": "Huntington", "countryCode": "US", "stateCode": "IN", "latitude": "40.88310000", "longitude": "-85.49748000"}, {"name": "Huntington County", "countryCode": "US", "stateCode": "IN", "latitude": "40.82924000", "longitude": "-85.48817000"}, {"name": "Indian Heights", "countryCode": "US", "stateCode": "IN", "latitude": "40.42726000", "longitude": "-86.12555000"}, {"name": "Indianapolis", "countryCode": "US", "stateCode": "IN", "latitude": "39.76838000", "longitude": "-86.15804000"}, {"name": "Ingalls", "countryCode": "US", "stateCode": "IN", "latitude": "39.95699000", "longitude": "-85.80526000"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "IN", "latitude": "38.90642000", "longitude": "-86.03754000"}, {"name": "Jasonville", "countryCode": "US", "stateCode": "IN", "latitude": "39.16310000", "longitude": "-87.19918000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.39144000", "longitude": "-86.93111000"}, {"name": "Jasper County", "countryCode": "US", "stateCode": "IN", "latitude": "41.02300000", "longitude": "-87.11612000"}, {"name": "Jay County", "countryCode": "US", "stateCode": "IN", "latitude": "40.43792000", "longitude": "-85.00564000"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "IN", "latitude": "38.78582000", "longitude": "-85.43857000"}, {"name": "Jeffersonville", "countryCode": "US", "stateCode": "IN", "latitude": "38.27757000", "longitude": "-85.73718000"}, {"name": "Jennings County", "countryCode": "US", "stateCode": "IN", "latitude": "38.99693000", "longitude": "-85.62806000"}, {"name": "Johnson County", "countryCode": "US", "stateCode": "IN", "latitude": "39.48997000", "longitude": "-86.10164000"}, {"name": "Jonesboro", "countryCode": "US", "stateCode": "IN", "latitude": "40.47976000", "longitude": "-85.62775000"}, {"name": "Kendallville", "countryCode": "US", "stateCode": "IN", "latitude": "41.44144000", "longitude": "-85.26498000"}, {"name": "Kentland", "countryCode": "US", "stateCode": "IN", "latitude": "40.77032000", "longitude": "-87.44530000"}, {"name": "Kingsford Heights", "countryCode": "US", "stateCode": "IN", "latitude": "41.48060000", "longitude": "-86.69169000"}, {"name": "Knightstown", "countryCode": "US", "stateCode": "IN", "latitude": "39.79560000", "longitude": "-85.52636000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.29588000", "longitude": "-86.62501000"}, {"name": "Knox County", "countryCode": "US", "stateCode": "IN", "latitude": "38.68909000", "longitude": "-87.41801000"}, {"name": "Kokomo", "countryCode": "US", "stateCode": "IN", "latitude": "40.48643000", "longitude": "-86.13360000"}, {"name": "Koontz Lake", "countryCode": "US", "stateCode": "IN", "latitude": "41.41810000", "longitude": "-86.48585000"}, {"name": "Kosciusko County", "countryCode": "US", "stateCode": "IN", "latitude": "41.24410000", "longitude": "-85.86072000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.31671000", "longitude": "-87.02586000"}, {"name": "La Porte", "countryCode": "US", "stateCode": "IN", "latitude": "41.60774000", "longitude": "-86.71389000"}, {"name": "LaGrange County", "countryCode": "US", "stateCode": "IN", "latitude": "41.64261000", "longitude": "-85.42650000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.61060000", "longitude": "-86.72252000"}, {"name": "LaPorte County", "countryCode": "US", "stateCode": "IN", "latitude": "41.54902000", "longitude": "-86.74237000"}, {"name": "Lafayette", "countryCode": "US", "stateCode": "IN", "latitude": "40.41670000", "longitude": "-86.87529000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.64172000", "longitude": "-85.41665000"}, {"name": "Lake County", "countryCode": "US", "stateCode": "IN", "latitude": "41.47221000", "longitude": "-87.37637000"}, {"name": "Lake Dalecarlia", "countryCode": "US", "stateCode": "IN", "latitude": "41.33087000", "longitude": "-87.39476000"}, {"name": "Lake Station", "countryCode": "US", "stateCode": "IN", "latitude": "41.57504000", "longitude": "-87.23892000"}, {"name": "Lakes of the Four Seasons", "countryCode": "US", "stateCode": "IN", "latitude": "41.41032000", "longitude": "-87.21309000"}, {"name": "Lapel", "countryCode": "US", "stateCode": "IN", "latitude": "40.06837000", "longitude": "-85.84831000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.83865000", "longitude": "-86.02526000"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "IN", "latitude": "38.84116000", "longitude": "-86.48345000"}, {"name": "Lawrenceburg", "countryCode": "US", "stateCode": "IN", "latitude": "39.09089000", "longitude": "-84.84995000"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "IN", "latitude": "40.04837000", "longitude": "-86.46917000"}, {"name": "Leo-Cedarville", "countryCode": "US", "stateCode": "IN", "latitude": "41.21255000", "longitude": "-85.01664000"}, {"name": "Liberty", "countryCode": "US", "stateCode": "IN", "latitude": "39.63560000", "longitude": "-84.93107000"}, {"name": "Ligonier", "countryCode": "US", "stateCode": "IN", "latitude": "41.46588000", "longitude": "-85.58748000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.03477000", "longitude": "-87.16585000"}, {"name": "Logansport", "countryCode": "US", "stateCode": "IN", "latitude": "40.75448000", "longitude": "-86.35667000"}, {"name": "Long Beach", "countryCode": "US", "stateCode": "IN", "latitude": "41.73893000", "longitude": "-86.85697000"}, {"name": "Loogootee", "countryCode": "US", "stateCode": "IN", "latitude": "38.67699000", "longitude": "-86.91417000"}, {"name": "Lowell", "countryCode": "US", "stateCode": "IN", "latitude": "41.29142000", "longitude": "-87.42059000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.04977000", "longitude": "-84.93969000"}, {"name": "Madison", "countryCode": "US", "stateCode": "IN", "latitude": "38.73589000", "longitude": "-85.37996000"}, {"name": "Madison County", "countryCode": "US", "stateCode": "IN", "latitude": "40.16166000", "longitude": "-85.71935000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.55837000", "longitude": "-85.65914000"}, {"name": "Marion County", "countryCode": "US", "stateCode": "IN", "latitude": "39.78171000", "longitude": "-86.13847000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.82462000", "longitude": "-85.33884000"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "IN", "latitude": "41.32485000", "longitude": "-86.26176000"}, {"name": "Martin County", "countryCode": "US", "stateCode": "IN", "latitude": "38.70801000", "longitude": "-86.80307000"}, {"name": "Martinsville", "countryCode": "US", "stateCode": "IN", "latitude": "39.42783000", "longitude": "-86.42833000"}, {"name": "M<PERSON>C<PERSON>sville", "countryCode": "US", "stateCode": "IN", "latitude": "39.90810000", "longitude": "-85.92276000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.02615000", "longitude": "-87.51585000"}, {"name": "Meridian Hills", "countryCode": "US", "stateCode": "IN", "latitude": "39.89004000", "longitude": "-86.15721000"}, {"name": "Merrillville", "countryCode": "US", "stateCode": "IN", "latitude": "41.48281000", "longitude": "-87.33281000"}, {"name": "Miami County", "countryCode": "US", "stateCode": "IN", "latitude": "40.76950000", "longitude": "-86.04502000"}, {"name": "Michigan City", "countryCode": "US", "stateCode": "IN", "latitude": "41.70754000", "longitude": "-86.89503000"}, {"name": "Middlebury", "countryCode": "US", "stateCode": "IN", "latitude": "41.67533000", "longitude": "-85.70610000"}, {"name": "Middletown", "countryCode": "US", "stateCode": "IN", "latitude": "40.05727000", "longitude": "-85.53720000"}, {"name": "Milan", "countryCode": "US", "stateCode": "IN", "latitude": "39.12117000", "longitude": "-85.13135000"}, {"name": "Milford", "countryCode": "US", "stateCode": "IN", "latitude": "41.40977000", "longitude": "-85.84555000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.66199000", "longitude": "-86.15862000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.73283000", "longitude": "-86.47360000"}, {"name": "Monon", "countryCode": "US", "stateCode": "IN", "latitude": "40.86782000", "longitude": "-86.87890000"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "IN", "latitude": "39.16092000", "longitude": "-86.52314000"}, {"name": "Monroeville", "countryCode": "US", "stateCode": "IN", "latitude": "40.97477000", "longitude": "-84.86830000"}, {"name": "Monrovia", "countryCode": "US", "stateCode": "IN", "latitude": "39.57894000", "longitude": "-86.48222000"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "IN", "latitude": "40.04038000", "longitude": "-86.89330000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.74532000", "longitude": "-86.76473000"}, {"name": "Montpelier", "countryCode": "US", "stateCode": "IN", "latitude": "40.55393000", "longitude": "-85.27747000"}, {"name": "Mooresville", "countryCode": "US", "stateCode": "IN", "latitude": "39.61282000", "longitude": "-86.37416000"}, {"name": "Morgan County", "countryCode": "US", "stateCode": "IN", "latitude": "39.48155000", "longitude": "-86.44621000"}, {"name": "Morocco", "countryCode": "US", "stateCode": "IN", "latitude": "40.94615000", "longitude": "-87.45336000"}, {"name": "Morristown", "countryCode": "US", "stateCode": "IN", "latitude": "39.67338000", "longitude": "-85.69859000"}, {"name": "Mount Vernon", "countryCode": "US", "stateCode": "IN", "latitude": "37.93227000", "longitude": "-87.89503000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.34448000", "longitude": "-86.66528000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.19338000", "longitude": "-85.38636000"}, {"name": "Munster", "countryCode": "US", "stateCode": "IN", "latitude": "41.56448000", "longitude": "-87.51254000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.44283000", "longitude": "-86.00139000"}, {"name": "Nashville", "countryCode": "US", "stateCode": "IN", "latitude": "39.20727000", "longitude": "-86.25110000"}, {"name": "New Albany", "countryCode": "US", "stateCode": "IN", "latitude": "38.28562000", "longitude": "-85.82413000"}, {"name": "New Carlisle", "countryCode": "US", "stateCode": "IN", "latitude": "41.70032000", "longitude": "-86.50946000"}, {"name": "New Castle", "countryCode": "US", "stateCode": "IN", "latitude": "39.92894000", "longitude": "-85.37025000"}, {"name": "New Chicago", "countryCode": "US", "stateCode": "IN", "latitude": "41.55837000", "longitude": "-87.27448000"}, {"name": "New Haven", "countryCode": "US", "stateCode": "IN", "latitude": "41.07060000", "longitude": "-85.01441000"}, {"name": "New Palestine", "countryCode": "US", "stateCode": "IN", "latitude": "39.72199000", "longitude": "-85.88915000"}, {"name": "New Paris", "countryCode": "US", "stateCode": "IN", "latitude": "41.50033000", "longitude": "-85.82805000"}, {"name": "New Pekin", "countryCode": "US", "stateCode": "IN", "latitude": "38.50506000", "longitude": "-86.01692000"}, {"name": "New Whiteland", "countryCode": "US", "stateCode": "IN", "latitude": "39.55810000", "longitude": "-86.09526000"}, {"name": "Newburgh", "countryCode": "US", "stateCode": "IN", "latitude": "37.94449000", "longitude": "-87.40529000"}, {"name": "Newport", "countryCode": "US", "stateCode": "IN", "latitude": "39.88420000", "longitude": "-87.40863000"}, {"name": "Newton County", "countryCode": "US", "stateCode": "IN", "latitude": "40.95585000", "longitude": "-87.39754000"}, {"name": "Noble County", "countryCode": "US", "stateCode": "IN", "latitude": "41.39860000", "longitude": "-85.41747000"}, {"name": "Noblesville", "countryCode": "US", "stateCode": "IN", "latitude": "40.04559000", "longitude": "-86.00860000"}, {"name": "North Judson", "countryCode": "US", "stateCode": "IN", "latitude": "41.21504000", "longitude": "-86.77585000"}, {"name": "North Liberty", "countryCode": "US", "stateCode": "IN", "latitude": "41.53421000", "longitude": "-86.42723000"}, {"name": "North Madison", "countryCode": "US", "stateCode": "IN", "latitude": "38.76784000", "longitude": "-85.39663000"}, {"name": "North Manchester", "countryCode": "US", "stateCode": "IN", "latitude": "41.00060000", "longitude": "-85.76860000"}, {"name": "North Terre Haute", "countryCode": "US", "stateCode": "IN", "latitude": "39.52781000", "longitude": "-87.36030000"}, {"name": "North Vernon", "countryCode": "US", "stateCode": "IN", "latitude": "39.00617000", "longitude": "-85.62358000"}, {"name": "North Webster", "countryCode": "US", "stateCode": "IN", "latitude": "41.32560000", "longitude": "-85.69776000"}, {"name": "Notre Dame", "countryCode": "US", "stateCode": "IN", "latitude": "41.70019000", "longitude": "-86.23793000"}, {"name": "Oak Park", "countryCode": "US", "stateCode": "IN", "latitude": "38.30562000", "longitude": "-85.69635000"}, {"name": "Oakland City", "countryCode": "US", "stateCode": "IN", "latitude": "38.33866000", "longitude": "-87.34501000"}, {"name": "<PERSON>don", "countryCode": "US", "stateCode": "IN", "latitude": "38.84283000", "longitude": "-86.99140000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.62281000", "longitude": "-87.19170000"}, {"name": "Ohio County", "countryCode": "US", "stateCode": "IN", "latitude": "38.95010000", "longitude": "-84.96503000"}, {"name": "Oolitic", "countryCode": "US", "stateCode": "IN", "latitude": "38.90088000", "longitude": "-86.52527000"}, {"name": "Orange County", "countryCode": "US", "stateCode": "IN", "latitude": "38.54178000", "longitude": "-86.49507000"}, {"name": "Orleans", "countryCode": "US", "stateCode": "IN", "latitude": "38.66172000", "longitude": "-86.45166000"}, {"name": "Osceola", "countryCode": "US", "stateCode": "IN", "latitude": "41.66505000", "longitude": "-86.07584000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.12922000", "longitude": "-85.29163000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.88060000", "longitude": "-85.16636000"}, {"name": "Otterbein", "countryCode": "US", "stateCode": "IN", "latitude": "40.49059000", "longitude": "-87.09640000"}, {"name": "Owen County", "countryCode": "US", "stateCode": "IN", "latitude": "39.31281000", "longitude": "-86.83765000"}, {"name": "Owensville", "countryCode": "US", "stateCode": "IN", "latitude": "38.27199000", "longitude": "-87.68780000"}, {"name": "Oxford", "countryCode": "US", "stateCode": "IN", "latitude": "40.51976000", "longitude": "-87.24779000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.55617000", "longitude": "-86.46832000"}, {"name": "Parke County", "countryCode": "US", "stateCode": "IN", "latitude": "39.77363000", "longitude": "-87.20636000"}, {"name": "Parker City", "countryCode": "US", "stateCode": "IN", "latitude": "40.18893000", "longitude": "-85.20413000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.99754000", "longitude": "-85.74664000"}, {"name": "Perry County", "countryCode": "US", "stateCode": "IN", "latitude": "38.07965000", "longitude": "-86.63803000"}, {"name": "Peru", "countryCode": "US", "stateCode": "IN", "latitude": "40.75365000", "longitude": "-86.06888000"}, {"name": "Petersburg", "countryCode": "US", "stateCode": "IN", "latitude": "38.49199000", "longitude": "-87.27862000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.20032000", "longitude": "-85.70554000"}, {"name": "Pike County", "countryCode": "US", "stateCode": "IN", "latitude": "38.39878000", "longitude": "-87.23216000"}, {"name": "Pittsboro", "countryCode": "US", "stateCode": "IN", "latitude": "39.86393000", "longitude": "-86.46694000"}, {"name": "Plainfield", "countryCode": "US", "stateCode": "IN", "latitude": "39.70421000", "longitude": "-86.39944000"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "IN", "latitude": "41.34366000", "longitude": "-86.30973000"}, {"name": "Portage", "countryCode": "US", "stateCode": "IN", "latitude": "41.57587000", "longitude": "-87.17615000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.61559000", "longitude": "-87.07420000"}, {"name": "Porter County", "countryCode": "US", "stateCode": "IN", "latitude": "41.50884000", "longitude": "-87.07332000"}, {"name": "Portland", "countryCode": "US", "stateCode": "IN", "latitude": "40.43449000", "longitude": "-84.97775000"}, {"name": "Posey County", "countryCode": "US", "stateCode": "IN", "latitude": "38.02189000", "longitude": "-87.86847000"}, {"name": "Poseyville", "countryCode": "US", "stateCode": "IN", "latitude": "38.17004000", "longitude": "-87.78308000"}, {"name": "Princes Lakes", "countryCode": "US", "stateCode": "IN", "latitude": "39.35366000", "longitude": "-86.09805000"}, {"name": "Princeton", "countryCode": "US", "stateCode": "IN", "latitude": "38.35532000", "longitude": "-87.56752000"}, {"name": "Pulaski County", "countryCode": "US", "stateCode": "IN", "latitude": "41.04183000", "longitude": "-86.69878000"}, {"name": "Putnam County", "countryCode": "US", "stateCode": "IN", "latitude": "39.66626000", "longitude": "-86.84500000"}, {"name": "Randolph County", "countryCode": "US", "stateCode": "IN", "latitude": "40.15764000", "longitude": "-85.01131000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.34893000", "longitude": "-85.14997000"}, {"name": "Remington", "countryCode": "US", "stateCode": "IN", "latitude": "40.76087000", "longitude": "-87.15085000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.93670000", "longitude": "-87.15086000"}, {"name": "Richmond", "countryCode": "US", "stateCode": "IN", "latitude": "39.82894000", "longitude": "-84.89024000"}, {"name": "Ripley County", "countryCode": "US", "stateCode": "IN", "latitude": "39.10345000", "longitude": "-85.26239000"}, {"name": "Rising Sun", "countryCode": "US", "stateCode": "IN", "latitude": "38.94950000", "longitude": "-84.85384000"}, {"name": "Roanoke", "countryCode": "US", "stateCode": "IN", "latitude": "40.96255000", "longitude": "-85.37331000"}, {"name": "Rochester", "countryCode": "US", "stateCode": "IN", "latitude": "41.06476000", "longitude": "-86.21583000"}, {"name": "Rockport", "countryCode": "US", "stateCode": "IN", "latitude": "37.88311000", "longitude": "-87.04944000"}, {"name": "Rockville", "countryCode": "US", "stateCode": "IN", "latitude": "39.76254000", "longitude": "-87.22918000"}, {"name": "Rome City", "countryCode": "US", "stateCode": "IN", "latitude": "41.49616000", "longitude": "-85.37665000"}, {"name": "Roselawn", "countryCode": "US", "stateCode": "IN", "latitude": "41.14170000", "longitude": "-87.31475000"}, {"name": "Rossville", "countryCode": "US", "stateCode": "IN", "latitude": "40.41698000", "longitude": "-86.59472000"}, {"name": "Rush County", "countryCode": "US", "stateCode": "IN", "latitude": "39.61995000", "longitude": "-85.46576000"}, {"name": "Rushville", "countryCode": "US", "stateCode": "IN", "latitude": "39.60921000", "longitude": "-85.44636000"}, {"name": "Russiaville", "countryCode": "US", "stateCode": "IN", "latitude": "40.41754000", "longitude": "-86.27138000"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.45004000", "longitude": "-87.47004000"}, {"name": "Saint Joseph County", "countryCode": "US", "stateCode": "IN", "latitude": "41.61672000", "longitude": "-86.28986000"}, {"name": "Saint Paul", "countryCode": "US", "stateCode": "IN", "latitude": "39.42810000", "longitude": "-85.62831000"}, {"name": "Salem", "countryCode": "US", "stateCode": "IN", "latitude": "38.60561000", "longitude": "-86.10109000"}, {"name": "Santa Claus", "countryCode": "US", "stateCode": "IN", "latitude": "38.12005000", "longitude": "-86.91416000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.47892000", "longitude": "-87.45476000"}, {"name": "Scott County", "countryCode": "US", "stateCode": "IN", "latitude": "38.68507000", "longitude": "-85.74747000"}, {"name": "Scottsburg", "countryCode": "US", "stateCode": "IN", "latitude": "38.68561000", "longitude": "-85.77025000"}, {"name": "Seelyville", "countryCode": "US", "stateCode": "IN", "latitude": "39.49198000", "longitude": "-87.26724000"}, {"name": "Sellersburg", "countryCode": "US", "stateCode": "IN", "latitude": "38.39812000", "longitude": "-85.75496000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.95922000", "longitude": "-85.89025000"}, {"name": "Shadeland", "countryCode": "US", "stateCode": "IN", "latitude": "40.37365000", "longitude": "-86.94890000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.17837000", "longitude": "-87.39363000"}, {"name": "Shelby County", "countryCode": "US", "stateCode": "IN", "latitude": "39.52369000", "longitude": "-85.79170000"}, {"name": "Shelbyville", "countryCode": "US", "stateCode": "IN", "latitude": "39.52144000", "longitude": "-85.77692000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.13504000", "longitude": "-86.22055000"}, {"name": "Shoals", "countryCode": "US", "stateCode": "IN", "latitude": "38.66644000", "longitude": "-86.79111000"}, {"name": "Shorewood Forest", "countryCode": "US", "stateCode": "IN", "latitude": "41.46315000", "longitude": "-87.14472000"}, {"name": "Simonton Lake", "countryCode": "US", "stateCode": "IN", "latitude": "41.75422000", "longitude": "-85.97500000"}, {"name": "Smithville-Sanders", "countryCode": "US", "stateCode": "IN", "latitude": "39.05969000", "longitude": "-86.51077000"}, {"name": "South Bend", "countryCode": "US", "stateCode": "IN", "latitude": "41.68338000", "longitude": "-86.25001000"}, {"name": "South Haven", "countryCode": "US", "stateCode": "IN", "latitude": "41.54198000", "longitude": "-87.13726000"}, {"name": "South Whitley", "countryCode": "US", "stateCode": "IN", "latitude": "41.08477000", "longitude": "-85.62804000"}, {"name": "Southport", "countryCode": "US", "stateCode": "IN", "latitude": "39.66505000", "longitude": "-86.12776000"}, {"name": "Speedway", "countryCode": "US", "stateCode": "IN", "latitude": "39.80227000", "longitude": "-86.26721000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.28671000", "longitude": "-86.76251000"}, {"name": "Spencer County", "countryCode": "US", "stateCode": "IN", "latitude": "38.01406000", "longitude": "-87.00771000"}, {"name": "Starke County", "countryCode": "US", "stateCode": "IN", "latitude": "41.28093000", "longitude": "-86.64765000"}, {"name": "Steuben County", "countryCode": "US", "stateCode": "IN", "latitude": "41.64387000", "longitude": "-85.00077000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "39.09532000", "longitude": "-87.40585000"}, {"name": "Sullivan County", "countryCode": "US", "stateCode": "IN", "latitude": "39.08883000", "longitude": "-87.41469000"}, {"name": "Sunman", "countryCode": "US", "stateCode": "IN", "latitude": "39.23700000", "longitude": "-85.09468000"}, {"name": "Sweetser", "countryCode": "US", "stateCode": "IN", "latitude": "40.57198000", "longitude": "-85.76915000"}, {"name": "Switzerland County", "countryCode": "US", "stateCode": "IN", "latitude": "38.82616000", "longitude": "-85.03700000"}, {"name": "Syracuse", "countryCode": "US", "stateCode": "IN", "latitude": "41.42783000", "longitude": "-85.75249000"}, {"name": "Tell City", "countryCode": "US", "stateCode": "IN", "latitude": "37.95144000", "longitude": "-86.76777000"}, {"name": "Terre Haute", "countryCode": "US", "stateCode": "IN", "latitude": "39.46670000", "longitude": "-87.41391000"}, {"name": "Thorntown", "countryCode": "US", "stateCode": "IN", "latitude": "40.12948000", "longitude": "-86.60667000"}, {"name": "Tippecanoe County", "countryCode": "US", "stateCode": "IN", "latitude": "40.38862000", "longitude": "-86.89410000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.28226000", "longitude": "-86.04110000"}, {"name": "Tipton County", "countryCode": "US", "stateCode": "IN", "latitude": "40.31135000", "longitude": "-86.05186000"}, {"name": "Topeka", "countryCode": "US", "stateCode": "IN", "latitude": "41.53922000", "longitude": "-85.53971000"}, {"name": "Trafalgar", "countryCode": "US", "stateCode": "IN", "latitude": "39.41616000", "longitude": "-86.15082000"}, {"name": "Trail Creek", "countryCode": "US", "stateCode": "IN", "latitude": "41.69837000", "longitude": "-86.85920000"}, {"name": "Tri-Lakes", "countryCode": "US", "stateCode": "IN", "latitude": "41.24588000", "longitude": "-85.44192000"}, {"name": "Union City", "countryCode": "US", "stateCode": "IN", "latitude": "40.20199000", "longitude": "-84.80913000"}, {"name": "Union County", "countryCode": "US", "stateCode": "IN", "latitude": "39.62555000", "longitude": "-84.92514000"}, {"name": "Upland", "countryCode": "US", "stateCode": "IN", "latitude": "40.47560000", "longitude": "-85.49442000"}, {"name": "Valparaiso", "countryCode": "US", "stateCode": "IN", "latitude": "41.47309000", "longitude": "-87.06114000"}, {"name": "Vanderburgh County", "countryCode": "US", "stateCode": "IN", "latitude": "38.02514000", "longitude": "-87.58578000"}, {"name": "Veedersburg", "countryCode": "US", "stateCode": "IN", "latitude": "40.11309000", "longitude": "-87.26251000"}, {"name": "Vermillion County", "countryCode": "US", "stateCode": "IN", "latitude": "39.85380000", "longitude": "-87.46397000"}, {"name": "Versailles", "countryCode": "US", "stateCode": "IN", "latitude": "39.07200000", "longitude": "-85.25190000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "38.74784000", "longitude": "-85.06717000"}, {"name": "Vigo County", "countryCode": "US", "stateCode": "IN", "latitude": "39.43064000", "longitude": "-87.38996000"}, {"name": "Vincennes", "countryCode": "US", "stateCode": "IN", "latitude": "38.67727000", "longitude": "-87.52863000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.79782000", "longitude": "-85.82054000"}, {"name": "Wabash County", "countryCode": "US", "stateCode": "IN", "latitude": "40.84569000", "longitude": "-85.79401000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.53616000", "longitude": "-86.02083000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.46671000", "longitude": "-86.48307000"}, {"name": "Walton", "countryCode": "US", "stateCode": "IN", "latitude": "40.66087000", "longitude": "-86.24194000"}, {"name": "Wanatah", "countryCode": "US", "stateCode": "IN", "latitude": "41.43060000", "longitude": "-86.89836000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "40.68282000", "longitude": "-85.42720000"}, {"name": "Warren County", "countryCode": "US", "stateCode": "IN", "latitude": "40.34690000", "longitude": "-87.35331000"}, {"name": "Warren Park", "countryCode": "US", "stateCode": "IN", "latitude": "39.78199000", "longitude": "-86.05026000"}, {"name": "Warrick County", "countryCode": "US", "stateCode": "IN", "latitude": "38.09217000", "longitude": "-87.27205000"}, {"name": "Warsaw", "countryCode": "US", "stateCode": "IN", "latitude": "41.23810000", "longitude": "-85.85305000"}, {"name": "Washington", "countryCode": "US", "stateCode": "IN", "latitude": "38.65922000", "longitude": "-87.17279000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "IN", "latitude": "38.59998000", "longitude": "-86.10531000"}, {"name": "Waterloo", "countryCode": "US", "stateCode": "IN", "latitude": "41.43199000", "longitude": "-85.01997000"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "IN", "latitude": "39.86442000", "longitude": "-85.00988000"}, {"name": "Wells County", "countryCode": "US", "stateCode": "IN", "latitude": "40.72919000", "longitude": "-85.22122000"}, {"name": "West Lafayette", "countryCode": "US", "stateCode": "IN", "latitude": "40.42587000", "longitude": "-86.90807000"}, {"name": "West Terre Haute", "countryCode": "US", "stateCode": "IN", "latitude": "39.46504000", "longitude": "-87.45002000"}, {"name": "Westfield", "countryCode": "US", "stateCode": "IN", "latitude": "40.04282000", "longitude": "-86.12749000"}, {"name": "Westport", "countryCode": "US", "stateCode": "IN", "latitude": "39.17589000", "longitude": "-85.57303000"}, {"name": "Westville", "countryCode": "US", "stateCode": "IN", "latitude": "41.54143000", "longitude": "-86.90058000"}, {"name": "White County", "countryCode": "US", "stateCode": "IN", "latitude": "40.74977000", "longitude": "-86.86547000"}, {"name": "Whiteland", "countryCode": "US", "stateCode": "IN", "latitude": "39.55005000", "longitude": "-86.07971000"}, {"name": "Whitestown", "countryCode": "US", "stateCode": "IN", "latitude": "39.99726000", "longitude": "-86.34583000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.67976000", "longitude": "-87.49449000"}, {"name": "Whitley County", "countryCode": "US", "stateCode": "IN", "latitude": "41.13938000", "longitude": "-85.50512000"}, {"name": "Williamsport", "countryCode": "US", "stateCode": "IN", "latitude": "40.28837000", "longitude": "-87.29390000"}, {"name": "Winamac", "countryCode": "US", "stateCode": "IN", "latitude": "41.05143000", "longitude": "-86.60306000"}, {"name": "Winchester", "countryCode": "US", "stateCode": "IN", "latitude": "40.17199000", "longitude": "-84.98135000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.40531000", "longitude": "-87.27531000"}, {"name": "Winona Lake", "countryCode": "US", "stateCode": "IN", "latitude": "41.22727000", "longitude": "-85.82193000"}, {"name": "Wolcottville", "countryCode": "US", "stateCode": "IN", "latitude": "41.52588000", "longitude": "-85.36665000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "IN", "latitude": "41.12533000", "longitude": "-84.85330000"}, {"name": "Worthington", "countryCode": "US", "stateCode": "IN", "latitude": "39.12504000", "longitude": "-86.97945000"}, {"name": "Yorktown", "countryCode": "US", "stateCode": "IN", "latitude": "40.17365000", "longitude": "-85.49414000"}, {"name": "Zionsville", "countryCode": "US", "stateCode": "IN", "latitude": "39.95087000", "longitude": "-86.26194000"}]