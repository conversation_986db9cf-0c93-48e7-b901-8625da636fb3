[{"name": "Ada", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Afton", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Aitkin County", "countryCode": "US", "stateCode": "MN"}, {"name": "Albany", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Albertville", "countryCode": "US", "stateCode": "MN"}, {"name": "Alexandria", "countryCode": "US", "stateCode": "MN"}, {"name": "Andover", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Anoka County", "countryCode": "US", "stateCode": "MN"}, {"name": "Apple Valley", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Arden Hills", "countryCode": "US", "stateCode": "MN"}, {"name": "Arlington", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Atwater", "countryCode": "US", "stateCode": "MN"}, {"name": "Aurora", "countryCode": "US", "stateCode": "MN"}, {"name": "Austin", "countryCode": "US", "stateCode": "MN"}, {"name": "Avon", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Barnesville", "countryCode": "US", "stateCode": "MN"}, {"name": "Baudette", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Bayport", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Becker County", "countryCode": "US", "stateCode": "MN"}, {"name": "Belle Plaine", "countryCode": "US", "stateCode": "MN"}, {"name": "Beltrami County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Benton County", "countryCode": "US", "stateCode": "MN"}, {"name": "Big Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Big Stone County", "countryCode": "US", "stateCode": "MN"}, {"name": "Birchwood", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Blooming Prairie", "countryCode": "US", "stateCode": "MN"}, {"name": "Bloomington", "countryCode": "US", "stateCode": "MN"}, {"name": "Blue Earth", "countryCode": "US", "stateCode": "MN"}, {"name": "Blue Earth County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Branch", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Breezy Point", "countryCode": "US", "stateCode": "MN"}, {"name": "Brooklyn Center", "countryCode": "US", "stateCode": "MN"}, {"name": "Brooklyn Park", "countryCode": "US", "stateCode": "MN"}, {"name": "Brown County", "countryCode": "US", "stateCode": "MN"}, {"name": "Buffalo", "countryCode": "US", "stateCode": "MN"}, {"name": "Burnsville", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Caledonia", "countryCode": "US", "stateCode": "MN"}, {"name": "Cambridge", "countryCode": "US", "stateCode": "MN"}, {"name": "Canby", "countryCode": "US", "stateCode": "MN"}, {"name": "Cannon Falls", "countryCode": "US", "stateCode": "MN"}, {"name": "Carlton", "countryCode": "US", "stateCode": "MN"}, {"name": "Carlton County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Carver County", "countryCode": "US", "stateCode": "MN"}, {"name": "Cass County", "countryCode": "US", "stateCode": "MN"}, {"name": "Center City", "countryCode": "US", "stateCode": "MN"}, {"name": "Centerville", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Chaska", "countryCode": "US", "stateCode": "MN"}, {"name": "Chatfield", "countryCode": "US", "stateCode": "MN"}, {"name": "Chippewa County", "countryCode": "US", "stateCode": "MN"}, {"name": "Chisago City", "countryCode": "US", "stateCode": "MN"}, {"name": "Chisago County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Circle Pines", "countryCode": "US", "stateCode": "MN"}, {"name": "Clara City", "countryCode": "US", "stateCode": "MN"}, {"name": "Clay County", "countryCode": "US", "stateCode": "MN"}, {"name": "Clearwater", "countryCode": "US", "stateCode": "MN"}, {"name": "Clearwater County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Cohasset", "countryCode": "US", "stateCode": "MN"}, {"name": "Cokato", "countryCode": "US", "stateCode": "MN"}, {"name": "Cold Spring", "countryCode": "US", "stateCode": "MN"}, {"name": "Coleraine", "countryCode": "US", "stateCode": "MN"}, {"name": "Collegeville", "countryCode": "US", "stateCode": "MN"}, {"name": "Cologne", "countryCode": "US", "stateCode": "MN"}, {"name": "Columbia Heights", "countryCode": "US", "stateCode": "MN"}, {"name": "Columbus", "countryCode": "US", "stateCode": "MN"}, {"name": "Cook County", "countryCode": "US", "stateCode": "MN"}, {"name": "Coon Rapids", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Cottage Grove", "countryCode": "US", "stateCode": "MN"}, {"name": "Cottonwood", "countryCode": "US", "stateCode": "MN"}, {"name": "Cottonwood County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Cross Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Crow Wing County", "countryCode": "US", "stateCode": "MN"}, {"name": "Crystal", "countryCode": "US", "stateCode": "MN"}, {"name": "Dakota County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Dayton", "countryCode": "US", "stateCode": "MN"}, {"name": "Deephaven", "countryCode": "US", "stateCode": "MN"}, {"name": "Delano", "countryCode": "US", "stateCode": "MN"}, {"name": "Dellwood", "countryCode": "US", "stateCode": "MN"}, {"name": "Detroit Lakes", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Dodge Center", "countryCode": "US", "stateCode": "MN"}, {"name": "Dodge County", "countryCode": "US", "stateCode": "MN"}, {"name": "Douglas County", "countryCode": "US", "stateCode": "MN"}, {"name": "Duluth", "countryCode": "US", "stateCode": "MN"}, {"name": "Dundas", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Eagle Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "East Bethel", "countryCode": "US", "stateCode": "MN"}, {"name": "East Grand Forks", "countryCode": "US", "stateCode": "MN"}, {"name": "East Gull Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Eden Prairie", "countryCode": "US", "stateCode": "MN"}, {"name": "Eden Valley", "countryCode": "US", "stateCode": "MN"}, {"name": "Edgerton", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Elbow Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Elgin", "countryCode": "US", "stateCode": "MN"}, {"name": "Elk River", "countryCode": "US", "stateCode": "MN"}, {"name": "Elko New Market", "countryCode": "US", "stateCode": "MN"}, {"name": "Ely", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Excelsior", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Fairfax", "countryCode": "US", "stateCode": "MN"}, {"name": "Fairmont", "countryCode": "US", "stateCode": "MN"}, {"name": "Falcon Heights", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Faribault County", "countryCode": "US", "stateCode": "MN"}, {"name": "Farmington", "countryCode": "US", "stateCode": "MN"}, {"name": "Fergus Falls", "countryCode": "US", "stateCode": "MN"}, {"name": "Fillmore County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Forest Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Frazee", "countryCode": "US", "stateCode": "MN"}, {"name": "Freeborn County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Glencoe", "countryCode": "US", "stateCode": "MN"}, {"name": "Glenwood", "countryCode": "US", "stateCode": "MN"}, {"name": "Glyndon", "countryCode": "US", "stateCode": "MN"}, {"name": "Golden Valley", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Goodhue County", "countryCode": "US", "stateCode": "MN"}, {"name": "Goodview", "countryCode": "US", "stateCode": "MN"}, {"name": "Grand Marais", "countryCode": "US", "stateCode": "MN"}, {"name": "Grand Meadow", "countryCode": "US", "stateCode": "MN"}, {"name": "Grand Rapids", "countryCode": "US", "stateCode": "MN"}, {"name": "Granite Falls", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Grant County", "countryCode": "US", "stateCode": "MN"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Ham Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Hanover", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Hastings", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Hayfield", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Hennepin County", "countryCode": "US", "stateCode": "MN"}, {"name": "Hermantown", "countryCode": "US", "stateCode": "MN"}, {"name": "Hibbing", "countryCode": "US", "stateCode": "MN"}, {"name": "Hinckley", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Houston County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Hoyt Lakes", "countryCode": "US", "stateCode": "MN"}, {"name": "Hubbard County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Independence", "countryCode": "US", "stateCode": "MN"}, {"name": "International Falls", "countryCode": "US", "stateCode": "MN"}, {"name": "Inver Grove Heights", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Isanti County", "countryCode": "US", "stateCode": "MN"}, {"name": "Itasca County", "countryCode": "US", "stateCode": "MN"}, {"name": "Ivanhoe", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "MN"}, {"name": "Janesville", "countryCode": "US", "stateCode": "MN"}, {"name": "Jordan", "countryCode": "US", "stateCode": "MN"}, {"name": "Kanabec County", "countryCode": "US", "stateCode": "MN"}, {"name": "Kandiyohi County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Kittson County", "countryCode": "US", "stateCode": "MN"}, {"name": "Koochiching County", "countryCode": "US", "stateCode": "MN"}, {"name": "La Crescent", "countryCode": "US", "stateCode": "MN"}, {"name": "Lac qui Parle County", "countryCode": "US", "stateCode": "MN"}, {"name": "Lake City", "countryCode": "US", "stateCode": "MN"}, {"name": "Lake County", "countryCode": "US", "stateCode": "MN"}, {"name": "Lake Crystal", "countryCode": "US", "stateCode": "MN"}, {"name": "Lake Elmo", "countryCode": "US", "stateCode": "MN"}, {"name": "Lake Saint Croix Beach", "countryCode": "US", "stateCode": "MN"}, {"name": "Lake Shore", "countryCode": "US", "stateCode": "MN"}, {"name": "Lake of the Woods County", "countryCode": "US", "stateCode": "MN"}, {"name": "Lakefield", "countryCode": "US", "stateCode": "MN"}, {"name": "Lakeland", "countryCode": "US", "stateCode": "MN"}, {"name": "Lakeville", "countryCode": "US", "stateCode": "MN"}, {"name": "Lauderdale", "countryCode": "US", "stateCode": "MN"}, {"name": "Le Center", "countryCode": "US", "stateCode": "MN"}, {"name": "Le Sueur", "countryCode": "US", "stateCode": "MN"}, {"name": "Le Sueur County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Lexington", "countryCode": "US", "stateCode": "MN"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Lino Lakes", "countryCode": "US", "stateCode": "MN"}, {"name": "Litchfield", "countryCode": "US", "stateCode": "MN"}, {"name": "Little Canada", "countryCode": "US", "stateCode": "MN"}, {"name": "Little Falls", "countryCode": "US", "stateCode": "MN"}, {"name": "Little Rock", "countryCode": "US", "stateCode": "MN"}, {"name": "Long Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Long Prairie", "countryCode": "US", "stateCode": "MN"}, {"name": "Lonsdale", "countryCode": "US", "stateCode": "MN"}, {"name": "Luverne", "countryCode": "US", "stateCode": "MN"}, {"name": "Lyon County", "countryCode": "US", "stateCode": "MN"}, {"name": "Madelia", "countryCode": "US", "stateCode": "MN"}, {"name": "Madison", "countryCode": "US", "stateCode": "MN"}, {"name": "Madison Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Ma<PERSON><PERSON>n", "countryCode": "US", "stateCode": "MN"}, {"name": "Mahnomen County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Mankato", "countryCode": "US", "stateCode": "MN"}, {"name": "Mantorville", "countryCode": "US", "stateCode": "MN"}, {"name": "Maple Grove", "countryCode": "US", "stateCode": "MN"}, {"name": "Maple Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Maple Plain", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>ton", "countryCode": "US", "stateCode": "MN"}, {"name": "Maplewood", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "MN"}, {"name": "Martin County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "McLeod County", "countryCode": "US", "stateCode": "MN"}, {"name": "Medford", "countryCode": "US", "stateCode": "MN"}, {"name": "Medina", "countryCode": "US", "stateCode": "MN"}, {"name": "Meeker County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Men<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Mendota Heights", "countryCode": "US", "stateCode": "MN"}, {"name": "Milaca", "countryCode": "US", "stateCode": "MN"}, {"name": "Mille Lacs County", "countryCode": "US", "stateCode": "MN"}, {"name": "Minneapolis", "countryCode": "US", "stateCode": "MN"}, {"name": "Minneota", "countryCode": "US", "stateCode": "MN"}, {"name": "Minnetonka", "countryCode": "US", "stateCode": "MN"}, {"name": "Minnetonka Mills", "countryCode": "US", "stateCode": "MN"}, {"name": "Minnetrist<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Montevideo", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Moorhead", "countryCode": "US", "stateCode": "MN"}, {"name": "Moose Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Morrison County", "countryCode": "US", "stateCode": "MN"}, {"name": "Mound", "countryCode": "US", "stateCode": "MN"}, {"name": "Mounds View", "countryCode": "US", "stateCode": "MN"}, {"name": "Mountain Iron", "countryCode": "US", "stateCode": "MN"}, {"name": "Mountain Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Mower County", "countryCode": "US", "stateCode": "MN"}, {"name": "Murray County", "countryCode": "US", "stateCode": "MN"}, {"name": "New Brighton", "countryCode": "US", "stateCode": "MN"}, {"name": "New Hope", "countryCode": "US", "stateCode": "MN"}, {"name": "New London", "countryCode": "US", "stateCode": "MN"}, {"name": "New Prague", "countryCode": "US", "stateCode": "MN"}, {"name": "New Richland", "countryCode": "US", "stateCode": "MN"}, {"name": "New Ulm", "countryCode": "US", "stateCode": "MN"}, {"name": "New York Mills", "countryCode": "US", "stateCode": "MN"}, {"name": "Newport", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Nicollet County", "countryCode": "US", "stateCode": "MN"}, {"name": "Nisswa", "countryCode": "US", "stateCode": "MN"}, {"name": "Nobles County", "countryCode": "US", "stateCode": "MN"}, {"name": "Norman County", "countryCode": "US", "stateCode": "MN"}, {"name": "North Branch", "countryCode": "US", "stateCode": "MN"}, {"name": "North Mankato", "countryCode": "US", "stateCode": "MN"}, {"name": "North Oaks", "countryCode": "US", "stateCode": "MN"}, {"name": "North Saint Paul", "countryCode": "US", "stateCode": "MN"}, {"name": "Northfield", "countryCode": "US", "stateCode": "MN"}, {"name": "Norwood (historical)", "countryCode": "US", "stateCode": "MN"}, {"name": "Norwood Young America", "countryCode": "US", "stateCode": "MN"}, {"name": "Nowthen", "countryCode": "US", "stateCode": "MN"}, {"name": "Oak Grove", "countryCode": "US", "stateCode": "MN"}, {"name": "Oak Park Heights", "countryCode": "US", "stateCode": "MN"}, {"name": "Oakdale", "countryCode": "US", "stateCode": "MN"}, {"name": "Oakport", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Olmsted County", "countryCode": "US", "stateCode": "MN"}, {"name": "Orono", "countryCode": "US", "stateCode": "MN"}, {"name": "Oronoco", "countryCode": "US", "stateCode": "MN"}, {"name": "Ortonville", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Otsego", "countryCode": "US", "stateCode": "MN"}, {"name": "Otter Tail County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Park Rapids", "countryCode": "US", "stateCode": "MN"}, {"name": "Parkers Prairie", "countryCode": "US", "stateCode": "MN"}, {"name": "Parkville", "countryCode": "US", "stateCode": "MN"}, {"name": "Paynesville", "countryCode": "US", "stateCode": "MN"}, {"name": "Pelican Rapids", "countryCode": "US", "stateCode": "MN"}, {"name": "Pennington County", "countryCode": "US", "stateCode": "MN"}, {"name": "Pequot Lakes", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Pine City", "countryCode": "US", "stateCode": "MN"}, {"name": "Pine County", "countryCode": "US", "stateCode": "MN"}, {"name": "Pine Island", "countryCode": "US", "stateCode": "MN"}, {"name": "Pipestone", "countryCode": "US", "stateCode": "MN"}, {"name": "Pipestone County", "countryCode": "US", "stateCode": "MN"}, {"name": "Plainview", "countryCode": "US", "stateCode": "MN"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "MN"}, {"name": "Polk County", "countryCode": "US", "stateCode": "MN"}, {"name": "Pope County", "countryCode": "US", "stateCode": "MN"}, {"name": "Preston", "countryCode": "US", "stateCode": "MN"}, {"name": "Princeton", "countryCode": "US", "stateCode": "MN"}, {"name": "Prior <PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Proctor", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Ramsey County", "countryCode": "US", "stateCode": "MN"}, {"name": "Red Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Red Lake County", "countryCode": "US", "stateCode": "MN"}, {"name": "Red Lake Falls", "countryCode": "US", "stateCode": "MN"}, {"name": "Red Wing", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Redwood County", "countryCode": "US", "stateCode": "MN"}, {"name": "Redwood Falls", "countryCode": "US", "stateCode": "MN"}, {"name": "Renville", "countryCode": "US", "stateCode": "MN"}, {"name": "Renville County", "countryCode": "US", "stateCode": "MN"}, {"name": "Rice", "countryCode": "US", "stateCode": "MN"}, {"name": "Rice County", "countryCode": "US", "stateCode": "MN"}, {"name": "Richfield", "countryCode": "US", "stateCode": "MN"}, {"name": "Richmond", "countryCode": "US", "stateCode": "MN"}, {"name": "Robbinsdale", "countryCode": "US", "stateCode": "MN"}, {"name": "Rochester", "countryCode": "US", "stateCode": "MN"}, {"name": "Rock County", "countryCode": "US", "stateCode": "MN"}, {"name": "Rock Creek", "countryCode": "US", "stateCode": "MN"}, {"name": "Rockford", "countryCode": "US", "stateCode": "MN"}, {"name": "Rockville", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Roseau County", "countryCode": "US", "stateCode": "MN"}, {"name": "Rosemount", "countryCode": "US", "stateCode": "MN"}, {"name": "Roseville", "countryCode": "US", "stateCode": "MN"}, {"name": "Royalton", "countryCode": "US", "stateCode": "MN"}, {"name": "Rush City", "countryCode": "US", "stateCode": "MN"}, {"name": "Rush<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Saint Augusta", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Saint Cloud", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Saint Louis County", "countryCode": "US", "stateCode": "MN"}, {"name": "Saint Louis Park", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Saint Paul", "countryCode": "US", "stateCode": "MN"}, {"name": "Saint Paul Park", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Sandstone", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Sauk Centre", "countryCode": "US", "stateCode": "MN"}, {"name": "Sauk Rapids", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Scandia", "countryCode": "US", "stateCode": "MN"}, {"name": "Scott County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Sherburn", "countryCode": "US", "stateCode": "MN"}, {"name": "Sherburne County", "countryCode": "US", "stateCode": "MN"}, {"name": "Shoreview", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>wood", "countryCode": "US", "stateCode": "MN"}, {"name": "Sibley County", "countryCode": "US", "stateCode": "MN"}, {"name": "Silver Bay", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Sleepy Eye", "countryCode": "US", "stateCode": "MN"}, {"name": "South Saint Paul", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Spring Grove", "countryCode": "US", "stateCode": "MN"}, {"name": "Spring Lake Park", "countryCode": "US", "stateCode": "MN"}, {"name": "Spring Park", "countryCode": "US", "stateCode": "MN"}, {"name": "Spring Valley", "countryCode": "US", "stateCode": "MN"}, {"name": "Springfield", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Starbuck", "countryCode": "US", "stateCode": "MN"}, {"name": "Stearns County", "countryCode": "US", "stateCode": "MN"}, {"name": "Steele County", "countryCode": "US", "stateCode": "MN"}, {"name": "Stevens County", "countryCode": "US", "stateCode": "MN"}, {"name": "Stewartville", "countryCode": "US", "stateCode": "MN"}, {"name": "Stillwater", "countryCode": "US", "stateCode": "MN"}, {"name": "Swift County", "countryCode": "US", "stateCode": "MN"}, {"name": "Taylors Falls", "countryCode": "US", "stateCode": "MN"}, {"name": "Thief River Falls", "countryCode": "US", "stateCode": "MN"}, {"name": "Todd County", "countryCode": "US", "stateCode": "MN"}, {"name": "Tonka Bay", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Traverse County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Two Harbors", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Vadnais Heights", "countryCode": "US", "stateCode": "MN"}, {"name": "Victoria", "countryCode": "US", "stateCode": "MN"}, {"name": "Vineland", "countryCode": "US", "stateCode": "MN"}, {"name": "Virginia", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Wabasha County", "countryCode": "US", "stateCode": "MN"}, {"name": "Waconia", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Wadena County", "countryCode": "US", "stateCode": "MN"}, {"name": "Waite Park", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Wanamingo", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Warroad", "countryCode": "US", "stateCode": "MN"}, {"name": "Waseca", "countryCode": "US", "stateCode": "MN"}, {"name": "Waseca County", "countryCode": "US", "stateCode": "MN"}, {"name": "Washington County", "countryCode": "US", "stateCode": "MN"}, {"name": "Watertown", "countryCode": "US", "stateCode": "MN"}, {"name": "Waterville", "countryCode": "US", "stateCode": "MN"}, {"name": "Watonwan County", "countryCode": "US", "stateCode": "MN"}, {"name": "Waverly", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "West Coon Rapids", "countryCode": "US", "stateCode": "MN"}, {"name": "West Saint Paul", "countryCode": "US", "stateCode": "MN"}, {"name": "Wheaton", "countryCode": "US", "stateCode": "MN"}, {"name": "White Bear Lake", "countryCode": "US", "stateCode": "MN"}, {"name": "Wilkin County", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Winnebago", "countryCode": "US", "stateCode": "MN"}, {"name": "Winona", "countryCode": "US", "stateCode": "MN"}, {"name": "Winona County", "countryCode": "US", "stateCode": "MN"}, {"name": "Winsted", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "Woodbury", "countryCode": "US", "stateCode": "MN"}, {"name": "Worthington", "countryCode": "US", "stateCode": "MN"}, {"name": "Wright County", "countryCode": "US", "stateCode": "MN"}, {"name": "Wyoming", "countryCode": "US", "stateCode": "MN"}, {"name": "Yellow Medicine County", "countryCode": "US", "stateCode": "MN"}, {"name": "Young America (historical)", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN"}]