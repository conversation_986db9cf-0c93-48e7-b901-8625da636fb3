[{"name": "Ada", "countryCode": "US", "stateCode": "MN", "latitude": "47.29969000", "longitude": "-96.51535000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.63497000", "longitude": "-95.93280000"}, {"name": "Afton", "countryCode": "US", "stateCode": "MN", "latitude": "44.90275000", "longitude": "-92.78354000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.53301000", "longitude": "-93.71025000"}, {"name": "Aitkin County", "countryCode": "US", "stateCode": "MN", "latitude": "46.60826000", "longitude": "-93.41543000"}, {"name": "Albany", "countryCode": "US", "stateCode": "MN", "latitude": "45.62996000", "longitude": "-94.57000000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.64801000", "longitude": "-93.36827000"}, {"name": "Albertville", "countryCode": "US", "stateCode": "MN", "latitude": "45.23774000", "longitude": "-93.65441000"}, {"name": "Alexandria", "countryCode": "US", "stateCode": "MN", "latitude": "45.88524000", "longitude": "-95.37754000"}, {"name": "Andover", "countryCode": "US", "stateCode": "MN", "latitude": "45.23330000", "longitude": "-93.29134000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.26274000", "longitude": "-94.12443000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.19774000", "longitude": "-93.38718000"}, {"name": "Anoka County", "countryCode": "US", "stateCode": "MN", "latitude": "45.27324000", "longitude": "-93.24645000"}, {"name": "Apple Valley", "countryCode": "US", "stateCode": "MN", "latitude": "44.73191000", "longitude": "-93.21772000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.19691000", "longitude": "-96.01977000"}, {"name": "Arden Hills", "countryCode": "US", "stateCode": "MN", "latitude": "45.05024000", "longitude": "-93.15661000"}, {"name": "Arlington", "countryCode": "US", "stateCode": "MN", "latitude": "44.60830000", "longitude": "-94.08053000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.88022000", "longitude": "-92.09047000"}, {"name": "Atwater", "countryCode": "US", "stateCode": "MN", "latitude": "45.13885000", "longitude": "-94.77806000"}, {"name": "Aurora", "countryCode": "US", "stateCode": "MN", "latitude": "47.52993000", "longitude": "-92.23712000"}, {"name": "Austin", "countryCode": "US", "stateCode": "MN", "latitude": "43.66663000", "longitude": "-92.97464000"}, {"name": "Avon", "countryCode": "US", "stateCode": "MN", "latitude": "45.60913000", "longitude": "-94.45167000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.70853000", "longitude": "-91.94460000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.52162000", "longitude": "-95.39835000"}, {"name": "Barnesville", "countryCode": "US", "stateCode": "MN", "latitude": "46.65218000", "longitude": "-96.41979000"}, {"name": "Baudette", "countryCode": "US", "stateCode": "MN", "latitude": "48.71247000", "longitude": "-94.59993000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.34330000", "longitude": "-94.28667000"}, {"name": "Bayport", "countryCode": "US", "stateCode": "MN", "latitude": "45.02136000", "longitude": "-92.78104000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.39330000", "longitude": "-93.87692000"}, {"name": "Becker County", "countryCode": "US", "stateCode": "MN", "latitude": "46.93465000", "longitude": "-95.67392000"}, {"name": "Belle Plaine", "countryCode": "US", "stateCode": "MN", "latitude": "44.62274000", "longitude": "-93.76857000"}, {"name": "Beltrami County", "countryCode": "US", "stateCode": "MN", "latitude": "47.97378000", "longitude": "-94.93765000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.47356000", "longitude": "-94.88028000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.31496000", "longitude": "-95.60003000"}, {"name": "Benton County", "countryCode": "US", "stateCode": "MN", "latitude": "45.69913000", "longitude": "-93.99884000"}, {"name": "Big Lake", "countryCode": "US", "stateCode": "MN", "latitude": "45.33246000", "longitude": "-93.74608000"}, {"name": "Big Stone County", "countryCode": "US", "stateCode": "MN", "latitude": "45.42610000", "longitude": "-96.41092000"}, {"name": "Birchwood", "countryCode": "US", "stateCode": "MN", "latitude": "45.06108000", "longitude": "-92.97605000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.16080000", "longitude": "-93.23495000"}, {"name": "Blooming Prairie", "countryCode": "US", "stateCode": "MN", "latitude": "43.86663000", "longitude": "-93.05103000"}, {"name": "Bloomington", "countryCode": "US", "stateCode": "MN", "latitude": "44.84080000", "longitude": "-93.29828000"}, {"name": "Blue Earth", "countryCode": "US", "stateCode": "MN", "latitude": "43.63746000", "longitude": "-94.10218000"}, {"name": "Blue Earth County", "countryCode": "US", "stateCode": "MN", "latitude": "44.03459000", "longitude": "-94.06703000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.72274000", "longitude": "-93.17078000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.35802000", "longitude": "-94.20083000"}, {"name": "Branch", "countryCode": "US", "stateCode": "MN", "latitude": "45.48524000", "longitude": "-92.96188000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.26357000", "longitude": "-96.58813000"}, {"name": "Breezy Point", "countryCode": "US", "stateCode": "MN", "latitude": "46.59001000", "longitude": "-94.21982000"}, {"name": "Brooklyn Center", "countryCode": "US", "stateCode": "MN", "latitude": "45.07608000", "longitude": "-93.33273000"}, {"name": "Brooklyn Park", "countryCode": "US", "stateCode": "MN", "latitude": "45.09413000", "longitude": "-93.35634000"}, {"name": "Brown County", "countryCode": "US", "stateCode": "MN", "latitude": "44.24217000", "longitude": "-94.72748000"}, {"name": "Buffalo", "countryCode": "US", "stateCode": "MN", "latitude": "45.17191000", "longitude": "-93.87469000"}, {"name": "Burnsville", "countryCode": "US", "stateCode": "MN", "latitude": "44.76774000", "longitude": "-93.27772000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.03274000", "longitude": "-92.64546000"}, {"name": "Caledonia", "countryCode": "US", "stateCode": "MN", "latitude": "43.63469000", "longitude": "-91.49681000"}, {"name": "Cambridge", "countryCode": "US", "stateCode": "MN", "latitude": "45.57274000", "longitude": "-93.22439000"}, {"name": "Canby", "countryCode": "US", "stateCode": "MN", "latitude": "44.70885000", "longitude": "-96.27643000"}, {"name": "Cannon Falls", "countryCode": "US", "stateCode": "MN", "latitude": "44.50691000", "longitude": "-92.90548000"}, {"name": "Carlton", "countryCode": "US", "stateCode": "MN", "latitude": "46.66383000", "longitude": "-92.42491000"}, {"name": "Carlton County", "countryCode": "US", "stateCode": "MN", "latitude": "46.59240000", "longitude": "-92.67705000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.76357000", "longitude": "-93.62579000"}, {"name": "Carver County", "countryCode": "US", "stateCode": "MN", "latitude": "44.82076000", "longitude": "-93.80258000"}, {"name": "Cass County", "countryCode": "US", "stateCode": "MN", "latitude": "46.94959000", "longitude": "-94.32535000"}, {"name": "Center City", "countryCode": "US", "stateCode": "MN", "latitude": "45.39385000", "longitude": "-92.81660000"}, {"name": "Centerville", "countryCode": "US", "stateCode": "MN", "latitude": "45.16302000", "longitude": "-93.05578000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.18885000", "longitude": "-93.39745000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.86219000", "longitude": "-93.53079000"}, {"name": "Chaska", "countryCode": "US", "stateCode": "MN", "latitude": "44.78941000", "longitude": "-93.60218000"}, {"name": "Chatfield", "countryCode": "US", "stateCode": "MN", "latitude": "43.84552000", "longitude": "-92.18905000"}, {"name": "Chippewa County", "countryCode": "US", "stateCode": "MN", "latitude": "45.02234000", "longitude": "-95.56669000"}, {"name": "Chisago City", "countryCode": "US", "stateCode": "MN", "latitude": "45.37358000", "longitude": "-92.88994000"}, {"name": "Chisago County", "countryCode": "US", "stateCode": "MN", "latitude": "45.50247000", "longitude": "-92.90834000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.48910000", "longitude": "-92.88380000"}, {"name": "Circle Pines", "countryCode": "US", "stateCode": "MN", "latitude": "45.14858000", "longitude": "-93.15161000"}, {"name": "Clara City", "countryCode": "US", "stateCode": "MN", "latitude": "44.95496000", "longitude": "-95.36640000"}, {"name": "Clay County", "countryCode": "US", "stateCode": "MN", "latitude": "46.89234000", "longitude": "-96.49065000"}, {"name": "Clearwater", "countryCode": "US", "stateCode": "MN", "latitude": "45.41941000", "longitude": "-94.04887000"}, {"name": "Clearwater County", "countryCode": "US", "stateCode": "MN", "latitude": "47.57766000", "longitude": "-95.37903000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.72161000", "longitude": "-92.45936000"}, {"name": "Cohasset", "countryCode": "US", "stateCode": "MN", "latitude": "47.26356000", "longitude": "-93.62022000"}, {"name": "Cokato", "countryCode": "US", "stateCode": "MN", "latitude": "45.07580000", "longitude": "-94.18998000"}, {"name": "Cold Spring", "countryCode": "US", "stateCode": "MN", "latitude": "45.45580000", "longitude": "-94.42888000"}, {"name": "Coleraine", "countryCode": "US", "stateCode": "MN", "latitude": "47.28883000", "longitude": "-93.42771000"}, {"name": "Collegeville", "countryCode": "US", "stateCode": "MN", "latitude": "45.59441000", "longitude": "-94.36305000"}, {"name": "Cologne", "countryCode": "US", "stateCode": "MN", "latitude": "44.77163000", "longitude": "-93.78135000"}, {"name": "Columbia Heights", "countryCode": "US", "stateCode": "MN", "latitude": "45.04080000", "longitude": "-93.26300000"}, {"name": "Columbus", "countryCode": "US", "stateCode": "MN", "latitude": "45.26522000", "longitude": "-93.05015000"}, {"name": "Cook County", "countryCode": "US", "stateCode": "MN", "latitude": "47.81684000", "longitude": "-90.54108000"}, {"name": "Coon Rapids", "countryCode": "US", "stateCode": "MN", "latitude": "45.11997000", "longitude": "-93.28773000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.09524000", "longitude": "-93.54746000"}, {"name": "Cottage Grove", "countryCode": "US", "stateCode": "MN", "latitude": "44.82774000", "longitude": "-92.94382000"}, {"name": "Cottonwood", "countryCode": "US", "stateCode": "MN", "latitude": "44.60885000", "longitude": "-95.67419000"}, {"name": "Cottonwood County", "countryCode": "US", "stateCode": "MN", "latitude": "44.00711000", "longitude": "-95.18115000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.77414000", "longitude": "-96.60812000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.48218000", "longitude": "-93.95776000"}, {"name": "Cross Lake", "countryCode": "US", "stateCode": "MN", "latitude": "46.65941000", "longitude": "-94.11387000"}, {"name": "Crow Wing County", "countryCode": "US", "stateCode": "MN", "latitude": "46.48237000", "longitude": "-94.07087000"}, {"name": "Crystal", "countryCode": "US", "stateCode": "MN", "latitude": "45.03274000", "longitude": "-93.36023000"}, {"name": "Dakota County", "countryCode": "US", "stateCode": "MN", "latitude": "44.67189000", "longitude": "-93.06544000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.08163000", "longitude": "-94.30693000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.93274000", "longitude": "-96.05448000"}, {"name": "Dayton", "countryCode": "US", "stateCode": "MN", "latitude": "45.24385000", "longitude": "-93.51496000"}, {"name": "Deephaven", "countryCode": "US", "stateCode": "MN", "latitude": "44.92969000", "longitude": "-93.52246000"}, {"name": "Delano", "countryCode": "US", "stateCode": "MN", "latitude": "45.04191000", "longitude": "-93.78913000"}, {"name": "Dellwood", "countryCode": "US", "stateCode": "MN", "latitude": "45.08997000", "longitude": "-92.97244000"}, {"name": "Detroit Lakes", "countryCode": "US", "stateCode": "MN", "latitude": "46.81718000", "longitude": "-95.84533000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.87663000", "longitude": "-96.70341000"}, {"name": "Dodge Center", "countryCode": "US", "stateCode": "MN", "latitude": "44.02802000", "longitude": "-92.85464000"}, {"name": "Dodge County", "countryCode": "US", "stateCode": "MN", "latitude": "44.02259000", "longitude": "-92.86205000"}, {"name": "Douglas County", "countryCode": "US", "stateCode": "MN", "latitude": "45.93372000", "longitude": "-95.45352000"}, {"name": "Duluth", "countryCode": "US", "stateCode": "MN", "latitude": "46.78327000", "longitude": "-92.10658000"}, {"name": "Dundas", "countryCode": "US", "stateCode": "MN", "latitude": "44.42941000", "longitude": "-93.20188000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.80413000", "longitude": "-93.16689000"}, {"name": "Eagle Lake", "countryCode": "US", "stateCode": "MN", "latitude": "44.16497000", "longitude": "-93.88134000"}, {"name": "East Bethel", "countryCode": "US", "stateCode": "MN", "latitude": "45.31941000", "longitude": "-93.20245000"}, {"name": "East Grand Forks", "countryCode": "US", "stateCode": "MN", "latitude": "47.92998000", "longitude": "-97.02452000"}, {"name": "East Gull Lake", "countryCode": "US", "stateCode": "MN", "latitude": "46.40802000", "longitude": "-94.35584000"}, {"name": "Eden Prairie", "countryCode": "US", "stateCode": "MN", "latitude": "44.85469000", "longitude": "-93.47079000"}, {"name": "Eden Valley", "countryCode": "US", "stateCode": "MN", "latitude": "45.32607000", "longitude": "-94.54611000"}, {"name": "Edgerton", "countryCode": "US", "stateCode": "MN", "latitude": "43.87247000", "longitude": "-96.12864000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.88969000", "longitude": "-93.34995000"}, {"name": "Elbow Lake", "countryCode": "US", "stateCode": "MN", "latitude": "45.99413000", "longitude": "-95.97672000"}, {"name": "Elgin", "countryCode": "US", "stateCode": "MN", "latitude": "44.13024000", "longitude": "-92.25156000"}, {"name": "Elk River", "countryCode": "US", "stateCode": "MN", "latitude": "45.30385000", "longitude": "-93.56718000"}, {"name": "Elko New Market", "countryCode": "US", "stateCode": "MN", "latitude": "44.56472000", "longitude": "-93.32694000"}, {"name": "Ely", "countryCode": "US", "stateCode": "MN", "latitude": "47.90324000", "longitude": "-91.86709000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.70578000", "longitude": "-92.36325000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.46243000", "longitude": "-92.53991000"}, {"name": "Excelsior", "countryCode": "US", "stateCode": "MN", "latitude": "44.90330000", "longitude": "-93.56635000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.98830000", "longitude": "-92.22850000"}, {"name": "Fairfax", "countryCode": "US", "stateCode": "MN", "latitude": "44.52913000", "longitude": "-94.72082000"}, {"name": "Fairmont", "countryCode": "US", "stateCode": "MN", "latitude": "43.65218000", "longitude": "-94.46108000"}, {"name": "Falcon Heights", "countryCode": "US", "stateCode": "MN", "latitude": "44.99163000", "longitude": "-93.16633000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.29496000", "longitude": "-93.26883000"}, {"name": "Faribault County", "countryCode": "US", "stateCode": "MN", "latitude": "43.67393000", "longitude": "-93.94800000"}, {"name": "Farmington", "countryCode": "US", "stateCode": "MN", "latitude": "44.64024000", "longitude": "-93.14355000"}, {"name": "Fergus Falls", "countryCode": "US", "stateCode": "MN", "latitude": "46.28302000", "longitude": "-96.07756000"}, {"name": "Fillmore County", "countryCode": "US", "stateCode": "MN", "latitude": "43.67400000", "longitude": "-92.09017000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.66469000", "longitude": "-93.90970000"}, {"name": "Forest Lake", "countryCode": "US", "stateCode": "MN", "latitude": "45.27886000", "longitude": "-92.98522000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.57635000", "longitude": "-95.75141000"}, {"name": "Frazee", "countryCode": "US", "stateCode": "MN", "latitude": "46.72801000", "longitude": "-95.70088000"}, {"name": "Freeborn County", "countryCode": "US", "stateCode": "MN", "latitude": "43.67384000", "longitude": "-93.34882000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.08608000", "longitude": "-93.26328000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.87052000", "longitude": "-95.60029000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.55302000", "longitude": "-94.22053000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.48882000", "longitude": "-92.46491000"}, {"name": "Glencoe", "countryCode": "US", "stateCode": "MN", "latitude": "44.76913000", "longitude": "-94.15164000"}, {"name": "Glenwood", "countryCode": "US", "stateCode": "MN", "latitude": "45.65024000", "longitude": "-95.38976000"}, {"name": "Glyndon", "countryCode": "US", "stateCode": "MN", "latitude": "46.87524000", "longitude": "-96.57896000"}, {"name": "Golden Valley", "countryCode": "US", "stateCode": "MN", "latitude": "45.00969000", "longitude": "-93.34912000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.40052000", "longitude": "-92.62380000"}, {"name": "Goodhue County", "countryCode": "US", "stateCode": "MN", "latitude": "44.40985000", "longitude": "-92.72259000"}, {"name": "Goodview", "countryCode": "US", "stateCode": "MN", "latitude": "44.06246000", "longitude": "-91.69571000"}, {"name": "Grand Marais", "countryCode": "US", "stateCode": "MN", "latitude": "47.75045000", "longitude": "-90.33427000"}, {"name": "Grand Meadow", "countryCode": "US", "stateCode": "MN", "latitude": "43.70580000", "longitude": "-92.57212000"}, {"name": "Grand Rapids", "countryCode": "US", "stateCode": "MN", "latitude": "47.23717000", "longitude": "-93.53021000"}, {"name": "Granite Falls", "countryCode": "US", "stateCode": "MN", "latitude": "44.80996000", "longitude": "-95.54558000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.08441000", "longitude": "-92.91049000"}, {"name": "Grant County", "countryCode": "US", "stateCode": "MN", "latitude": "45.93405000", "longitude": "-96.01218000"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "MN", "latitude": "45.10330000", "longitude": "-93.69135000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "48.77443000", "longitude": "-96.94645000"}, {"name": "Ham Lake", "countryCode": "US", "stateCode": "MN", "latitude": "45.25024000", "longitude": "-93.24995000"}, {"name": "Hanover", "countryCode": "US", "stateCode": "MN", "latitude": "45.15580000", "longitude": "-93.66635000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.58635000", "longitude": "-92.97466000"}, {"name": "Hastings", "countryCode": "US", "stateCode": "MN", "latitude": "44.74330000", "longitude": "-92.85243000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.88079000", "longitude": "-96.31673000"}, {"name": "Hayfield", "countryCode": "US", "stateCode": "MN", "latitude": "43.89052000", "longitude": "-92.84769000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.74385000", "longitude": "-94.71555000"}, {"name": "Hennepin County", "countryCode": "US", "stateCode": "MN", "latitude": "45.00458000", "longitude": "-93.47688000"}, {"name": "Hermantown", "countryCode": "US", "stateCode": "MN", "latitude": "46.80689000", "longitude": "-92.23825000"}, {"name": "Hibbing", "countryCode": "US", "stateCode": "MN", "latitude": "47.42715000", "longitude": "-92.93769000"}, {"name": "Hinckley", "countryCode": "US", "stateCode": "MN", "latitude": "46.01134000", "longitude": "-92.94437000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.92496000", "longitude": "-93.46273000"}, {"name": "Houston County", "countryCode": "US", "stateCode": "MN", "latitude": "43.67144000", "longitude": "-91.49283000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.06080000", "longitude": "-94.07331000"}, {"name": "Hoyt Lakes", "countryCode": "US", "stateCode": "MN", "latitude": "47.51965000", "longitude": "-92.13851000"}, {"name": "Hubbard County", "countryCode": "US", "stateCode": "MN", "latitude": "47.10865000", "longitude": "-94.91664000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.15997000", "longitude": "-92.99327000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.88774000", "longitude": "-94.36971000"}, {"name": "Independence", "countryCode": "US", "stateCode": "MN", "latitude": "45.02524000", "longitude": "-93.70746000"}, {"name": "International Falls", "countryCode": "US", "stateCode": "MN", "latitude": "48.60105000", "longitude": "-93.41098000"}, {"name": "Inver Grove Heights", "countryCode": "US", "stateCode": "MN", "latitude": "44.84802000", "longitude": "-93.04272000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.49024000", "longitude": "-93.24773000"}, {"name": "Isanti County", "countryCode": "US", "stateCode": "MN", "latitude": "45.56149000", "longitude": "-93.29518000"}, {"name": "Itasca County", "countryCode": "US", "stateCode": "MN", "latitude": "47.50953000", "longitude": "-93.63200000"}, {"name": "Ivanhoe", "countryCode": "US", "stateCode": "MN", "latitude": "44.46330000", "longitude": "-96.24726000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.62079000", "longitude": "-94.98860000"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "MN", "latitude": "43.67417000", "longitude": "-95.15411000"}, {"name": "Janesville", "countryCode": "US", "stateCode": "MN", "latitude": "44.11608000", "longitude": "-93.70800000"}, {"name": "Jordan", "countryCode": "US", "stateCode": "MN", "latitude": "44.66691000", "longitude": "-93.62690000"}, {"name": "Kanabec County", "countryCode": "US", "stateCode": "MN", "latitude": "45.94522000", "longitude": "-93.29343000"}, {"name": "Kandiyohi County", "countryCode": "US", "stateCode": "MN", "latitude": "45.15238000", "longitude": "-95.00474000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.02996000", "longitude": "-92.75074000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.39966000", "longitude": "-93.07242000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.27219000", "longitude": "-92.98548000"}, {"name": "Kittson County", "countryCode": "US", "stateCode": "MN", "latitude": "48.77663000", "longitude": "-96.78285000"}, {"name": "Koochiching County", "countryCode": "US", "stateCode": "MN", "latitude": "48.24527000", "longitude": "-93.78337000"}, {"name": "La Crescent", "countryCode": "US", "stateCode": "MN", "latitude": "43.82802000", "longitude": "-91.30403000"}, {"name": "Lac qui Parle County", "countryCode": "US", "stateCode": "MN", "latitude": "44.99549000", "longitude": "-96.17348000"}, {"name": "Lake City", "countryCode": "US", "stateCode": "MN", "latitude": "44.44968000", "longitude": "-92.26820000"}, {"name": "Lake County", "countryCode": "US", "stateCode": "MN", "latitude": "47.52317000", "longitude": "-91.40885000"}, {"name": "Lake Crystal", "countryCode": "US", "stateCode": "MN", "latitude": "44.10580000", "longitude": "-94.21885000"}, {"name": "Lake Elmo", "countryCode": "US", "stateCode": "MN", "latitude": "44.99580000", "longitude": "-92.87938000"}, {"name": "Lake Saint Croix Beach", "countryCode": "US", "stateCode": "MN", "latitude": "44.92080000", "longitude": "-92.76687000"}, {"name": "Lake Shore", "countryCode": "US", "stateCode": "MN", "latitude": "46.48552000", "longitude": "-94.36056000"}, {"name": "Lake of the Woods County", "countryCode": "US", "stateCode": "MN", "latitude": "48.77051000", "longitude": "-94.90503000"}, {"name": "Lakefield", "countryCode": "US", "stateCode": "MN", "latitude": "43.67746000", "longitude": "-95.17166000"}, {"name": "Lakeland", "countryCode": "US", "stateCode": "MN", "latitude": "44.95636000", "longitude": "-92.76576000"}, {"name": "Lakeville", "countryCode": "US", "stateCode": "MN", "latitude": "44.64969000", "longitude": "-93.24272000"}, {"name": "Lauderdale", "countryCode": "US", "stateCode": "MN", "latitude": "44.99858000", "longitude": "-93.20578000"}, {"name": "Le Center", "countryCode": "US", "stateCode": "MN", "latitude": "44.38941000", "longitude": "-93.73023000"}, {"name": "Le Sueur", "countryCode": "US", "stateCode": "MN", "latitude": "44.46135000", "longitude": "-93.91524000"}, {"name": "Le Sueur County", "countryCode": "US", "stateCode": "MN", "latitude": "44.37143000", "longitude": "-93.73008000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.88385000", "longitude": "-94.04164000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.98441000", "longitude": "-91.86932000"}, {"name": "Lexington", "countryCode": "US", "stateCode": "MN", "latitude": "45.14247000", "longitude": "-93.16328000"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "MN", "latitude": "44.41260000", "longitude": "-96.26709000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.38941000", "longitude": "-92.84799000"}, {"name": "Lino Lakes", "countryCode": "US", "stateCode": "MN", "latitude": "45.16024000", "longitude": "-93.08883000"}, {"name": "Litchfield", "countryCode": "US", "stateCode": "MN", "latitude": "45.12718000", "longitude": "-94.52805000"}, {"name": "Little Canada", "countryCode": "US", "stateCode": "MN", "latitude": "45.02691000", "longitude": "-93.08772000"}, {"name": "Little Falls", "countryCode": "US", "stateCode": "MN", "latitude": "45.97635000", "longitude": "-94.36250000"}, {"name": "Little Rock", "countryCode": "US", "stateCode": "MN", "latitude": "47.86801000", "longitude": "-95.11055000"}, {"name": "Long Lake", "countryCode": "US", "stateCode": "MN", "latitude": "44.98663000", "longitude": "-93.57162000"}, {"name": "Long Prairie", "countryCode": "US", "stateCode": "MN", "latitude": "45.97469000", "longitude": "-94.86558000"}, {"name": "Lonsdale", "countryCode": "US", "stateCode": "MN", "latitude": "44.48024000", "longitude": "-93.42856000"}, {"name": "Luverne", "countryCode": "US", "stateCode": "MN", "latitude": "43.65414000", "longitude": "-96.21281000"}, {"name": "Lyon County", "countryCode": "US", "stateCode": "MN", "latitude": "44.41349000", "longitude": "-95.83897000"}, {"name": "Madelia", "countryCode": "US", "stateCode": "MN", "latitude": "44.05079000", "longitude": "-94.41830000"}, {"name": "Madison", "countryCode": "US", "stateCode": "MN", "latitude": "45.00968000", "longitude": "-96.19588000"}, {"name": "Madison Lake", "countryCode": "US", "stateCode": "MN", "latitude": "44.20441000", "longitude": "-93.81551000"}, {"name": "Ma<PERSON><PERSON>n", "countryCode": "US", "stateCode": "MN", "latitude": "47.31524000", "longitude": "-95.96865000"}, {"name": "Mahnomen County", "countryCode": "US", "stateCode": "MN", "latitude": "47.32524000", "longitude": "-95.80905000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.06969000", "longitude": "-92.95160000"}, {"name": "Mankato", "countryCode": "US", "stateCode": "MN", "latitude": "44.15906000", "longitude": "-94.00915000"}, {"name": "Mantorville", "countryCode": "US", "stateCode": "MN", "latitude": "44.06913000", "longitude": "-92.75575000"}, {"name": "Maple Grove", "countryCode": "US", "stateCode": "MN", "latitude": "45.07246000", "longitude": "-93.45579000"}, {"name": "Maple Lake", "countryCode": "US", "stateCode": "MN", "latitude": "45.22913000", "longitude": "-94.00192000"}, {"name": "Maple Plain", "countryCode": "US", "stateCode": "MN", "latitude": "45.00719000", "longitude": "-93.65579000"}, {"name": "<PERSON>ton", "countryCode": "US", "stateCode": "MN", "latitude": "43.92885000", "longitude": "-93.95606000"}, {"name": "Maplewood", "countryCode": "US", "stateCode": "MN", "latitude": "44.95302000", "longitude": "-92.99522000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.44690000", "longitude": "-95.78835000"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "MN", "latitude": "48.35813000", "longitude": "-96.36847000"}, {"name": "Martin County", "countryCode": "US", "stateCode": "MN", "latitude": "43.67436000", "longitude": "-94.55107000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.88496000", "longitude": "-93.88775000"}, {"name": "McLeod County", "countryCode": "US", "stateCode": "MN", "latitude": "44.82354000", "longitude": "-94.27242000"}, {"name": "Medford", "countryCode": "US", "stateCode": "MN", "latitude": "44.17413000", "longitude": "-93.24632000"}, {"name": "Medina", "countryCode": "US", "stateCode": "MN", "latitude": "45.03524000", "longitude": "-93.58246000"}, {"name": "Meeker County", "countryCode": "US", "stateCode": "MN", "latitude": "45.12312000", "longitude": "-94.52731000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.67469000", "longitude": "-94.80752000"}, {"name": "Men<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.75385000", "longitude": "-95.09808000"}, {"name": "Mendota Heights", "countryCode": "US", "stateCode": "MN", "latitude": "44.88358000", "longitude": "-93.13827000"}, {"name": "Milaca", "countryCode": "US", "stateCode": "MN", "latitude": "45.75580000", "longitude": "-93.65441000"}, {"name": "Mille Lacs County", "countryCode": "US", "stateCode": "MN", "latitude": "45.93805000", "longitude": "-93.63009000"}, {"name": "Minneapolis", "countryCode": "US", "stateCode": "MN", "latitude": "44.97997000", "longitude": "-93.26384000"}, {"name": "Minneota", "countryCode": "US", "stateCode": "MN", "latitude": "44.55885000", "longitude": "-95.98559000"}, {"name": "Minnetonka", "countryCode": "US", "stateCode": "MN", "latitude": "44.91330000", "longitude": "-93.50329000"}, {"name": "Minnetonka Mills", "countryCode": "US", "stateCode": "MN", "latitude": "44.94107000", "longitude": "-93.44190000"}, {"name": "Minnetrist<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.93830000", "longitude": "-93.71774000"}, {"name": "Montevideo", "countryCode": "US", "stateCode": "MN", "latitude": "44.94803000", "longitude": "-95.71701000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.43885000", "longitude": "-93.58134000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.30552000", "longitude": "-93.79414000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.06496000", "longitude": "-93.91108000"}, {"name": "Moorhead", "countryCode": "US", "stateCode": "MN", "latitude": "46.87386000", "longitude": "-96.76951000"}, {"name": "Moose Lake", "countryCode": "US", "stateCode": "MN", "latitude": "46.45411000", "longitude": "-92.76187000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.87690000", "longitude": "-93.29384000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.58607000", "longitude": "-95.91394000"}, {"name": "Morrison County", "countryCode": "US", "stateCode": "MN", "latitude": "46.01262000", "longitude": "-94.26842000"}, {"name": "Mound", "countryCode": "US", "stateCode": "MN", "latitude": "44.93663000", "longitude": "-93.66607000"}, {"name": "Mounds View", "countryCode": "US", "stateCode": "MN", "latitude": "45.10497000", "longitude": "-93.20856000"}, {"name": "Mountain Iron", "countryCode": "US", "stateCode": "MN", "latitude": "47.53243000", "longitude": "-92.62351000"}, {"name": "Mountain Lake", "countryCode": "US", "stateCode": "MN", "latitude": "43.93885000", "longitude": "-94.92971000"}, {"name": "Mower County", "countryCode": "US", "stateCode": "MN", "latitude": "43.67147000", "longitude": "-92.75251000"}, {"name": "Murray County", "countryCode": "US", "stateCode": "MN", "latitude": "44.02212000", "longitude": "-95.76328000"}, {"name": "New Brighton", "countryCode": "US", "stateCode": "MN", "latitude": "45.06552000", "longitude": "-93.20189000"}, {"name": "New Hope", "countryCode": "US", "stateCode": "MN", "latitude": "45.03802000", "longitude": "-93.38662000"}, {"name": "New London", "countryCode": "US", "stateCode": "MN", "latitude": "45.30108000", "longitude": "-94.94418000"}, {"name": "New Prague", "countryCode": "US", "stateCode": "MN", "latitude": "44.54330000", "longitude": "-93.57607000"}, {"name": "New Richland", "countryCode": "US", "stateCode": "MN", "latitude": "43.89385000", "longitude": "-93.49383000"}, {"name": "New Ulm", "countryCode": "US", "stateCode": "MN", "latitude": "44.31246000", "longitude": "-94.46053000"}, {"name": "New York Mills", "countryCode": "US", "stateCode": "MN", "latitude": "46.51802000", "longitude": "-95.37615000"}, {"name": "Newport", "countryCode": "US", "stateCode": "MN", "latitude": "44.86636000", "longitude": "-93.00049000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.27608000", "longitude": "-94.18746000"}, {"name": "Nicollet County", "countryCode": "US", "stateCode": "MN", "latitude": "44.34989000", "longitude": "-94.24730000"}, {"name": "Nisswa", "countryCode": "US", "stateCode": "MN", "latitude": "46.52052000", "longitude": "-94.28861000"}, {"name": "Nobles County", "countryCode": "US", "stateCode": "MN", "latitude": "43.67424000", "longitude": "-95.75339000"}, {"name": "Norman County", "countryCode": "US", "stateCode": "MN", "latitude": "47.32648000", "longitude": "-96.45528000"}, {"name": "North Branch", "countryCode": "US", "stateCode": "MN", "latitude": "45.51135000", "longitude": "-92.98022000"}, {"name": "North Mankato", "countryCode": "US", "stateCode": "MN", "latitude": "44.17330000", "longitude": "-94.03385000"}, {"name": "North Oaks", "countryCode": "US", "stateCode": "MN", "latitude": "45.10274000", "longitude": "-93.07911000"}, {"name": "North Saint Paul", "countryCode": "US", "stateCode": "MN", "latitude": "45.01247000", "longitude": "-92.99188000"}, {"name": "Northfield", "countryCode": "US", "stateCode": "MN", "latitude": "44.45830000", "longitude": "-93.16160000"}, {"name": "Norwood (historical)", "countryCode": "US", "stateCode": "MN", "latitude": "44.76802000", "longitude": "-93.92747000"}, {"name": "Norwood Young America", "countryCode": "US", "stateCode": "MN", "latitude": "44.77357000", "longitude": "-93.92163000"}, {"name": "Nowthen", "countryCode": "US", "stateCode": "MN", "latitude": "45.32802000", "longitude": "-93.47023000"}, {"name": "Oak Grove", "countryCode": "US", "stateCode": "MN", "latitude": "45.34080000", "longitude": "-93.32690000"}, {"name": "Oak Park Heights", "countryCode": "US", "stateCode": "MN", "latitude": "45.03136000", "longitude": "-92.79298000"}, {"name": "Oakdale", "countryCode": "US", "stateCode": "MN", "latitude": "44.96302000", "longitude": "-92.96494000"}, {"name": "Oakport", "countryCode": "US", "stateCode": "MN", "latitude": "46.93191000", "longitude": "-96.77897000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.77635000", "longitude": "-94.98972000"}, {"name": "Olmsted County", "countryCode": "US", "stateCode": "MN", "latitude": "44.00375000", "longitude": "-92.40177000"}, {"name": "Orono", "countryCode": "US", "stateCode": "MN", "latitude": "44.97135000", "longitude": "-93.60440000"}, {"name": "Oronoco", "countryCode": "US", "stateCode": "MN", "latitude": "44.16608000", "longitude": "-92.53491000"}, {"name": "Ortonville", "countryCode": "US", "stateCode": "MN", "latitude": "45.30469000", "longitude": "-96.44478000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.86691000", "longitude": "-95.15225000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.11941000", "longitude": "-93.40245000"}, {"name": "Otsego", "countryCode": "US", "stateCode": "MN", "latitude": "45.27413000", "longitude": "-93.59135000"}, {"name": "Otter Tail County", "countryCode": "US", "stateCode": "MN", "latitude": "46.40880000", "longitude": "-95.70800000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.08385000", "longitude": "-93.22604000"}, {"name": "Park Rapids", "countryCode": "US", "stateCode": "MN", "latitude": "46.92218000", "longitude": "-95.05863000"}, {"name": "Parkers Prairie", "countryCode": "US", "stateCode": "MN", "latitude": "46.15302000", "longitude": "-95.32892000"}, {"name": "Parkville", "countryCode": "US", "stateCode": "MN", "latitude": "47.53104000", "longitude": "-92.57907000"}, {"name": "Paynesville", "countryCode": "US", "stateCode": "MN", "latitude": "45.38052000", "longitude": "-94.71195000"}, {"name": "Pelican Rapids", "countryCode": "US", "stateCode": "MN", "latitude": "46.57079000", "longitude": "-96.08311000"}, {"name": "Pennington County", "countryCode": "US", "stateCode": "MN", "latitude": "48.06623000", "longitude": "-96.03667000"}, {"name": "Pequot Lakes", "countryCode": "US", "stateCode": "MN", "latitude": "46.60302000", "longitude": "-94.30944000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.59440000", "longitude": "-95.57254000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.98163000", "longitude": "-94.10471000"}, {"name": "Pine City", "countryCode": "US", "stateCode": "MN", "latitude": "45.82607000", "longitude": "-92.96854000"}, {"name": "Pine County", "countryCode": "US", "stateCode": "MN", "latitude": "46.12077000", "longitude": "-92.74127000"}, {"name": "Pine Island", "countryCode": "US", "stateCode": "MN", "latitude": "44.20135000", "longitude": "-92.64630000"}, {"name": "Pipestone", "countryCode": "US", "stateCode": "MN", "latitude": "44.00053000", "longitude": "-96.31753000"}, {"name": "Pipestone County", "countryCode": "US", "stateCode": "MN", "latitude": "44.02300000", "longitude": "-96.25864000"}, {"name": "Plainview", "countryCode": "US", "stateCode": "MN", "latitude": "44.16497000", "longitude": "-92.17156000"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "MN", "latitude": "45.01052000", "longitude": "-93.45551000"}, {"name": "Polk County", "countryCode": "US", "stateCode": "MN", "latitude": "47.77385000", "longitude": "-96.40181000"}, {"name": "Pope County", "countryCode": "US", "stateCode": "MN", "latitude": "45.58602000", "longitude": "-95.44448000"}, {"name": "Preston", "countryCode": "US", "stateCode": "MN", "latitude": "43.67024000", "longitude": "-92.08322000"}, {"name": "Princeton", "countryCode": "US", "stateCode": "MN", "latitude": "45.56997000", "longitude": "-93.58163000"}, {"name": "Prior <PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.71330000", "longitude": "-93.42273000"}, {"name": "Proctor", "countryCode": "US", "stateCode": "MN", "latitude": "46.74716000", "longitude": "-92.22547000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.26110000", "longitude": "-93.45000000"}, {"name": "Ramsey County", "countryCode": "US", "stateCode": "MN", "latitude": "45.01706000", "longitude": "-93.09961000"}, {"name": "Red Lake", "countryCode": "US", "stateCode": "MN", "latitude": "47.87635000", "longitude": "-95.01694000"}, {"name": "Red Lake County", "countryCode": "US", "stateCode": "MN", "latitude": "47.87169000", "longitude": "-96.09530000"}, {"name": "Red Lake Falls", "countryCode": "US", "stateCode": "MN", "latitude": "47.88219000", "longitude": "-96.27421000"}, {"name": "Red Wing", "countryCode": "US", "stateCode": "MN", "latitude": "44.56247000", "longitude": "-92.53380000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.87857000", "longitude": "-94.91305000"}, {"name": "Redwood County", "countryCode": "US", "stateCode": "MN", "latitude": "44.40366000", "longitude": "-95.25383000"}, {"name": "Redwood Falls", "countryCode": "US", "stateCode": "MN", "latitude": "44.53940000", "longitude": "-95.11694000"}, {"name": "Renville", "countryCode": "US", "stateCode": "MN", "latitude": "44.78913000", "longitude": "-95.21167000"}, {"name": "Renville County", "countryCode": "US", "stateCode": "MN", "latitude": "44.72681000", "longitude": "-94.94714000"}, {"name": "Rice", "countryCode": "US", "stateCode": "MN", "latitude": "45.75191000", "longitude": "-94.22027000"}, {"name": "Rice County", "countryCode": "US", "stateCode": "MN", "latitude": "44.35426000", "longitude": "-93.29668000"}, {"name": "Richfield", "countryCode": "US", "stateCode": "MN", "latitude": "44.88330000", "longitude": "-93.28300000"}, {"name": "Richmond", "countryCode": "US", "stateCode": "MN", "latitude": "45.45413000", "longitude": "-94.51833000"}, {"name": "Robbinsdale", "countryCode": "US", "stateCode": "MN", "latitude": "45.03219000", "longitude": "-93.33856000"}, {"name": "Rochester", "countryCode": "US", "stateCode": "MN", "latitude": "44.02163000", "longitude": "-92.46990000"}, {"name": "Rock County", "countryCode": "US", "stateCode": "MN", "latitude": "43.67463000", "longitude": "-96.25321000"}, {"name": "Rock Creek", "countryCode": "US", "stateCode": "MN", "latitude": "45.75746000", "longitude": "-92.96243000"}, {"name": "Rockford", "countryCode": "US", "stateCode": "MN", "latitude": "45.08830000", "longitude": "-93.73441000"}, {"name": "Rockville", "countryCode": "US", "stateCode": "MN", "latitude": "45.47191000", "longitude": "-94.34083000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.18885000", "longitude": "-93.55301000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "48.84609000", "longitude": "-95.76277000"}, {"name": "Roseau County", "countryCode": "US", "stateCode": "MN", "latitude": "48.77514000", "longitude": "-95.81082000"}, {"name": "Rosemount", "countryCode": "US", "stateCode": "MN", "latitude": "44.73941000", "longitude": "-93.12577000"}, {"name": "Roseville", "countryCode": "US", "stateCode": "MN", "latitude": "45.00608000", "longitude": "-93.15661000"}, {"name": "Royalton", "countryCode": "US", "stateCode": "MN", "latitude": "45.82997000", "longitude": "-94.29361000"}, {"name": "Rush City", "countryCode": "US", "stateCode": "MN", "latitude": "45.68551000", "longitude": "-92.96549000"}, {"name": "Rush<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.80830000", "longitude": "-91.75293000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.02052000", "longitude": "-93.21800000"}, {"name": "Saint Augusta", "countryCode": "US", "stateCode": "MN", "latitude": "45.45830000", "longitude": "-94.19804000"}, {"name": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.90552000", "longitude": "-93.74746000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.96941000", "longitude": "-92.06433000"}, {"name": "Saint Cloud", "countryCode": "US", "stateCode": "MN", "latitude": "45.56080000", "longitude": "-94.16249000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.38691000", "longitude": "-93.35940000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.98246000", "longitude": "-94.62692000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.56496000", "longitude": "-94.31833000"}, {"name": "Saint Louis County", "countryCode": "US", "stateCode": "MN", "latitude": "47.58986000", "longitude": "-92.46147000"}, {"name": "Saint Louis Park", "countryCode": "US", "stateCode": "MN", "latitude": "44.94830000", "longitude": "-93.34801000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.20996000", "longitude": "-93.66496000"}, {"name": "Saint Paul", "countryCode": "US", "stateCode": "MN", "latitude": "44.94441000", "longitude": "-93.09327000"}, {"name": "Saint Paul Park", "countryCode": "US", "stateCode": "MN", "latitude": "44.84219000", "longitude": "-92.99132000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.32358000", "longitude": "-93.95801000"}, {"name": "Sandstone", "countryCode": "US", "stateCode": "MN", "latitude": "46.13106000", "longitude": "-92.86742000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.62163000", "longitude": "-94.20694000"}, {"name": "Sauk Centre", "countryCode": "US", "stateCode": "MN", "latitude": "45.73747000", "longitude": "-94.95252000"}, {"name": "Sauk Rapids", "countryCode": "US", "stateCode": "MN", "latitude": "45.59191000", "longitude": "-94.16610000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.77913000", "longitude": "-93.33634000"}, {"name": "Scandia", "countryCode": "US", "stateCode": "MN", "latitude": "45.25358000", "longitude": "-92.80577000"}, {"name": "Scott County", "countryCode": "US", "stateCode": "MN", "latitude": "44.64846000", "longitude": "-93.53593000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.38691000", "longitude": "-92.74771000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.79802000", "longitude": "-93.52690000"}, {"name": "Sherburn", "countryCode": "US", "stateCode": "MN", "latitude": "43.65218000", "longitude": "-94.72692000"}, {"name": "Sherburne County", "countryCode": "US", "stateCode": "MN", "latitude": "45.44395000", "longitude": "-93.77459000"}, {"name": "Shoreview", "countryCode": "US", "stateCode": "MN", "latitude": "45.07913000", "longitude": "-93.14717000"}, {"name": "<PERSON>wood", "countryCode": "US", "stateCode": "MN", "latitude": "44.90080000", "longitude": "-93.58912000"}, {"name": "Sibley County", "countryCode": "US", "stateCode": "MN", "latitude": "44.57948000", "longitude": "-94.23216000"}, {"name": "Silver Bay", "countryCode": "US", "stateCode": "MN", "latitude": "47.29436000", "longitude": "-91.25739000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.98774000", "longitude": "-95.75585000"}, {"name": "Sleepy Eye", "countryCode": "US", "stateCode": "MN", "latitude": "44.29718000", "longitude": "-94.72415000"}, {"name": "South Saint Paul", "countryCode": "US", "stateCode": "MN", "latitude": "44.89274000", "longitude": "-93.03494000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.23302000", "longitude": "-94.94001000"}, {"name": "Spring Grove", "countryCode": "US", "stateCode": "MN", "latitude": "43.56108000", "longitude": "-91.63598000"}, {"name": "Spring Lake Park", "countryCode": "US", "stateCode": "MN", "latitude": "45.10774000", "longitude": "-93.23800000"}, {"name": "Spring Park", "countryCode": "US", "stateCode": "MN", "latitude": "44.93524000", "longitude": "-93.63218000"}, {"name": "Spring Valley", "countryCode": "US", "stateCode": "MN", "latitude": "43.68691000", "longitude": "-92.38906000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "MN", "latitude": "44.23885000", "longitude": "-94.97582000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.39802000", "longitude": "-92.98744000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.35552000", "longitude": "-94.79224000"}, {"name": "Starbuck", "countryCode": "US", "stateCode": "MN", "latitude": "45.61440000", "longitude": "-95.53115000"}, {"name": "Stearns County", "countryCode": "US", "stateCode": "MN", "latitude": "45.55215000", "longitude": "-94.61302000"}, {"name": "Steele County", "countryCode": "US", "stateCode": "MN", "latitude": "44.02234000", "longitude": "-93.22604000"}, {"name": "Stevens County", "countryCode": "US", "stateCode": "MN", "latitude": "45.58613000", "longitude": "-96.00030000"}, {"name": "Stewartville", "countryCode": "US", "stateCode": "MN", "latitude": "43.85552000", "longitude": "-92.48851000"}, {"name": "Stillwater", "countryCode": "US", "stateCode": "MN", "latitude": "45.05636000", "longitude": "-92.80604000"}, {"name": "Swift County", "countryCode": "US", "stateCode": "MN", "latitude": "45.28271000", "longitude": "-95.68143000"}, {"name": "Taylors Falls", "countryCode": "US", "stateCode": "MN", "latitude": "45.40191000", "longitude": "-92.65243000"}, {"name": "Thief River Falls", "countryCode": "US", "stateCode": "MN", "latitude": "48.11914000", "longitude": "-96.18115000"}, {"name": "Todd County", "countryCode": "US", "stateCode": "MN", "latitude": "46.07062000", "longitude": "-94.89760000"}, {"name": "Tonka Bay", "countryCode": "US", "stateCode": "MN", "latitude": "44.90857000", "longitude": "-93.59301000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.23329000", "longitude": "-95.61918000"}, {"name": "Traverse County", "countryCode": "US", "stateCode": "MN", "latitude": "45.77218000", "longitude": "-96.47164000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.82773000", "longitude": "-94.43719000"}, {"name": "Two Harbors", "countryCode": "US", "stateCode": "MN", "latitude": "47.02271000", "longitude": "-91.67073000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.27830000", "longitude": "-96.13475000"}, {"name": "Vadnais Heights", "countryCode": "US", "stateCode": "MN", "latitude": "45.05747000", "longitude": "-93.07383000"}, {"name": "Victoria", "countryCode": "US", "stateCode": "MN", "latitude": "44.85857000", "longitude": "-93.66163000"}, {"name": "Vineland", "countryCode": "US", "stateCode": "MN", "latitude": "46.16357000", "longitude": "-93.75747000"}, {"name": "Virginia", "countryCode": "US", "stateCode": "MN", "latitude": "47.52326000", "longitude": "-92.53657000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.38386000", "longitude": "-92.03294000"}, {"name": "Wabasha County", "countryCode": "US", "stateCode": "MN", "latitude": "44.28428000", "longitude": "-92.23027000"}, {"name": "Waconia", "countryCode": "US", "stateCode": "MN", "latitude": "44.85080000", "longitude": "-93.78691000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "46.44246000", "longitude": "-95.13614000"}, {"name": "Wadena County", "countryCode": "US", "stateCode": "MN", "latitude": "46.58576000", "longitude": "-94.96941000"}, {"name": "Waite Park", "countryCode": "US", "stateCode": "MN", "latitude": "45.55719000", "longitude": "-94.22416000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "47.10135000", "longitude": "-94.58722000"}, {"name": "Wanamingo", "countryCode": "US", "stateCode": "MN", "latitude": "44.30441000", "longitude": "-92.79047000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "48.19664000", "longitude": "-96.77284000"}, {"name": "Warroad", "countryCode": "US", "stateCode": "MN", "latitude": "48.90527000", "longitude": "-95.31440000"}, {"name": "Waseca", "countryCode": "US", "stateCode": "MN", "latitude": "44.07774000", "longitude": "-93.50744000"}, {"name": "Waseca County", "countryCode": "US", "stateCode": "MN", "latitude": "44.02212000", "longitude": "-93.58728000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "MN", "latitude": "45.03873000", "longitude": "-92.88396000"}, {"name": "Watertown", "countryCode": "US", "stateCode": "MN", "latitude": "44.96357000", "longitude": "-93.84719000"}, {"name": "Waterville", "countryCode": "US", "stateCode": "MN", "latitude": "44.21885000", "longitude": "-93.56800000"}, {"name": "Watonwan County", "countryCode": "US", "stateCode": "MN", "latitude": "43.97843000", "longitude": "-94.61406000"}, {"name": "Waverly", "countryCode": "US", "stateCode": "MN", "latitude": "45.06663000", "longitude": "-93.96636000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.97413000", "longitude": "-93.50662000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.74607000", "longitude": "-93.72884000"}, {"name": "West Coon Rapids", "countryCode": "US", "stateCode": "MN", "latitude": "45.15969000", "longitude": "-93.34967000"}, {"name": "West Saint Paul", "countryCode": "US", "stateCode": "MN", "latitude": "44.91608000", "longitude": "-93.10161000"}, {"name": "Wheaton", "countryCode": "US", "stateCode": "MN", "latitude": "45.80441000", "longitude": "-96.49923000"}, {"name": "White Bear Lake", "countryCode": "US", "stateCode": "MN", "latitude": "45.08469000", "longitude": "-93.00994000"}, {"name": "Wilkin County", "countryCode": "US", "stateCode": "MN", "latitude": "46.35708000", "longitude": "-96.46835000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.12191000", "longitude": "-95.04334000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "43.86635000", "longitude": "-95.11694000"}, {"name": "Winnebago", "countryCode": "US", "stateCode": "MN", "latitude": "43.76773000", "longitude": "-94.16579000"}, {"name": "Winona", "countryCode": "US", "stateCode": "MN", "latitude": "44.04996000", "longitude": "-91.63932000"}, {"name": "Winona County", "countryCode": "US", "stateCode": "MN", "latitude": "43.98685000", "longitude": "-91.77913000"}, {"name": "Winsted", "countryCode": "US", "stateCode": "MN", "latitude": "44.96385000", "longitude": "-94.04747000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.54302000", "longitude": "-94.36637000"}, {"name": "Woodbury", "countryCode": "US", "stateCode": "MN", "latitude": "44.92386000", "longitude": "-92.95938000"}, {"name": "Worthington", "countryCode": "US", "stateCode": "MN", "latitude": "43.61996000", "longitude": "-95.59640000"}, {"name": "Wright County", "countryCode": "US", "stateCode": "MN", "latitude": "45.17393000", "longitude": "-93.96305000"}, {"name": "Wyoming", "countryCode": "US", "stateCode": "MN", "latitude": "45.33636000", "longitude": "-92.99716000"}, {"name": "Yellow Medicine County", "countryCode": "US", "stateCode": "MN", "latitude": "44.71625000", "longitude": "-95.86836000"}, {"name": "Young America (historical)", "countryCode": "US", "stateCode": "MN", "latitude": "44.78274000", "longitude": "-93.91358000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "45.44330000", "longitude": "-93.58996000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MN", "latitude": "44.29413000", "longitude": "-92.66908000"}]