[{"name": "Adair County", "countryCode": "US", "stateCode": "MO", "latitude": "40.19056000", "longitude": "-92.60072000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.39752000", "longitude": "-94.35162000"}, {"name": "Advance", "countryCode": "US", "stateCode": "MO", "latitude": "37.10455000", "longitude": "-89.90953000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.55061000", "longitude": "-90.33317000"}, {"name": "Albany", "countryCode": "US", "stateCode": "MO", "latitude": "40.24861000", "longitude": "-94.33107000"}, {"name": "Alton", "countryCode": "US", "stateCode": "MO", "latitude": "36.69423000", "longitude": "-91.39930000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.65063000", "longitude": "-94.44355000"}, {"name": "Andrew County", "countryCode": "US", "stateCode": "MO", "latitude": "39.98349000", "longitude": "-94.80205000"}, {"name": "Appleton City", "countryCode": "US", "stateCode": "MO", "latitude": "38.19058000", "longitude": "-94.02939000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.48168000", "longitude": "-94.35439000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.43283000", "longitude": "-90.37762000"}, {"name": "Ash Grove", "countryCode": "US", "stateCode": "MO", "latitude": "37.31533000", "longitude": "-93.58520000"}, {"name": "Ashland", "countryCode": "US", "stateCode": "MO", "latitude": "38.77448000", "longitude": "-92.25713000"}, {"name": "Atchison County", "countryCode": "US", "stateCode": "MO", "latitude": "40.43085000", "longitude": "-95.42809000"}, {"name": "Audrain County", "countryCode": "US", "stateCode": "MO", "latitude": "39.21576000", "longitude": "-91.84159000"}, {"name": "Aurora", "countryCode": "US", "stateCode": "MO", "latitude": "36.97089000", "longitude": "-93.71798000"}, {"name": "Ava", "countryCode": "US", "stateCode": "MO", "latitude": "36.95200000", "longitude": "-92.66045000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.59505000", "longitude": "-90.54623000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.34422000", "longitude": "-90.39345000"}, {"name": "Barry County", "countryCode": "US", "stateCode": "MO", "latitude": "36.70987000", "longitude": "-93.82907000"}, {"name": "Barton County", "countryCode": "US", "stateCode": "MO", "latitude": "37.50230000", "longitude": "-94.34711000"}, {"name": "Bates County", "countryCode": "US", "stateCode": "MO", "latitude": "38.25729000", "longitude": "-94.34000000"}, {"name": "Battlefield", "countryCode": "US", "stateCode": "MO", "latitude": "37.11561000", "longitude": "-93.37019000"}, {"name": "Bel-Nor", "countryCode": "US", "stateCode": "MO", "latitude": "38.70200000", "longitude": "-90.31678000"}, {"name": "Bel-Ridge", "countryCode": "US", "stateCode": "MO", "latitude": "38.70950000", "longitude": "-90.32539000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.28588000", "longitude": "-91.72044000"}, {"name": "Bellefontaine <PERSON>eighbors", "countryCode": "US", "stateCode": "MO", "latitude": "38.74033000", "longitude": "-90.22650000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.81195000", "longitude": "-94.53190000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.09783000", "longitude": "-89.56258000"}, {"name": "Benton County", "countryCode": "US", "stateCode": "MO", "latitude": "38.29485000", "longitude": "-93.28795000"}, {"name": "Berkeley", "countryCode": "US", "stateCode": "MO", "latitude": "38.75450000", "longitude": "-90.33123000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.66894000", "longitude": "-89.96870000"}, {"name": "Bethany", "countryCode": "US", "stateCode": "MO", "latitude": "40.26833000", "longitude": "-94.02829000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.06755000", "longitude": "-93.55214000"}, {"name": "Bismarck", "countryCode": "US", "stateCode": "MO", "latitude": "37.76922000", "longitude": "-90.62485000"}, {"name": "Black Jack", "countryCode": "US", "stateCode": "MO", "latitude": "38.79338000", "longitude": "-90.26733000"}, {"name": "Bloomfield", "countryCode": "US", "stateCode": "MO", "latitude": "36.88589000", "longitude": "-89.92926000"}, {"name": "Blue Springs", "countryCode": "US", "stateCode": "MO", "latitude": "39.01695000", "longitude": "-94.28161000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.61448000", "longitude": "-93.41047000"}, {"name": "Bollinger County", "countryCode": "US", "stateCode": "MO", "latitude": "37.32219000", "longitude": "-90.02595000"}, {"name": "Bonne Terre", "countryCode": "US", "stateCode": "MO", "latitude": "37.92311000", "longitude": "-90.55540000"}, {"name": "Boone County", "countryCode": "US", "stateCode": "MO", "latitude": "38.99062000", "longitude": "-92.30968000"}, {"name": "Boonville", "countryCode": "US", "stateCode": "MO", "latitude": "38.97364000", "longitude": "-92.74324000"}, {"name": "Bourbon", "countryCode": "US", "stateCode": "MO", "latitude": "38.15477000", "longitude": "-91.24403000"}, {"name": "Bowling Green", "countryCode": "US", "stateCode": "MO", "latitude": "39.34199000", "longitude": "-91.19514000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.64367000", "longitude": "-93.21851000"}, {"name": "Breckenridge Hills", "countryCode": "US", "stateCode": "MO", "latitude": "38.71450000", "longitude": "-90.36734000"}, {"name": "Brentwood", "countryCode": "US", "stateCode": "MO", "latitude": "38.61755000", "longitude": "-90.34928000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.76700000", "longitude": "-90.41151000"}, {"name": "Brookfield", "countryCode": "US", "stateCode": "MO", "latitude": "39.78447000", "longitude": "-93.07353000"}, {"name": "Buchanan County", "countryCode": "US", "stateCode": "MO", "latitude": "39.65986000", "longitude": "-94.80616000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.13251000", "longitude": "-94.19856000"}, {"name": "Buffalo", "countryCode": "US", "stateCode": "MO", "latitude": "37.64393000", "longitude": "-93.09241000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.25863000", "longitude": "-94.33051000"}, {"name": "Butler County", "countryCode": "US", "stateCode": "MO", "latitude": "36.71642000", "longitude": "-90.40656000"}, {"name": "Byrnes Mill", "countryCode": "US", "stateCode": "MO", "latitude": "38.43783000", "longitude": "-90.58179000"}, {"name": "Cabool", "countryCode": "US", "stateCode": "MO", "latitude": "37.12394000", "longitude": "-92.10127000"}, {"name": "Caldwell County", "countryCode": "US", "stateCode": "MO", "latitude": "39.65575000", "longitude": "-93.98280000"}, {"name": "California", "countryCode": "US", "stateCode": "MO", "latitude": "38.62753000", "longitude": "-92.56658000"}, {"name": "Callaway County", "countryCode": "US", "stateCode": "MO", "latitude": "38.83552000", "longitude": "-91.92601000"}, {"name": "Calverton Park", "countryCode": "US", "stateCode": "MO", "latitude": "38.76477000", "longitude": "-90.31373000"}, {"name": "Camden County", "countryCode": "US", "stateCode": "MO", "latitude": "38.02704000", "longitude": "-92.76605000"}, {"name": "Camdenton", "countryCode": "US", "stateCode": "MO", "latitude": "38.00809000", "longitude": "-92.74463000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.74028000", "longitude": "-94.24106000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.49339000", "longitude": "-90.07509000"}, {"name": "Canton", "countryCode": "US", "stateCode": "MO", "latitude": "40.12504000", "longitude": "-91.62516000"}, {"name": "Cape Girardeau", "countryCode": "US", "stateCode": "MO", "latitude": "37.30588000", "longitude": "-89.51815000"}, {"name": "Cape Girardeau County", "countryCode": "US", "stateCode": "MO", "latitude": "37.38404000", "longitude": "-89.68445000"}, {"name": "Carl Junction", "countryCode": "US", "stateCode": "MO", "latitude": "37.17672000", "longitude": "-94.56551000"}, {"name": "Carroll County", "countryCode": "US", "stateCode": "MO", "latitude": "39.42698000", "longitude": "-93.50518000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.35835000", "longitude": "-93.49577000"}, {"name": "Carter County", "countryCode": "US", "stateCode": "MO", "latitude": "36.94127000", "longitude": "-90.96231000"}, {"name": "Carterville", "countryCode": "US", "stateCode": "MO", "latitude": "37.14923000", "longitude": "-94.44300000"}, {"name": "Carthage", "countryCode": "US", "stateCode": "MO", "latitude": "37.17645000", "longitude": "-94.31022000"}, {"name": "Caruthersville", "countryCode": "US", "stateCode": "MO", "latitude": "36.19312000", "longitude": "-89.65564000"}, {"name": "Cass County", "countryCode": "US", "stateCode": "MO", "latitude": "38.64700000", "longitude": "-94.35482000"}, {"name": "Cassville", "countryCode": "US", "stateCode": "MO", "latitude": "36.67701000", "longitude": "-93.86881000"}, {"name": "Castle Point", "countryCode": "US", "stateCode": "MO", "latitude": "38.75811000", "longitude": "-90.24817000"}, {"name": "Cedar County", "countryCode": "US", "stateCode": "MO", "latitude": "37.72385000", "longitude": "-93.85661000"}, {"name": "Cedar Hill", "countryCode": "US", "stateCode": "MO", "latitude": "38.35339000", "longitude": "-90.64124000"}, {"name": "Centerville", "countryCode": "US", "stateCode": "MO", "latitude": "37.43505000", "longitude": "-90.95846000"}, {"name": "Centralia", "countryCode": "US", "stateCode": "MO", "latitude": "39.21032000", "longitude": "-92.13795000"}, {"name": "Chaffee", "countryCode": "US", "stateCode": "MO", "latitude": "37.18005000", "longitude": "-89.65509000"}, {"name": "Chariton County", "countryCode": "US", "stateCode": "MO", "latitude": "39.51508000", "longitude": "-92.96262000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.70255000", "longitude": "-90.34345000"}, {"name": "Charleston", "countryCode": "US", "stateCode": "MO", "latitude": "36.92089000", "longitude": "-89.35063000"}, {"name": "Chesterfield", "countryCode": "US", "stateCode": "MO", "latitude": "38.66311000", "longitude": "-90.57707000"}, {"name": "Chillicothe", "countryCode": "US", "stateCode": "MO", "latitude": "39.79529000", "longitude": "-93.55244000"}, {"name": "Christian County", "countryCode": "US", "stateCode": "MO", "latitude": "36.96957000", "longitude": "-93.18885000"}, {"name": "City of Saint Louis", "countryCode": "US", "stateCode": "MO", "latitude": "38.62727000", "longitude": "-90.19789000"}, {"name": "Clark County", "countryCode": "US", "stateCode": "MO", "latitude": "40.41036000", "longitude": "-91.73840000"}, {"name": "Clarkson Valley", "countryCode": "US", "stateCode": "MO", "latitude": "38.61839000", "longitude": "-90.58929000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.45173000", "longitude": "-89.96704000"}, {"name": "Clay County", "countryCode": "US", "stateCode": "MO", "latitude": "39.31052000", "longitude": "-94.42087000"}, {"name": "Claycomo", "countryCode": "US", "stateCode": "MO", "latitude": "39.20250000", "longitude": "-94.49245000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.64255000", "longitude": "-90.32373000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.03033000", "longitude": "-93.47297000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.36863000", "longitude": "-93.77827000"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "MO", "latitude": "39.60178000", "longitude": "-94.40459000"}, {"name": "Cole <PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.46002000", "longitude": "-93.20270000"}, {"name": "Cole County", "countryCode": "US", "stateCode": "MO", "latitude": "38.50541000", "longitude": "-92.28160000"}, {"name": "Columbia", "countryCode": "US", "stateCode": "MO", "latitude": "38.95171000", "longitude": "-92.33407000"}, {"name": "Concord", "countryCode": "US", "stateCode": "MO", "latitude": "38.52450000", "longitude": "-90.35734000"}, {"name": "Concordia", "countryCode": "US", "stateCode": "MO", "latitude": "38.98335000", "longitude": "-93.56855000"}, {"name": "Cool Valley", "countryCode": "US", "stateCode": "MO", "latitude": "38.72783000", "longitude": "-90.31011000"}, {"name": "Cooper County", "countryCode": "US", "stateCode": "MO", "latitude": "38.84354000", "longitude": "-92.81012000"}, {"name": "Cottleville", "countryCode": "US", "stateCode": "MO", "latitude": "38.74616000", "longitude": "-90.65401000"}, {"name": "Country Club Hills", "countryCode": "US", "stateCode": "MO", "latitude": "38.72088000", "longitude": "-90.27484000"}, {"name": "Country Club Village", "countryCode": "US", "stateCode": "MO", "latitude": "39.83222000", "longitude": "-94.82163000"}, {"name": "Crane", "countryCode": "US", "stateCode": "MO", "latitude": "36.90534000", "longitude": "-93.57158000"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "MO", "latitude": "37.97638000", "longitude": "-91.30396000"}, {"name": "Crestwood", "countryCode": "US", "stateCode": "MO", "latitude": "38.55700000", "longitude": "-90.38178000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.66089000", "longitude": "-90.42262000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.94893000", "longitude": "-92.26378000"}, {"name": "Crystal City", "countryCode": "US", "stateCode": "MO", "latitude": "38.22117000", "longitude": "-90.37901000"}, {"name": "Cuba", "countryCode": "US", "stateCode": "MO", "latitude": "38.06282000", "longitude": "-91.40348000"}, {"name": "Dade County", "countryCode": "US", "stateCode": "MO", "latitude": "37.43204000", "longitude": "-93.85029000"}, {"name": "Dallas County", "countryCode": "US", "stateCode": "MO", "latitude": "37.68041000", "longitude": "-93.02366000"}, {"name": "Dardenne Prairie", "countryCode": "US", "stateCode": "MO", "latitude": "38.76950000", "longitude": "-90.72902000"}, {"name": "Daviess County", "countryCode": "US", "stateCode": "MO", "latitude": "39.96075000", "longitude": "-93.98547000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.13950000", "longitude": "-90.55513000"}, {"name": "DeKalb County", "countryCode": "US", "stateCode": "MO", "latitude": "39.89318000", "longitude": "-94.40471000"}, {"name": "Dellwood", "countryCode": "US", "stateCode": "MO", "latitude": "38.74950000", "longitude": "-90.28567000"}, {"name": "Dent County", "countryCode": "US", "stateCode": "MO", "latitude": "37.60663000", "longitude": "-91.50788000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.60089000", "longitude": "-90.43290000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.87088000", "longitude": "-90.52735000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.79589000", "longitude": "-89.95787000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.99171000", "longitude": "-92.09378000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.62089000", "longitude": "-90.82346000"}, {"name": "Douglas County", "countryCode": "US", "stateCode": "MO", "latitude": "36.93260000", "longitude": "-92.49881000"}, {"name": "Duenweg", "countryCode": "US", "stateCode": "MO", "latitude": "37.08367000", "longitude": "-94.41356000"}, {"name": "Dunklin County", "countryCode": "US", "stateCode": "MO", "latitude": "36.10597000", "longitude": "-90.16576000"}, {"name": "Duquesne", "countryCode": "US", "stateCode": "MO", "latitude": "37.07673000", "longitude": "-94.45939000"}, {"name": "East Independence", "countryCode": "US", "stateCode": "MO", "latitude": "39.09556000", "longitude": "-94.35523000"}, {"name": "East Prairie", "countryCode": "US", "stateCode": "MO", "latitude": "36.77978000", "longitude": "-89.38563000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "40.16754000", "longitude": "-92.17268000"}, {"name": "El Dorado Springs", "countryCode": "US", "stateCode": "MO", "latitude": "37.87698000", "longitude": "-94.02133000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.34836000", "longitude": "-92.58158000"}, {"name": "Ellisville", "countryCode": "US", "stateCode": "MO", "latitude": "38.59255000", "longitude": "-90.58707000"}, {"name": "Elsberry", "countryCode": "US", "stateCode": "MO", "latitude": "39.16672000", "longitude": "-90.78096000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.83672000", "longitude": "-90.53290000"}, {"name": "Eminence", "countryCode": "US", "stateCode": "MO", "latitude": "37.15060000", "longitude": "-91.35764000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.85033000", "longitude": "-90.49874000"}, {"name": "Eureka", "countryCode": "US", "stateCode": "MO", "latitude": "38.50255000", "longitude": "-90.62790000"}, {"name": "Excelsior Springs", "countryCode": "US", "stateCode": "MO", "latitude": "39.33917000", "longitude": "-94.22606000"}, {"name": "Fair Grove", "countryCode": "US", "stateCode": "MO", "latitude": "37.38393000", "longitude": "-93.15130000"}, {"name": "Farmington", "countryCode": "US", "stateCode": "MO", "latitude": "37.78088000", "longitude": "-90.42179000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.14587000", "longitude": "-92.68379000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.51311000", "longitude": "-90.43595000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.74422000", "longitude": "-90.30539000"}, {"name": "Festus", "countryCode": "US", "stateCode": "MO", "latitude": "38.22061000", "longitude": "-90.39595000"}, {"name": "Flat River", "countryCode": "US", "stateCode": "MO", "latitude": "37.85005000", "longitude": "-90.51679000"}, {"name": "Florissant", "countryCode": "US", "stateCode": "MO", "latitude": "38.78922000", "longitude": "-90.32261000"}, {"name": "<PERSON>sy<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.68506000", "longitude": "-93.11990000"}, {"name": "Fort Leonard Wood", "countryCode": "US", "stateCode": "MO", "latitude": "37.70573000", "longitude": "-92.15717000"}, {"name": "Four Seasons", "countryCode": "US", "stateCode": "MO", "latitude": "38.19809000", "longitude": "-92.71102000"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "MO", "latitude": "38.41114000", "longitude": "-91.07499000"}, {"name": "Fredericktown", "countryCode": "US", "stateCode": "MO", "latitude": "37.55978000", "longitude": "-90.29401000"}, {"name": "Frontenac", "countryCode": "US", "stateCode": "MO", "latitude": "38.63561000", "longitude": "-90.41512000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.84671000", "longitude": "-91.94796000"}, {"name": "Gainesville", "countryCode": "US", "stateCode": "MO", "latitude": "36.60312000", "longitude": "-92.42822000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.80534000", "longitude": "-93.46658000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.91445000", "longitude": "-93.96217000"}, {"name": "Garden City", "countryCode": "US", "stateCode": "MO", "latitude": "38.56112000", "longitude": "-94.19133000"}, {"name": "Gasconade County", "countryCode": "US", "stateCode": "MO", "latitude": "38.44087000", "longitude": "-91.50793000"}, {"name": "Gentry County", "countryCode": "US", "stateCode": "MO", "latitude": "40.21211000", "longitude": "-94.40992000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.39977000", "longitude": "-91.33071000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.45201000", "longitude": "-89.91926000"}, {"name": "Gladstone", "countryCode": "US", "stateCode": "MO", "latitude": "39.20389000", "longitude": "-94.55468000"}, {"name": "Glasgow", "countryCode": "US", "stateCode": "MO", "latitude": "39.22725000", "longitude": "-92.84658000"}, {"name": "Glasgow Village", "countryCode": "US", "stateCode": "MO", "latitude": "38.75366000", "longitude": "-90.19844000"}, {"name": "Glendale", "countryCode": "US", "stateCode": "MO", "latitude": "38.59589000", "longitude": "-90.37706000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.74174000", "longitude": "-94.39911000"}, {"name": "<PERSON>wer", "countryCode": "US", "stateCode": "MO", "latitude": "39.61083000", "longitude": "-94.59940000"}, {"name": "Grain Valley", "countryCode": "US", "stateCode": "MO", "latitude": "39.01501000", "longitude": "-94.19856000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.91923000", "longitude": "-94.25522000"}, {"name": "Grandview", "countryCode": "US", "stateCode": "MO", "latitude": "38.88584000", "longitude": "-94.53301000"}, {"name": "Grant City", "countryCode": "US", "stateCode": "MO", "latitude": "40.48749000", "longitude": "-94.41107000"}, {"name": "Gray Summit", "countryCode": "US", "stateCode": "MO", "latitude": "38.48978000", "longitude": "-90.81680000"}, {"name": "Green Park", "countryCode": "US", "stateCode": "MO", "latitude": "38.52366000", "longitude": "-90.33845000"}, {"name": "Greene County", "countryCode": "US", "stateCode": "MO", "latitude": "37.25805000", "longitude": "-93.34199000"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "MO", "latitude": "37.41532000", "longitude": "-93.84104000"}, {"name": "Greenville", "countryCode": "US", "stateCode": "MO", "latitude": "37.12727000", "longitude": "-90.45011000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.85168000", "longitude": "-94.34384000"}, {"name": "Grundy County", "countryCode": "US", "stateCode": "MO", "latitude": "40.11393000", "longitude": "-93.56534000"}, {"name": "Hallsville", "countryCode": "US", "stateCode": "MO", "latitude": "39.11699000", "longitude": "-92.22074000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.74362000", "longitude": "-93.99827000"}, {"name": "Hanley Hills", "countryCode": "US", "stateCode": "MO", "latitude": "38.68588000", "longitude": "-90.32373000"}, {"name": "Hannibal", "countryCode": "US", "stateCode": "MO", "latitude": "39.70838000", "longitude": "-91.35848000"}, {"name": "Harrison County", "countryCode": "US", "stateCode": "MO", "latitude": "40.35467000", "longitude": "-93.99206000"}, {"name": "Harrisonville", "countryCode": "US", "stateCode": "MO", "latitude": "38.65334000", "longitude": "-94.34884000"}, {"name": "Hartville", "countryCode": "US", "stateCode": "MO", "latitude": "37.25088000", "longitude": "-92.51044000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.23368000", "longitude": "-89.74953000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.77144000", "longitude": "-90.37095000"}, {"name": "Henry County", "countryCode": "US", "stateCode": "MO", "latitude": "38.38516000", "longitude": "-93.79275000"}, {"name": "Herculaneum", "countryCode": "US", "stateCode": "MO", "latitude": "38.26839000", "longitude": "-90.38012000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.70421000", "longitude": "-91.43738000"}, {"name": "Hermitage", "countryCode": "US", "stateCode": "MO", "latitude": "37.94142000", "longitude": "-93.31631000"}, {"name": "Hickory County", "countryCode": "US", "stateCode": "MO", "latitude": "37.94079000", "longitude": "-93.32072000"}, {"name": "Higginsville", "countryCode": "US", "stateCode": "MO", "latitude": "39.07251000", "longitude": "-93.71716000"}, {"name": "High Ridge", "countryCode": "US", "stateCode": "MO", "latitude": "38.45894000", "longitude": "-90.53651000"}, {"name": "Hillsboro", "countryCode": "US", "stateCode": "MO", "latitude": "38.23228000", "longitude": "-90.56290000"}, {"name": "Hillsdale", "countryCode": "US", "stateCode": "MO", "latitude": "38.68338000", "longitude": "-90.28400000"}, {"name": "Holden", "countryCode": "US", "stateCode": "MO", "latitude": "38.71418000", "longitude": "-93.99133000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.62117000", "longitude": "-93.21546000"}, {"name": "Holt County", "countryCode": "US", "stateCode": "MO", "latitude": "40.09443000", "longitude": "-95.21551000"}, {"name": "Holts Summit", "countryCode": "US", "stateCode": "MO", "latitude": "38.64032000", "longitude": "-92.12241000"}, {"name": "Houston", "countryCode": "US", "stateCode": "MO", "latitude": "37.32616000", "longitude": "-91.95599000"}, {"name": "Howard County", "countryCode": "US", "stateCode": "MO", "latitude": "39.14250000", "longitude": "-92.69627000"}, {"name": "Howell County", "countryCode": "US", "stateCode": "MO", "latitude": "36.77401000", "longitude": "-91.88654000"}, {"name": "Humansville", "countryCode": "US", "stateCode": "MO", "latitude": "37.79448000", "longitude": "-93.57798000"}, {"name": "Huntsville", "countryCode": "US", "stateCode": "MO", "latitude": "39.44059000", "longitude": "-92.54518000"}, {"name": "Imperial", "countryCode": "US", "stateCode": "MO", "latitude": "38.36978000", "longitude": "-90.37845000"}, {"name": "Independence", "countryCode": "US", "stateCode": "MO", "latitude": "39.09112000", "longitude": "-94.41551000"}, {"name": "Iron County", "countryCode": "US", "stateCode": "MO", "latitude": "37.50426000", "longitude": "-90.69003000"}, {"name": "Ironton", "countryCode": "US", "stateCode": "MO", "latitude": "37.59727000", "longitude": "-90.62734000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.38227000", "longitude": "-89.66621000"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "MO", "latitude": "39.00850000", "longitude": "-94.34609000"}, {"name": "Jasper County", "countryCode": "US", "stateCode": "MO", "latitude": "37.20355000", "longitude": "-94.34061000"}, {"name": "Jefferson City", "countryCode": "US", "stateCode": "MO", "latitude": "38.57670000", "longitude": "-92.17352000"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "MO", "latitude": "38.26107000", "longitude": "-90.53769000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.71922000", "longitude": "-90.26039000"}, {"name": "Johnson County", "countryCode": "US", "stateCode": "MO", "latitude": "38.74409000", "longitude": "-93.80637000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.08423000", "longitude": "-94.51328000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "40.42032000", "longitude": "-91.71961000"}, {"name": "Kansas City", "countryCode": "US", "stateCode": "MO", "latitude": "39.09973000", "longitude": "-94.57857000"}, {"name": "Kearney", "countryCode": "US", "stateCode": "MO", "latitude": "39.36778000", "longitude": "-94.36217000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.23618000", "longitude": "-90.05565000"}, {"name": "Keytesville", "countryCode": "US", "stateCode": "MO", "latitude": "39.43447000", "longitude": "-92.93825000"}, {"name": "Kimberling City", "countryCode": "US", "stateCode": "MO", "latitude": "36.63340000", "longitude": "-93.41685000"}, {"name": "King City", "countryCode": "US", "stateCode": "MO", "latitude": "40.05138000", "longitude": "-94.52412000"}, {"name": "Kingston", "countryCode": "US", "stateCode": "MO", "latitude": "39.64417000", "longitude": "-94.03855000"}, {"name": "Kirksville", "countryCode": "US", "stateCode": "MO", "latitude": "40.19475000", "longitude": "-92.58325000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.58339000", "longitude": "-90.40678000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.68367000", "longitude": "-93.04990000"}, {"name": "Knob Noster", "countryCode": "US", "stateCode": "MO", "latitude": "38.76668000", "longitude": "-93.55855000"}, {"name": "Knox County", "countryCode": "US", "stateCode": "MO", "latitude": "40.12825000", "longitude": "-92.14807000"}, {"name": "La Monte", "countryCode": "US", "stateCode": "MO", "latitude": "38.77418000", "longitude": "-93.42521000"}, {"name": "La Plata", "countryCode": "US", "stateCode": "MO", "latitude": "40.02337000", "longitude": "-92.49158000"}, {"name": "LaBarque Creek", "countryCode": "US", "stateCode": "MO", "latitude": "38.41701000", "longitude": "-90.67989000"}, {"name": "Laclede County", "countryCode": "US", "stateCode": "MO", "latitude": "37.65832000", "longitude": "-92.59035000"}, {"name": "Ladue", "countryCode": "US", "stateCode": "MO", "latitude": "38.64977000", "longitude": "-90.38067000"}, {"name": "Lafayette County", "countryCode": "US", "stateCode": "MO", "latitude": "39.06559000", "longitude": "-93.78554000"}, {"name": "Lake Lotawana", "countryCode": "US", "stateCode": "MO", "latitude": "38.92306000", "longitude": "-94.24411000"}, {"name": "Lake Ozark", "countryCode": "US", "stateCode": "MO", "latitude": "38.19864000", "longitude": "-92.63880000"}, {"name": "Lake Saint Louis", "countryCode": "US", "stateCode": "MO", "latitude": "38.79755000", "longitude": "-90.78568000"}, {"name": "Lake Winnebago", "countryCode": "US", "stateCode": "MO", "latitude": "38.83140000", "longitude": "-94.35856000"}, {"name": "Lakeshire", "countryCode": "US", "stateCode": "MO", "latitude": "38.53866000", "longitude": "-90.33512000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.49505000", "longitude": "-94.27661000"}, {"name": "Lancaster", "countryCode": "US", "stateCode": "MO", "latitude": "40.52086000", "longitude": "-92.52797000"}, {"name": "Lathrop", "countryCode": "US", "stateCode": "MO", "latitude": "39.54834000", "longitude": "-94.32995000"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "MO", "latitude": "37.10635000", "longitude": "-93.83294000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.43834000", "longitude": "-94.20411000"}, {"name": "Leadwood", "countryCode": "US", "stateCode": "MO", "latitude": "37.86727000", "longitude": "-90.59318000"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "MO", "latitude": "37.68060000", "longitude": "-92.66379000"}, {"name": "Lee's Summit", "countryCode": "US", "stateCode": "MO", "latitude": "38.91084000", "longitude": "-94.38217000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.53339000", "longitude": "-90.27928000"}, {"name": "Lewis County", "countryCode": "US", "stateCode": "MO", "latitude": "40.09690000", "longitude": "-91.72214000"}, {"name": "Lexington", "countryCode": "US", "stateCode": "MO", "latitude": "39.18473000", "longitude": "-93.87994000"}, {"name": "Liberty", "countryCode": "US", "stateCode": "MO", "latitude": "39.24611000", "longitude": "-94.41912000"}, {"name": "Licking", "countryCode": "US", "stateCode": "MO", "latitude": "37.49949000", "longitude": "-91.85710000"}, {"name": "Lilbourn", "countryCode": "US", "stateCode": "MO", "latitude": "36.59228000", "longitude": "-89.61536000"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "MO", "latitude": "38.39086000", "longitude": "-93.33465000"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "MO", "latitude": "39.05802000", "longitude": "-90.96005000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.48587000", "longitude": "-91.85045000"}, {"name": "Linn County", "countryCode": "US", "stateCode": "MO", "latitude": "39.87021000", "longitude": "-93.10718000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.87863000", "longitude": "-93.18882000"}, {"name": "Livingston County", "countryCode": "US", "stateCode": "MO", "latitude": "39.78211000", "longitude": "-93.54828000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.87084000", "longitude": "-94.17383000"}, {"name": "Louisiana", "countryCode": "US", "stateCode": "MO", "latitude": "39.44894000", "longitude": "-91.05153000"}, {"name": "Macon", "countryCode": "US", "stateCode": "MO", "latitude": "39.74226000", "longitude": "-92.47269000"}, {"name": "Macon County", "countryCode": "US", "stateCode": "MO", "latitude": "39.83080000", "longitude": "-92.56461000"}, {"name": "Madison County", "countryCode": "US", "stateCode": "MO", "latitude": "37.47810000", "longitude": "-90.34503000"}, {"name": "Malden", "countryCode": "US", "stateCode": "MO", "latitude": "36.55700000", "longitude": "-89.96648000"}, {"name": "Manchester", "countryCode": "US", "stateCode": "MO", "latitude": "38.59700000", "longitude": "-90.50929000"}, {"name": "Mansfield", "countryCode": "US", "stateCode": "MO", "latitude": "37.10672000", "longitude": "-92.58072000"}, {"name": "Maplewood", "countryCode": "US", "stateCode": "MO", "latitude": "38.61255000", "longitude": "-90.32456000"}, {"name": "Marble Hill", "countryCode": "US", "stateCode": "MO", "latitude": "37.30589000", "longitude": "-89.97038000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.71197000", "longitude": "-92.94825000"}, {"name": "Maries County", "countryCode": "US", "stateCode": "MO", "latitude": "38.16163000", "longitude": "-91.92489000"}, {"name": "Marion County", "countryCode": "US", "stateCode": "MO", "latitude": "39.80596000", "longitude": "-91.62235000"}, {"name": "Marionville", "countryCode": "US", "stateCode": "MO", "latitude": "37.00311000", "longitude": "-93.63742000"}, {"name": "Marlborough", "countryCode": "US", "stateCode": "MO", "latitude": "38.57033000", "longitude": "-90.33706000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.12308000", "longitude": "-93.19687000"}, {"name": "Marshfield", "countryCode": "US", "stateCode": "MO", "latitude": "37.33866000", "longitude": "-92.90712000"}, {"name": "Marthasville", "countryCode": "US", "stateCode": "MO", "latitude": "38.62838000", "longitude": "-91.05764000"}, {"name": "Maryland Heights", "countryCode": "US", "stateCode": "MO", "latitude": "38.71311000", "longitude": "-90.42984000"}, {"name": "Maryville", "countryCode": "US", "stateCode": "MO", "latitude": "40.34610000", "longitude": "-94.87247000"}, {"name": "Maysville", "countryCode": "US", "stateCode": "MO", "latitude": "39.88917000", "longitude": "-94.36190000"}, {"name": "McDonald County", "countryCode": "US", "stateCode": "MO", "latitude": "36.62867000", "longitude": "-94.34836000"}, {"name": "Mehlville", "countryCode": "US", "stateCode": "MO", "latitude": "38.50839000", "longitude": "-90.32289000"}, {"name": "Memphis", "countryCode": "US", "stateCode": "MO", "latitude": "40.45781000", "longitude": "-92.17129000"}, {"name": "Mercer County", "countryCode": "US", "stateCode": "MO", "latitude": "40.42233000", "longitude": "-93.56856000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.71395000", "longitude": "-93.16185000"}, {"name": "Mexico", "countryCode": "US", "stateCode": "MO", "latitude": "39.16976000", "longitude": "-91.88295000"}, {"name": "Milan", "countryCode": "US", "stateCode": "MO", "latitude": "40.20224000", "longitude": "-93.12521000"}, {"name": "Miller County", "countryCode": "US", "stateCode": "MO", "latitude": "38.21453000", "longitude": "-92.42841000"}, {"name": "Mississippi County", "countryCode": "US", "stateCode": "MO", "latitude": "36.82810000", "longitude": "-89.29118000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.41837000", "longitude": "-92.43824000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.74699000", "longitude": "-90.24011000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.92895000", "longitude": "-93.92771000"}, {"name": "Moniteau County", "countryCode": "US", "stateCode": "MO", "latitude": "38.63275000", "longitude": "-92.58305000"}, {"name": "Monroe City", "countryCode": "US", "stateCode": "MO", "latitude": "39.65365000", "longitude": "-91.73461000"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "MO", "latitude": "39.49546000", "longitude": "-92.00074000"}, {"name": "Montgomery City", "countryCode": "US", "stateCode": "MO", "latitude": "38.97754000", "longitude": "-91.50488000"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "MO", "latitude": "38.94149000", "longitude": "-91.47021000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "40.11838000", "longitude": "-91.71211000"}, {"name": "Morgan County", "countryCode": "US", "stateCode": "MO", "latitude": "38.42374000", "longitude": "-92.88598000"}, {"name": "Moscow Mills", "countryCode": "US", "stateCode": "MO", "latitude": "38.94783000", "longitude": "-90.91819000"}, {"name": "Mound City", "countryCode": "US", "stateCode": "MO", "latitude": "40.13111000", "longitude": "-95.23164000"}, {"name": "Mount Vernon", "countryCode": "US", "stateCode": "MO", "latitude": "37.10367000", "longitude": "-93.81854000"}, {"name": "Mountain Grove", "countryCode": "US", "stateCode": "MO", "latitude": "37.13061000", "longitude": "-92.26349000"}, {"name": "Mountain View", "countryCode": "US", "stateCode": "MO", "latitude": "36.99533000", "longitude": "-91.70376000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.49033000", "longitude": "-90.48707000"}, {"name": "Neosho", "countryCode": "US", "stateCode": "MO", "latitude": "36.86896000", "longitude": "-94.36800000"}, {"name": "Nevada", "countryCode": "US", "stateCode": "MO", "latitude": "37.83921000", "longitude": "-94.35467000"}, {"name": "New Franklin", "countryCode": "US", "stateCode": "MO", "latitude": "39.01725000", "longitude": "-92.73741000"}, {"name": "New Haven", "countryCode": "US", "stateCode": "MO", "latitude": "38.60838000", "longitude": "-91.21904000"}, {"name": "New London", "countryCode": "US", "stateCode": "MO", "latitude": "39.58532000", "longitude": "-91.40098000"}, {"name": "New Madrid", "countryCode": "US", "stateCode": "MO", "latitude": "36.58645000", "longitude": "-89.52785000"}, {"name": "New Madrid County", "countryCode": "US", "stateCode": "MO", "latitude": "36.59465000", "longitude": "-89.65183000"}, {"name": "Newton County", "countryCode": "US", "stateCode": "MO", "latitude": "36.90551000", "longitude": "-94.33925000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.04339000", "longitude": "-93.29435000"}, {"name": "Nodaway County", "countryCode": "US", "stateCode": "MO", "latitude": "40.36077000", "longitude": "-94.88343000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.54563000", "longitude": "-94.48522000"}, {"name": "Normandy", "countryCode": "US", "stateCode": "MO", "latitude": "38.72088000", "longitude": "-90.29734000"}, {"name": "North Kansas City", "countryCode": "US", "stateCode": "MO", "latitude": "39.13000000", "longitude": "-94.56218000"}, {"name": "Northwoods", "countryCode": "US", "stateCode": "MO", "latitude": "38.70422000", "longitude": "-90.28345000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.81061000", "longitude": "-90.69985000"}, {"name": "Oak Grove", "countryCode": "US", "stateCode": "MO", "latitude": "39.00501000", "longitude": "-94.12939000"}, {"name": "Oakland", "countryCode": "US", "stateCode": "MO", "latitude": "38.57644000", "longitude": "-90.38567000"}, {"name": "Oakville", "countryCode": "US", "stateCode": "MO", "latitude": "38.47005000", "longitude": "-90.30456000"}, {"name": "Odessa", "countryCode": "US", "stateCode": "MO", "latitude": "38.99917000", "longitude": "-93.95356000"}, {"name": "Old Jamestown", "countryCode": "US", "stateCode": "MO", "latitude": "38.83494000", "longitude": "-90.28511000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.66533000", "longitude": "-90.37595000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.08505000", "longitude": "-89.65536000"}, {"name": "Oregon", "countryCode": "US", "stateCode": "MO", "latitude": "39.98694000", "longitude": "-95.14498000"}, {"name": "Oregon County", "countryCode": "US", "stateCode": "MO", "latitude": "36.68672000", "longitude": "-91.40329000"}, {"name": "Oronogo", "countryCode": "US", "stateCode": "MO", "latitude": "37.18839000", "longitude": "-94.47023000"}, {"name": "Osage Beach", "countryCode": "US", "stateCode": "MO", "latitude": "38.12956000", "longitude": "-92.65277000"}, {"name": "Osage County", "countryCode": "US", "stateCode": "MO", "latitude": "38.46037000", "longitude": "-91.86184000"}, {"name": "Osceola", "countryCode": "US", "stateCode": "MO", "latitude": "38.04670000", "longitude": "-93.70438000"}, {"name": "Overland", "countryCode": "US", "stateCode": "MO", "latitude": "38.70116000", "longitude": "-90.36234000"}, {"name": "Owensville", "countryCode": "US", "stateCode": "MO", "latitude": "38.34560000", "longitude": "-91.50155000"}, {"name": "Ozark", "countryCode": "US", "stateCode": "MO", "latitude": "37.02089000", "longitude": "-93.20602000"}, {"name": "Ozark County", "countryCode": "US", "stateCode": "MO", "latitude": "36.64932000", "longitude": "-92.44466000"}, {"name": "Pacific", "countryCode": "US", "stateCode": "MO", "latitude": "38.48200000", "longitude": "-90.74152000"}, {"name": "Pagedale", "countryCode": "US", "stateCode": "MO", "latitude": "38.68338000", "longitude": "-90.30761000"}, {"name": "Palmyra", "countryCode": "US", "stateCode": "MO", "latitude": "39.79421000", "longitude": "-91.52321000"}, {"name": "Paris", "countryCode": "US", "stateCode": "MO", "latitude": "39.48087000", "longitude": "-92.00128000"}, {"name": "Park Hills", "countryCode": "US", "stateCode": "MO", "latitude": "37.85422000", "longitude": "-90.51818000"}, {"name": "Parkville", "countryCode": "US", "stateCode": "MO", "latitude": "39.19500000", "longitude": "-94.68218000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.71918000", "longitude": "-94.45856000"}, {"name": "Pemiscot County", "countryCode": "US", "stateCode": "MO", "latitude": "36.21145000", "longitude": "-89.78538000"}, {"name": "Perry County", "countryCode": "US", "stateCode": "MO", "latitude": "37.70714000", "longitude": "-89.82441000"}, {"name": "Perryville", "countryCode": "US", "stateCode": "MO", "latitude": "37.72422000", "longitude": "-89.86122000"}, {"name": "Pettis County", "countryCode": "US", "stateCode": "MO", "latitude": "38.72829000", "longitude": "-93.28510000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.28339000", "longitude": "-90.39512000"}, {"name": "Phelps County", "countryCode": "US", "stateCode": "MO", "latitude": "37.87713000", "longitude": "-91.79236000"}, {"name": "Piedmont", "countryCode": "US", "stateCode": "MO", "latitude": "37.15449000", "longitude": "-90.69567000"}, {"name": "Pierce City", "countryCode": "US", "stateCode": "MO", "latitude": "36.94590000", "longitude": "-94.00021000"}, {"name": "Pike County", "countryCode": "US", "stateCode": "MO", "latitude": "39.34384000", "longitude": "-91.17136000"}, {"name": "Pine Lawn", "countryCode": "US", "stateCode": "MO", "latitude": "38.69588000", "longitude": "-90.27511000"}, {"name": "Pineville", "countryCode": "US", "stateCode": "MO", "latitude": "36.59452000", "longitude": "-94.38410000"}, {"name": "Platte City", "countryCode": "US", "stateCode": "MO", "latitude": "39.37028000", "longitude": "-94.78246000"}, {"name": "Platte County", "countryCode": "US", "stateCode": "MO", "latitude": "39.38050000", "longitude": "-94.77359000"}, {"name": "Plattsburg", "countryCode": "US", "stateCode": "MO", "latitude": "39.56555000", "longitude": "-94.44801000"}, {"name": "Pleasant Hill", "countryCode": "US", "stateCode": "MO", "latitude": "38.78751000", "longitude": "-94.26939000"}, {"name": "Pleasant Valley", "countryCode": "US", "stateCode": "MO", "latitude": "39.21639000", "longitude": "-94.48412000"}, {"name": "Polk County", "countryCode": "US", "stateCode": "MO", "latitude": "37.61648000", "longitude": "-93.40053000"}, {"name": "Poplar Bluff", "countryCode": "US", "stateCode": "MO", "latitude": "36.75700000", "longitude": "-90.39289000"}, {"name": "Portageville", "countryCode": "US", "stateCode": "MO", "latitude": "36.42534000", "longitude": "-89.69953000"}, {"name": "Po<PERSON>i", "countryCode": "US", "stateCode": "MO", "latitude": "37.93644000", "longitude": "-90.78791000"}, {"name": "Princeton", "countryCode": "US", "stateCode": "MO", "latitude": "40.40084000", "longitude": "-93.58050000"}, {"name": "Pulaski County", "countryCode": "US", "stateCode": "MO", "latitude": "37.82463000", "longitude": "-92.20766000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.81729000", "longitude": "-93.92076000"}, {"name": "Putnam County", "countryCode": "US", "stateCode": "MO", "latitude": "40.47891000", "longitude": "-93.01613000"}, {"name": "Ralls County", "countryCode": "US", "stateCode": "MO", "latitude": "39.52767000", "longitude": "-91.52202000"}, {"name": "Randolph County", "countryCode": "US", "stateCode": "MO", "latitude": "39.44017000", "longitude": "-92.49708000"}, {"name": "Ray County", "countryCode": "US", "stateCode": "MO", "latitude": "39.35241000", "longitude": "-93.98988000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.80195000", "longitude": "-94.45273000"}, {"name": "Raytown", "countryCode": "US", "stateCode": "MO", "latitude": "39.00862000", "longitude": "-94.46356000"}, {"name": "Republic", "countryCode": "US", "stateCode": "MO", "latitude": "37.12005000", "longitude": "-93.48019000"}, {"name": "Reynolds County", "countryCode": "US", "stateCode": "MO", "latitude": "37.36233000", "longitude": "-90.96908000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.09642000", "longitude": "-94.36106000"}, {"name": "Richland", "countryCode": "US", "stateCode": "MO", "latitude": "37.85698000", "longitude": "-92.40434000"}, {"name": "Richmond", "countryCode": "US", "stateCode": "MO", "latitude": "39.27862000", "longitude": "-93.97689000"}, {"name": "Richmond Heights", "countryCode": "US", "stateCode": "MO", "latitude": "38.62866000", "longitude": "-90.31956000"}, {"name": "Ripley County", "countryCode": "US", "stateCode": "MO", "latitude": "36.65282000", "longitude": "-90.86388000"}, {"name": "Riverside", "countryCode": "US", "stateCode": "MO", "latitude": "39.17750000", "longitude": "-94.61301000"}, {"name": "Riverview", "countryCode": "US", "stateCode": "MO", "latitude": "38.74783000", "longitude": "-90.21150000"}, {"name": "Rock Hill", "countryCode": "US", "stateCode": "MO", "latitude": "38.60755000", "longitude": "-90.37845000"}, {"name": "Rock Port", "countryCode": "US", "stateCode": "MO", "latitude": "40.41111000", "longitude": "-95.51693000"}, {"name": "Rogersville", "countryCode": "US", "stateCode": "MO", "latitude": "37.11700000", "longitude": "-93.05573000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.95143000", "longitude": "-91.77127000"}, {"name": "Saint Ann", "countryCode": "US", "stateCode": "MO", "latitude": "38.72727000", "longitude": "-90.38317000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.78394000", "longitude": "-90.48123000"}, {"name": "Saint Charles County", "countryCode": "US", "stateCode": "MO", "latitude": "38.78192000", "longitude": "-90.67490000"}, {"name": "Saint Clair", "countryCode": "US", "stateCode": "MO", "latitude": "38.34533000", "longitude": "-90.98097000"}, {"name": "Saint Clair County", "countryCode": "US", "stateCode": "MO", "latitude": "38.03718000", "longitude": "-93.77598000"}, {"name": "Saint Francois County", "countryCode": "US", "stateCode": "MO", "latitude": "37.81028000", "longitude": "-90.47227000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.53672000", "longitude": "-90.31484000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.99726000", "longitude": "-91.61432000"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.71484000", "longitude": "-90.34627000"}, {"name": "Saint Johns", "countryCode": "US", "stateCode": "MO", "latitude": "38.71338000", "longitude": "-90.34317000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.76861000", "longitude": "-94.84663000"}, {"name": "Saint Louis County", "countryCode": "US", "stateCode": "MO", "latitude": "38.64068000", "longitude": "-90.44341000"}, {"name": "Saint Martins", "countryCode": "US", "stateCode": "MO", "latitude": "38.59420000", "longitude": "-92.33713000"}, {"name": "Saint Paul", "countryCode": "US", "stateCode": "MO", "latitude": "38.86144000", "longitude": "-90.74179000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.80033000", "longitude": "-90.62651000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.82810000", "longitude": "-92.17767000"}, {"name": "Sainte Genevieve", "countryCode": "US", "stateCode": "MO", "latitude": "37.98144000", "longitude": "-90.04178000"}, {"name": "Sainte Genevieve County", "countryCode": "US", "stateCode": "MO", "latitude": "37.89440000", "longitude": "-90.19442000"}, {"name": "Salem", "countryCode": "US", "stateCode": "MO", "latitude": "37.64560000", "longitude": "-91.53598000"}, {"name": "Saline County", "countryCode": "US", "stateCode": "MO", "latitude": "39.13684000", "longitude": "-93.20185000"}, {"name": "Salisbury", "countryCode": "US", "stateCode": "MO", "latitude": "39.42392000", "longitude": "-92.80158000"}, {"name": "Sappington", "countryCode": "US", "stateCode": "MO", "latitude": "38.53700000", "longitude": "-90.37984000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.06923000", "longitude": "-94.11660000"}, {"name": "Savannah", "countryCode": "US", "stateCode": "MO", "latitude": "39.94166000", "longitude": "-94.83025000"}, {"name": "Schuyler County", "countryCode": "US", "stateCode": "MO", "latitude": "40.47027000", "longitude": "-92.52094000"}, {"name": "Scotland County", "countryCode": "US", "stateCode": "MO", "latitude": "40.45260000", "longitude": "-92.14705000"}, {"name": "Scott City", "countryCode": "US", "stateCode": "MO", "latitude": "37.21672000", "longitude": "-89.52453000"}, {"name": "Scott County", "countryCode": "US", "stateCode": "MO", "latitude": "37.05305000", "longitude": "-89.56851000"}, {"name": "Sedalia", "countryCode": "US", "stateCode": "MO", "latitude": "38.70446000", "longitude": "-93.22826000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.13423000", "longitude": "-90.15982000"}, {"name": "Seneca", "countryCode": "US", "stateCode": "MO", "latitude": "36.84146000", "longitude": "-94.61106000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.14644000", "longitude": "-92.76878000"}, {"name": "Shannon County", "countryCode": "US", "stateCode": "MO", "latitude": "37.15739000", "longitude": "-91.40051000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.69393000", "longitude": "-92.04295000"}, {"name": "Shelby County", "countryCode": "US", "stateCode": "MO", "latitude": "39.79778000", "longitude": "-92.07662000"}, {"name": "Shelbyville", "countryCode": "US", "stateCode": "MO", "latitude": "39.80587000", "longitude": "-92.04156000"}, {"name": "Shell Knob", "countryCode": "US", "stateCode": "MO", "latitude": "36.63229000", "longitude": "-93.63436000"}, {"name": "Shrewsbury", "countryCode": "US", "stateCode": "MO", "latitude": "38.59033000", "longitude": "-90.33678000"}, {"name": "Sikeston", "countryCode": "US", "stateCode": "MO", "latitude": "36.87672000", "longitude": "-89.58786000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "39.21808000", "longitude": "-93.06909000"}, {"name": "Smithville", "countryCode": "US", "stateCode": "MO", "latitude": "39.38694000", "longitude": "-94.58107000"}, {"name": "Spanish Lake", "countryCode": "US", "stateCode": "MO", "latitude": "38.78783000", "longitude": "-90.21594000"}, {"name": "Sparta", "countryCode": "US", "stateCode": "MO", "latitude": "37.00116000", "longitude": "-93.08157000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "MO", "latitude": "37.21533000", "longitude": "-93.29824000"}, {"name": "St. Louis", "countryCode": "US", "stateCode": "MO", "latitude": "38.62727000", "longitude": "-90.19789000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "40.21777000", "longitude": "-94.53829000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.08396000", "longitude": "-89.82925000"}, {"name": "Steelville", "countryCode": "US", "stateCode": "MO", "latitude": "37.96810000", "longitude": "-91.35487000"}, {"name": "Stockton", "countryCode": "US", "stateCode": "MO", "latitude": "37.69893000", "longitude": "-93.79604000"}, {"name": "Stoddard County", "countryCode": "US", "stateCode": "MO", "latitude": "36.85562000", "longitude": "-89.94431000"}, {"name": "Stone County", "countryCode": "US", "stateCode": "MO", "latitude": "36.74694000", "longitude": "-93.45600000"}, {"name": "Stover", "countryCode": "US", "stateCode": "MO", "latitude": "38.44086000", "longitude": "-92.99187000"}, {"name": "Strafford", "countryCode": "US", "stateCode": "MO", "latitude": "37.26838000", "longitude": "-93.11713000"}, {"name": "Sugar Creek", "countryCode": "US", "stateCode": "MO", "latitude": "39.10973000", "longitude": "-94.44467000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.20810000", "longitude": "-91.16042000"}, {"name": "Sullivan County", "countryCode": "US", "stateCode": "MO", "latitude": "40.21064000", "longitude": "-93.11152000"}, {"name": "Sunset Hills", "countryCode": "US", "stateCode": "MO", "latitude": "38.53894000", "longitude": "-90.40734000"}, {"name": "Sweet Springs", "countryCode": "US", "stateCode": "MO", "latitude": "38.96363000", "longitude": "-93.41493000"}, {"name": "Taney County", "countryCode": "US", "stateCode": "MO", "latitude": "36.65473000", "longitude": "-93.04111000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.50643000", "longitude": "-92.07101000"}, {"name": "Tarkio", "countryCode": "US", "stateCode": "MO", "latitude": "40.44028000", "longitude": "-95.37776000"}, {"name": "Terre Haute", "countryCode": "US", "stateCode": "MO", "latitude": "40.43946000", "longitude": "-93.23410000"}, {"name": "Terre du Lac", "countryCode": "US", "stateCode": "MO", "latitude": "37.91172000", "longitude": "-90.62541000"}, {"name": "Texas County", "countryCode": "US", "stateCode": "MO", "latitude": "37.31731000", "longitude": "-91.96505000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.52451000", "longitude": "-91.53820000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.65558000", "longitude": "-92.77992000"}, {"name": "Town and Country", "countryCode": "US", "stateCode": "MO", "latitude": "38.61228000", "longitude": "-90.46345000"}, {"name": "Trenton", "countryCode": "US", "stateCode": "MO", "latitude": "40.07890000", "longitude": "-93.61661000"}, {"name": "Troy", "countryCode": "US", "stateCode": "MO", "latitude": "38.97949000", "longitude": "-90.98070000"}, {"name": "Tuscumbia", "countryCode": "US", "stateCode": "MO", "latitude": "38.23309000", "longitude": "-92.45852000"}, {"name": "Union", "countryCode": "US", "stateCode": "MO", "latitude": "38.45005000", "longitude": "-91.00848000"}, {"name": "Unionville", "countryCode": "US", "stateCode": "MO", "latitude": "40.47696000", "longitude": "-93.00326000"}, {"name": "University City", "countryCode": "US", "stateCode": "MO", "latitude": "38.65588000", "longitude": "-90.30928000"}, {"name": "Valley Park", "countryCode": "US", "stateCode": "MO", "latitude": "38.54922000", "longitude": "-90.49262000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "36.99561000", "longitude": "-91.01457000"}, {"name": "Vandalia", "countryCode": "US", "stateCode": "MO", "latitude": "39.31087000", "longitude": "-91.48849000"}, {"name": "Velda Village", "countryCode": "US", "stateCode": "MO", "latitude": "38.69005000", "longitude": "-90.29428000"}, {"name": "Velda Village Hills", "countryCode": "US", "stateCode": "MO", "latitude": "38.69061000", "longitude": "-90.28734000"}, {"name": "Vernon County", "countryCode": "US", "stateCode": "MO", "latitude": "37.85058000", "longitude": "-94.34244000"}, {"name": "Versailles", "countryCode": "US", "stateCode": "MO", "latitude": "38.43141000", "longitude": "-92.84103000"}, {"name": "Vienna", "countryCode": "US", "stateCode": "MO", "latitude": "38.18671000", "longitude": "-91.94711000"}, {"name": "Villa Ridge", "countryCode": "US", "stateCode": "MO", "latitude": "38.47255000", "longitude": "-90.88680000"}, {"name": "Vinita Park", "countryCode": "US", "stateCode": "MO", "latitude": "38.69005000", "longitude": "-90.34262000"}, {"name": "Wardsville", "countryCode": "US", "stateCode": "MO", "latitude": "38.48892000", "longitude": "-92.17435000"}, {"name": "Warren County", "countryCode": "US", "stateCode": "MO", "latitude": "38.76462000", "longitude": "-91.16069000"}, {"name": "Warrensburg", "countryCode": "US", "stateCode": "MO", "latitude": "38.76279000", "longitude": "-93.73605000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.81144000", "longitude": "-91.14154000"}, {"name": "Warsaw", "countryCode": "US", "stateCode": "MO", "latitude": "38.24308000", "longitude": "-93.38187000"}, {"name": "<PERSON>on Woods", "countryCode": "US", "stateCode": "MO", "latitude": "38.60727000", "longitude": "-90.38345000"}, {"name": "Washington", "countryCode": "US", "stateCode": "MO", "latitude": "38.55811000", "longitude": "-91.01209000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "MO", "latitude": "37.96168000", "longitude": "-90.87742000"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "MO", "latitude": "37.11264000", "longitude": "-90.46143000"}, {"name": "Waynesville", "countryCode": "US", "stateCode": "MO", "latitude": "37.82865000", "longitude": "-92.20072000"}, {"name": "Weatherby Lake", "countryCode": "US", "stateCode": "MO", "latitude": "39.23778000", "longitude": "-94.69607000"}, {"name": "Webb City", "countryCode": "US", "stateCode": "MO", "latitude": "37.14645000", "longitude": "-94.46300000"}, {"name": "Webster County", "countryCode": "US", "stateCode": "MO", "latitude": "37.28091000", "longitude": "-92.87588000"}, {"name": "<PERSON>s", "countryCode": "US", "stateCode": "MO", "latitude": "38.59255000", "longitude": "-90.35734000"}, {"name": "Weldon Spring", "countryCode": "US", "stateCode": "MO", "latitude": "38.71339000", "longitude": "-90.68929000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.67283000", "longitude": "-90.29928000"}, {"name": "Wellsville", "countryCode": "US", "stateCode": "MO", "latitude": "39.07198000", "longitude": "-91.57016000"}, {"name": "Wentzville", "countryCode": "US", "stateCode": "MO", "latitude": "38.81144000", "longitude": "-90.85291000"}, {"name": "West Plains", "countryCode": "US", "stateCode": "MO", "latitude": "36.72812000", "longitude": "-91.85237000"}, {"name": "Weston", "countryCode": "US", "stateCode": "MO", "latitude": "39.41111000", "longitude": "-94.90163000"}, {"name": "Whiteman Air Force Base", "countryCode": "US", "stateCode": "MO", "latitude": "38.73018000", "longitude": "-93.55895000"}, {"name": "Wildwood", "countryCode": "US", "stateCode": "MO", "latitude": "38.58283000", "longitude": "-90.66290000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "37.30505000", "longitude": "-93.42853000"}, {"name": "Willow Springs", "countryCode": "US", "stateCode": "MO", "latitude": "36.99228000", "longitude": "-91.96987000"}, {"name": "Winchester", "countryCode": "US", "stateCode": "MO", "latitude": "38.59033000", "longitude": "-90.52790000"}, {"name": "Windsor", "countryCode": "US", "stateCode": "MO", "latitude": "38.53224000", "longitude": "-93.52215000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MO", "latitude": "38.99727000", "longitude": "-90.73846000"}, {"name": "Winona", "countryCode": "US", "stateCode": "MO", "latitude": "37.00977000", "longitude": "-91.32347000"}, {"name": "Woodson Terrace", "countryCode": "US", "stateCode": "MO", "latitude": "38.72505000", "longitude": "-90.35845000"}, {"name": "Worth County", "countryCode": "US", "stateCode": "MO", "latitude": "40.47914000", "longitude": "-94.42210000"}, {"name": "Wright City", "countryCode": "US", "stateCode": "MO", "latitude": "38.82755000", "longitude": "-91.02014000"}, {"name": "Wright County", "countryCode": "US", "stateCode": "MO", "latitude": "37.27014000", "longitude": "-92.46870000"}]