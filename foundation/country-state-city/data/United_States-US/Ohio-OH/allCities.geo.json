[{"name": "Ada", "countryCode": "US", "stateCode": "OH", "latitude": "40.76950000", "longitude": "-83.82271000"}, {"name": "Adams County", "countryCode": "US", "stateCode": "OH", "latitude": "38.84551000", "longitude": "-83.47215000"}, {"name": "Akron", "countryCode": "US", "stateCode": "OH", "latitude": "41.08144000", "longitude": "-81.51901000"}, {"name": "Allen County", "countryCode": "US", "stateCode": "OH", "latitude": "40.77152000", "longitude": "-84.10578000"}, {"name": "Alliance", "countryCode": "US", "stateCode": "OH", "latitude": "40.91534000", "longitude": "-81.10593000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.20478000", "longitude": "-84.42800000"}, {"name": "Amelia", "countryCode": "US", "stateCode": "OH", "latitude": "39.02840000", "longitude": "-84.21771000"}, {"name": "Amherst", "countryCode": "US", "stateCode": "OH", "latitude": "41.39782000", "longitude": "-82.22238000"}, {"name": "Andover", "countryCode": "US", "stateCode": "OH", "latitude": "41.60672000", "longitude": "-80.57230000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.39449000", "longitude": "-84.17272000"}, {"name": "<PERSON>son<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.21449000", "longitude": "-84.63690000"}, {"name": "Antwerp", "countryCode": "US", "stateCode": "OH", "latitude": "41.18144000", "longitude": "-84.74051000"}, {"name": "Apple Creek", "countryCode": "US", "stateCode": "OH", "latitude": "40.75172000", "longitude": "-81.83930000"}, {"name": "Apple Valley", "countryCode": "US", "stateCode": "OH", "latitude": "40.43890000", "longitude": "-82.35391000"}, {"name": "Arcanum", "countryCode": "US", "stateCode": "OH", "latitude": "39.99005000", "longitude": "-84.55329000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.52144000", "longitude": "-84.30717000"}, {"name": "Arlington", "countryCode": "US", "stateCode": "OH", "latitude": "40.89366000", "longitude": "-83.65021000"}, {"name": "Ashland", "countryCode": "US", "stateCode": "OH", "latitude": "40.86867000", "longitude": "-82.31822000"}, {"name": "Ashland County", "countryCode": "US", "stateCode": "OH", "latitude": "40.84602000", "longitude": "-82.27069000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.40895000", "longitude": "-82.95546000"}, {"name": "Ashtabula", "countryCode": "US", "stateCode": "OH", "latitude": "41.86505000", "longitude": "-80.78981000"}, {"name": "Ashtabula County", "countryCode": "US", "stateCode": "OH", "latitude": "41.89638000", "longitude": "-80.75901000"}, {"name": "Ashville", "countryCode": "US", "stateCode": "OH", "latitude": "39.71562000", "longitude": "-82.95296000"}, {"name": "Athens", "countryCode": "US", "stateCode": "OH", "latitude": "39.32924000", "longitude": "-82.10126000"}, {"name": "Athens County", "countryCode": "US", "stateCode": "OH", "latitude": "39.33386000", "longitude": "-82.04513000"}, {"name": "Auglaize County", "countryCode": "US", "stateCode": "OH", "latitude": "40.56091000", "longitude": "-84.22174000"}, {"name": "Aurora", "countryCode": "US", "stateCode": "OH", "latitude": "41.31755000", "longitude": "-81.34539000"}, {"name": "Austintown", "countryCode": "US", "stateCode": "OH", "latitude": "41.10172000", "longitude": "-80.76452000"}, {"name": "Avon", "countryCode": "US", "stateCode": "OH", "latitude": "41.45171000", "longitude": "-82.03542000"}, {"name": "Avon Center", "countryCode": "US", "stateCode": "OH", "latitude": "41.45976000", "longitude": "-82.01959000"}, {"name": "Avon Lake", "countryCode": "US", "stateCode": "OH", "latitude": "41.50532000", "longitude": "-82.02820000"}, {"name": "Bainbridge", "countryCode": "US", "stateCode": "OH", "latitude": "41.38644000", "longitude": "-81.33955000"}, {"name": "Ballville", "countryCode": "US", "stateCode": "OH", "latitude": "41.32783000", "longitude": "-83.13214000"}, {"name": "Baltimore", "countryCode": "US", "stateCode": "OH", "latitude": "39.84534000", "longitude": "-82.60072000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.01283000", "longitude": "-81.60512000"}, {"name": "Barnesville", "countryCode": "US", "stateCode": "OH", "latitude": "39.98813000", "longitude": "-81.17650000"}, {"name": "Batavia", "countryCode": "US", "stateCode": "OH", "latitude": "39.07701000", "longitude": "-84.17688000"}, {"name": "Bay Village", "countryCode": "US", "stateCode": "OH", "latitude": "41.48477000", "longitude": "-81.92208000"}, {"name": "Beach City", "countryCode": "US", "stateCode": "OH", "latitude": "40.65312000", "longitude": "-81.58096000"}, {"name": "Beachwood", "countryCode": "US", "stateCode": "OH", "latitude": "41.46450000", "longitude": "-81.50873000"}, {"name": "Beavercreek", "countryCode": "US", "stateCode": "OH", "latitude": "39.70923000", "longitude": "-84.06327000"}, {"name": "Beckett Ridge", "countryCode": "US", "stateCode": "OH", "latitude": "39.34700000", "longitude": "-84.43522000"}, {"name": "Bedford", "countryCode": "US", "stateCode": "OH", "latitude": "41.39311000", "longitude": "-81.53651000"}, {"name": "Bedford Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.41700000", "longitude": "-81.52734000"}, {"name": "Beechwood Trails", "countryCode": "US", "stateCode": "OH", "latitude": "40.02367000", "longitude": "-82.65072000"}, {"name": "Bellaire", "countryCode": "US", "stateCode": "OH", "latitude": "40.01618000", "longitude": "-80.74231000"}, {"name": "Bellbrook", "countryCode": "US", "stateCode": "OH", "latitude": "39.63562000", "longitude": "-84.07077000"}, {"name": "Bellefontaine", "countryCode": "US", "stateCode": "OH", "latitude": "40.36116000", "longitude": "-83.75966000"}, {"name": "Bellevue", "countryCode": "US", "stateCode": "OH", "latitude": "41.27366000", "longitude": "-82.84158000"}, {"name": "Bellville", "countryCode": "US", "stateCode": "OH", "latitude": "40.62006000", "longitude": "-82.51072000"}, {"name": "Belmont County", "countryCode": "US", "stateCode": "OH", "latitude": "40.01580000", "longitude": "-80.98854000"}, {"name": "Belpre", "countryCode": "US", "stateCode": "OH", "latitude": "39.27396000", "longitude": "-81.57290000"}, {"name": "Berea", "countryCode": "US", "stateCode": "OH", "latitude": "41.36616000", "longitude": "-81.85430000"}, {"name": "Bethel", "countryCode": "US", "stateCode": "OH", "latitude": "38.96368000", "longitude": "-84.08077000"}, {"name": "Bethesda", "countryCode": "US", "stateCode": "OH", "latitude": "40.01618000", "longitude": "-81.07260000"}, {"name": "Beverly", "countryCode": "US", "stateCode": "OH", "latitude": "39.54785000", "longitude": "-81.63957000"}, {"name": "Bexley", "countryCode": "US", "stateCode": "OH", "latitude": "39.96895000", "longitude": "-82.93768000"}, {"name": "Blacklick Estates", "countryCode": "US", "stateCode": "OH", "latitude": "39.90506000", "longitude": "-82.86434000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.29312000", "longitude": "-83.98882000"}, {"name": "Blue Ash", "countryCode": "US", "stateCode": "OH", "latitude": "39.23200000", "longitude": "-84.37827000"}, {"name": "Bluffton", "countryCode": "US", "stateCode": "OH", "latitude": "40.89533000", "longitude": "-83.88883000"}, {"name": "Boardman", "countryCode": "US", "stateCode": "OH", "latitude": "41.02423000", "longitude": "-80.66285000"}, {"name": "Bolindale", "countryCode": "US", "stateCode": "OH", "latitude": "41.20728000", "longitude": "-80.77758000"}, {"name": "Boston Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.26478000", "longitude": "-81.51317000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.46783000", "longitude": "-84.18050000"}, {"name": "Bowling Green", "countryCode": "US", "stateCode": "OH", "latitude": "41.37477000", "longitude": "-83.65132000"}, {"name": "Bradford", "countryCode": "US", "stateCode": "OH", "latitude": "40.13227000", "longitude": "-84.43078000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.32422000", "longitude": "-83.43854000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.54255000", "longitude": "-81.62624000"}, {"name": "Brecksville", "countryCode": "US", "stateCode": "OH", "latitude": "41.31978000", "longitude": "-81.62679000"}, {"name": "Bremen", "countryCode": "US", "stateCode": "OH", "latitude": "39.70173000", "longitude": "-82.42682000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.70700000", "longitude": "-81.59818000"}, {"name": "Bridgeport", "countryCode": "US", "stateCode": "OH", "latitude": "40.06979000", "longitude": "-80.74008000"}, {"name": "Bridgetown", "countryCode": "US", "stateCode": "OH", "latitude": "39.15311000", "longitude": "-84.63717000"}, {"name": "Brilliant", "countryCode": "US", "stateCode": "OH", "latitude": "40.26479000", "longitude": "-80.62619000"}, {"name": "Brimfield", "countryCode": "US", "stateCode": "OH", "latitude": "41.10006000", "longitude": "-81.34650000"}, {"name": "Broadview Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.31394000", "longitude": "-81.68513000"}, {"name": "Brook Park", "countryCode": "US", "stateCode": "OH", "latitude": "41.39838000", "longitude": "-81.80458000"}, {"name": "Brookfield Center", "countryCode": "US", "stateCode": "OH", "latitude": "41.24061000", "longitude": "-80.55785000"}, {"name": "Brooklyn", "countryCode": "US", "stateCode": "OH", "latitude": "41.43977000", "longitude": "-81.73541000"}, {"name": "Brooklyn Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.42533000", "longitude": "-81.68818000"}, {"name": "Brookville", "countryCode": "US", "stateCode": "OH", "latitude": "39.83672000", "longitude": "-84.41134000"}, {"name": "Brown County", "countryCode": "US", "stateCode": "OH", "latitude": "38.93405000", "longitude": "-83.86743000"}, {"name": "Brunswick", "countryCode": "US", "stateCode": "OH", "latitude": "41.23811000", "longitude": "-81.84180000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.47477000", "longitude": "-84.55245000"}, {"name": "Buckeye Lake", "countryCode": "US", "stateCode": "OH", "latitude": "39.93368000", "longitude": "-82.47238000"}, {"name": "<PERSON><PERSON>yr<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.80839000", "longitude": "-82.97546000"}, {"name": "Burlington", "countryCode": "US", "stateCode": "OH", "latitude": "38.40730000", "longitude": "-82.53571000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.47061000", "longitude": "-81.14510000"}, {"name": "Butler County", "countryCode": "US", "stateCode": "OH", "latitude": "39.43865000", "longitude": "-84.57566000"}, {"name": "Byesville", "countryCode": "US", "stateCode": "OH", "latitude": "39.96979000", "longitude": "-81.53651000"}, {"name": "Cadiz", "countryCode": "US", "stateCode": "OH", "latitude": "40.27285000", "longitude": "-80.99676000"}, {"name": "Calcutta", "countryCode": "US", "stateCode": "OH", "latitude": "40.67340000", "longitude": "-80.57646000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.74785000", "longitude": "-81.51651000"}, {"name": "Cambridge", "countryCode": "US", "stateCode": "OH", "latitude": "40.03118000", "longitude": "-81.58846000"}, {"name": "Camden", "countryCode": "US", "stateCode": "OH", "latitude": "39.62894000", "longitude": "-84.64856000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.07839000", "longitude": "-80.59924000"}, {"name": "Canal Fulton", "countryCode": "US", "stateCode": "OH", "latitude": "40.88978000", "longitude": "-81.59762000"}, {"name": "Canal Winchester", "countryCode": "US", "stateCode": "OH", "latitude": "39.84284000", "longitude": "-82.80462000"}, {"name": "Canfield", "countryCode": "US", "stateCode": "OH", "latitude": "41.02506000", "longitude": "-80.76091000"}, {"name": "Canton", "countryCode": "US", "stateCode": "OH", "latitude": "40.79895000", "longitude": "-81.37845000"}, {"name": "Cardington", "countryCode": "US", "stateCode": "OH", "latitude": "40.50062000", "longitude": "-82.89351000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.95256000", "longitude": "-83.38242000"}, {"name": "Carlisle", "countryCode": "US", "stateCode": "OH", "latitude": "39.58200000", "longitude": "-84.32022000"}, {"name": "Carroll County", "countryCode": "US", "stateCode": "OH", "latitude": "40.57959000", "longitude": "-81.08972000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.57284000", "longitude": "-81.08565000"}, {"name": "Cedarville", "countryCode": "US", "stateCode": "OH", "latitude": "39.74423000", "longitude": "-83.80854000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.54894000", "longitude": "-84.57023000"}, {"name": "Centerburg", "countryCode": "US", "stateCode": "OH", "latitude": "40.30451000", "longitude": "-82.69628000"}, {"name": "Centerville", "countryCode": "US", "stateCode": "OH", "latitude": "39.62839000", "longitude": "-84.15938000"}, {"name": "Chagrin Falls", "countryCode": "US", "stateCode": "OH", "latitude": "41.43616000", "longitude": "-81.38650000"}, {"name": "Champaign County", "countryCode": "US", "stateCode": "OH", "latitude": "40.13767000", "longitude": "-83.76950000"}, {"name": "Champion Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.28999000", "longitude": "-80.84595000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.61422000", "longitude": "-81.14899000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.39785000", "longitude": "-82.12931000"}, {"name": "Cherry Grove", "countryCode": "US", "stateCode": "OH", "latitude": "39.07256000", "longitude": "-84.32188000"}, {"name": "Chesterland", "countryCode": "US", "stateCode": "OH", "latitude": "41.52227000", "longitude": "-81.33789000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.15700000", "longitude": "-84.61328000"}, {"name": "Chillicothe", "countryCode": "US", "stateCode": "OH", "latitude": "39.33312000", "longitude": "-82.98240000"}, {"name": "Choctaw Lake", "countryCode": "US", "stateCode": "OH", "latitude": "39.96006000", "longitude": "-83.48492000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.16200000", "longitude": "-80.66480000"}, {"name": "Cincinnati", "countryCode": "US", "stateCode": "OH", "latitude": "39.12711000", "longitude": "-84.51439000"}, {"name": "Circleville", "countryCode": "US", "stateCode": "OH", "latitude": "39.60062000", "longitude": "-82.94601000"}, {"name": "Clark County", "countryCode": "US", "stateCode": "OH", "latitude": "39.91678000", "longitude": "-83.78390000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.46402000", "longitude": "-81.70979000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.86311000", "longitude": "-84.36050000"}, {"name": "Clermont County", "countryCode": "US", "stateCode": "OH", "latitude": "39.04743000", "longitude": "-84.15192000"}, {"name": "Cleveland", "countryCode": "US", "stateCode": "OH", "latitude": "41.49950000", "longitude": "-81.69541000"}, {"name": "Cleveland Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.52005000", "longitude": "-81.55624000"}, {"name": "Cleves", "countryCode": "US", "stateCode": "OH", "latitude": "39.16172000", "longitude": "-84.74912000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.92672000", "longitude": "-81.63040000"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "OH", "latitude": "39.41498000", "longitude": "-83.80838000"}, {"name": "Clyde", "countryCode": "US", "stateCode": "OH", "latitude": "41.30422000", "longitude": "-82.97519000"}, {"name": "Coal Grove", "countryCode": "US", "stateCode": "OH", "latitude": "38.50341000", "longitude": "-82.64711000"}, {"name": "Coldwater", "countryCode": "US", "stateCode": "OH", "latitude": "40.47977000", "longitude": "-84.62829000"}, {"name": "Collinwood", "countryCode": "US", "stateCode": "OH", "latitude": "41.55838000", "longitude": "-81.56929000"}, {"name": "Columbiana", "countryCode": "US", "stateCode": "OH", "latitude": "40.88839000", "longitude": "-80.69396000"}, {"name": "Columbiana County", "countryCode": "US", "stateCode": "OH", "latitude": "40.76843000", "longitude": "-80.77719000"}, {"name": "Columbus", "countryCode": "US", "stateCode": "OH", "latitude": "39.96118000", "longitude": "-82.99879000"}, {"name": "Columbus Grove", "countryCode": "US", "stateCode": "OH", "latitude": "40.91950000", "longitude": "-84.05689000"}, {"name": "Commercial Point", "countryCode": "US", "stateCode": "OH", "latitude": "39.76840000", "longitude": "-83.05713000"}, {"name": "Conneaut", "countryCode": "US", "stateCode": "OH", "latitude": "41.94756000", "longitude": "-80.55424000"}, {"name": "Continental", "countryCode": "US", "stateCode": "OH", "latitude": "41.10033000", "longitude": "-84.26634000"}, {"name": "Convoy", "countryCode": "US", "stateCode": "OH", "latitude": "40.91672000", "longitude": "-84.70274000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.09894000", "longitude": "-81.64457000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.33033000", "longitude": "-80.72536000"}, {"name": "Coshocton", "countryCode": "US", "stateCode": "OH", "latitude": "40.27202000", "longitude": "-81.85958000"}, {"name": "Coshocton County", "countryCode": "US", "stateCode": "OH", "latitude": "40.30164000", "longitude": "-81.92001000"}, {"name": "Covedale", "countryCode": "US", "stateCode": "OH", "latitude": "39.12117000", "longitude": "-84.60633000"}, {"name": "Covington", "countryCode": "US", "stateCode": "OH", "latitude": "40.11727000", "longitude": "-84.35384000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.11700000", "longitude": "-80.98342000"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "OH", "latitude": "40.85077000", "longitude": "-82.91978000"}, {"name": "Crestline", "countryCode": "US", "stateCode": "OH", "latitude": "40.78756000", "longitude": "-82.73657000"}, {"name": "Creston", "countryCode": "US", "stateCode": "OH", "latitude": "40.98700000", "longitude": "-81.89375000"}, {"name": "Cridersville", "countryCode": "US", "stateCode": "OH", "latitude": "40.65422000", "longitude": "-84.15078000"}, {"name": "Crooksville", "countryCode": "US", "stateCode": "OH", "latitude": "39.76896000", "longitude": "-82.09209000"}, {"name": "Crystal Lakes", "countryCode": "US", "stateCode": "OH", "latitude": "39.88923000", "longitude": "-84.02660000"}, {"name": "Curtice", "countryCode": "US", "stateCode": "OH", "latitude": "41.61838000", "longitude": "-83.36771000"}, {"name": "Cuyahoga County", "countryCode": "US", "stateCode": "OH", "latitude": "41.47875000", "longitude": "-81.67786000"}, {"name": "Cuyahoga Falls", "countryCode": "US", "stateCode": "OH", "latitude": "41.13394000", "longitude": "-81.48456000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.79894000", "longitude": "-81.69541000"}, {"name": "Danville", "countryCode": "US", "stateCode": "OH", "latitude": "40.44756000", "longitude": "-82.26016000"}, {"name": "Darke County", "countryCode": "US", "stateCode": "OH", "latitude": "40.13323000", "longitude": "-84.61931000"}, {"name": "Day Heights", "countryCode": "US", "stateCode": "OH", "latitude": "39.17395000", "longitude": "-84.22633000"}, {"name": "Dayton", "countryCode": "US", "stateCode": "OH", "latitude": "39.75895000", "longitude": "-84.19161000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.31200000", "longitude": "-83.91577000"}, {"name": "Deer Park", "countryCode": "US", "stateCode": "OH", "latitude": "39.20534000", "longitude": "-84.39466000"}, {"name": "Defiance", "countryCode": "US", "stateCode": "OH", "latitude": "41.28449000", "longitude": "-84.35578000"}, {"name": "Defiance County", "countryCode": "US", "stateCode": "OH", "latitude": "41.32392000", "longitude": "-84.49050000"}, {"name": "Delaware", "countryCode": "US", "stateCode": "OH", "latitude": "40.29867000", "longitude": "-83.06797000"}, {"name": "Delaware County", "countryCode": "US", "stateCode": "OH", "latitude": "40.27839000", "longitude": "-83.00489000"}, {"name": "Delhi Hills", "countryCode": "US", "stateCode": "OH", "latitude": "39.09284000", "longitude": "-84.61272000"}, {"name": "Delphos", "countryCode": "US", "stateCode": "OH", "latitude": "40.84338000", "longitude": "-84.34162000"}, {"name": "Delta", "countryCode": "US", "stateCode": "OH", "latitude": "41.57366000", "longitude": "-84.00522000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.39340000", "longitude": "-81.33372000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.18589000", "longitude": "-84.65134000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.20755000", "longitude": "-83.89911000"}, {"name": "Detroit-Shoreway", "countryCode": "US", "stateCode": "OH", "latitude": "41.47772000", "longitude": "-81.72991000"}, {"name": "Devola", "countryCode": "US", "stateCode": "OH", "latitude": "39.47369000", "longitude": "-81.47901000"}, {"name": "Dillonvale", "countryCode": "US", "stateCode": "OH", "latitude": "39.21811000", "longitude": "-84.40216000"}, {"name": "Dover", "countryCode": "US", "stateCode": "OH", "latitude": "40.52062000", "longitude": "-81.47401000"}, {"name": "Doylestown", "countryCode": "US", "stateCode": "OH", "latitude": "40.97005000", "longitude": "-81.69652000"}, {"name": "Dresden", "countryCode": "US", "stateCode": "OH", "latitude": "40.12146000", "longitude": "-82.01069000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.74645000", "longitude": "-84.28661000"}, {"name": "Dry Ridge", "countryCode": "US", "stateCode": "OH", "latitude": "39.25922000", "longitude": "-84.61911000"}, {"name": "Dry Run", "countryCode": "US", "stateCode": "OH", "latitude": "39.10423000", "longitude": "-84.33049000"}, {"name": "Dublin", "countryCode": "US", "stateCode": "OH", "latitude": "40.09923000", "longitude": "-83.11408000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.29228000", "longitude": "-84.61800000"}, {"name": "East Canton", "countryCode": "US", "stateCode": "OH", "latitude": "40.78728000", "longitude": "-81.28261000"}, {"name": "East Cleveland", "countryCode": "US", "stateCode": "OH", "latitude": "41.53311000", "longitude": "-81.57901000"}, {"name": "East Liverpool", "countryCode": "US", "stateCode": "OH", "latitude": "40.61868000", "longitude": "-80.57729000"}, {"name": "East Palestine", "countryCode": "US", "stateCode": "OH", "latitude": "40.83395000", "longitude": "-80.54035000"}, {"name": "Eastlake", "countryCode": "US", "stateCode": "OH", "latitude": "41.65394000", "longitude": "-81.45039000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.74394000", "longitude": "-84.63662000"}, {"name": "Eaton Estates", "countryCode": "US", "stateCode": "OH", "latitude": "41.30894000", "longitude": "-82.00570000"}, {"name": "Edgerton", "countryCode": "US", "stateCode": "OH", "latitude": "41.44866000", "longitude": "-84.74801000"}, {"name": "Edgewood", "countryCode": "US", "stateCode": "OH", "latitude": "41.87283000", "longitude": "-80.77286000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.78866000", "longitude": "-84.20384000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.47616000", "longitude": "-83.29576000"}, {"name": "Elmwood Place", "countryCode": "US", "stateCode": "OH", "latitude": "39.18728000", "longitude": "-84.48800000"}, {"name": "Elyria", "countryCode": "US", "stateCode": "OH", "latitude": "41.36838000", "longitude": "-82.10765000"}, {"name": "Englewood", "countryCode": "US", "stateCode": "OH", "latitude": "39.87756000", "longitude": "-84.30217000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.87812000", "longitude": "-83.93688000"}, {"name": "Erie County", "countryCode": "US", "stateCode": "OH", "latitude": "41.43209000", "longitude": "-82.69958000"}, {"name": "Etna", "countryCode": "US", "stateCode": "OH", "latitude": "39.95729000", "longitude": "-82.68183000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.59310000", "longitude": "-81.52679000"}, {"name": "Evendale", "countryCode": "US", "stateCode": "OH", "latitude": "39.25617000", "longitude": "-84.41800000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.82089000", "longitude": "-84.01938000"}, {"name": "Fairfax", "countryCode": "US", "stateCode": "OH", "latitude": "39.14534000", "longitude": "-84.39327000"}, {"name": "Fairfield", "countryCode": "US", "stateCode": "OH", "latitude": "39.34589000", "longitude": "-84.56050000"}, {"name": "Fairfield Beach", "countryCode": "US", "stateCode": "OH", "latitude": "39.91590000", "longitude": "-82.47516000"}, {"name": "Fairfield County", "countryCode": "US", "stateCode": "OH", "latitude": "39.75160000", "longitude": "-82.63059000"}, {"name": "Fairlawn", "countryCode": "US", "stateCode": "OH", "latitude": "41.12783000", "longitude": "-81.60984000"}, {"name": "Fairport Harbor", "countryCode": "US", "stateCode": "OH", "latitude": "41.75004000", "longitude": "-81.27399000"}, {"name": "Fairview Park", "countryCode": "US", "stateCode": "OH", "latitude": "41.44144000", "longitude": "-81.86430000"}, {"name": "Farmersville", "countryCode": "US", "stateCode": "OH", "latitude": "39.67950000", "longitude": "-84.42911000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.67338000", "longitude": "-84.32689000"}, {"name": "Fayette County", "countryCode": "US", "stateCode": "OH", "latitude": "39.55988000", "longitude": "-83.45610000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.04422000", "longitude": "-83.64993000"}, {"name": "Finneytown", "countryCode": "US", "stateCode": "OH", "latitude": "39.20034000", "longitude": "-84.52050000"}, {"name": "Five Points", "countryCode": "US", "stateCode": "OH", "latitude": "39.56867000", "longitude": "-84.19299000"}, {"name": "Forest", "countryCode": "US", "stateCode": "OH", "latitude": "40.80172000", "longitude": "-83.51048000"}, {"name": "Forest Park", "countryCode": "US", "stateCode": "OH", "latitude": "39.29034000", "longitude": "-84.50411000"}, {"name": "Forestville", "countryCode": "US", "stateCode": "OH", "latitude": "39.07506000", "longitude": "-84.34494000"}, {"name": "Fort Loramie", "countryCode": "US", "stateCode": "OH", "latitude": "40.35144000", "longitude": "-84.37384000"}, {"name": "Fort McKinley", "countryCode": "US", "stateCode": "OH", "latitude": "39.79756000", "longitude": "-84.25355000"}, {"name": "Fort Recovery", "countryCode": "US", "stateCode": "OH", "latitude": "40.41282000", "longitude": "-84.77635000"}, {"name": "Fort Shawnee", "countryCode": "US", "stateCode": "OH", "latitude": "40.68672000", "longitude": "-84.13773000"}, {"name": "Fostoria", "countryCode": "US", "stateCode": "OH", "latitude": "41.15700000", "longitude": "-83.41687000"}, {"name": "Frankfort", "countryCode": "US", "stateCode": "OH", "latitude": "39.40145000", "longitude": "-83.18074000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.55895000", "longitude": "-84.30411000"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "OH", "latitude": "39.96952000", "longitude": "-83.00935000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "38.64508000", "longitude": "-82.84878000"}, {"name": "Frazeysburg", "countryCode": "US", "stateCode": "OH", "latitude": "40.11729000", "longitude": "-82.11931000"}, {"name": "Fredericktown", "countryCode": "US", "stateCode": "OH", "latitude": "40.48117000", "longitude": "-82.54072000"}, {"name": "Fremont", "countryCode": "US", "stateCode": "OH", "latitude": "41.35033000", "longitude": "-83.12186000"}, {"name": "Fruit Hill", "countryCode": "US", "stateCode": "OH", "latitude": "39.07562000", "longitude": "-84.36438000"}, {"name": "Fulton County", "countryCode": "US", "stateCode": "OH", "latitude": "41.60180000", "longitude": "-84.13007000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.01923000", "longitude": "-82.87934000"}, {"name": "G<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.73367000", "longitude": "-82.78990000"}, {"name": "Gallia County", "countryCode": "US", "stateCode": "OH", "latitude": "38.82467000", "longitude": "-82.31691000"}, {"name": "Gallipolis", "countryCode": "US", "stateCode": "OH", "latitude": "38.80980000", "longitude": "-82.20237000"}, {"name": "Gambier", "countryCode": "US", "stateCode": "OH", "latitude": "40.37562000", "longitude": "-82.39710000"}, {"name": "Garfield Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.41700000", "longitude": "-81.60596000"}, {"name": "Garrettsville", "countryCode": "US", "stateCode": "OH", "latitude": "41.28422000", "longitude": "-81.09649000"}, {"name": "Gates Mills", "countryCode": "US", "stateCode": "OH", "latitude": "41.51755000", "longitude": "-81.40345000"}, {"name": "Geauga County", "countryCode": "US", "stateCode": "OH", "latitude": "41.49954000", "longitude": "-81.17865000"}, {"name": "Geneva", "countryCode": "US", "stateCode": "OH", "latitude": "41.80505000", "longitude": "-80.94815000"}, {"name": "Geneva-on-the-Lake", "countryCode": "US", "stateCode": "OH", "latitude": "41.85950000", "longitude": "-80.95398000"}, {"name": "Genoa", "countryCode": "US", "stateCode": "OH", "latitude": "41.51811000", "longitude": "-83.35909000"}, {"name": "Georgetown", "countryCode": "US", "stateCode": "OH", "latitude": "38.86451000", "longitude": "-83.90409000"}, {"name": "Germantown", "countryCode": "US", "stateCode": "OH", "latitude": "39.62617000", "longitude": "-84.36939000"}, {"name": "Gibsonburg", "countryCode": "US", "stateCode": "OH", "latitude": "41.38450000", "longitude": "-83.32048000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.15395000", "longitude": "-80.70147000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.02894000", "longitude": "-84.07911000"}, {"name": "Glendale", "countryCode": "US", "stateCode": "OH", "latitude": "39.27061000", "longitude": "-84.45939000"}, {"name": "Glenmoor", "countryCode": "US", "stateCode": "OH", "latitude": "40.66617000", "longitude": "-80.62313000"}, {"name": "Glenville", "countryCode": "US", "stateCode": "OH", "latitude": "41.53338000", "longitude": "-81.61735000"}, {"name": "Glouster", "countryCode": "US", "stateCode": "OH", "latitude": "39.50313000", "longitude": "-82.08459000"}, {"name": "Gnadenhutten", "countryCode": "US", "stateCode": "OH", "latitude": "40.35840000", "longitude": "-81.43428000"}, {"name": "Golf Manor", "countryCode": "US", "stateCode": "OH", "latitude": "39.18728000", "longitude": "-84.44633000"}, {"name": "Goshen", "countryCode": "US", "stateCode": "OH", "latitude": "39.23339000", "longitude": "-84.16132000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.27255000", "longitude": "-82.05459000"}, {"name": "Grandview", "countryCode": "US", "stateCode": "OH", "latitude": "39.19422000", "longitude": "-84.72439000"}, {"name": "Grandview Heights", "countryCode": "US", "stateCode": "OH", "latitude": "39.97979000", "longitude": "-83.04074000"}, {"name": "Granville", "countryCode": "US", "stateCode": "OH", "latitude": "40.06812000", "longitude": "-82.51960000"}, {"name": "Granville South", "countryCode": "US", "stateCode": "OH", "latitude": "40.05207000", "longitude": "-82.54166000"}, {"name": "Green", "countryCode": "US", "stateCode": "OH", "latitude": "40.94589000", "longitude": "-81.48317000"}, {"name": "Green Meadows", "countryCode": "US", "stateCode": "OH", "latitude": "39.86895000", "longitude": "-83.94438000"}, {"name": "Green Springs", "countryCode": "US", "stateCode": "OH", "latitude": "41.25616000", "longitude": "-83.05158000"}, {"name": "Greene County", "countryCode": "US", "stateCode": "OH", "latitude": "39.69148000", "longitude": "-83.88989000"}, {"name": "Greenfield", "countryCode": "US", "stateCode": "OH", "latitude": "39.35201000", "longitude": "-83.38269000"}, {"name": "Greenhills", "countryCode": "US", "stateCode": "OH", "latitude": "39.26811000", "longitude": "-84.52300000"}, {"name": "Greensburg", "countryCode": "US", "stateCode": "OH", "latitude": "40.93172000", "longitude": "-81.46484000"}, {"name": "Greentown", "countryCode": "US", "stateCode": "OH", "latitude": "40.92756000", "longitude": "-81.40261000"}, {"name": "Greenville", "countryCode": "US", "stateCode": "OH", "latitude": "40.10283000", "longitude": "-84.63301000"}, {"name": "Greenwich", "countryCode": "US", "stateCode": "OH", "latitude": "41.03005000", "longitude": "-82.51573000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.22311000", "longitude": "-84.58689000"}, {"name": "Grove City", "countryCode": "US", "stateCode": "OH", "latitude": "39.88145000", "longitude": "-83.09296000"}, {"name": "Groveport", "countryCode": "US", "stateCode": "OH", "latitude": "39.87840000", "longitude": "-82.88379000"}, {"name": "Guernsey County", "countryCode": "US", "stateCode": "OH", "latitude": "40.05205000", "longitude": "-81.49426000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.39950000", "longitude": "-84.56134000"}, {"name": "Hamilton County", "countryCode": "US", "stateCode": "OH", "latitude": "39.19553000", "longitude": "-84.54277000"}, {"name": "Hancock County", "countryCode": "US", "stateCode": "OH", "latitude": "41.00194000", "longitude": "-83.66654000"}, {"name": "Hanover", "countryCode": "US", "stateCode": "OH", "latitude": "40.07979000", "longitude": "-82.26098000"}, {"name": "Harbor Hills", "countryCode": "US", "stateCode": "OH", "latitude": "39.93673000", "longitude": "-82.43515000"}, {"name": "Hardin County", "countryCode": "US", "stateCode": "OH", "latitude": "40.66151000", "longitude": "-83.65944000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.26200000", "longitude": "-84.81995000"}, {"name": "Harrison County", "countryCode": "US", "stateCode": "OH", "latitude": "40.29384000", "longitude": "-81.09114000"}, {"name": "Hartville", "countryCode": "US", "stateCode": "OH", "latitude": "40.96367000", "longitude": "-81.33122000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.46477000", "longitude": "-83.70605000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.02284000", "longitude": "-82.44460000"}, {"name": "Hebron", "countryCode": "US", "stateCode": "OH", "latitude": "39.96173000", "longitude": "-82.49127000"}, {"name": "Henry County", "countryCode": "US", "stateCode": "OH", "latitude": "41.33389000", "longitude": "-84.06823000"}, {"name": "Hicksville", "countryCode": "US", "stateCode": "OH", "latitude": "41.29311000", "longitude": "-84.76190000"}, {"name": "Highland County", "countryCode": "US", "stateCode": "OH", "latitude": "39.18474000", "longitude": "-83.60097000"}, {"name": "Highland Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.55200000", "longitude": "-81.47845000"}, {"name": "Highpoint", "countryCode": "US", "stateCode": "OH", "latitude": "39.28839000", "longitude": "-84.35022000"}, {"name": "Hilliard", "countryCode": "US", "stateCode": "OH", "latitude": "40.03340000", "longitude": "-83.15825000"}, {"name": "Hillsboro", "countryCode": "US", "stateCode": "OH", "latitude": "39.20229000", "longitude": "-83.61159000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.31256000", "longitude": "-81.14371000"}, {"name": "Hocking County", "countryCode": "US", "stateCode": "OH", "latitude": "39.49702000", "longitude": "-82.47925000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.24894000", "longitude": "-84.13300000"}, {"name": "Holiday Valley", "countryCode": "US", "stateCode": "OH", "latitude": "39.85617000", "longitude": "-83.96854000"}, {"name": "Holland", "countryCode": "US", "stateCode": "OH", "latitude": "41.62172000", "longitude": "-83.71160000"}, {"name": "Holmes County", "countryCode": "US", "stateCode": "OH", "latitude": "40.56120000", "longitude": "-81.92936000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.51200000", "longitude": "-81.63652000"}, {"name": "Howland Center", "countryCode": "US", "stateCode": "OH", "latitude": "41.25117000", "longitude": "-80.74536000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.15645000", "longitude": "-80.56924000"}, {"name": "Huber Heights", "countryCode": "US", "stateCode": "OH", "latitude": "39.84395000", "longitude": "-84.12466000"}, {"name": "Huber Ridge", "countryCode": "US", "stateCode": "OH", "latitude": "40.08867000", "longitude": "-82.91657000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.24006000", "longitude": "-81.44067000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.49284000", "longitude": "-84.28966000"}, {"name": "Huron", "countryCode": "US", "stateCode": "OH", "latitude": "41.39505000", "longitude": "-82.55517000"}, {"name": "Huron County", "countryCode": "US", "stateCode": "OH", "latitude": "41.14615000", "longitude": "-82.59841000"}, {"name": "Independence", "countryCode": "US", "stateCode": "OH", "latitude": "41.36866000", "longitude": "-81.63790000"}, {"name": "Ironton", "countryCode": "US", "stateCode": "OH", "latitude": "38.53675000", "longitude": "-82.68294000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.05202000", "longitude": "-82.63655000"}, {"name": "Jackson Center", "countryCode": "US", "stateCode": "OH", "latitude": "40.43949000", "longitude": "-84.04022000"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "OH", "latitude": "39.01967000", "longitude": "-82.61838000"}, {"name": "Jamestown", "countryCode": "US", "stateCode": "OH", "latitude": "39.65812000", "longitude": "-83.73492000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.73867000", "longitude": "-80.76981000"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "OH", "latitude": "40.38502000", "longitude": "-80.76097000"}, {"name": "Jeffersonville", "countryCode": "US", "stateCode": "OH", "latitude": "39.65367000", "longitude": "-83.56381000"}, {"name": "Johnstown", "countryCode": "US", "stateCode": "OH", "latitude": "40.15367000", "longitude": "-82.68517000"}, {"name": "Kalida", "countryCode": "US", "stateCode": "OH", "latitude": "40.98283000", "longitude": "-84.19939000"}, {"name": "Kent", "countryCode": "US", "stateCode": "OH", "latitude": "41.15367000", "longitude": "-81.35789000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.64700000", "longitude": "-83.60965000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.21061000", "longitude": "-84.36716000"}, {"name": "Kettering", "countryCode": "US", "stateCode": "OH", "latitude": "39.68950000", "longitude": "-84.16883000"}, {"name": "Kings Mills", "countryCode": "US", "stateCode": "OH", "latitude": "39.35561000", "longitude": "-84.24855000"}, {"name": "Kingston", "countryCode": "US", "stateCode": "OH", "latitude": "39.47395000", "longitude": "-82.91073000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.62894000", "longitude": "-81.36150000"}, {"name": "Knox County", "countryCode": "US", "stateCode": "OH", "latitude": "40.39877000", "longitude": "-82.42153000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.64590000", "longitude": "-80.59785000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.23728000", "longitude": "-82.11987000"}, {"name": "Lake County", "countryCode": "US", "stateCode": "OH", "latitude": "41.71393000", "longitude": "-81.24527000"}, {"name": "Lake Darby", "countryCode": "US", "stateCode": "OH", "latitude": "39.95728000", "longitude": "-83.22880000"}, {"name": "Lake Lakengren", "countryCode": "US", "stateCode": "OH", "latitude": "39.68843000", "longitude": "-84.69347000"}, {"name": "Lake Mohawk", "countryCode": "US", "stateCode": "OH", "latitude": "40.66673000", "longitude": "-81.19927000"}, {"name": "Lakemore", "countryCode": "US", "stateCode": "OH", "latitude": "41.02089000", "longitude": "-81.43595000"}, {"name": "Lakeview", "countryCode": "US", "stateCode": "OH", "latitude": "40.48477000", "longitude": "-83.92300000"}, {"name": "Lakewood", "countryCode": "US", "stateCode": "OH", "latitude": "41.48199000", "longitude": "-81.79819000"}, {"name": "Lancaster", "countryCode": "US", "stateCode": "OH", "latitude": "39.71368000", "longitude": "-82.59933000"}, {"name": "Landen", "countryCode": "US", "stateCode": "OH", "latitude": "39.31200000", "longitude": "-84.28299000"}, {"name": "Lawrence County", "countryCode": "US", "stateCode": "OH", "latitude": "38.59847000", "longitude": "-82.53675000"}, {"name": "Leavittsburg", "countryCode": "US", "stateCode": "OH", "latitude": "41.24783000", "longitude": "-80.87703000"}, {"name": "Lebanon", "countryCode": "US", "stateCode": "OH", "latitude": "39.43534000", "longitude": "-84.20299000"}, {"name": "Leesburg", "countryCode": "US", "stateCode": "OH", "latitude": "39.34506000", "longitude": "-83.55297000"}, {"name": "Leetonia", "countryCode": "US", "stateCode": "OH", "latitude": "40.87728000", "longitude": "-80.75536000"}, {"name": "Leipsic", "countryCode": "US", "stateCode": "OH", "latitude": "41.09838000", "longitude": "-83.98467000"}, {"name": "Lewis Center", "countryCode": "US", "stateCode": "OH", "latitude": "40.19840000", "longitude": "-83.01018000"}, {"name": "Lewisburg", "countryCode": "US", "stateCode": "OH", "latitude": "39.84616000", "longitude": "-84.53967000"}, {"name": "Lexington", "countryCode": "US", "stateCode": "OH", "latitude": "40.67867000", "longitude": "-82.58239000"}, {"name": "Liberty Center", "countryCode": "US", "stateCode": "OH", "latitude": "41.44338000", "longitude": "-84.00883000"}, {"name": "Licking County", "countryCode": "US", "stateCode": "OH", "latitude": "40.09161000", "longitude": "-82.48315000"}, {"name": "Lima", "countryCode": "US", "stateCode": "OH", "latitude": "40.74255000", "longitude": "-84.10523000"}, {"name": "Lincoln Heights", "countryCode": "US", "stateCode": "OH", "latitude": "39.23895000", "longitude": "-84.45550000"}, {"name": "Lincoln Village", "countryCode": "US", "stateCode": "OH", "latitude": "39.95479000", "longitude": "-83.13074000"}, {"name": "Lisbon", "countryCode": "US", "stateCode": "OH", "latitude": "39.86089000", "longitude": "-83.63520000"}, {"name": "Lithopolis", "countryCode": "US", "stateCode": "OH", "latitude": "39.80284000", "longitude": "-82.80628000"}, {"name": "Lockland", "countryCode": "US", "stateCode": "OH", "latitude": "39.22922000", "longitude": "-84.45772000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.03339000", "longitude": "-82.01209000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.54007000", "longitude": "-82.40710000"}, {"name": "Logan County", "countryCode": "US", "stateCode": "OH", "latitude": "40.38845000", "longitude": "-83.76587000"}, {"name": "Logan Elm Village", "countryCode": "US", "stateCode": "OH", "latitude": "39.56978000", "longitude": "-82.95185000"}, {"name": "London", "countryCode": "US", "stateCode": "OH", "latitude": "39.88645000", "longitude": "-83.44825000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.45282000", "longitude": "-82.18237000"}, {"name": "Lorain County", "countryCode": "US", "stateCode": "OH", "latitude": "41.45252000", "longitude": "-82.15147000"}, {"name": "Lordstown", "countryCode": "US", "stateCode": "OH", "latitude": "41.16561000", "longitude": "-80.85758000"}, {"name": "Loudonville", "countryCode": "US", "stateCode": "OH", "latitude": "40.63534000", "longitude": "-82.23321000"}, {"name": "Louisville", "countryCode": "US", "stateCode": "OH", "latitude": "40.83728000", "longitude": "-81.25955000"}, {"name": "Loveland", "countryCode": "US", "stateCode": "OH", "latitude": "39.26895000", "longitude": "-84.26383000"}, {"name": "Loveland Park", "countryCode": "US", "stateCode": "OH", "latitude": "39.29978000", "longitude": "-84.26327000"}, {"name": "Lowellville", "countryCode": "US", "stateCode": "OH", "latitude": "41.03534000", "longitude": "-80.53646000"}, {"name": "Lucas County", "countryCode": "US", "stateCode": "OH", "latitude": "41.68419000", "longitude": "-83.46826000"}, {"name": "Lucasville", "countryCode": "US", "stateCode": "OH", "latitude": "38.87952000", "longitude": "-82.99684000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.45061000", "longitude": "-83.48743000"}, {"name": "Lynchburg", "countryCode": "US", "stateCode": "OH", "latitude": "39.24173000", "longitude": "-83.79131000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.52005000", "longitude": "-81.48873000"}, {"name": "Macedonia", "countryCode": "US", "stateCode": "OH", "latitude": "41.31367000", "longitude": "-81.50845000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.15811000", "longitude": "-84.64967000"}, {"name": "Madeira", "countryCode": "US", "stateCode": "OH", "latitude": "39.19089000", "longitude": "-84.36355000"}, {"name": "Madison", "countryCode": "US", "stateCode": "OH", "latitude": "41.77116000", "longitude": "-81.04982000"}, {"name": "Madison County", "countryCode": "US", "stateCode": "OH", "latitude": "39.89403000", "longitude": "-83.40020000"}, {"name": "Mahoning County", "countryCode": "US", "stateCode": "OH", "latitude": "41.01464000", "longitude": "-80.77629000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.69173000", "longitude": "-81.18121000"}, {"name": "Manchester", "countryCode": "US", "stateCode": "OH", "latitude": "38.68813000", "longitude": "-83.60936000"}, {"name": "Mansfield", "countryCode": "US", "stateCode": "OH", "latitude": "40.75839000", "longitude": "-82.51545000"}, {"name": "Mantu<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.28394000", "longitude": "-81.22399000"}, {"name": "Maple Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.41533000", "longitude": "-81.56596000"}, {"name": "Mariemont", "countryCode": "US", "stateCode": "OH", "latitude": "39.14506000", "longitude": "-84.37438000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.41535000", "longitude": "-81.45484000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.58867000", "longitude": "-83.12852000"}, {"name": "Marion County", "countryCode": "US", "stateCode": "OH", "latitude": "40.58719000", "longitude": "-83.16087000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.09591000", "longitude": "-80.72453000"}, {"name": "Marysville", "countryCode": "US", "stateCode": "OH", "latitude": "40.23645000", "longitude": "-83.36714000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.36006000", "longitude": "-84.30994000"}, {"name": "Massillon", "countryCode": "US", "stateCode": "OH", "latitude": "40.79672000", "longitude": "-81.52151000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.21117000", "longitude": "-80.53785000"}, {"name": "Ma<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.56283000", "longitude": "-83.65382000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.55200000", "longitude": "-81.43928000"}, {"name": "Mayfield Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.51922000", "longitude": "-81.45790000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.24646000", "longitude": "-82.47849000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.10755000", "longitude": "-83.79271000"}, {"name": "McConnelsville", "countryCode": "US", "stateCode": "OH", "latitude": "39.64868000", "longitude": "-81.85319000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.16367000", "longitude": "-80.72424000"}, {"name": "McKinley Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.18367000", "longitude": "-80.71730000"}, {"name": "Mechanicsburg", "countryCode": "US", "stateCode": "OH", "latitude": "40.07200000", "longitude": "-83.55631000"}, {"name": "Medina", "countryCode": "US", "stateCode": "OH", "latitude": "41.13839000", "longitude": "-81.86375000"}, {"name": "Medina County", "countryCode": "US", "stateCode": "OH", "latitude": "41.11759000", "longitude": "-81.89971000"}, {"name": "Meigs County", "countryCode": "US", "stateCode": "OH", "latitude": "39.08224000", "longitude": "-82.02290000"}, {"name": "Mentor", "countryCode": "US", "stateCode": "OH", "latitude": "41.66616000", "longitude": "-81.33955000"}, {"name": "Mentor-on-the-Lake", "countryCode": "US", "stateCode": "OH", "latitude": "41.70504000", "longitude": "-81.36039000"}, {"name": "Mercer County", "countryCode": "US", "stateCode": "OH", "latitude": "40.54001000", "longitude": "-84.62936000"}, {"name": "Miami County", "countryCode": "US", "stateCode": "OH", "latitude": "40.05345000", "longitude": "-84.22885000"}, {"name": "Miami Heights", "countryCode": "US", "stateCode": "OH", "latitude": "39.16506000", "longitude": "-84.72050000"}, {"name": "Miamisburg", "countryCode": "US", "stateCode": "OH", "latitude": "39.64284000", "longitude": "-84.28661000"}, {"name": "Miamitown", "countryCode": "US", "stateCode": "OH", "latitude": "39.21589000", "longitude": "-84.70411000"}, {"name": "Middleburg Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.36144000", "longitude": "-81.81291000"}, {"name": "Middlefield", "countryCode": "US", "stateCode": "OH", "latitude": "41.46200000", "longitude": "-81.07371000"}, {"name": "Middleport", "countryCode": "US", "stateCode": "OH", "latitude": "39.00175000", "longitude": "-82.04875000"}, {"name": "Middletown", "countryCode": "US", "stateCode": "OH", "latitude": "39.51506000", "longitude": "-84.39828000"}, {"name": "Milan", "countryCode": "US", "stateCode": "OH", "latitude": "41.29755000", "longitude": "-82.60545000"}, {"name": "Milford", "countryCode": "US", "stateCode": "OH", "latitude": "39.17534000", "longitude": "-84.29438000"}, {"name": "Millbury", "countryCode": "US", "stateCode": "OH", "latitude": "41.56616000", "longitude": "-83.42465000"}, {"name": "Millersburg", "countryCode": "US", "stateCode": "OH", "latitude": "40.55451000", "longitude": "-81.91792000"}, {"name": "Millersport", "countryCode": "US", "stateCode": "OH", "latitude": "39.90006000", "longitude": "-82.53405000"}, {"name": "Mineral Ridge", "countryCode": "US", "stateCode": "OH", "latitude": "41.14006000", "longitude": "-80.76897000"}, {"name": "Minerva", "countryCode": "US", "stateCode": "OH", "latitude": "40.72978000", "longitude": "-81.10538000"}, {"name": "Minerva Park", "countryCode": "US", "stateCode": "OH", "latitude": "40.07645000", "longitude": "-82.94379000"}, {"name": "Mingo Junction", "countryCode": "US", "stateCode": "OH", "latitude": "40.32174000", "longitude": "-80.60980000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.39310000", "longitude": "-84.37606000"}, {"name": "Mogadore", "countryCode": "US", "stateCode": "OH", "latitude": "41.04645000", "longitude": "-81.39789000"}, {"name": "Monfort Heights", "countryCode": "US", "stateCode": "OH", "latitude": "39.18839000", "longitude": "-84.59522000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.44034000", "longitude": "-84.36216000"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "OH", "latitude": "39.72735000", "longitude": "-81.08292000"}, {"name": "Monroeville", "countryCode": "US", "stateCode": "OH", "latitude": "41.24422000", "longitude": "-82.69629000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.22811000", "longitude": "-84.35411000"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "OH", "latitude": "39.75459000", "longitude": "-84.29068000"}, {"name": "Montpelier", "countryCode": "US", "stateCode": "OH", "latitude": "41.58450000", "longitude": "-84.60551000"}, {"name": "Montrose-Ghent", "countryCode": "US", "stateCode": "OH", "latitude": "41.15380000", "longitude": "-81.64378000"}, {"name": "Moraine", "countryCode": "US", "stateCode": "OH", "latitude": "39.70617000", "longitude": "-84.21938000"}, {"name": "Moreland Hills", "countryCode": "US", "stateCode": "OH", "latitude": "41.44783000", "longitude": "-81.42762000"}, {"name": "Morgan County", "countryCode": "US", "stateCode": "OH", "latitude": "39.62037000", "longitude": "-81.85266000"}, {"name": "Morgandale", "countryCode": "US", "stateCode": "OH", "latitude": "41.26561000", "longitude": "-80.78286000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.35450000", "longitude": "-84.12716000"}, {"name": "Morrow County", "countryCode": "US", "stateCode": "OH", "latitude": "40.52409000", "longitude": "-82.79407000"}, {"name": "Mount Carmel", "countryCode": "US", "stateCode": "OH", "latitude": "39.10589000", "longitude": "-84.30410000"}, {"name": "Mount Gilead", "countryCode": "US", "stateCode": "OH", "latitude": "40.54923000", "longitude": "-82.82740000"}, {"name": "Mount Healthy", "countryCode": "US", "stateCode": "OH", "latitude": "39.23367000", "longitude": "-84.54578000"}, {"name": "Mount Healthy Heights", "countryCode": "US", "stateCode": "OH", "latitude": "39.27033000", "longitude": "-84.56800000"}, {"name": "Mount Orab", "countryCode": "US", "stateCode": "OH", "latitude": "39.02757000", "longitude": "-83.91965000"}, {"name": "Mount Repose", "countryCode": "US", "stateCode": "OH", "latitude": "39.20062000", "longitude": "-84.22438000"}, {"name": "Mount Sterling", "countryCode": "US", "stateCode": "OH", "latitude": "39.71951000", "longitude": "-83.26519000"}, {"name": "Mount Vernon", "countryCode": "US", "stateCode": "OH", "latitude": "40.39340000", "longitude": "-82.48572000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.19339000", "longitude": "-84.24216000"}, {"name": "Munroe Falls", "countryCode": "US", "stateCode": "OH", "latitude": "41.14450000", "longitude": "-81.43983000"}, {"name": "Muskingum County", "countryCode": "US", "stateCode": "OH", "latitude": "39.96542000", "longitude": "-81.94438000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.39227000", "longitude": "-84.12522000"}, {"name": "Navarre", "countryCode": "US", "stateCode": "OH", "latitude": "40.72450000", "longitude": "-81.52207000"}, {"name": "Nelsonville", "countryCode": "US", "stateCode": "OH", "latitude": "39.45868000", "longitude": "-82.23182000"}, {"name": "New Albany", "countryCode": "US", "stateCode": "OH", "latitude": "40.08117000", "longitude": "-82.80879000"}, {"name": "New Boston", "countryCode": "US", "stateCode": "OH", "latitude": "38.75230000", "longitude": "-82.93684000"}, {"name": "New Bremen", "countryCode": "US", "stateCode": "OH", "latitude": "40.43699000", "longitude": "-84.37967000"}, {"name": "New Burlington", "countryCode": "US", "stateCode": "OH", "latitude": "39.25950000", "longitude": "-84.55717000"}, {"name": "New California", "countryCode": "US", "stateCode": "OH", "latitude": "40.15617000", "longitude": "-83.23658000"}, {"name": "New Carlisle", "countryCode": "US", "stateCode": "OH", "latitude": "39.93617000", "longitude": "-84.02549000"}, {"name": "New Concord", "countryCode": "US", "stateCode": "OH", "latitude": "39.99368000", "longitude": "-81.73402000"}, {"name": "New Franklin", "countryCode": "US", "stateCode": "OH", "latitude": "40.94172000", "longitude": "-81.54151000"}, {"name": "New Lebanon", "countryCode": "US", "stateCode": "OH", "latitude": "39.74533000", "longitude": "-84.38495000"}, {"name": "New Lexington", "countryCode": "US", "stateCode": "OH", "latitude": "39.71396000", "longitude": "-82.20848000"}, {"name": "New London", "countryCode": "US", "stateCode": "OH", "latitude": "41.08505000", "longitude": "-82.39989000"}, {"name": "New Matamoras", "countryCode": "US", "stateCode": "OH", "latitude": "39.52452000", "longitude": "-81.06705000"}, {"name": "New Miami", "countryCode": "US", "stateCode": "OH", "latitude": "39.43478000", "longitude": "-84.53689000"}, {"name": "New Middletown", "countryCode": "US", "stateCode": "OH", "latitude": "40.96117000", "longitude": "-80.55757000"}, {"name": "New Paris", "countryCode": "US", "stateCode": "OH", "latitude": "39.85699000", "longitude": "-84.79329000"}, {"name": "New Philadelphia", "countryCode": "US", "stateCode": "OH", "latitude": "40.48979000", "longitude": "-81.44567000"}, {"name": "New Richmond", "countryCode": "US", "stateCode": "OH", "latitude": "38.94868000", "longitude": "-84.27994000"}, {"name": "New Vienna", "countryCode": "US", "stateCode": "OH", "latitude": "39.32367000", "longitude": "-83.69103000"}, {"name": "New Waterford", "countryCode": "US", "stateCode": "OH", "latitude": "40.84506000", "longitude": "-80.61452000"}, {"name": "Newark", "countryCode": "US", "stateCode": "OH", "latitude": "40.05812000", "longitude": "-82.40126000"}, {"name": "Newburgh Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.45005000", "longitude": "-81.66346000"}, {"name": "Newcomerstown", "countryCode": "US", "stateCode": "OH", "latitude": "40.27229000", "longitude": "-81.60595000"}, {"name": "Newport", "countryCode": "US", "stateCode": "OH", "latitude": "39.39091000", "longitude": "-81.22678000"}, {"name": "Newton Falls", "countryCode": "US", "stateCode": "OH", "latitude": "41.18839000", "longitude": "-80.97815000"}, {"name": "Newtown", "countryCode": "US", "stateCode": "OH", "latitude": "39.12450000", "longitude": "-84.36161000"}, {"name": "Niles", "countryCode": "US", "stateCode": "OH", "latitude": "41.18284000", "longitude": "-80.76536000"}, {"name": "Noble County", "countryCode": "US", "stateCode": "OH", "latitude": "39.76596000", "longitude": "-81.45556000"}, {"name": "North Baltimore", "countryCode": "US", "stateCode": "OH", "latitude": "41.18283000", "longitude": "-83.67827000"}, {"name": "North Canton", "countryCode": "US", "stateCode": "OH", "latitude": "40.87589000", "longitude": "-81.40234000"}, {"name": "North College Hill", "countryCode": "US", "stateCode": "OH", "latitude": "39.21839000", "longitude": "-84.55078000"}, {"name": "North Fork Village", "countryCode": "US", "stateCode": "OH", "latitude": "39.33590000", "longitude": "-83.02907000"}, {"name": "North Kingsville", "countryCode": "US", "stateCode": "OH", "latitude": "41.90589000", "longitude": "-80.69036000"}, {"name": "North Lewisburg", "countryCode": "US", "stateCode": "OH", "latitude": "40.22311000", "longitude": "-83.55743000"}, {"name": "North Madison", "countryCode": "US", "stateCode": "OH", "latitude": "41.80200000", "longitude": "-81.04899000"}, {"name": "North Olmsted", "countryCode": "US", "stateCode": "OH", "latitude": "41.41560000", "longitude": "-81.92347000"}, {"name": "North Randall", "countryCode": "US", "stateCode": "OH", "latitude": "41.43478000", "longitude": "-81.52568000"}, {"name": "North Ridgeville", "countryCode": "US", "stateCode": "OH", "latitude": "41.38949000", "longitude": "-82.01903000"}, {"name": "North Royalton", "countryCode": "US", "stateCode": "OH", "latitude": "41.31366000", "longitude": "-81.72457000"}, {"name": "North Zanesville", "countryCode": "US", "stateCode": "OH", "latitude": "39.97868000", "longitude": "-82.00347000"}, {"name": "Northbrook", "countryCode": "US", "stateCode": "OH", "latitude": "39.24645000", "longitude": "-84.58356000"}, {"name": "Northfield", "countryCode": "US", "stateCode": "OH", "latitude": "41.34505000", "longitude": "-81.52845000"}, {"name": "Northgate", "countryCode": "US", "stateCode": "OH", "latitude": "39.25283000", "longitude": "-84.59245000"}, {"name": "Northridge", "countryCode": "US", "stateCode": "OH", "latitude": "39.80756000", "longitude": "-84.19689000"}, {"name": "Northwood", "countryCode": "US", "stateCode": "OH", "latitude": "40.47283000", "longitude": "-83.73243000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.02922000", "longitude": "-81.63818000"}, {"name": "Norwalk", "countryCode": "US", "stateCode": "OH", "latitude": "41.24255000", "longitude": "-82.61573000"}, {"name": "Norwood", "countryCode": "US", "stateCode": "OH", "latitude": "39.15561000", "longitude": "-84.45966000"}, {"name": "Oak Harbor", "countryCode": "US", "stateCode": "OH", "latitude": "41.50672000", "longitude": "-83.14659000"}, {"name": "Oak Hill", "countryCode": "US", "stateCode": "OH", "latitude": "38.89396000", "longitude": "-82.57349000"}, {"name": "Oakwood", "countryCode": "US", "stateCode": "OH", "latitude": "39.72534000", "longitude": "-84.17411000"}, {"name": "Oberlin", "countryCode": "US", "stateCode": "OH", "latitude": "41.29394000", "longitude": "-82.21738000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.87895000", "longitude": "-82.95074000"}, {"name": "Olmsted Falls", "countryCode": "US", "stateCode": "OH", "latitude": "41.37505000", "longitude": "-81.90819000"}, {"name": "Ontario", "countryCode": "US", "stateCode": "OH", "latitude": "40.75950000", "longitude": "-82.59017000"}, {"name": "Orange", "countryCode": "US", "stateCode": "OH", "latitude": "41.44978000", "longitude": "-81.48067000"}, {"name": "Oregon", "countryCode": "US", "stateCode": "OH", "latitude": "41.64366000", "longitude": "-83.48688000"}, {"name": "Orrville", "countryCode": "US", "stateCode": "OH", "latitude": "40.84367000", "longitude": "-81.76402000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.53506000", "longitude": "-80.86814000"}, {"name": "Ottawa", "countryCode": "US", "stateCode": "OH", "latitude": "41.01922000", "longitude": "-84.04717000"}, {"name": "Ottawa County", "countryCode": "US", "stateCode": "OH", "latitude": "41.49675000", "longitude": "-82.94128000"}, {"name": "Ottawa Hills", "countryCode": "US", "stateCode": "OH", "latitude": "41.66422000", "longitude": "-83.64327000"}, {"name": "Oxford", "countryCode": "US", "stateCode": "OH", "latitude": "39.50700000", "longitude": "-84.74523000"}, {"name": "Painesville", "countryCode": "US", "stateCode": "OH", "latitude": "41.72449000", "longitude": "-81.24566000"}, {"name": "Pandora", "countryCode": "US", "stateCode": "OH", "latitude": "40.94811000", "longitude": "-83.96105000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.88645000", "longitude": "-84.03966000"}, {"name": "Parma", "countryCode": "US", "stateCode": "OH", "latitude": "41.40477000", "longitude": "-81.72291000"}, {"name": "Parma Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.39005000", "longitude": "-81.75958000"}, {"name": "Pataskala", "countryCode": "US", "stateCode": "OH", "latitude": "39.99562000", "longitude": "-82.67433000"}, {"name": "Paulding", "countryCode": "US", "stateCode": "OH", "latitude": "41.13811000", "longitude": "-84.58051000"}, {"name": "Paulding County", "countryCode": "US", "stateCode": "OH", "latitude": "41.11662000", "longitude": "-84.58020000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.07755000", "longitude": "-84.72718000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "38.94896000", "longitude": "-83.40575000"}, {"name": "Pemberville", "countryCode": "US", "stateCode": "OH", "latitude": "41.41089000", "longitude": "-83.46104000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.47839000", "longitude": "-81.46373000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.76033000", "longitude": "-81.14093000"}, {"name": "Perry County", "countryCode": "US", "stateCode": "OH", "latitude": "39.73715000", "longitude": "-82.23614000"}, {"name": "Perry Heights", "countryCode": "US", "stateCode": "OH", "latitude": "40.79534000", "longitude": "-81.47345000"}, {"name": "Perrysburg", "countryCode": "US", "stateCode": "OH", "latitude": "41.55700000", "longitude": "-83.62716000"}, {"name": "Pickaway County", "countryCode": "US", "stateCode": "OH", "latitude": "39.64194000", "longitude": "-83.02439000"}, {"name": "<PERSON><PERSON>ton", "countryCode": "US", "stateCode": "OH", "latitude": "39.88423000", "longitude": "-82.75350000"}, {"name": "Pike County", "countryCode": "US", "stateCode": "OH", "latitude": "39.07737000", "longitude": "-83.06685000"}, {"name": "Piketon", "countryCode": "US", "stateCode": "OH", "latitude": "39.06813000", "longitude": "-83.01434000"}, {"name": "Pioneer", "countryCode": "US", "stateCode": "OH", "latitude": "41.68005000", "longitude": "-84.55301000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.14477000", "longitude": "-84.24244000"}, {"name": "Plain City", "countryCode": "US", "stateCode": "OH", "latitude": "40.10756000", "longitude": "-83.26742000"}, {"name": "Pleasant Grove", "countryCode": "US", "stateCode": "OH", "latitude": "39.95201000", "longitude": "-81.95902000"}, {"name": "Pleasant Hill", "countryCode": "US", "stateCode": "OH", "latitude": "40.05172000", "longitude": "-84.34439000"}, {"name": "Pleasant Run", "countryCode": "US", "stateCode": "OH", "latitude": "39.29978000", "longitude": "-84.56356000"}, {"name": "Pleasant Run Farm", "countryCode": "US", "stateCode": "OH", "latitude": "39.30311000", "longitude": "-84.54800000"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "OH", "latitude": "40.99561000", "longitude": "-82.66712000"}, {"name": "Poland", "countryCode": "US", "stateCode": "OH", "latitude": "41.02423000", "longitude": "-80.61480000"}, {"name": "Pomeroy", "countryCode": "US", "stateCode": "OH", "latitude": "39.02758000", "longitude": "-82.03375000"}, {"name": "Port Clinton", "countryCode": "US", "stateCode": "OH", "latitude": "41.51200000", "longitude": "-82.93769000"}, {"name": "Portage County", "countryCode": "US", "stateCode": "OH", "latitude": "41.16768000", "longitude": "-81.19740000"}, {"name": "Portage Lakes", "countryCode": "US", "stateCode": "OH", "latitude": "41.00728000", "longitude": "-81.52706000"}, {"name": "Portsmouth", "countryCode": "US", "stateCode": "OH", "latitude": "38.73174000", "longitude": "-82.99767000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.15784000", "longitude": "-83.07519000"}, {"name": "Powhatan Point", "countryCode": "US", "stateCode": "OH", "latitude": "39.86008000", "longitude": "-80.81537000"}, {"name": "Preble County", "countryCode": "US", "stateCode": "OH", "latitude": "39.74157000", "longitude": "-84.64802000"}, {"name": "Prospect", "countryCode": "US", "stateCode": "OH", "latitude": "40.45034000", "longitude": "-83.18853000"}, {"name": "Putnam County", "countryCode": "US", "stateCode": "OH", "latitude": "41.02208000", "longitude": "-84.13173000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.15756000", "longitude": "-81.24205000"}, {"name": "Reading", "countryCode": "US", "stateCode": "OH", "latitude": "39.22367000", "longitude": "-84.44216000"}, {"name": "Reminderville", "countryCode": "US", "stateCode": "OH", "latitude": "41.34589000", "longitude": "-81.39511000"}, {"name": "Reno", "countryCode": "US", "stateCode": "OH", "latitude": "39.37285000", "longitude": "-81.39567000"}, {"name": "Reynoldsburg", "countryCode": "US", "stateCode": "OH", "latitude": "39.95479000", "longitude": "-82.81212000"}, {"name": "Richfield", "countryCode": "US", "stateCode": "OH", "latitude": "41.23978000", "longitude": "-81.63818000"}, {"name": "Richland County", "countryCode": "US", "stateCode": "OH", "latitude": "40.77468000", "longitude": "-82.53648000"}, {"name": "Richmond Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.55283000", "longitude": "-81.51012000"}, {"name": "Richville", "countryCode": "US", "stateCode": "OH", "latitude": "40.75117000", "longitude": "-81.47790000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.42645000", "longitude": "-83.29686000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "38.74563000", "longitude": "-83.84492000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.97811000", "longitude": "-81.78208000"}, {"name": "Riverside", "countryCode": "US", "stateCode": "OH", "latitude": "39.77978000", "longitude": "-84.12410000"}, {"name": "Roaming Shores", "countryCode": "US", "stateCode": "OH", "latitude": "41.64311000", "longitude": "-80.82342000"}, {"name": "Rockford", "countryCode": "US", "stateCode": "OH", "latitude": "40.68783000", "longitude": "-84.64663000"}, {"name": "Rocky River", "countryCode": "US", "stateCode": "OH", "latitude": "41.47560000", "longitude": "-81.83930000"}, {"name": "Rosemount", "countryCode": "US", "stateCode": "OH", "latitude": "38.78619000", "longitude": "-82.97906000"}, {"name": "Roseville", "countryCode": "US", "stateCode": "OH", "latitude": "39.80729000", "longitude": "-82.07125000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.31228000", "longitude": "-84.65050000"}, {"name": "Ross County", "countryCode": "US", "stateCode": "OH", "latitude": "39.33763000", "longitude": "-83.05703000"}, {"name": "Rossford", "countryCode": "US", "stateCode": "OH", "latitude": "41.60977000", "longitude": "-83.56438000"}, {"name": "Rossmoyne", "countryCode": "US", "stateCode": "OH", "latitude": "39.21367000", "longitude": "-84.38688000"}, {"name": "Russells Point", "countryCode": "US", "stateCode": "OH", "latitude": "40.47116000", "longitude": "-83.89272000"}, {"name": "Sabina", "countryCode": "US", "stateCode": "OH", "latitude": "39.48867000", "longitude": "-83.63687000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.16700000", "longitude": "-84.49855000"}, {"name": "Saint <PERSON>sville", "countryCode": "US", "stateCode": "OH", "latitude": "40.08063000", "longitude": "-80.90009000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.41755000", "longitude": "-84.63968000"}, {"name": "Saint Marys", "countryCode": "US", "stateCode": "OH", "latitude": "40.54227000", "longitude": "-84.38940000"}, {"name": "Saint Paris", "countryCode": "US", "stateCode": "OH", "latitude": "40.12839000", "longitude": "-83.95966000"}, {"name": "Salem", "countryCode": "US", "stateCode": "OH", "latitude": "40.90089000", "longitude": "-80.85675000"}, {"name": "Salem Heights", "countryCode": "US", "stateCode": "OH", "latitude": "39.07173000", "longitude": "-84.37827000"}, {"name": "Salineville", "countryCode": "US", "stateCode": "OH", "latitude": "40.62256000", "longitude": "-80.83786000"}, {"name": "Sandusky", "countryCode": "US", "stateCode": "OH", "latitude": "41.44894000", "longitude": "-82.70796000"}, {"name": "Sandusky County", "countryCode": "US", "stateCode": "OH", "latitude": "41.35742000", "longitude": "-83.14391000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.03783000", "longitude": "-81.44095000"}, {"name": "Scioto County", "countryCode": "US", "stateCode": "OH", "latitude": "38.80396000", "longitude": "-82.99283000"}, {"name": "Sciot<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "38.75480000", "longitude": "-82.86878000"}, {"name": "Sebring", "countryCode": "US", "stateCode": "OH", "latitude": "40.92284000", "longitude": "-81.01898000"}, {"name": "Seneca County", "countryCode": "US", "stateCode": "OH", "latitude": "41.12388000", "longitude": "-83.12771000"}, {"name": "Seven Hills", "countryCode": "US", "stateCode": "OH", "latitude": "41.39533000", "longitude": "-81.67624000"}, {"name": "Seville", "countryCode": "US", "stateCode": "OH", "latitude": "41.01006000", "longitude": "-81.86236000"}, {"name": "Shadyside", "countryCode": "US", "stateCode": "OH", "latitude": "39.97091000", "longitude": "-80.75064000"}, {"name": "Shaker Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.47394000", "longitude": "-81.53707000"}, {"name": "Sharonville", "countryCode": "US", "stateCode": "OH", "latitude": "39.26811000", "longitude": "-84.41327000"}, {"name": "Shawnee Hills", "countryCode": "US", "stateCode": "OH", "latitude": "39.65284000", "longitude": "-83.78687000"}, {"name": "Sheffield", "countryCode": "US", "stateCode": "OH", "latitude": "41.42115000", "longitude": "-82.09626000"}, {"name": "Sheffield Lake", "countryCode": "US", "stateCode": "OH", "latitude": "41.48754000", "longitude": "-82.10154000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.88145000", "longitude": "-82.66184000"}, {"name": "Shelby County", "countryCode": "US", "stateCode": "OH", "latitude": "40.33153000", "longitude": "-84.20473000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.08478000", "longitude": "-84.36077000"}, {"name": "Shiloh", "countryCode": "US", "stateCode": "OH", "latitude": "39.81867000", "longitude": "-84.22855000"}, {"name": "<PERSON><PERSON>eve", "countryCode": "US", "stateCode": "OH", "latitude": "40.68145000", "longitude": "-82.02181000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.28422000", "longitude": "-84.15550000"}, {"name": "Silver Lake", "countryCode": "US", "stateCode": "OH", "latitude": "41.15895000", "longitude": "-81.45428000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.19284000", "longitude": "-84.40050000"}, {"name": "Sixteen Mile Stand", "countryCode": "US", "stateCode": "OH", "latitude": "39.27284000", "longitude": "-84.32744000"}, {"name": "Skyline Acres", "countryCode": "US", "stateCode": "OH", "latitude": "39.22867000", "longitude": "-84.56689000"}, {"name": "Smithville", "countryCode": "US", "stateCode": "OH", "latitude": "40.86228000", "longitude": "-81.86180000"}, {"name": "Solon", "countryCode": "US", "stateCode": "OH", "latitude": "41.38978000", "longitude": "-81.44123000"}, {"name": "Somerset", "countryCode": "US", "stateCode": "OH", "latitude": "39.80701000", "longitude": "-82.29709000"}, {"name": "South Amherst", "countryCode": "US", "stateCode": "OH", "latitude": "41.35588000", "longitude": "-82.25377000"}, {"name": "South Bloomfield", "countryCode": "US", "stateCode": "OH", "latitude": "39.71840000", "longitude": "-82.98685000"}, {"name": "South Canal", "countryCode": "US", "stateCode": "OH", "latitude": "41.17728000", "longitude": "-80.98676000"}, {"name": "South Charleston", "countryCode": "US", "stateCode": "OH", "latitude": "39.82534000", "longitude": "-83.63437000"}, {"name": "South Euclid", "countryCode": "US", "stateCode": "OH", "latitude": "41.52311000", "longitude": "-81.51846000"}, {"name": "South Lebanon", "countryCode": "US", "stateCode": "OH", "latitude": "39.37089000", "longitude": "-84.21327000"}, {"name": "South Point", "countryCode": "US", "stateCode": "OH", "latitude": "38.41786000", "longitude": "-82.58627000"}, {"name": "South Russell", "countryCode": "US", "stateCode": "OH", "latitude": "41.43144000", "longitude": "-81.36539000"}, {"name": "South Zanesville", "countryCode": "US", "stateCode": "OH", "latitude": "39.89923000", "longitude": "-82.00625000"}, {"name": "Spencerville", "countryCode": "US", "stateCode": "OH", "latitude": "40.70894000", "longitude": "-84.35356000"}, {"name": "Springboro", "countryCode": "US", "stateCode": "OH", "latitude": "39.55228000", "longitude": "-84.23327000"}, {"name": "Springdale", "countryCode": "US", "stateCode": "OH", "latitude": "39.28700000", "longitude": "-84.48522000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "OH", "latitude": "39.92423000", "longitude": "-83.80882000"}, {"name": "Stark County", "countryCode": "US", "stateCode": "OH", "latitude": "40.81389000", "longitude": "-81.36564000"}, {"name": "Steubenville", "countryCode": "US", "stateCode": "OH", "latitude": "40.36979000", "longitude": "-80.63396000"}, {"name": "Stony Prairie", "countryCode": "US", "stateCode": "OH", "latitude": "41.35144000", "longitude": "-83.15520000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.15950000", "longitude": "-81.44039000"}, {"name": "Strasburg", "countryCode": "US", "stateCode": "OH", "latitude": "40.59478000", "longitude": "-81.52679000"}, {"name": "Streetsboro", "countryCode": "US", "stateCode": "OH", "latitude": "41.23922000", "longitude": "-81.34594000"}, {"name": "Strongsville", "countryCode": "US", "stateCode": "OH", "latitude": "41.31450000", "longitude": "-81.83569000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.05256000", "longitude": "-80.60785000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.50366000", "longitude": "-84.41412000"}, {"name": "Sugarcreek", "countryCode": "US", "stateCode": "OH", "latitude": "40.50312000", "longitude": "-81.64096000"}, {"name": "Sugarcreek Police Dept", "countryCode": "US", "stateCode": "OH", "latitude": "40.50253000", "longitude": "-81.64176000"}, {"name": "Summerside", "countryCode": "US", "stateCode": "OH", "latitude": "39.10478000", "longitude": "-84.28827000"}, {"name": "Summit County", "countryCode": "US", "stateCode": "OH", "latitude": "41.12598000", "longitude": "-81.53217000"}, {"name": "Sunbury", "countryCode": "US", "stateCode": "OH", "latitude": "40.24256000", "longitude": "-82.85907000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.58866000", "longitude": "-83.89105000"}, {"name": "Sylvania", "countryCode": "US", "stateCode": "OH", "latitude": "41.71894000", "longitude": "-83.71299000"}, {"name": "Tallmadge", "countryCode": "US", "stateCode": "OH", "latitude": "41.10145000", "longitude": "-81.44178000"}, {"name": "Terrace Park", "countryCode": "US", "stateCode": "OH", "latitude": "39.15923000", "longitude": "-84.30716000"}, {"name": "The Plains", "countryCode": "US", "stateCode": "OH", "latitude": "39.36896000", "longitude": "-82.13237000"}, {"name": "The Village of Indian Hill", "countryCode": "US", "stateCode": "OH", "latitude": "39.17949000", "longitude": "-84.33517000"}, {"name": "Thornport", "countryCode": "US", "stateCode": "OH", "latitude": "39.91312000", "longitude": "-82.41099000"}, {"name": "Tiffin", "countryCode": "US", "stateCode": "OH", "latitude": "41.11450000", "longitude": "-83.17797000"}, {"name": "Tiltonsville", "countryCode": "US", "stateCode": "OH", "latitude": "40.16674000", "longitude": "-80.69980000"}, {"name": "Tipp City", "countryCode": "US", "stateCode": "OH", "latitude": "39.95839000", "longitude": "-84.17216000"}, {"name": "Toledo", "countryCode": "US", "stateCode": "OH", "latitude": "41.66394000", "longitude": "-83.55521000"}, {"name": "Toronto", "countryCode": "US", "stateCode": "OH", "latitude": "40.46423000", "longitude": "-80.60091000"}, {"name": "Trenton", "countryCode": "US", "stateCode": "OH", "latitude": "39.48089000", "longitude": "-84.45772000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.79728000", "longitude": "-84.31133000"}, {"name": "Troy", "countryCode": "US", "stateCode": "OH", "latitude": "40.03950000", "longitude": "-84.20328000"}, {"name": "Trumbull County", "countryCode": "US", "stateCode": "OH", "latitude": "41.31717000", "longitude": "-80.76116000"}, {"name": "Turpin Hills", "countryCode": "US", "stateCode": "OH", "latitude": "39.11006000", "longitude": "-84.37994000"}, {"name": "Tuscarawas", "countryCode": "US", "stateCode": "OH", "latitude": "40.39479000", "longitude": "-81.40706000"}, {"name": "Tuscarawas County", "countryCode": "US", "stateCode": "OH", "latitude": "40.44096000", "longitude": "-81.47377000"}, {"name": "Twinsburg", "countryCode": "US", "stateCode": "OH", "latitude": "41.31256000", "longitude": "-81.44011000"}, {"name": "Uhrichsville", "countryCode": "US", "stateCode": "OH", "latitude": "40.39312000", "longitude": "-81.34650000"}, {"name": "Union", "countryCode": "US", "stateCode": "OH", "latitude": "39.89783000", "longitude": "-84.30633000"}, {"name": "Union City", "countryCode": "US", "stateCode": "OH", "latitude": "40.19938000", "longitude": "-84.80353000"}, {"name": "Union County", "countryCode": "US", "stateCode": "OH", "latitude": "40.29940000", "longitude": "-83.37158000"}, {"name": "Uniontown", "countryCode": "US", "stateCode": "OH", "latitude": "40.97506000", "longitude": "-81.40817000"}, {"name": "University Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.49783000", "longitude": "-81.53735000"}, {"name": "Upper Arlington", "countryCode": "US", "stateCode": "OH", "latitude": "39.99451000", "longitude": "-83.06241000"}, {"name": "Upper Sandusky", "countryCode": "US", "stateCode": "OH", "latitude": "40.82728000", "longitude": "-83.28131000"}, {"name": "Urbana", "countryCode": "US", "stateCode": "OH", "latitude": "40.10839000", "longitude": "-83.75243000"}, {"name": "Urbancrest", "countryCode": "US", "stateCode": "OH", "latitude": "39.89756000", "longitude": "-83.08685000"}, {"name": "Utica", "countryCode": "US", "stateCode": "OH", "latitude": "40.23423000", "longitude": "-82.45127000"}, {"name": "Valley View", "countryCode": "US", "stateCode": "OH", "latitude": "41.38783000", "longitude": "-81.60457000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.86949000", "longitude": "-84.58412000"}, {"name": "Van Wert County", "countryCode": "US", "stateCode": "OH", "latitude": "40.85540000", "longitude": "-84.58610000"}, {"name": "Vandalia", "countryCode": "US", "stateCode": "OH", "latitude": "39.89061000", "longitude": "-84.19883000"}, {"name": "Vermilion", "countryCode": "US", "stateCode": "OH", "latitude": "41.42199000", "longitude": "-82.36461000"}, {"name": "Vermilion-on-the-Lake", "countryCode": "US", "stateCode": "OH", "latitude": "41.42838000", "longitude": "-82.32377000"}, {"name": "Versailles", "countryCode": "US", "stateCode": "OH", "latitude": "40.22255000", "longitude": "-84.48440000"}, {"name": "Vinton County", "countryCode": "US", "stateCode": "OH", "latitude": "39.25099000", "longitude": "-82.48535000"}, {"name": "Wadsworth", "countryCode": "US", "stateCode": "OH", "latitude": "41.02561000", "longitude": "-81.72985000"}, {"name": "Wakeman", "countryCode": "US", "stateCode": "OH", "latitude": "41.25450000", "longitude": "-82.39961000"}, {"name": "Walbridge", "countryCode": "US", "stateCode": "OH", "latitude": "41.58783000", "longitude": "-83.49327000"}, {"name": "Walton Hills", "countryCode": "US", "stateCode": "OH", "latitude": "41.36561000", "longitude": "-81.56123000"}, {"name": "Wapakon<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "40.56783000", "longitude": "-84.19356000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.23756000", "longitude": "-80.81842000"}, {"name": "Warren County", "countryCode": "US", "stateCode": "OH", "latitude": "39.42758000", "longitude": "-84.16676000"}, {"name": "Warrensville Heights", "countryCode": "US", "stateCode": "OH", "latitude": "41.43505000", "longitude": "-81.53623000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "OH", "latitude": "39.45532000", "longitude": "-81.49525000"}, {"name": "Washington Court House", "countryCode": "US", "stateCode": "OH", "latitude": "39.53645000", "longitude": "-83.43908000"}, {"name": "Waterville", "countryCode": "US", "stateCode": "OH", "latitude": "41.50089000", "longitude": "-83.71827000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.54922000", "longitude": "-84.14161000"}, {"name": "Waverly", "countryCode": "US", "stateCode": "OH", "latitude": "39.12673000", "longitude": "-82.98546000"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "OH", "latitude": "40.82887000", "longitude": "-81.88803000"}, {"name": "Waynesville", "countryCode": "US", "stateCode": "OH", "latitude": "39.52978000", "longitude": "-84.08660000"}, {"name": "Wellington", "countryCode": "US", "stateCode": "OH", "latitude": "41.16894000", "longitude": "-82.21794000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "39.12341000", "longitude": "-82.53294000"}, {"name": "Wellsville", "countryCode": "US", "stateCode": "OH", "latitude": "40.60284000", "longitude": "-80.64896000"}, {"name": "West Alexandria", "countryCode": "US", "stateCode": "OH", "latitude": "39.74450000", "longitude": "-84.53217000"}, {"name": "West Carrollton City", "countryCode": "US", "stateCode": "OH", "latitude": "39.67228000", "longitude": "-84.25216000"}, {"name": "West Hill", "countryCode": "US", "stateCode": "OH", "latitude": "41.23283000", "longitude": "-80.51924000"}, {"name": "West Jefferson", "countryCode": "US", "stateCode": "OH", "latitude": "39.94478000", "longitude": "-83.26880000"}, {"name": "West Lafayette", "countryCode": "US", "stateCode": "OH", "latitude": "40.27535000", "longitude": "-81.75096000"}, {"name": "West Liberty", "countryCode": "US", "stateCode": "OH", "latitude": "40.25228000", "longitude": "-83.75577000"}, {"name": "West Milton", "countryCode": "US", "stateCode": "OH", "latitude": "39.96255000", "longitude": "-84.32800000"}, {"name": "West Portsmouth", "countryCode": "US", "stateCode": "OH", "latitude": "38.75841000", "longitude": "-83.02906000"}, {"name": "West Salem", "countryCode": "US", "stateCode": "OH", "latitude": "40.97144000", "longitude": "-82.10987000"}, {"name": "West Union", "countryCode": "US", "stateCode": "OH", "latitude": "38.79452000", "longitude": "-83.54519000"}, {"name": "West Unity", "countryCode": "US", "stateCode": "OH", "latitude": "41.58616000", "longitude": "-84.43495000"}, {"name": "Westerville", "countryCode": "US", "stateCode": "OH", "latitude": "40.12617000", "longitude": "-82.92907000"}, {"name": "Westfield Center", "countryCode": "US", "stateCode": "OH", "latitude": "41.02644000", "longitude": "-81.93320000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.45532000", "longitude": "-81.91792000"}, {"name": "Weston", "countryCode": "US", "stateCode": "OH", "latitude": "41.34477000", "longitude": "-83.79716000"}, {"name": "Wetherington", "countryCode": "US", "stateCode": "OH", "latitude": "39.36367000", "longitude": "-84.37744000"}, {"name": "Wheelersburg", "countryCode": "US", "stateCode": "OH", "latitude": "38.73035000", "longitude": "-82.85545000"}, {"name": "White Oak", "countryCode": "US", "stateCode": "OH", "latitude": "39.21311000", "longitude": "-84.59939000"}, {"name": "Whitehall", "countryCode": "US", "stateCode": "OH", "latitude": "39.96673000", "longitude": "-82.88546000"}, {"name": "Whitehouse", "countryCode": "US", "stateCode": "OH", "latitude": "41.51894000", "longitude": "-83.80383000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.60533000", "longitude": "-81.45345000"}, {"name": "Wilberforce", "countryCode": "US", "stateCode": "OH", "latitude": "39.71617000", "longitude": "-83.87771000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.05311000", "longitude": "-82.72629000"}, {"name": "Williams County", "countryCode": "US", "stateCode": "OH", "latitude": "41.56029000", "longitude": "-84.58814000"}, {"name": "Williamsburg", "countryCode": "US", "stateCode": "OH", "latitude": "39.05423000", "longitude": "-84.05299000"}, {"name": "Williamsport", "countryCode": "US", "stateCode": "OH", "latitude": "39.58590000", "longitude": "-83.12046000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.63977000", "longitude": "-81.40650000"}, {"name": "Willoughby Hills", "countryCode": "US", "stateCode": "OH", "latitude": "41.59838000", "longitude": "-81.41845000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "OH", "latitude": "41.63310000", "longitude": "-81.46873000"}, {"name": "Wilmington", "countryCode": "US", "stateCode": "OH", "latitude": "39.44534000", "longitude": "-83.82854000"}, {"name": "Winchester", "countryCode": "US", "stateCode": "OH", "latitude": "38.94174000", "longitude": "-83.65075000"}, {"name": "Windham", "countryCode": "US", "stateCode": "OH", "latitude": "41.23506000", "longitude": "-81.04926000"}, {"name": "Wintersville", "countryCode": "US", "stateCode": "OH", "latitude": "40.37535000", "longitude": "-80.70369000"}, {"name": "Withamsville", "countryCode": "US", "stateCode": "OH", "latitude": "39.06228000", "longitude": "-84.28827000"}, {"name": "Wolfhurst", "countryCode": "US", "stateCode": "OH", "latitude": "40.06924000", "longitude": "-80.78370000"}, {"name": "Wood County", "countryCode": "US", "stateCode": "OH", "latitude": "41.36169000", "longitude": "-83.62299000"}, {"name": "Woodlawn", "countryCode": "US", "stateCode": "OH", "latitude": "39.25200000", "longitude": "-84.47022000"}, {"name": "Woodsfield", "countryCode": "US", "stateCode": "OH", "latitude": "39.76257000", "longitude": "-81.11538000"}, {"name": "Woodville", "countryCode": "US", "stateCode": "OH", "latitude": "41.45144000", "longitude": "-83.36576000"}, {"name": "Wooster", "countryCode": "US", "stateCode": "OH", "latitude": "40.80517000", "longitude": "-81.93646000"}, {"name": "Worthington", "countryCode": "US", "stateCode": "OH", "latitude": "40.09312000", "longitude": "-83.01796000"}, {"name": "Wright-Patterson AFB", "countryCode": "US", "stateCode": "OH", "latitude": "39.81113000", "longitude": "-84.05731000"}, {"name": "Wyandot County", "countryCode": "US", "stateCode": "OH", "latitude": "40.84237000", "longitude": "-83.30437000"}, {"name": "Wyoming", "countryCode": "US", "stateCode": "OH", "latitude": "39.23117000", "longitude": "-84.46578000"}, {"name": "Xenia", "countryCode": "US", "stateCode": "OH", "latitude": "39.68478000", "longitude": "-83.92965000"}, {"name": "Yellow Springs", "countryCode": "US", "stateCode": "OH", "latitude": "39.80645000", "longitude": "-83.88687000"}, {"name": "Yorkville", "countryCode": "US", "stateCode": "OH", "latitude": "40.15452000", "longitude": "-80.71036000"}, {"name": "Youngstown", "countryCode": "US", "stateCode": "OH", "latitude": "41.09978000", "longitude": "-80.64952000"}, {"name": "Zanesville", "countryCode": "US", "stateCode": "OH", "latitude": "39.94035000", "longitude": "-82.01319000"}]