[{"name": "Alpine", "countryCode": "US", "stateCode": "UT", "latitude": "40.45328000", "longitude": "-111.77799000"}, {"name": "American Fork", "countryCode": "US", "stateCode": "UT", "latitude": "40.37690000", "longitude": "-111.79576000"}, {"name": "Aurora", "countryCode": "US", "stateCode": "UT", "latitude": "38.92219000", "longitude": "-111.93409000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.28940000", "longitude": "-109.94320000"}, {"name": "Beaver", "countryCode": "US", "stateCode": "UT", "latitude": "38.27691000", "longitude": "-112.64105000"}, {"name": "Beaver County", "countryCode": "US", "stateCode": "UT", "latitude": "38.35771000", "longitude": "-113.23576000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.09829000", "longitude": "-111.73132000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.78743000", "longitude": "-111.93022000"}, {"name": "Blanding", "countryCode": "US", "stateCode": "UT", "latitude": "37.62433000", "longitude": "-109.47966000"}, {"name": "Bluffdale", "countryCode": "US", "stateCode": "UT", "latitude": "40.48967000", "longitude": "-111.93882000"}, {"name": "Bountiful", "countryCode": "US", "stateCode": "UT", "latitude": "40.88939000", "longitude": "-111.88077000"}, {"name": "Box Elder County", "countryCode": "US", "stateCode": "UT", "latitude": "41.52097000", "longitude": "-113.08209000"}, {"name": "Brigham City", "countryCode": "US", "stateCode": "UT", "latitude": "41.51021000", "longitude": "-112.01550000"}, {"name": "Cache County", "countryCode": "US", "stateCode": "UT", "latitude": "41.72237000", "longitude": "-111.74356000"}, {"name": "Canyon Rim", "countryCode": "US", "stateCode": "UT", "latitude": "40.70661000", "longitude": "-111.82188000"}, {"name": "Carbon County", "countryCode": "US", "stateCode": "UT", "latitude": "39.64817000", "longitude": "-110.58898000"}, {"name": "Carbonville", "countryCode": "US", "stateCode": "UT", "latitude": "39.61996000", "longitude": "-110.83433000"}, {"name": "Castle Dale", "countryCode": "US", "stateCode": "UT", "latitude": "39.21219000", "longitude": "-111.01961000"}, {"name": "Cedar City", "countryCode": "US", "stateCode": "UT", "latitude": "37.67748000", "longitude": "-113.06189000"}, {"name": "Cedar Hills", "countryCode": "US", "stateCode": "UT", "latitude": "40.41412000", "longitude": "-111.75854000"}, {"name": "Centerfield", "countryCode": "US", "stateCode": "UT", "latitude": "39.12524000", "longitude": "-111.81909000"}, {"name": "Centerville", "countryCode": "US", "stateCode": "UT", "latitude": "40.91800000", "longitude": "-111.87216000"}, {"name": "Clearfield", "countryCode": "US", "stateCode": "UT", "latitude": "41.11078000", "longitude": "-112.02605000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.13967000", "longitude": "-112.05050000"}, {"name": "Coalville", "countryCode": "US", "stateCode": "UT", "latitude": "40.91773000", "longitude": "-111.39936000"}, {"name": "Cottonwood Heights", "countryCode": "US", "stateCode": "UT", "latitude": "40.61967000", "longitude": "-111.81021000"}, {"name": "Daggett County", "countryCode": "US", "stateCode": "UT", "latitude": "40.88727000", "longitude": "-109.50765000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.47079000", "longitude": "-111.41463000"}, {"name": "Davis County", "countryCode": "US", "stateCode": "UT", "latitude": "40.99061000", "longitude": "-112.11124000"}, {"name": "Delta", "countryCode": "US", "stateCode": "UT", "latitude": "39.35218000", "longitude": "-112.57717000"}, {"name": "Draper", "countryCode": "US", "stateCode": "UT", "latitude": "40.52467000", "longitude": "-111.86382000"}, {"name": "Duchesne", "countryCode": "US", "stateCode": "UT", "latitude": "40.16329000", "longitude": "-110.40293000"}, {"name": "Duchesne County", "countryCode": "US", "stateCode": "UT", "latitude": "40.29751000", "longitude": "-110.42476000"}, {"name": "Eagle Mountain", "countryCode": "US", "stateCode": "UT", "latitude": "40.31412000", "longitude": "-112.00688000"}, {"name": "East Carbon City", "countryCode": "US", "stateCode": "UT", "latitude": "39.54774000", "longitude": "-110.41488000"}, {"name": "East Millcreek", "countryCode": "US", "stateCode": "UT", "latitude": "40.69995000", "longitude": "-111.81049000"}, {"name": "Elk Ridge", "countryCode": "US", "stateCode": "UT", "latitude": "40.01134000", "longitude": "-111.67687000"}, {"name": "Elwood", "countryCode": "US", "stateCode": "UT", "latitude": "41.69048000", "longitude": "-112.14106000"}, {"name": "Emery County", "countryCode": "US", "stateCode": "UT", "latitude": "38.99677000", "longitude": "-110.70067000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "37.77331000", "longitude": "-113.02439000"}, {"name": "Enterprise", "countryCode": "US", "stateCode": "UT", "latitude": "37.57359000", "longitude": "-113.71913000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "39.35968000", "longitude": "-111.58631000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.61272000", "longitude": "-112.30439000"}, {"name": "Fairview", "countryCode": "US", "stateCode": "UT", "latitude": "39.62635000", "longitude": "-111.43963000"}, {"name": "Farmington", "countryCode": "US", "stateCode": "UT", "latitude": "40.98050000", "longitude": "-111.88744000"}, {"name": "Farr West", "countryCode": "US", "stateCode": "UT", "latitude": "41.29717000", "longitude": "-112.02772000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "39.09358000", "longitude": "-111.13322000"}, {"name": "Fillmore", "countryCode": "US", "stateCode": "UT", "latitude": "38.96885000", "longitude": "-112.32355000"}, {"name": "Fountain Green", "countryCode": "US", "stateCode": "UT", "latitude": "39.62996000", "longitude": "-111.63520000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.61051000", "longitude": "-111.28074000"}, {"name": "Fruit Heights", "countryCode": "US", "stateCode": "UT", "latitude": "41.03217000", "longitude": "-111.90216000"}, {"name": "Garfield County", "countryCode": "US", "stateCode": "UT", "latitude": "37.85492000", "longitude": "-111.44313000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.74104000", "longitude": "-112.16162000"}, {"name": "Genola", "countryCode": "US", "stateCode": "UT", "latitude": "39.99634000", "longitude": "-111.84327000"}, {"name": "Grand County", "countryCode": "US", "stateCode": "UT", "latitude": "38.98174000", "longitude": "-109.56971000"}, {"name": "Granite", "countryCode": "US", "stateCode": "UT", "latitude": "40.57300000", "longitude": "-111.80604000"}, {"name": "Grantsville", "countryCode": "US", "stateCode": "UT", "latitude": "40.59994000", "longitude": "-112.46440000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "39.15524000", "longitude": "-111.81826000"}, {"name": "Harrisville", "countryCode": "US", "stateCode": "UT", "latitude": "41.28133000", "longitude": "-111.98828000"}, {"name": "Heber City", "countryCode": "US", "stateCode": "UT", "latitude": "40.50690000", "longitude": "-111.41324000"}, {"name": "Helper", "countryCode": "US", "stateCode": "UT", "latitude": "39.68413000", "longitude": "-110.85461000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.51411000", "longitude": "-112.03299000"}, {"name": "Highland", "countryCode": "US", "stateCode": "UT", "latitude": "40.42548000", "longitude": "-111.79447000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "37.00360000", "longitude": "-112.96688000"}, {"name": "Hill Air Force Base", "countryCode": "US", "stateCode": "UT", "latitude": "41.11118000", "longitude": "-111.97712000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.66884000", "longitude": "-111.82466000"}, {"name": "Honeyville", "countryCode": "US", "stateCode": "UT", "latitude": "41.63854000", "longitude": "-112.07939000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.16383000", "longitude": "-112.12244000"}, {"name": "Huntington", "countryCode": "US", "stateCode": "UT", "latitude": "39.32664000", "longitude": "-110.96461000"}, {"name": "Hurricane", "countryCode": "US", "stateCode": "UT", "latitude": "37.17526000", "longitude": "-113.28995000"}, {"name": "Hyde Park", "countryCode": "US", "stateCode": "UT", "latitude": "41.79882000", "longitude": "-111.81911000"}, {"name": "Hyrum", "countryCode": "US", "stateCode": "UT", "latitude": "41.63410000", "longitude": "-111.85217000"}, {"name": "Iron County", "countryCode": "US", "stateCode": "UT", "latitude": "37.85917000", "longitude": "-113.28927000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "37.16859000", "longitude": "-113.67941000"}, {"name": "Juab County", "countryCode": "US", "stateCode": "UT", "latitude": "39.70262000", "longitude": "-112.78477000"}, {"name": "Junction", "countryCode": "US", "stateCode": "UT", "latitude": "38.23748000", "longitude": "-112.21993000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.64301000", "longitude": "-111.28074000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "37.04749000", "longitude": "-112.52631000"}, {"name": "Kane County", "countryCode": "US", "stateCode": "UT", "latitude": "37.28507000", "longitude": "-111.88785000"}, {"name": "Kaysville", "countryCode": "US", "stateCode": "UT", "latitude": "41.03522000", "longitude": "-111.93855000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.65995000", "longitude": "-111.99633000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "37.20109000", "longitude": "-113.26967000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.06022000", "longitude": "-111.97105000"}, {"name": "Le<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.39162000", "longitude": "-111.85077000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.97576000", "longitude": "-111.85634000"}, {"name": "Liberty", "countryCode": "US", "stateCode": "UT", "latitude": "41.33355000", "longitude": "-111.86355000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.34329000", "longitude": "-111.72076000"}, {"name": "Little Cottonwood Creek Valley", "countryCode": "US", "stateCode": "UT", "latitude": "40.60439000", "longitude": "-111.82938000"}, {"name": "Loa", "countryCode": "US", "stateCode": "UT", "latitude": "38.40276000", "longitude": "-111.64296000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.73549000", "longitude": "-111.83439000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.47718000", "longitude": "-109.58681000"}, {"name": "Magna", "countryCode": "US", "stateCode": "UT", "latitude": "40.70911000", "longitude": "-112.10161000"}, {"name": "Manila", "countryCode": "US", "stateCode": "UT", "latitude": "40.98801000", "longitude": "-109.72265000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "39.26830000", "longitude": "-111.63686000"}, {"name": "<PERSON>ton", "countryCode": "US", "stateCode": "UT", "latitude": "40.13023000", "longitude": "-111.57853000"}, {"name": "Marriott-Slaterville", "countryCode": "US", "stateCode": "UT", "latitude": "41.25161000", "longitude": "-112.02550000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.70993000", "longitude": "-111.97773000"}, {"name": "Midvale", "countryCode": "US", "stateCode": "UT", "latitude": "40.61106000", "longitude": "-111.89994000"}, {"name": "Midway", "countryCode": "US", "stateCode": "UT", "latitude": "40.51218000", "longitude": "-111.47435000"}, {"name": "Milford", "countryCode": "US", "stateCode": "UT", "latitude": "38.39691000", "longitude": "-113.01079000"}, {"name": "Millard County", "countryCode": "US", "stateCode": "UT", "latitude": "39.07381000", "longitude": "-113.10046000"}, {"name": "Millcreek", "countryCode": "US", "stateCode": "UT", "latitude": "40.68689000", "longitude": "-111.87549000"}, {"name": "Millville", "countryCode": "US", "stateCode": "UT", "latitude": "41.68160000", "longitude": "-111.82300000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "38.57332000", "longitude": "-109.54984000"}, {"name": "Mona", "countryCode": "US", "stateCode": "UT", "latitude": "39.81607000", "longitude": "-111.85549000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "38.62997000", "longitude": "-112.12076000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "37.87138000", "longitude": "-109.34289000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.03606000", "longitude": "-111.67688000"}, {"name": "Morgan County", "countryCode": "US", "stateCode": "UT", "latitude": "41.08932000", "longitude": "-111.57312000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "39.52496000", "longitude": "-111.59047000"}, {"name": "Mount Olympus", "countryCode": "US", "stateCode": "UT", "latitude": "40.68550000", "longitude": "-111.78854000"}, {"name": "Mount Pleasant", "countryCode": "US", "stateCode": "UT", "latitude": "39.54691000", "longitude": "-111.45547000"}, {"name": "Mountain Green", "countryCode": "US", "stateCode": "UT", "latitude": "41.14300000", "longitude": "-111.79160000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.66689000", "longitude": "-111.88799000"}, {"name": "Naples", "countryCode": "US", "stateCode": "UT", "latitude": "40.42691000", "longitude": "-109.49930000"}, {"name": "Nephi", "countryCode": "US", "stateCode": "UT", "latitude": "39.71023000", "longitude": "-111.83632000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.67438000", "longitude": "-111.83300000"}, {"name": "North Logan", "countryCode": "US", "stateCode": "UT", "latitude": "41.76937000", "longitude": "-111.80467000"}, {"name": "North Ogden", "countryCode": "US", "stateCode": "UT", "latitude": "41.30716000", "longitude": "-111.96022000"}, {"name": "North Salt Lake", "countryCode": "US", "stateCode": "UT", "latitude": "40.84856000", "longitude": "-111.90688000"}, {"name": "<PERSON>ley", "countryCode": "US", "stateCode": "UT", "latitude": "40.71467000", "longitude": "-111.30074000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.22300000", "longitude": "-111.97383000"}, {"name": "<PERSON><PERSON>rr<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.63050000", "longitude": "-112.03383000"}, {"name": "Orangeville", "countryCode": "US", "stateCode": "UT", "latitude": "39.22719000", "longitude": "-111.05350000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.29690000", "longitude": "-111.69465000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "37.82276000", "longitude": "-112.43576000"}, {"name": "Park City", "countryCode": "US", "stateCode": "UT", "latitude": "40.64606000", "longitude": "-111.49797000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "37.84220000", "longitude": "-112.82800000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.04440000", "longitude": "-111.73215000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.46494000", "longitude": "-112.03245000"}, {"name": "Piute County", "countryCode": "US", "stateCode": "UT", "latitude": "38.33645000", "longitude": "-112.12695000"}, {"name": "Plain City", "countryCode": "US", "stateCode": "UT", "latitude": "41.29800000", "longitude": "-112.08605000"}, {"name": "Pleasant Grove", "countryCode": "US", "stateCode": "UT", "latitude": "40.36412000", "longitude": "-111.73854000"}, {"name": "Pleasant View", "countryCode": "US", "stateCode": "UT", "latitude": "41.31828000", "longitude": "-111.99216000"}, {"name": "Price", "countryCode": "US", "stateCode": "UT", "latitude": "39.59941000", "longitude": "-110.81071000"}, {"name": "Providence", "countryCode": "US", "stateCode": "UT", "latitude": "41.70632000", "longitude": "-111.81717000"}, {"name": "Provo", "countryCode": "US", "stateCode": "UT", "latitude": "40.23384000", "longitude": "-111.65853000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.66578000", "longitude": "-111.18214000"}, {"name": "Rich County", "countryCode": "US", "stateCode": "UT", "latitude": "41.63232000", "longitude": "-111.24445000"}, {"name": "Richfield", "countryCode": "US", "stateCode": "UT", "latitude": "38.77247000", "longitude": "-112.08409000"}, {"name": "Richmond", "countryCode": "US", "stateCode": "UT", "latitude": "41.92271000", "longitude": "-111.81356000"}, {"name": "River Heights", "countryCode": "US", "stateCode": "UT", "latitude": "41.72160000", "longitude": "-111.82133000"}, {"name": "Riverdale", "countryCode": "US", "stateCode": "UT", "latitude": "41.17689000", "longitude": "-112.00383000"}, {"name": "Riverton", "countryCode": "US", "stateCode": "UT", "latitude": "40.52189000", "longitude": "-111.93910000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.29940000", "longitude": "-109.98876000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.16161000", "longitude": "-112.02633000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "37.10415000", "longitude": "-113.58412000"}, {"name": "Salem", "countryCode": "US", "stateCode": "UT", "latitude": "40.05301000", "longitude": "-111.67354000"}, {"name": "Salina", "countryCode": "US", "stateCode": "UT", "latitude": "38.95774000", "longitude": "-111.85993000"}, {"name": "Salt Lake City", "countryCode": "US", "stateCode": "UT", "latitude": "40.76078000", "longitude": "-111.89105000"}, {"name": "Salt Lake County", "countryCode": "US", "stateCode": "UT", "latitude": "40.66758000", "longitude": "-111.92403000"}, {"name": "San Juan County", "countryCode": "US", "stateCode": "UT", "latitude": "37.62601000", "longitude": "-109.80457000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.59161000", "longitude": "-111.88410000"}, {"name": "Sandy Hills", "countryCode": "US", "stateCode": "UT", "latitude": "40.58106000", "longitude": "-111.85077000"}, {"name": "Sanpete County", "countryCode": "US", "stateCode": "UT", "latitude": "39.37396000", "longitude": "-111.57634000"}, {"name": "Santa Clara", "countryCode": "US", "stateCode": "UT", "latitude": "37.13304000", "longitude": "-113.65413000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "39.97551000", "longitude": "-111.78521000"}, {"name": "Saratoga Springs", "countryCode": "US", "stateCode": "UT", "latitude": "40.34912000", "longitude": "-111.90466000"}, {"name": "Sevier County", "countryCode": "US", "stateCode": "UT", "latitude": "38.74764000", "longitude": "-111.80464000"}, {"name": "Silver Summit", "countryCode": "US", "stateCode": "UT", "latitude": "40.74144000", "longitude": "-111.48775000"}, {"name": "Smithfield", "countryCode": "US", "stateCode": "UT", "latitude": "41.83826000", "longitude": "-111.83272000"}, {"name": "Snyderville", "countryCode": "US", "stateCode": "UT", "latitude": "40.69439000", "longitude": "-111.54381000"}, {"name": "South Jordan", "countryCode": "US", "stateCode": "UT", "latitude": "40.56217000", "longitude": "-111.92966000"}, {"name": "South Jordan Heights", "countryCode": "US", "stateCode": "UT", "latitude": "40.56384000", "longitude": "-111.94938000"}, {"name": "South Ogden", "countryCode": "US", "stateCode": "UT", "latitude": "41.19189000", "longitude": "-111.97133000"}, {"name": "South Salt Lake", "countryCode": "US", "stateCode": "UT", "latitude": "40.71884000", "longitude": "-111.88827000"}, {"name": "South Weber", "countryCode": "US", "stateCode": "UT", "latitude": "41.13244000", "longitude": "-111.93022000"}, {"name": "South Willard", "countryCode": "US", "stateCode": "UT", "latitude": "41.36327000", "longitude": "-112.03578000"}, {"name": "Spanish Fork", "countryCode": "US", "stateCode": "UT", "latitude": "40.11496000", "longitude": "-111.65492000"}, {"name": "Spring City", "countryCode": "US", "stateCode": "UT", "latitude": "39.48246000", "longitude": "-111.49602000"}, {"name": "Spring Glen", "countryCode": "US", "stateCode": "UT", "latitude": "39.65941000", "longitude": "-110.85349000"}, {"name": "Springville", "countryCode": "US", "stateCode": "UT", "latitude": "40.16523000", "longitude": "-111.61075000"}, {"name": "Stansbury park", "countryCode": "US", "stateCode": "UT", "latitude": "40.63772000", "longitude": "-112.29606000"}, {"name": "Summit County", "countryCode": "US", "stateCode": "UT", "latitude": "40.86815000", "longitude": "-110.95567000"}, {"name": "Summit Park", "countryCode": "US", "stateCode": "UT", "latitude": "40.74578000", "longitude": "-111.61159000"}, {"name": "Sunset", "countryCode": "US", "stateCode": "UT", "latitude": "41.13633000", "longitude": "-112.03105000"}, {"name": "Syracuse", "countryCode": "US", "stateCode": "UT", "latitude": "41.08939000", "longitude": "-112.06467000"}, {"name": "Taylorsville", "countryCode": "US", "stateCode": "UT", "latitude": "40.66772000", "longitude": "-111.93883000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.53078000", "longitude": "-112.29828000"}, {"name": "Tooele County", "countryCode": "US", "stateCode": "UT", "latitude": "40.44875000", "longitude": "-113.13106000"}, {"name": "Toquerville", "countryCode": "US", "stateCode": "UT", "latitude": "37.25332000", "longitude": "-113.28467000"}, {"name": "Tremonton", "countryCode": "US", "stateCode": "UT", "latitude": "41.71187000", "longitude": "-112.16551000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.14411000", "longitude": "-111.92327000"}, {"name": "Uintah County", "countryCode": "US", "stateCode": "UT", "latitude": "40.12495000", "longitude": "-109.51839000"}, {"name": "Utah County", "countryCode": "US", "stateCode": "UT", "latitude": "40.11995000", "longitude": "-111.67031000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "40.45552000", "longitude": "-109.52875000"}, {"name": "Vineyard", "countryCode": "US", "stateCode": "UT", "latitude": "40.29704000", "longitude": "-111.74670000"}, {"name": "Wasatch County", "countryCode": "US", "stateCode": "UT", "latitude": "40.33035000", "longitude": "-111.16847000"}, {"name": "Washington", "countryCode": "US", "stateCode": "UT", "latitude": "37.13054000", "longitude": "-113.50829000"}, {"name": "Washington County", "countryCode": "US", "stateCode": "UT", "latitude": "37.28036000", "longitude": "-113.50494000"}, {"name": "Washington Terrace", "countryCode": "US", "stateCode": "UT", "latitude": "41.17272000", "longitude": "-111.97661000"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "UT", "latitude": "38.32436000", "longitude": "-110.90367000"}, {"name": "Weber County", "countryCode": "US", "stateCode": "UT", "latitude": "41.26988000", "longitude": "-111.91327000"}, {"name": "Wellington", "countryCode": "US", "stateCode": "UT", "latitude": "39.54247000", "longitude": "-110.73543000"}, {"name": "Wellsville", "countryCode": "US", "stateCode": "UT", "latitude": "41.63854000", "longitude": "-111.93383000"}, {"name": "Wendover", "countryCode": "US", "stateCode": "UT", "latitude": "40.73715000", "longitude": "-114.03751000"}, {"name": "West Bountiful", "countryCode": "US", "stateCode": "UT", "latitude": "40.89383000", "longitude": "-111.90188000"}, {"name": "West Haven", "countryCode": "US", "stateCode": "UT", "latitude": "41.20300000", "longitude": "-112.05105000"}, {"name": "West Jordan", "countryCode": "US", "stateCode": "UT", "latitude": "40.60967000", "longitude": "-111.93910000"}, {"name": "West Mountain", "countryCode": "US", "stateCode": "UT", "latitude": "40.06079000", "longitude": "-111.78827000"}, {"name": "West Point", "countryCode": "US", "stateCode": "UT", "latitude": "41.11828000", "longitude": "-112.08411000"}, {"name": "West Valley City", "countryCode": "US", "stateCode": "UT", "latitude": "40.69161000", "longitude": "-112.00105000"}, {"name": "White City", "countryCode": "US", "stateCode": "UT", "latitude": "40.56578000", "longitude": "-111.86438000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "UT", "latitude": "41.40911000", "longitude": "-112.03606000"}, {"name": "Wolf Creek", "countryCode": "US", "stateCode": "UT", "latitude": "41.33327000", "longitude": "-111.82716000"}, {"name": "Woodland Hills", "countryCode": "US", "stateCode": "UT", "latitude": "40.01532000", "longitude": "-111.64868000"}, {"name": "Woods Cross", "countryCode": "US", "stateCode": "UT", "latitude": "40.87161000", "longitude": "-111.89216000"}]