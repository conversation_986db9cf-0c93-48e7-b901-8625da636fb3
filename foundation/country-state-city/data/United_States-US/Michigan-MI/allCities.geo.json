[{"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "41.89755000", "longitude": "-84.03717000"}, {"name": "Albion", "countryCode": "US", "stateCode": "MI", "latitude": "42.24310000", "longitude": "-84.75303000"}, {"name": "Alcona County", "countryCode": "US", "stateCode": "MI", "latitude": "44.71161000", "longitude": "-83.34366000"}, {"name": "Alger County", "countryCode": "US", "stateCode": "MI", "latitude": "46.45110000", "longitude": "-86.54755000"}, {"name": "Algonac", "countryCode": "US", "stateCode": "MI", "latitude": "42.61858000", "longitude": "-82.53230000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.52920000", "longitude": "-85.85530000"}, {"name": "Allegan County", "countryCode": "US", "stateCode": "MI", "latitude": "42.51766000", "longitude": "-85.91034000"}, {"name": "Allen Park", "countryCode": "US", "stateCode": "MI", "latitude": "42.25754000", "longitude": "-83.21104000"}, {"name": "Allendale", "countryCode": "US", "stateCode": "MI", "latitude": "42.97225000", "longitude": "-85.95365000"}, {"name": "Alma", "countryCode": "US", "stateCode": "MI", "latitude": "43.37892000", "longitude": "-84.65973000"}, {"name": "Almont", "countryCode": "US", "stateCode": "MI", "latitude": "42.92058000", "longitude": "-83.04493000"}, {"name": "Alpena", "countryCode": "US", "stateCode": "MI", "latitude": "45.06168000", "longitude": "-83.43275000"}, {"name": "Alpena County", "countryCode": "US", "stateCode": "MI", "latitude": "45.06350000", "longitude": "-83.46039000"}, {"name": "Ann Arbor", "countryCode": "US", "stateCode": "MI", "latitude": "42.27756000", "longitude": "-83.74088000"}, {"name": "Antrim County", "countryCode": "US", "stateCode": "MI", "latitude": "45.00737000", "longitude": "-85.17579000"}, {"name": "Arenac County", "countryCode": "US", "stateCode": "MI", "latitude": "44.04289000", "longitude": "-83.74725000"}, {"name": "Argentine", "countryCode": "US", "stateCode": "MI", "latitude": "42.79142000", "longitude": "-83.84634000"}, {"name": "Armada", "countryCode": "US", "stateCode": "MI", "latitude": "42.84420000", "longitude": "-82.88437000"}, {"name": "Athens", "countryCode": "US", "stateCode": "MI", "latitude": "42.08866000", "longitude": "-85.23471000"}, {"name": "Atlanta", "countryCode": "US", "stateCode": "MI", "latitude": "45.00473000", "longitude": "-84.14389000"}, {"name": "Au Sable", "countryCode": "US", "stateCode": "MI", "latitude": "44.41085000", "longitude": "-83.33219000"}, {"name": "Auburn", "countryCode": "US", "stateCode": "MI", "latitude": "43.60336000", "longitude": "-84.06970000"}, {"name": "Auburn Hills", "countryCode": "US", "stateCode": "MI", "latitude": "42.68753000", "longitude": "-83.23410000"}, {"name": "Bad Axe", "countryCode": "US", "stateCode": "MI", "latitude": "43.80196000", "longitude": "-83.00078000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.90112000", "longitude": "-85.85173000"}, {"name": "Bangor", "countryCode": "US", "stateCode": "MI", "latitude": "42.31254000", "longitude": "-86.11308000"}, {"name": "Baraga", "countryCode": "US", "stateCode": "MI", "latitude": "46.77854000", "longitude": "-88.48902000"}, {"name": "Baraga County", "countryCode": "US", "stateCode": "MI", "latitude": "46.69976000", "longitude": "-88.35215000"}, {"name": "<PERSON>-Millers Lake", "countryCode": "US", "stateCode": "MI", "latitude": "43.17956000", "longitude": "-83.31230000"}, {"name": "Barry County", "countryCode": "US", "stateCode": "MI", "latitude": "42.59503000", "longitude": "-85.30897000"}, {"name": "Bath", "countryCode": "US", "stateCode": "MI", "latitude": "42.81864000", "longitude": "-84.44859000"}, {"name": "Battle Creek", "countryCode": "US", "stateCode": "MI", "latitude": "42.31730000", "longitude": "-85.17816000"}, {"name": "Bay City", "countryCode": "US", "stateCode": "MI", "latitude": "43.59447000", "longitude": "-83.88886000"}, {"name": "Bay County", "countryCode": "US", "stateCode": "MI", "latitude": "43.72137000", "longitude": "-83.94184000"}, {"name": "Bay Harbor", "countryCode": "US", "stateCode": "MI", "latitude": "45.36413000", "longitude": "-85.08208000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.88225000", "longitude": "-84.48473000"}, {"name": "Beecher", "countryCode": "US", "stateCode": "MI", "latitude": "43.09003000", "longitude": "-83.69440000"}, {"name": "Beechwood", "countryCode": "US", "stateCode": "MI", "latitude": "42.79697000", "longitude": "-86.12588000"}, {"name": "Belding", "countryCode": "US", "stateCode": "MI", "latitude": "43.09781000", "longitude": "-85.22891000"}, {"name": "Bellaire", "countryCode": "US", "stateCode": "MI", "latitude": "44.98028000", "longitude": "-85.21117000"}, {"name": "Belleville", "countryCode": "US", "stateCode": "MI", "latitude": "42.20476000", "longitude": "-83.48521000"}, {"name": "Bellevue", "countryCode": "US", "stateCode": "MI", "latitude": "42.44337000", "longitude": "-85.01805000"}, {"name": "Benton Harbor", "countryCode": "US", "stateCode": "MI", "latitude": "42.11671000", "longitude": "-86.45419000"}, {"name": "Benton Heights", "countryCode": "US", "stateCode": "MI", "latitude": "42.13115000", "longitude": "-86.40724000"}, {"name": "Benzie County", "countryCode": "US", "stateCode": "MI", "latitude": "44.61687000", "longitude": "-86.13899000"}, {"name": "Berkley", "countryCode": "US", "stateCode": "MI", "latitude": "42.50309000", "longitude": "-83.18354000"}, {"name": "Berrien County", "countryCode": "US", "stateCode": "MI", "latitude": "41.91863000", "longitude": "-86.42807000"}, {"name": "Berrien Springs", "countryCode": "US", "stateCode": "MI", "latitude": "41.94643000", "longitude": "-86.33890000"}, {"name": "Bessemer", "countryCode": "US", "stateCode": "MI", "latitude": "46.48134000", "longitude": "-90.05295000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "44.63194000", "longitude": "-86.09092000"}, {"name": "Beverly Hills", "countryCode": "US", "stateCode": "MI", "latitude": "42.52392000", "longitude": "-83.22326000"}, {"name": "Big Rapids", "countryCode": "US", "stateCode": "MI", "latitude": "43.69808000", "longitude": "-85.48366000"}, {"name": "Bingham Farms", "countryCode": "US", "stateCode": "MI", "latitude": "42.51587000", "longitude": "-83.27326000"}, {"name": "Birch Run", "countryCode": "US", "stateCode": "MI", "latitude": "43.25086000", "longitude": "-83.79413000"}, {"name": "Birmingham", "countryCode": "US", "stateCode": "MI", "latitude": "42.54670000", "longitude": "-83.21132000"}, {"name": "Blissfield", "countryCode": "US", "stateCode": "MI", "latitude": "41.83255000", "longitude": "-83.86244000"}, {"name": "Bloomfield Hills", "countryCode": "US", "stateCode": "MI", "latitude": "42.58364000", "longitude": "-83.24549000"}, {"name": "Boyne City", "countryCode": "US", "stateCode": "MI", "latitude": "45.21668000", "longitude": "-85.01394000"}, {"name": "Branch County", "countryCode": "US", "stateCode": "MI", "latitude": "41.91611000", "longitude": "-85.05903000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.40808000", "longitude": "-84.47500000"}, {"name": "Bridgeport", "countryCode": "US", "stateCode": "MI", "latitude": "43.35947000", "longitude": "-83.88164000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "41.94310000", "longitude": "-86.55697000"}, {"name": "Brighton", "countryCode": "US", "stateCode": "MI", "latitude": "42.52948000", "longitude": "-83.78022000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "41.87227000", "longitude": "-85.19470000"}, {"name": "Brooklyn", "countryCode": "US", "stateCode": "MI", "latitude": "42.10587000", "longitude": "-84.24828000"}, {"name": "Brown City", "countryCode": "US", "stateCode": "MI", "latitude": "43.21225000", "longitude": "-82.98966000"}, {"name": "Brownlee Park", "countryCode": "US", "stateCode": "MI", "latitude": "42.31893000", "longitude": "-85.14249000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "41.82727000", "longitude": "-86.36112000"}, {"name": "Buena Vista", "countryCode": "US", "stateCode": "MI", "latitude": "43.42030000", "longitude": "-83.89858000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.23669000", "longitude": "-83.90636000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.99947000", "longitude": "-83.61634000"}, {"name": "Byron Center", "countryCode": "US", "stateCode": "MI", "latitude": "42.81225000", "longitude": "-85.72281000"}, {"name": "Cadillac", "countryCode": "US", "stateCode": "MI", "latitude": "44.25195000", "longitude": "-85.40116000"}, {"name": "Caledonia", "countryCode": "US", "stateCode": "MI", "latitude": "42.78920000", "longitude": "-85.51669000"}, {"name": "Calhoun County", "countryCode": "US", "stateCode": "MI", "latitude": "42.24653000", "longitude": "-85.00559000"}, {"name": "Canadian Lakes", "countryCode": "US", "stateCode": "MI", "latitude": "43.57919000", "longitude": "-85.30170000"}, {"name": "Canton", "countryCode": "US", "stateCode": "MI", "latitude": "42.30865000", "longitude": "-83.48216000"}, {"name": "Capac", "countryCode": "US", "stateCode": "MI", "latitude": "43.01253000", "longitude": "-82.92799000"}, {"name": "Carleton", "countryCode": "US", "stateCode": "MI", "latitude": "42.05921000", "longitude": "-83.39077000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.49073000", "longitude": "-83.39885000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.45864000", "longitude": "-83.93025000"}, {"name": "Carson City", "countryCode": "US", "stateCode": "MI", "latitude": "43.17698000", "longitude": "-84.84639000"}, {"name": "Cass City", "countryCode": "US", "stateCode": "MI", "latitude": "43.60085000", "longitude": "-83.17467000"}, {"name": "Cass County", "countryCode": "US", "stateCode": "MI", "latitude": "41.91540000", "longitude": "-85.99346000"}, {"name": "Cassopolis", "countryCode": "US", "stateCode": "MI", "latitude": "41.91171000", "longitude": "-86.01001000"}, {"name": "Cedar Springs", "countryCode": "US", "stateCode": "MI", "latitude": "43.22336000", "longitude": "-85.55142000"}, {"name": "Center Line", "countryCode": "US", "stateCode": "MI", "latitude": "42.48504000", "longitude": "-83.02770000"}, {"name": "Centreville", "countryCode": "US", "stateCode": "MI", "latitude": "41.92338000", "longitude": "-85.52832000"}, {"name": "Charlevoix", "countryCode": "US", "stateCode": "MI", "latitude": "45.31806000", "longitude": "-85.25840000"}, {"name": "Charlevoix County", "countryCode": "US", "stateCode": "MI", "latitude": "45.26715000", "longitude": "-85.24017000"}, {"name": "Charlotte", "countryCode": "US", "stateCode": "MI", "latitude": "42.56365000", "longitude": "-84.83582000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "45.64696000", "longitude": "-84.47448000"}, {"name": "Cheboygan County", "countryCode": "US", "stateCode": "MI", "latitude": "45.47294000", "longitude": "-84.49206000"}, {"name": "Chelsea", "countryCode": "US", "stateCode": "MI", "latitude": "42.31807000", "longitude": "-84.02181000"}, {"name": "Chesaning", "countryCode": "US", "stateCode": "MI", "latitude": "43.18475000", "longitude": "-84.11497000"}, {"name": "Chippewa County", "countryCode": "US", "stateCode": "MI", "latitude": "46.32818000", "longitude": "-84.52936000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.81947000", "longitude": "-84.76863000"}, {"name": "Clare County", "countryCode": "US", "stateCode": "MI", "latitude": "43.98787000", "longitude": "-84.84784000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.73586000", "longitude": "-83.41883000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.53337000", "longitude": "-83.14632000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.07199000", "longitude": "-83.97161000"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "MI", "latitude": "42.94365000", "longitude": "-84.60152000"}, {"name": "Clinton Township", "countryCode": "US", "stateCode": "MI", "latitude": "42.58698000", "longitude": "-82.91992000"}, {"name": "Clio", "countryCode": "US", "stateCode": "MI", "latitude": "43.17753000", "longitude": "-83.73413000"}, {"name": "Coldwater", "countryCode": "US", "stateCode": "MI", "latitude": "41.94033000", "longitude": "-85.00052000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.75669000", "longitude": "-84.58584000"}, {"name": "Coloma", "countryCode": "US", "stateCode": "MI", "latitude": "42.18615000", "longitude": "-86.30836000"}, {"name": "Colon", "countryCode": "US", "stateCode": "MI", "latitude": "41.95838000", "longitude": "-85.32498000"}, {"name": "Comstock Northwest", "countryCode": "US", "stateCode": "MI", "latitude": "42.32182000", "longitude": "-85.51759000"}, {"name": "Comstock Park", "countryCode": "US", "stateCode": "MI", "latitude": "43.03864000", "longitude": "-85.67003000"}, {"name": "Concord", "countryCode": "US", "stateCode": "MI", "latitude": "42.17782000", "longitude": "-84.64302000"}, {"name": "Constantine", "countryCode": "US", "stateCode": "MI", "latitude": "41.84116000", "longitude": "-85.66860000"}, {"name": "Coopersville", "countryCode": "US", "stateCode": "MI", "latitude": "43.06391000", "longitude": "-85.93477000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.98197000", "longitude": "-84.11775000"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "MI", "latitude": "44.68361000", "longitude": "-84.61030000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.27558000", "longitude": "-82.62104000"}, {"name": "Crystal Falls", "countryCode": "US", "stateCode": "MI", "latitude": "46.09801000", "longitude": "-88.33402000"}, {"name": "Cutlerville", "countryCode": "US", "stateCode": "MI", "latitude": "42.84086000", "longitude": "-85.66364000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.03475000", "longitude": "-83.51801000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.84226000", "longitude": "-84.56915000"}, {"name": "Dearborn", "countryCode": "US", "stateCode": "MI", "latitude": "42.32226000", "longitude": "-83.17631000"}, {"name": "Dearborn Heights", "countryCode": "US", "stateCode": "MI", "latitude": "42.33698000", "longitude": "-83.27326000"}, {"name": "Decatur", "countryCode": "US", "stateCode": "MI", "latitude": "42.10810000", "longitude": "-85.97446000"}, {"name": "Delta County", "countryCode": "US", "stateCode": "MI", "latitude": "45.79162000", "longitude": "-86.87060000"}, {"name": "Detroit", "countryCode": "US", "stateCode": "MI", "latitude": "42.33143000", "longitude": "-83.04575000"}, {"name": "Detroit Beach", "countryCode": "US", "stateCode": "MI", "latitude": "41.93116000", "longitude": "-83.32688000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.33834000", "longitude": "-83.88954000"}, {"name": "Dickinson County", "countryCode": "US", "stateCode": "MI", "latitude": "46.00935000", "longitude": "-87.87021000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.64559000", "longitude": "-84.64887000"}, {"name": "Dollar Bay", "countryCode": "US", "stateCode": "MI", "latitude": "47.11965000", "longitude": "-88.51151000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.64336000", "longitude": "-86.20059000"}, {"name": "Dowagiac", "countryCode": "US", "stateCode": "MI", "latitude": "41.98421000", "longitude": "-86.10862000"}, {"name": "Dundee", "countryCode": "US", "stateCode": "MI", "latitude": "41.95727000", "longitude": "-83.65966000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.91198000", "longitude": "-83.98468000"}, {"name": "Eagle River", "countryCode": "US", "stateCode": "MI", "latitude": "47.41381000", "longitude": "-88.29566000"}, {"name": "East Grand Rapids", "countryCode": "US", "stateCode": "MI", "latitude": "42.94114000", "longitude": "-85.61003000"}, {"name": "East Jordan", "countryCode": "US", "stateCode": "MI", "latitude": "45.15806000", "longitude": "-85.12423000"}, {"name": "East Lansing", "countryCode": "US", "stateCode": "MI", "latitude": "42.73698000", "longitude": "-84.48387000"}, {"name": "East Tawas", "countryCode": "US", "stateCode": "MI", "latitude": "44.27946000", "longitude": "-83.49025000"}, {"name": "Eastpointe", "countryCode": "US", "stateCode": "MI", "latitude": "42.46837000", "longitude": "-82.95547000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.30310000", "longitude": "-85.55028000"}, {"name": "Eaton County", "countryCode": "US", "stateCode": "MI", "latitude": "42.59607000", "longitude": "-84.83831000"}, {"name": "Eaton Rapids", "countryCode": "US", "stateCode": "MI", "latitude": "42.50920000", "longitude": "-84.65581000"}, {"name": "Ecorse", "countryCode": "US", "stateCode": "MI", "latitude": "42.24448000", "longitude": "-83.14576000"}, {"name": "Edgemont Park", "countryCode": "US", "stateCode": "MI", "latitude": "42.74670000", "longitude": "-84.59359000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.40809000", "longitude": "-85.03863000"}, {"name": "Edwardsburg", "countryCode": "US", "stateCode": "MI", "latitude": "41.79560000", "longitude": "-86.08084000"}, {"name": "Elk Rapids", "countryCode": "US", "stateCode": "MI", "latitude": "44.89556000", "longitude": "-85.41646000"}, {"name": "Emmet County", "countryCode": "US", "stateCode": "MI", "latitude": "45.58754000", "longitude": "-84.98147000"}, {"name": "Escanaba", "countryCode": "US", "stateCode": "MI", "latitude": "45.74525000", "longitude": "-87.06458000"}, {"name": "Essexville", "countryCode": "US", "stateCode": "MI", "latitude": "43.61530000", "longitude": "-83.84192000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.90058000", "longitude": "-85.25810000"}, {"name": "Fair Plain", "countryCode": "US", "stateCode": "MI", "latitude": "42.08699000", "longitude": "-86.45586000"}, {"name": "Farmington", "countryCode": "US", "stateCode": "MI", "latitude": "42.46448000", "longitude": "-83.37632000"}, {"name": "Farmington Hills", "countryCode": "US", "stateCode": "MI", "latitude": "42.48531000", "longitude": "-83.37716000"}, {"name": "Fennville", "countryCode": "US", "stateCode": "MI", "latitude": "42.59392000", "longitude": "-86.10170000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.79781000", "longitude": "-83.70495000"}, {"name": "Ferndale", "countryCode": "US", "stateCode": "MI", "latitude": "42.46059000", "longitude": "-83.13465000"}, {"name": "Ferrysburg", "countryCode": "US", "stateCode": "MI", "latitude": "43.08446000", "longitude": "-86.22033000"}, {"name": "Flat Rock", "countryCode": "US", "stateCode": "MI", "latitude": "42.09643000", "longitude": "-83.29187000"}, {"name": "Flint", "countryCode": "US", "stateCode": "MI", "latitude": "43.01253000", "longitude": "-83.68746000"}, {"name": "Flushing", "countryCode": "US", "stateCode": "MI", "latitude": "43.06308000", "longitude": "-83.85107000"}, {"name": "Forest Hills", "countryCode": "US", "stateCode": "MI", "latitude": "42.95947000", "longitude": "-85.48975000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.00170000", "longitude": "-84.73972000"}, {"name": "Fowlerville", "countryCode": "US", "stateCode": "MI", "latitude": "42.66059000", "longitude": "-84.07301000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.33169000", "longitude": "-83.73802000"}, {"name": "Frankfort", "countryCode": "US", "stateCode": "MI", "latitude": "44.63361000", "longitude": "-86.23454000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.52226000", "longitude": "-83.30604000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.53920000", "longitude": "-82.94937000"}, {"name": "Freeland", "countryCode": "US", "stateCode": "MI", "latitude": "43.52503000", "longitude": "-84.12276000"}, {"name": "Fremont", "countryCode": "US", "stateCode": "MI", "latitude": "43.46752000", "longitude": "-85.94200000"}, {"name": "Fruitport", "countryCode": "US", "stateCode": "MI", "latitude": "43.13196000", "longitude": "-86.15478000"}, {"name": "Galesburg", "countryCode": "US", "stateCode": "MI", "latitude": "42.28865000", "longitude": "-85.41806000"}, {"name": "Garden City", "countryCode": "US", "stateCode": "MI", "latitude": "42.32559000", "longitude": "-83.33104000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "45.02751000", "longitude": "-84.67475000"}, {"name": "Genesee County", "countryCode": "US", "stateCode": "MI", "latitude": "43.02172000", "longitude": "-83.70671000"}, {"name": "Gibraltar", "countryCode": "US", "stateCode": "MI", "latitude": "42.09504000", "longitude": "-83.18965000"}, {"name": "Gladstone", "countryCode": "US", "stateCode": "MI", "latitude": "45.85274000", "longitude": "-87.02180000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.98085000", "longitude": "-84.48640000"}, {"name": "Gladwin County", "countryCode": "US", "stateCode": "MI", "latitude": "43.99067000", "longitude": "-84.38825000"}, {"name": "Gogebic County", "countryCode": "US", "stateCode": "MI", "latitude": "46.49552000", "longitude": "-89.79555000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.91697000", "longitude": "-83.50634000"}, {"name": "Grand Blanc", "countryCode": "US", "stateCode": "MI", "latitude": "42.92753000", "longitude": "-83.62995000"}, {"name": "Grand Haven", "countryCode": "US", "stateCode": "MI", "latitude": "43.06307000", "longitude": "-86.22839000"}, {"name": "Grand Ledge", "countryCode": "US", "stateCode": "MI", "latitude": "42.75337000", "longitude": "-84.74638000"}, {"name": "Grand Rapids", "countryCode": "US", "stateCode": "MI", "latitude": "42.96336000", "longitude": "-85.66809000"}, {"name": "Grand Traverse County", "countryCode": "US", "stateCode": "MI", "latitude": "44.71624000", "longitude": "-85.55220000"}, {"name": "Grandville", "countryCode": "US", "stateCode": "MI", "latitude": "42.90975000", "longitude": "-85.76309000"}, {"name": "Grass Lake", "countryCode": "US", "stateCode": "MI", "latitude": "42.25087000", "longitude": "-84.21301000"}, {"name": "Gratiot County", "countryCode": "US", "stateCode": "MI", "latitude": "43.29273000", "longitude": "-84.60491000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "44.66140000", "longitude": "-84.71475000"}, {"name": "Greenville", "countryCode": "US", "stateCode": "MI", "latitude": "43.17753000", "longitude": "-85.25280000"}, {"name": "G<PERSON><PERSON>ckville", "countryCode": "US", "stateCode": "MI", "latitude": "44.78306000", "longitude": "-85.63869000"}, {"name": "Grosse Ile", "countryCode": "US", "stateCode": "MI", "latitude": "42.12921000", "longitude": "-83.14437000"}, {"name": "Grosse Pointe", "countryCode": "US", "stateCode": "MI", "latitude": "42.38615000", "longitude": "-82.91186000"}, {"name": "Grosse Pointe Farms", "countryCode": "US", "stateCode": "MI", "latitude": "42.40920000", "longitude": "-82.89186000"}, {"name": "Grosse Pointe Park", "countryCode": "US", "stateCode": "MI", "latitude": "42.37587000", "longitude": "-82.93742000"}, {"name": "Grosse Pointe Shores", "countryCode": "US", "stateCode": "MI", "latitude": "42.43670000", "longitude": "-82.87686000"}, {"name": "Grosse Pointe Woods", "countryCode": "US", "stateCode": "MI", "latitude": "42.44365000", "longitude": "-82.90686000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "46.28106000", "longitude": "-87.44097000"}, {"name": "Hamtramck", "countryCode": "US", "stateCode": "MI", "latitude": "42.39282000", "longitude": "-83.04964000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "47.12687000", "longitude": "-88.58096000"}, {"name": "Harbor Beach", "countryCode": "US", "stateCode": "MI", "latitude": "43.84474000", "longitude": "-82.65132000"}, {"name": "Harbor Springs", "countryCode": "US", "stateCode": "MI", "latitude": "45.43168000", "longitude": "-84.99200000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.43309000", "longitude": "-82.92408000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "44.01919000", "longitude": "-84.79947000"}, {"name": "Harrisville", "countryCode": "US", "stateCode": "MI", "latitude": "44.65640000", "longitude": "-83.29469000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.69834000", "longitude": "-86.36397000"}, {"name": "Hartford", "countryCode": "US", "stateCode": "MI", "latitude": "42.20671000", "longitude": "-86.16669000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "46.49466000", "longitude": "-87.35431000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.74698000", "longitude": "-84.40108000"}, {"name": "Hastings", "countryCode": "US", "stateCode": "MI", "latitude": "42.64587000", "longitude": "-85.29084000"}, {"name": "Hazel Park", "countryCode": "US", "stateCode": "MI", "latitude": "42.46254000", "longitude": "-83.10409000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.41475000", "longitude": "-84.23054000"}, {"name": "Highland Park", "countryCode": "US", "stateCode": "MI", "latitude": "42.40559000", "longitude": "-83.09687000"}, {"name": "Hillsdale", "countryCode": "US", "stateCode": "MI", "latitude": "41.92005000", "longitude": "-84.63051000"}, {"name": "Hillsdale County", "countryCode": "US", "stateCode": "MI", "latitude": "41.88777000", "longitude": "-84.59293000"}, {"name": "Holland", "countryCode": "US", "stateCode": "MI", "latitude": "42.78752000", "longitude": "-86.10893000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.79197000", "longitude": "-83.62773000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.64059000", "longitude": "-84.51525000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.14588000", "longitude": "-84.80886000"}, {"name": "Houghton", "countryCode": "US", "stateCode": "MI", "latitude": "47.12187000", "longitude": "-88.56901000"}, {"name": "Houghton County", "countryCode": "US", "stateCode": "MI", "latitude": "46.99155000", "longitude": "-88.65206000"}, {"name": "Houghton Lake", "countryCode": "US", "stateCode": "MI", "latitude": "44.31474000", "longitude": "-84.76475000"}, {"name": "Howard City", "countryCode": "US", "stateCode": "MI", "latitude": "43.39558000", "longitude": "-85.46782000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.60726000", "longitude": "-83.92940000"}, {"name": "Hubbard Lake", "countryCode": "US", "stateCode": "MI", "latitude": "44.75973000", "longitude": "-83.54442000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "41.85505000", "longitude": "-84.35384000"}, {"name": "Hudsonville", "countryCode": "US", "stateCode": "MI", "latitude": "42.87086000", "longitude": "-85.86504000"}, {"name": "Huntington Woods", "countryCode": "US", "stateCode": "MI", "latitude": "42.48059000", "longitude": "-83.16687000"}, {"name": "Huron County", "countryCode": "US", "stateCode": "MI", "latitude": "43.91007000", "longitude": "-82.85551000"}, {"name": "Imlay City", "countryCode": "US", "stateCode": "MI", "latitude": "43.02475000", "longitude": "-83.07772000"}, {"name": "Indian River", "countryCode": "US", "stateCode": "MI", "latitude": "45.41251000", "longitude": "-84.61254000"}, {"name": "Ingham County", "countryCode": "US", "stateCode": "MI", "latitude": "42.59710000", "longitude": "-84.37354000"}, {"name": "Inkster", "countryCode": "US", "stateCode": "MI", "latitude": "42.29420000", "longitude": "-83.30993000"}, {"name": "Ionia", "countryCode": "US", "stateCode": "MI", "latitude": "42.98725000", "longitude": "-85.07112000"}, {"name": "Ionia County", "countryCode": "US", "stateCode": "MI", "latitude": "42.94509000", "longitude": "-85.07460000"}, {"name": "Iosco County", "countryCode": "US", "stateCode": "MI", "latitude": "44.30125000", "longitude": "-83.51395000"}, {"name": "Iron County", "countryCode": "US", "stateCode": "MI", "latitude": "46.20869000", "longitude": "-88.53053000"}, {"name": "Iron Mountain", "countryCode": "US", "stateCode": "MI", "latitude": "45.82023000", "longitude": "-88.06596000"}, {"name": "Iron River", "countryCode": "US", "stateCode": "MI", "latitude": "46.09273000", "longitude": "-88.64235000"}, {"name": "Ironwood", "countryCode": "US", "stateCode": "MI", "latitude": "46.45467000", "longitude": "-90.17101000"}, {"name": "Isabella County", "countryCode": "US", "stateCode": "MI", "latitude": "43.64060000", "longitude": "-84.84680000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "46.48855000", "longitude": "-87.66764000"}, {"name": "Ithaca", "countryCode": "US", "stateCode": "MI", "latitude": "43.29170000", "longitude": "-84.60750000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.24587000", "longitude": "-84.40135000"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "MI", "latitude": "42.24849000", "longitude": "-84.42344000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.90725000", "longitude": "-85.79198000"}, {"name": "Jonesville", "countryCode": "US", "stateCode": "MI", "latitude": "41.98421000", "longitude": "-84.66190000"}, {"name": "<PERSON><PERSON> <PERSON><PERSON> Air Force Base", "countryCode": "US", "stateCode": "MI", "latitude": "46.34651000", "longitude": "-87.38632000"}, {"name": "Kalamazoo", "countryCode": "US", "stateCode": "MI", "latitude": "42.29171000", "longitude": "-85.58723000"}, {"name": "Kalamazoo County", "countryCode": "US", "stateCode": "MI", "latitude": "42.24545000", "longitude": "-85.53118000"}, {"name": "Kalkaska", "countryCode": "US", "stateCode": "MI", "latitude": "44.73417000", "longitude": "-85.17589000"}, {"name": "Kalkaska County", "countryCode": "US", "stateCode": "MI", "latitude": "44.68466000", "longitude": "-85.09023000"}, {"name": "Keego Harbor", "countryCode": "US", "stateCode": "MI", "latitude": "42.60809000", "longitude": "-83.34382000"}, {"name": "Kent City", "countryCode": "US", "stateCode": "MI", "latitude": "43.22002000", "longitude": "-85.75115000"}, {"name": "Kent County", "countryCode": "US", "stateCode": "MI", "latitude": "43.03216000", "longitude": "-85.54930000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.86947000", "longitude": "-85.64475000"}, {"name": "Keweenaw County", "countryCode": "US", "stateCode": "MI", "latitude": "47.28296000", "longitude": "-88.21198000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.75613000", "longitude": "-83.35690000"}, {"name": "Kingsford", "countryCode": "US", "stateCode": "MI", "latitude": "45.79496000", "longitude": "-88.07207000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "44.58473000", "longitude": "-85.53590000"}, {"name": "L'Anse", "countryCode": "US", "stateCode": "MI", "latitude": "46.75660000", "longitude": "-88.45291000"}, {"name": "Laingsburg", "countryCode": "US", "stateCode": "MI", "latitude": "42.89031000", "longitude": "-84.35136000"}, {"name": "Lake City", "countryCode": "US", "stateCode": "MI", "latitude": "44.33529000", "longitude": "-85.21505000"}, {"name": "Lake County", "countryCode": "US", "stateCode": "MI", "latitude": "43.99001000", "longitude": "-85.80170000"}, {"name": "Lake Fenton", "countryCode": "US", "stateCode": "MI", "latitude": "42.84614000", "longitude": "-83.70773000"}, {"name": "Lake Isabella", "countryCode": "US", "stateCode": "MI", "latitude": "43.64364000", "longitude": "-84.99725000"}, {"name": "Lake Michigan Beach", "countryCode": "US", "stateCode": "MI", "latitude": "42.22087000", "longitude": "-86.36947000"}, {"name": "Lake Odessa", "countryCode": "US", "stateCode": "MI", "latitude": "42.78476000", "longitude": "-85.13834000"}, {"name": "Lake Orion", "countryCode": "US", "stateCode": "MI", "latitude": "42.78448000", "longitude": "-83.23966000"}, {"name": "Lakeview", "countryCode": "US", "stateCode": "MI", "latitude": "43.44642000", "longitude": "-85.27420000"}, {"name": "Lakewood Club", "countryCode": "US", "stateCode": "MI", "latitude": "43.37112000", "longitude": "-86.26034000"}, {"name": "Lambertville", "countryCode": "US", "stateCode": "MI", "latitude": "41.76588000", "longitude": "-83.62799000"}, {"name": "Lansing", "countryCode": "US", "stateCode": "MI", "latitude": "42.73253000", "longitude": "-84.55553000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.05142000", "longitude": "-83.31883000"}, {"name": "Lapeer County", "countryCode": "US", "stateCode": "MI", "latitude": "43.09015000", "longitude": "-83.22178000"}, {"name": "Lathrup Village", "countryCode": "US", "stateCode": "MI", "latitude": "42.49642000", "longitude": "-83.22271000"}, {"name": "Laurium", "countryCode": "US", "stateCode": "MI", "latitude": "47.23743000", "longitude": "-88.44317000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.16726000", "longitude": "-85.84695000"}, {"name": "Leelanau County", "countryCode": "US", "stateCode": "MI", "latitude": "45.15177000", "longitude": "-86.03850000"}, {"name": "Leland", "countryCode": "US", "stateCode": "MI", "latitude": "45.02305000", "longitude": "-85.75981000"}, {"name": "Lenawee County", "countryCode": "US", "stateCode": "MI", "latitude": "41.89508000", "longitude": "-84.06636000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.45143000", "longitude": "-84.43247000"}, {"name": "Level Park-Oak Park", "countryCode": "US", "stateCode": "MI", "latitude": "42.36418000", "longitude": "-85.26650000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "44.88390000", "longitude": "-84.30557000"}, {"name": "Lexington", "countryCode": "US", "stateCode": "MI", "latitude": "43.26808000", "longitude": "-82.53076000"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "MI", "latitude": "42.25059000", "longitude": "-83.17854000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.81447000", "longitude": "-83.78245000"}, {"name": "Litchfield", "countryCode": "US", "stateCode": "MI", "latitude": "42.04393000", "longitude": "-84.75746000"}, {"name": "Livingston County", "countryCode": "US", "stateCode": "MI", "latitude": "42.60292000", "longitude": "-83.91153000"}, {"name": "Livonia", "countryCode": "US", "stateCode": "MI", "latitude": "42.36837000", "longitude": "-83.35271000"}, {"name": "Lowell", "countryCode": "US", "stateCode": "MI", "latitude": "42.93364000", "longitude": "-85.34196000"}, {"name": "Luce County", "countryCode": "US", "stateCode": "MI", "latitude": "46.36778000", "longitude": "-85.50934000"}, {"name": "Ludington", "countryCode": "US", "stateCode": "MI", "latitude": "43.95528000", "longitude": "-86.45258000"}, {"name": "Luna Pier", "countryCode": "US", "stateCode": "MI", "latitude": "41.80699000", "longitude": "-83.44243000"}, {"name": "Mackinac County", "countryCode": "US", "stateCode": "MI", "latitude": "45.87184000", "longitude": "-84.76227000"}, {"name": "Macomb County", "countryCode": "US", "stateCode": "MI", "latitude": "42.67279000", "longitude": "-82.91016000"}, {"name": "Madison Heights", "countryCode": "US", "stateCode": "MI", "latitude": "42.48587000", "longitude": "-83.10520000"}, {"name": "Mancelona", "countryCode": "US", "stateCode": "MI", "latitude": "44.90223000", "longitude": "-85.06088000"}, {"name": "Manchester", "countryCode": "US", "stateCode": "MI", "latitude": "42.15032000", "longitude": "-84.03772000"}, {"name": "Manistee", "countryCode": "US", "stateCode": "MI", "latitude": "44.24445000", "longitude": "-86.32425000"}, {"name": "Manistee County", "countryCode": "US", "stateCode": "MI", "latitude": "44.23831000", "longitude": "-86.28799000"}, {"name": "Manistique", "countryCode": "US", "stateCode": "MI", "latitude": "45.95775000", "longitude": "-86.24625000"}, {"name": "Manitou Beach-Devils Lake", "countryCode": "US", "stateCode": "MI", "latitude": "41.97565000", "longitude": "-84.28616000"}, {"name": "Manton", "countryCode": "US", "stateCode": "MI", "latitude": "44.41084000", "longitude": "-85.39894000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.02588000", "longitude": "-85.81556000"}, {"name": "Marine City", "countryCode": "US", "stateCode": "MI", "latitude": "42.71948000", "longitude": "-82.49213000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.32697000", "longitude": "-83.08022000"}, {"name": "Marquette", "countryCode": "US", "stateCode": "MI", "latitude": "46.54354000", "longitude": "-87.39542000"}, {"name": "Marquette County", "countryCode": "US", "stateCode": "MI", "latitude": "46.66295000", "longitude": "-87.57350000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.27226000", "longitude": "-84.96331000"}, {"name": "Marysville", "countryCode": "US", "stateCode": "MI", "latitude": "42.91253000", "longitude": "-82.48686000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.57920000", "longitude": "-84.44358000"}, {"name": "Mason County", "countryCode": "US", "stateCode": "MI", "latitude": "43.95625000", "longitude": "-86.42258000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.20948000", "longitude": "-85.78445000"}, {"name": "Mecosta County", "countryCode": "US", "stateCode": "MI", "latitude": "43.64080000", "longitude": "-85.32462000"}, {"name": "Melvindale", "countryCode": "US", "stateCode": "MI", "latitude": "42.28254000", "longitude": "-83.17520000"}, {"name": "Memphis", "countryCode": "US", "stateCode": "MI", "latitude": "42.89642000", "longitude": "-82.76881000"}, {"name": "Menominee", "countryCode": "US", "stateCode": "MI", "latitude": "45.10776000", "longitude": "-87.61427000"}, {"name": "Menominee County", "countryCode": "US", "stateCode": "MI", "latitude": "45.52514000", "longitude": "-87.50969000"}, {"name": "Michigan Center", "countryCode": "US", "stateCode": "MI", "latitude": "42.23309000", "longitude": "-84.32718000"}, {"name": "Middleville", "countryCode": "US", "stateCode": "MI", "latitude": "42.71309000", "longitude": "-85.46196000"}, {"name": "Midland", "countryCode": "US", "stateCode": "MI", "latitude": "43.61558000", "longitude": "-84.24721000"}, {"name": "Midland County", "countryCode": "US", "stateCode": "MI", "latitude": "43.64686000", "longitude": "-84.38811000"}, {"name": "Milan", "countryCode": "US", "stateCode": "MI", "latitude": "42.08532000", "longitude": "-83.68244000"}, {"name": "Milford", "countryCode": "US", "stateCode": "MI", "latitude": "42.59364000", "longitude": "-83.59939000"}, {"name": "Millington", "countryCode": "US", "stateCode": "MI", "latitude": "43.28141000", "longitude": "-83.52968000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "44.65224000", "longitude": "-84.12973000"}, {"name": "Missaukee County", "countryCode": "US", "stateCode": "MI", "latitude": "44.33730000", "longitude": "-85.09467000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "41.91643000", "longitude": "-83.39771000"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "MI", "latitude": "41.92140000", "longitude": "-83.49426000"}, {"name": "Montague", "countryCode": "US", "stateCode": "MI", "latitude": "43.41668000", "longitude": "-86.35701000"}, {"name": "Montcalm County", "countryCode": "US", "stateCode": "MI", "latitude": "43.31096000", "longitude": "-85.15252000"}, {"name": "Montmorency County", "countryCode": "US", "stateCode": "MI", "latitude": "45.02755000", "longitude": "-84.12721000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.17669000", "longitude": "-83.89274000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "41.71949000", "longitude": "-84.21800000"}, {"name": "Mount Clemens", "countryCode": "US", "stateCode": "MI", "latitude": "42.59726000", "longitude": "-82.87798000"}, {"name": "Mount Morris", "countryCode": "US", "stateCode": "MI", "latitude": "43.11864000", "longitude": "-83.69496000"}, {"name": "Mount Pleasant", "countryCode": "US", "stateCode": "MI", "latitude": "43.59781000", "longitude": "-84.76751000"}, {"name": "Munising", "countryCode": "US", "stateCode": "MI", "latitude": "46.41120000", "longitude": "-86.64926000"}, {"name": "Muskegon", "countryCode": "US", "stateCode": "MI", "latitude": "43.23418000", "longitude": "-86.24839000"}, {"name": "Muskegon County", "countryCode": "US", "stateCode": "MI", "latitude": "43.21919000", "longitude": "-86.21246000"}, {"name": "Muskegon Heights", "countryCode": "US", "stateCode": "MI", "latitude": "43.20113000", "longitude": "-86.23895000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.16059000", "longitude": "-84.24606000"}, {"name": "Nashville", "countryCode": "US", "stateCode": "MI", "latitude": "42.60281000", "longitude": "-85.09305000"}, {"name": "Negaunee", "countryCode": "US", "stateCode": "MI", "latitude": "46.49910000", "longitude": "-87.61180000"}, {"name": "New Baltimore", "countryCode": "US", "stateCode": "MI", "latitude": "42.68114000", "longitude": "-82.73686000"}, {"name": "New Buffalo", "countryCode": "US", "stateCode": "MI", "latitude": "41.79393000", "longitude": "-86.74392000"}, {"name": "New Haven", "countryCode": "US", "stateCode": "MI", "latitude": "42.72948000", "longitude": "-82.80131000"}, {"name": "Newaygo", "countryCode": "US", "stateCode": "MI", "latitude": "43.41974000", "longitude": "-85.80005000"}, {"name": "Newaygo County", "countryCode": "US", "stateCode": "MI", "latitude": "43.55417000", "longitude": "-85.80091000"}, {"name": "Newberry", "countryCode": "US", "stateCode": "MI", "latitude": "46.35500000", "longitude": "-85.50956000"}, {"name": "Niles", "countryCode": "US", "stateCode": "MI", "latitude": "41.82977000", "longitude": "-86.25418000"}, {"name": "North Branch", "countryCode": "US", "stateCode": "MI", "latitude": "43.22947000", "longitude": "-83.19661000"}, {"name": "North Muskegon", "countryCode": "US", "stateCode": "MI", "latitude": "43.25613000", "longitude": "-86.26756000"}, {"name": "Northview", "countryCode": "US", "stateCode": "MI", "latitude": "43.04558000", "longitude": "-85.60059000"}, {"name": "Northville", "countryCode": "US", "stateCode": "MI", "latitude": "42.43115000", "longitude": "-83.48327000"}, {"name": "Norton Shores", "countryCode": "US", "stateCode": "MI", "latitude": "43.16890000", "longitude": "-86.26395000"}, {"name": "Norway", "countryCode": "US", "stateCode": "MI", "latitude": "45.78690000", "longitude": "-87.90374000"}, {"name": "Novi", "countryCode": "US", "stateCode": "MI", "latitude": "42.48059000", "longitude": "-83.47549000"}, {"name": "Oak Park", "countryCode": "US", "stateCode": "MI", "latitude": "42.45948000", "longitude": "-83.18271000"}, {"name": "Oakland County", "countryCode": "US", "stateCode": "MI", "latitude": "42.66041000", "longitude": "-83.38580000"}, {"name": "Oceana County", "countryCode": "US", "stateCode": "MI", "latitude": "43.68178000", "longitude": "-86.31683000"}, {"name": "Ogemaw County", "countryCode": "US", "stateCode": "MI", "latitude": "44.33494000", "longitude": "-84.12641000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.72226000", "longitude": "-84.42747000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.44143000", "longitude": "-84.92415000"}, {"name": "Ontonagon", "countryCode": "US", "stateCode": "MI", "latitude": "46.87105000", "longitude": "-89.31403000"}, {"name": "Ontonagon County", "countryCode": "US", "stateCode": "MI", "latitude": "46.77749000", "longitude": "-89.30511000"}, {"name": "Orchard Lake", "countryCode": "US", "stateCode": "MI", "latitude": "42.58309000", "longitude": "-83.35938000"}, {"name": "Ortonville", "countryCode": "US", "stateCode": "MI", "latitude": "42.85225000", "longitude": "-83.44300000"}, {"name": "Osceola County", "countryCode": "US", "stateCode": "MI", "latitude": "43.98987000", "longitude": "-85.32528000"}, {"name": "Oscoda County", "countryCode": "US", "stateCode": "MI", "latitude": "44.68175000", "longitude": "-84.12974000"}, {"name": "Otsego", "countryCode": "US", "stateCode": "MI", "latitude": "42.46059000", "longitude": "-85.69641000"}, {"name": "Otsego County", "countryCode": "US", "stateCode": "MI", "latitude": "45.02144000", "longitude": "-84.59898000"}, {"name": "Ottawa County", "countryCode": "US", "stateCode": "MI", "latitude": "43.00264000", "longitude": "-86.17950000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.00586000", "longitude": "-84.37164000"}, {"name": "Owosso", "countryCode": "US", "stateCode": "MI", "latitude": "42.99780000", "longitude": "-84.17664000"}, {"name": "Oxford", "countryCode": "US", "stateCode": "MI", "latitude": "42.82475000", "longitude": "-83.26466000"}, {"name": "Parchment", "countryCode": "US", "stateCode": "MI", "latitude": "42.32810000", "longitude": "-85.56973000"}, {"name": "<PERSON><PERSON>w", "countryCode": "US", "stateCode": "MI", "latitude": "42.21782000", "longitude": "-85.89112000"}, {"name": "Paw Paw Lake", "countryCode": "US", "stateCode": "MI", "latitude": "42.21226000", "longitude": "-86.27197000"}, {"name": "Pearl Beach", "countryCode": "US", "stateCode": "MI", "latitude": "42.62670000", "longitude": "-82.59769000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.82642000", "longitude": "-84.21941000"}, {"name": "Petersburg", "countryCode": "US", "stateCode": "MI", "latitude": "41.90116000", "longitude": "-83.71494000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "45.37334000", "longitude": "-84.95533000"}, {"name": "<PERSON>eon", "countryCode": "US", "stateCode": "MI", "latitude": "43.83002000", "longitude": "-83.26996000"}, {"name": "Pinckney", "countryCode": "US", "stateCode": "MI", "latitude": "42.45700000", "longitude": "-83.94791000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.85363000", "longitude": "-83.96499000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.44004000", "longitude": "-85.64890000"}, {"name": "Pleasant Ridge", "countryCode": "US", "stateCode": "MI", "latitude": "42.47115000", "longitude": "-83.14215000"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "MI", "latitude": "42.37143000", "longitude": "-83.47021000"}, {"name": "Pontiac", "countryCode": "US", "stateCode": "MI", "latitude": "42.63892000", "longitude": "-83.29105000"}, {"name": "Port Huron", "countryCode": "US", "stateCode": "MI", "latitude": "42.97086000", "longitude": "-82.42491000"}, {"name": "Portage", "countryCode": "US", "stateCode": "MI", "latitude": "42.20115000", "longitude": "-85.58000000"}, {"name": "Portland", "countryCode": "US", "stateCode": "MI", "latitude": "42.86920000", "longitude": "-84.90305000"}, {"name": "Potterville", "countryCode": "US", "stateCode": "MI", "latitude": "42.62920000", "longitude": "-84.73887000"}, {"name": "Presque Isle County", "countryCode": "US", "stateCode": "MI", "latitude": "45.39845000", "longitude": "-83.84354000"}, {"name": "Prudenville", "countryCode": "US", "stateCode": "MI", "latitude": "44.29835000", "longitude": "-84.65197000"}, {"name": "Quincy", "countryCode": "US", "stateCode": "MI", "latitude": "41.94421000", "longitude": "-84.88385000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "45.80635000", "longitude": "-87.98846000"}, {"name": "Rapid City", "countryCode": "US", "stateCode": "MI", "latitude": "44.83445000", "longitude": "-85.28256000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.18947000", "longitude": "-85.93699000"}, {"name": "Reading", "countryCode": "US", "stateCode": "MI", "latitude": "41.83949000", "longitude": "-84.74801000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.38337000", "longitude": "-83.29660000"}, {"name": "Reed City", "countryCode": "US", "stateCode": "MI", "latitude": "43.87502000", "longitude": "-85.51005000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.45058000", "longitude": "-83.69635000"}, {"name": "Richmond", "countryCode": "US", "stateCode": "MI", "latitude": "42.80920000", "longitude": "-82.75576000"}, {"name": "River Rouge", "countryCode": "US", "stateCode": "MI", "latitude": "42.27337000", "longitude": "-83.13437000"}, {"name": "Riverview", "countryCode": "US", "stateCode": "MI", "latitude": "42.17421000", "longitude": "-83.17937000"}, {"name": "Rochester", "countryCode": "US", "stateCode": "MI", "latitude": "42.68059000", "longitude": "-83.13382000"}, {"name": "Rochester Hills", "countryCode": "US", "stateCode": "MI", "latitude": "42.65837000", "longitude": "-83.14993000"}, {"name": "Rockford", "countryCode": "US", "stateCode": "MI", "latitude": "43.12003000", "longitude": "-85.56003000"}, {"name": "Rockwood", "countryCode": "US", "stateCode": "MI", "latitude": "42.07088000", "longitude": "-83.24660000"}, {"name": "Rogers City", "countryCode": "US", "stateCode": "MI", "latitude": "45.42140000", "longitude": "-83.81833000"}, {"name": "Romeo", "countryCode": "US", "stateCode": "MI", "latitude": "42.80281000", "longitude": "-83.01299000"}, {"name": "Romulus", "countryCode": "US", "stateCode": "MI", "latitude": "42.22226000", "longitude": "-83.39660000"}, {"name": "Roosevelt Park", "countryCode": "US", "stateCode": "MI", "latitude": "43.19640000", "longitude": "-86.27228000"}, {"name": "Roscommon", "countryCode": "US", "stateCode": "MI", "latitude": "44.49835000", "longitude": "-84.59197000"}, {"name": "Roscommon County", "countryCode": "US", "stateCode": "MI", "latitude": "44.33561000", "longitude": "-84.61160000"}, {"name": "Roseville", "countryCode": "US", "stateCode": "MI", "latitude": "42.49726000", "longitude": "-82.93714000"}, {"name": "Royal Oak", "countryCode": "US", "stateCode": "MI", "latitude": "42.48948000", "longitude": "-83.14465000"}, {"name": "Saginaw", "countryCode": "US", "stateCode": "MI", "latitude": "43.41947000", "longitude": "-83.95081000"}, {"name": "Saginaw County", "countryCode": "US", "stateCode": "MI", "latitude": "43.33503000", "longitude": "-84.05319000"}, {"name": "Saginaw Township North", "countryCode": "US", "stateCode": "MI", "latitude": "43.46004000", "longitude": "-84.00674000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.29697000", "longitude": "-84.14053000"}, {"name": "Saint Clair", "countryCode": "US", "stateCode": "MI", "latitude": "42.82087000", "longitude": "-82.48602000"}, {"name": "Saint Clair County", "countryCode": "US", "stateCode": "MI", "latitude": "42.93112000", "longitude": "-82.66437000"}, {"name": "Saint Clair Shores", "countryCode": "US", "stateCode": "MI", "latitude": "42.49698000", "longitude": "-82.88881000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "44.36363000", "longitude": "-84.41029000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "45.86614000", "longitude": "-84.72751000"}, {"name": "Saint Johns", "countryCode": "US", "stateCode": "MI", "latitude": "43.00114000", "longitude": "-84.55915000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.10976000", "longitude": "-86.48002000"}, {"name": "Saint Joseph County", "countryCode": "US", "stateCode": "MI", "latitude": "41.91441000", "longitude": "-85.52774000"}, {"name": "Saint Louis", "countryCode": "US", "stateCode": "MI", "latitude": "43.40836000", "longitude": "-84.60667000"}, {"name": "Saline", "countryCode": "US", "stateCode": "MI", "latitude": "42.16671000", "longitude": "-83.78161000"}, {"name": "Sand Lake", "countryCode": "US", "stateCode": "MI", "latitude": "44.31918000", "longitude": "-83.68470000"}, {"name": "Sandusky", "countryCode": "US", "stateCode": "MI", "latitude": "43.42030000", "longitude": "-82.82966000"}, {"name": "Sanilac County", "countryCode": "US", "stateCode": "MI", "latitude": "43.44331000", "longitude": "-82.64575000"}, {"name": "Saranac", "countryCode": "US", "stateCode": "MI", "latitude": "42.92948000", "longitude": "-85.21307000"}, {"name": "Sault Ste. Marie", "countryCode": "US", "stateCode": "MI", "latitude": "46.49530000", "longitude": "-84.34532000"}, {"name": "Schoolcraft", "countryCode": "US", "stateCode": "MI", "latitude": "42.11421000", "longitude": "-85.63778000"}, {"name": "Schoolcraft County", "countryCode": "US", "stateCode": "MI", "latitude": "46.04249000", "longitude": "-86.17730000"}, {"name": "Scottville", "countryCode": "US", "stateCode": "MI", "latitude": "43.95473000", "longitude": "-86.28008000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.73224000", "longitude": "-83.45107000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.60862000", "longitude": "-86.36396000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.52447000", "longitude": "-84.69473000"}, {"name": "Shiawassee County", "countryCode": "US", "stateCode": "MI", "latitude": "42.95373000", "longitude": "-84.14673000"}, {"name": "Shields", "countryCode": "US", "stateCode": "MI", "latitude": "43.41530000", "longitude": "-84.05637000"}, {"name": "Shorewood-Tower Hills-Harbert", "countryCode": "US", "stateCode": "MI", "latitude": "41.88169000", "longitude": "-86.61409000"}, {"name": "Skidway Lake", "countryCode": "US", "stateCode": "MI", "latitude": "44.18335000", "longitude": "-84.03527000"}, {"name": "South Gull Lake", "countryCode": "US", "stateCode": "MI", "latitude": "42.38754000", "longitude": "-85.39667000"}, {"name": "South Haven", "countryCode": "US", "stateCode": "MI", "latitude": "42.40309000", "longitude": "-86.27364000"}, {"name": "South Lyon", "countryCode": "US", "stateCode": "MI", "latitude": "42.46059000", "longitude": "-83.65161000"}, {"name": "South Monroe", "countryCode": "US", "stateCode": "MI", "latitude": "41.89588000", "longitude": "-83.41771000"}, {"name": "South Rockwood", "countryCode": "US", "stateCode": "MI", "latitude": "42.06393000", "longitude": "-83.26104000"}, {"name": "Southfield", "countryCode": "US", "stateCode": "MI", "latitude": "42.47337000", "longitude": "-83.22187000"}, {"name": "Southgate", "countryCode": "US", "stateCode": "MI", "latitude": "42.21393000", "longitude": "-83.19381000"}, {"name": "Sparta", "countryCode": "US", "stateCode": "MI", "latitude": "43.16086000", "longitude": "-85.71004000"}, {"name": "Spring Arbor", "countryCode": "US", "stateCode": "MI", "latitude": "42.20504000", "longitude": "-84.55274000"}, {"name": "Spring Lake", "countryCode": "US", "stateCode": "MI", "latitude": "43.07696000", "longitude": "-86.19700000"}, {"name": "Springfield", "countryCode": "US", "stateCode": "MI", "latitude": "42.32643000", "longitude": "-85.23916000"}, {"name": "Stambaugh, Iron River", "countryCode": "US", "stateCode": "MI", "latitude": "46.08107000", "longitude": "-88.62708000"}, {"name": "Standish", "countryCode": "US", "stateCode": "MI", "latitude": "43.98308000", "longitude": "-83.95888000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.29253000", "longitude": "-85.08141000"}, {"name": "Sterling Heights", "countryCode": "US", "stateCode": "MI", "latitude": "42.58031000", "longitude": "-83.03020000"}, {"name": "Stevensville", "countryCode": "US", "stateCode": "MI", "latitude": "42.01449000", "longitude": "-86.51947000"}, {"name": "Stockbridge", "countryCode": "US", "stateCode": "MI", "latitude": "42.45115000", "longitude": "-84.18051000"}, {"name": "Stony Point", "countryCode": "US", "stateCode": "MI", "latitude": "41.94143000", "longitude": "-83.26493000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "41.79922000", "longitude": "-85.41915000"}, {"name": "Swartz Creek", "countryCode": "US", "stateCode": "MI", "latitude": "42.95725000", "longitude": "-83.83051000"}, {"name": "Sylvan Lake", "countryCode": "US", "stateCode": "MI", "latitude": "42.61142000", "longitude": "-83.32855000"}, {"name": "Tawas City", "countryCode": "US", "stateCode": "MI", "latitude": "44.26946000", "longitude": "-83.51470000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.24087000", "longitude": "-83.26965000"}, {"name": "Tecumseh", "countryCode": "US", "stateCode": "MI", "latitude": "42.00393000", "longitude": "-83.94494000"}, {"name": "Temperance", "countryCode": "US", "stateCode": "MI", "latitude": "41.77921000", "longitude": "-83.56882000"}, {"name": "Three Oaks", "countryCode": "US", "stateCode": "MI", "latitude": "41.79865000", "longitude": "-86.61058000"}, {"name": "Three Rivers", "countryCode": "US", "stateCode": "MI", "latitude": "41.94394000", "longitude": "-85.63249000"}, {"name": "Traverse City", "countryCode": "US", "stateCode": "MI", "latitude": "44.76306000", "longitude": "-85.62063000"}, {"name": "Trenton", "countryCode": "US", "stateCode": "MI", "latitude": "42.13949000", "longitude": "-83.17826000"}, {"name": "Trowbridge Park", "countryCode": "US", "stateCode": "MI", "latitude": "46.55660000", "longitude": "-87.43736000"}, {"name": "Troy", "countryCode": "US", "stateCode": "MI", "latitude": "42.60559000", "longitude": "-83.14993000"}, {"name": "Tuscola County", "countryCode": "US", "stateCode": "MI", "latitude": "43.49134000", "longitude": "-83.43987000"}, {"name": "Twin Lake", "countryCode": "US", "stateCode": "MI", "latitude": "43.36279000", "longitude": "-86.16478000"}, {"name": "Union City", "countryCode": "US", "stateCode": "MI", "latitude": "42.06671000", "longitude": "-85.13609000"}, {"name": "Utica", "countryCode": "US", "stateCode": "MI", "latitude": "42.62614000", "longitude": "-83.03354000"}, {"name": "Van Buren County", "countryCode": "US", "stateCode": "MI", "latitude": "42.28511000", "longitude": "-86.30642000"}, {"name": "Vandercook Lake", "countryCode": "US", "stateCode": "MI", "latitude": "42.19337000", "longitude": "-84.39107000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.37197000", "longitude": "-83.58329000"}, {"name": "Vicksburg", "countryCode": "US", "stateCode": "MI", "latitude": "42.12005000", "longitude": "-85.53278000"}, {"name": "W<PERSON>ust<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.82781000", "longitude": "-84.70082000"}, {"name": "Wakefield", "countryCode": "US", "stateCode": "MI", "latitude": "46.47523000", "longitude": "-89.93989000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "43.00141000", "longitude": "-85.76809000"}, {"name": "Walled Lake", "countryCode": "US", "stateCode": "MI", "latitude": "42.53781000", "longitude": "-83.48105000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.49044000", "longitude": "-83.01304000"}, {"name": "Washtenaw County", "countryCode": "US", "stateCode": "MI", "latitude": "42.25323000", "longitude": "-83.83877000"}, {"name": "Waterford", "countryCode": "US", "stateCode": "MI", "latitude": "42.69303000", "longitude": "-83.41181000"}, {"name": "Watervliet", "countryCode": "US", "stateCode": "MI", "latitude": "42.18671000", "longitude": "-86.26058000"}, {"name": "Waverly", "countryCode": "US", "stateCode": "MI", "latitude": "42.73920000", "longitude": "-84.62081000"}, {"name": "Wayland", "countryCode": "US", "stateCode": "MI", "latitude": "42.67392000", "longitude": "-85.64474000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.28143000", "longitude": "-83.38632000"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "MI", "latitude": "42.28478000", "longitude": "-83.26113000"}, {"name": "Webberville", "countryCode": "US", "stateCode": "MI", "latitude": "42.66698000", "longitude": "-84.17413000"}, {"name": "West Bloomfield Township", "countryCode": "US", "stateCode": "MI", "latitude": "42.56891000", "longitude": "-83.38356000"}, {"name": "West Branch", "countryCode": "US", "stateCode": "MI", "latitude": "44.27641000", "longitude": "-84.23861000"}, {"name": "West Ishpeming", "countryCode": "US", "stateCode": "MI", "latitude": "46.48355000", "longitude": "-87.70097000"}, {"name": "West Monroe", "countryCode": "US", "stateCode": "MI", "latitude": "41.91393000", "longitude": "-83.43160000"}, {"name": "Westland", "countryCode": "US", "stateCode": "MI", "latitude": "42.32420000", "longitude": "-83.40021000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.30282000", "longitude": "-85.63362000"}, {"name": "Wexford County", "countryCode": "US", "stateCode": "MI", "latitude": "44.33835000", "longitude": "-85.57842000"}, {"name": "White Cloud", "countryCode": "US", "stateCode": "MI", "latitude": "43.55030000", "longitude": "-85.77200000"}, {"name": "White Pigeon", "countryCode": "US", "stateCode": "MI", "latitude": "41.79811000", "longitude": "-85.64332000"}, {"name": "Whitehall", "countryCode": "US", "stateCode": "MI", "latitude": "43.41001000", "longitude": "-86.34868000"}, {"name": "Whitmore Lake", "countryCode": "US", "stateCode": "MI", "latitude": "42.43970000", "longitude": "-83.74530000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI", "latitude": "42.68892000", "longitude": "-84.28302000"}, {"name": "Wixom", "countryCode": "US", "stateCode": "MI", "latitude": "42.52476000", "longitude": "-83.53633000"}, {"name": "Wolf Lake", "countryCode": "US", "stateCode": "MI", "latitude": "43.25474000", "longitude": "-86.10978000"}, {"name": "Wolverine Lake", "countryCode": "US", "stateCode": "MI", "latitude": "42.55670000", "longitude": "-83.47383000"}, {"name": "Woodhaven", "countryCode": "US", "stateCode": "MI", "latitude": "42.13893000", "longitude": "-83.24160000"}, {"name": "Woodland Beach", "countryCode": "US", "stateCode": "MI", "latitude": "41.94005000", "longitude": "-83.31326000"}, {"name": "Wyandotte", "countryCode": "US", "stateCode": "MI", "latitude": "42.21421000", "longitude": "-83.14992000"}, {"name": "Wyoming", "countryCode": "US", "stateCode": "MI", "latitude": "42.91336000", "longitude": "-85.70531000"}, {"name": "Yale", "countryCode": "US", "stateCode": "MI", "latitude": "43.13003000", "longitude": "-82.79826000"}, {"name": "Ypsilanti", "countryCode": "US", "stateCode": "MI", "latitude": "42.24115000", "longitude": "-83.61299000"}, {"name": "Zeeland", "countryCode": "US", "stateCode": "MI", "latitude": "42.81252000", "longitude": "-86.01865000"}, {"name": "Zilwaukee", "countryCode": "US", "stateCode": "MI", "latitude": "43.47641000", "longitude": "-83.92053000"}]