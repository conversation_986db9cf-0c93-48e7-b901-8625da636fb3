[{"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Albion", "countryCode": "US", "stateCode": "MI"}, {"name": "Alcona County", "countryCode": "US", "stateCode": "MI"}, {"name": "Alger County", "countryCode": "US", "stateCode": "MI"}, {"name": "Algonac", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Allegan County", "countryCode": "US", "stateCode": "MI"}, {"name": "Allen Park", "countryCode": "US", "stateCode": "MI"}, {"name": "Allendale", "countryCode": "US", "stateCode": "MI"}, {"name": "Alma", "countryCode": "US", "stateCode": "MI"}, {"name": "Almont", "countryCode": "US", "stateCode": "MI"}, {"name": "Alpena", "countryCode": "US", "stateCode": "MI"}, {"name": "Alpena County", "countryCode": "US", "stateCode": "MI"}, {"name": "Ann Arbor", "countryCode": "US", "stateCode": "MI"}, {"name": "Antrim County", "countryCode": "US", "stateCode": "MI"}, {"name": "Arenac County", "countryCode": "US", "stateCode": "MI"}, {"name": "Argentine", "countryCode": "US", "stateCode": "MI"}, {"name": "Armada", "countryCode": "US", "stateCode": "MI"}, {"name": "Athens", "countryCode": "US", "stateCode": "MI"}, {"name": "Atlanta", "countryCode": "US", "stateCode": "MI"}, {"name": "Au Sable", "countryCode": "US", "stateCode": "MI"}, {"name": "Auburn", "countryCode": "US", "stateCode": "MI"}, {"name": "Auburn Hills", "countryCode": "US", "stateCode": "MI"}, {"name": "Bad Axe", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Bangor", "countryCode": "US", "stateCode": "MI"}, {"name": "Baraga", "countryCode": "US", "stateCode": "MI"}, {"name": "Baraga County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>-Millers Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Barry County", "countryCode": "US", "stateCode": "MI"}, {"name": "Bath", "countryCode": "US", "stateCode": "MI"}, {"name": "Battle Creek", "countryCode": "US", "stateCode": "MI"}, {"name": "Bay City", "countryCode": "US", "stateCode": "MI"}, {"name": "Bay County", "countryCode": "US", "stateCode": "MI"}, {"name": "Bay Harbor", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Beecher", "countryCode": "US", "stateCode": "MI"}, {"name": "Beechwood", "countryCode": "US", "stateCode": "MI"}, {"name": "Belding", "countryCode": "US", "stateCode": "MI"}, {"name": "Bellaire", "countryCode": "US", "stateCode": "MI"}, {"name": "Belleville", "countryCode": "US", "stateCode": "MI"}, {"name": "Bellevue", "countryCode": "US", "stateCode": "MI"}, {"name": "Benton Harbor", "countryCode": "US", "stateCode": "MI"}, {"name": "Benton Heights", "countryCode": "US", "stateCode": "MI"}, {"name": "Benzie County", "countryCode": "US", "stateCode": "MI"}, {"name": "Berkley", "countryCode": "US", "stateCode": "MI"}, {"name": "Berrien County", "countryCode": "US", "stateCode": "MI"}, {"name": "Berrien Springs", "countryCode": "US", "stateCode": "MI"}, {"name": "Bessemer", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Beverly Hills", "countryCode": "US", "stateCode": "MI"}, {"name": "Big Rapids", "countryCode": "US", "stateCode": "MI"}, {"name": "Bingham Farms", "countryCode": "US", "stateCode": "MI"}, {"name": "Birch Run", "countryCode": "US", "stateCode": "MI"}, {"name": "Birmingham", "countryCode": "US", "stateCode": "MI"}, {"name": "Blissfield", "countryCode": "US", "stateCode": "MI"}, {"name": "Bloomfield Hills", "countryCode": "US", "stateCode": "MI"}, {"name": "Boyne City", "countryCode": "US", "stateCode": "MI"}, {"name": "Branch County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Bridgeport", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Brighton", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Brooklyn", "countryCode": "US", "stateCode": "MI"}, {"name": "Brown City", "countryCode": "US", "stateCode": "MI"}, {"name": "Brownlee Park", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Buena Vista", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Byron Center", "countryCode": "US", "stateCode": "MI"}, {"name": "Cadillac", "countryCode": "US", "stateCode": "MI"}, {"name": "Caledonia", "countryCode": "US", "stateCode": "MI"}, {"name": "Calhoun County", "countryCode": "US", "stateCode": "MI"}, {"name": "Canadian Lakes", "countryCode": "US", "stateCode": "MI"}, {"name": "Canton", "countryCode": "US", "stateCode": "MI"}, {"name": "Capac", "countryCode": "US", "stateCode": "MI"}, {"name": "Carleton", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Carson City", "countryCode": "US", "stateCode": "MI"}, {"name": "Cass City", "countryCode": "US", "stateCode": "MI"}, {"name": "Cass County", "countryCode": "US", "stateCode": "MI"}, {"name": "Cassopolis", "countryCode": "US", "stateCode": "MI"}, {"name": "Cedar Springs", "countryCode": "US", "stateCode": "MI"}, {"name": "Center Line", "countryCode": "US", "stateCode": "MI"}, {"name": "Centreville", "countryCode": "US", "stateCode": "MI"}, {"name": "Charlevoix", "countryCode": "US", "stateCode": "MI"}, {"name": "Charlevoix County", "countryCode": "US", "stateCode": "MI"}, {"name": "Charlotte", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Cheboygan County", "countryCode": "US", "stateCode": "MI"}, {"name": "Chelsea", "countryCode": "US", "stateCode": "MI"}, {"name": "Chesaning", "countryCode": "US", "stateCode": "MI"}, {"name": "Chippewa County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Clare County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Clinton County", "countryCode": "US", "stateCode": "MI"}, {"name": "Clinton Township", "countryCode": "US", "stateCode": "MI"}, {"name": "Clio", "countryCode": "US", "stateCode": "MI"}, {"name": "Coldwater", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Coloma", "countryCode": "US", "stateCode": "MI"}, {"name": "Colon", "countryCode": "US", "stateCode": "MI"}, {"name": "Comstock Northwest", "countryCode": "US", "stateCode": "MI"}, {"name": "Comstock Park", "countryCode": "US", "stateCode": "MI"}, {"name": "Concord", "countryCode": "US", "stateCode": "MI"}, {"name": "Constantine", "countryCode": "US", "stateCode": "MI"}, {"name": "Coopersville", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Crystal Falls", "countryCode": "US", "stateCode": "MI"}, {"name": "Cutlerville", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Dearborn", "countryCode": "US", "stateCode": "MI"}, {"name": "Dearborn Heights", "countryCode": "US", "stateCode": "MI"}, {"name": "Decatur", "countryCode": "US", "stateCode": "MI"}, {"name": "Delta County", "countryCode": "US", "stateCode": "MI"}, {"name": "Detroit", "countryCode": "US", "stateCode": "MI"}, {"name": "Detroit Beach", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Dickinson County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Dollar Bay", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Dowagiac", "countryCode": "US", "stateCode": "MI"}, {"name": "Dundee", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Eagle River", "countryCode": "US", "stateCode": "MI"}, {"name": "East Grand Rapids", "countryCode": "US", "stateCode": "MI"}, {"name": "East Jordan", "countryCode": "US", "stateCode": "MI"}, {"name": "East Lansing", "countryCode": "US", "stateCode": "MI"}, {"name": "East Tawas", "countryCode": "US", "stateCode": "MI"}, {"name": "Eastpointe", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Eaton County", "countryCode": "US", "stateCode": "MI"}, {"name": "Eaton Rapids", "countryCode": "US", "stateCode": "MI"}, {"name": "Ecorse", "countryCode": "US", "stateCode": "MI"}, {"name": "Edgemont Park", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Edwardsburg", "countryCode": "US", "stateCode": "MI"}, {"name": "Elk Rapids", "countryCode": "US", "stateCode": "MI"}, {"name": "Emmet County", "countryCode": "US", "stateCode": "MI"}, {"name": "Escanaba", "countryCode": "US", "stateCode": "MI"}, {"name": "Essexville", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Fair Plain", "countryCode": "US", "stateCode": "MI"}, {"name": "Farmington", "countryCode": "US", "stateCode": "MI"}, {"name": "Farmington Hills", "countryCode": "US", "stateCode": "MI"}, {"name": "Fennville", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Ferndale", "countryCode": "US", "stateCode": "MI"}, {"name": "Ferrysburg", "countryCode": "US", "stateCode": "MI"}, {"name": "Flat Rock", "countryCode": "US", "stateCode": "MI"}, {"name": "Flint", "countryCode": "US", "stateCode": "MI"}, {"name": "Flushing", "countryCode": "US", "stateCode": "MI"}, {"name": "Forest Hills", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Fowlerville", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Frankfort", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Freeland", "countryCode": "US", "stateCode": "MI"}, {"name": "Fremont", "countryCode": "US", "stateCode": "MI"}, {"name": "Fruitport", "countryCode": "US", "stateCode": "MI"}, {"name": "Galesburg", "countryCode": "US", "stateCode": "MI"}, {"name": "Garden City", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Genesee County", "countryCode": "US", "stateCode": "MI"}, {"name": "Gibraltar", "countryCode": "US", "stateCode": "MI"}, {"name": "Gladstone", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Gladwin County", "countryCode": "US", "stateCode": "MI"}, {"name": "Gogebic County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Grand Blanc", "countryCode": "US", "stateCode": "MI"}, {"name": "Grand Haven", "countryCode": "US", "stateCode": "MI"}, {"name": "Grand Ledge", "countryCode": "US", "stateCode": "MI"}, {"name": "Grand Rapids", "countryCode": "US", "stateCode": "MI"}, {"name": "Grand Traverse County", "countryCode": "US", "stateCode": "MI"}, {"name": "Grandville", "countryCode": "US", "stateCode": "MI"}, {"name": "Grass Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Gratiot County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Greenville", "countryCode": "US", "stateCode": "MI"}, {"name": "G<PERSON><PERSON>ckville", "countryCode": "US", "stateCode": "MI"}, {"name": "Grosse Ile", "countryCode": "US", "stateCode": "MI"}, {"name": "Grosse Pointe", "countryCode": "US", "stateCode": "MI"}, {"name": "Grosse Pointe Farms", "countryCode": "US", "stateCode": "MI"}, {"name": "Grosse Pointe Park", "countryCode": "US", "stateCode": "MI"}, {"name": "Grosse Pointe Shores", "countryCode": "US", "stateCode": "MI"}, {"name": "Grosse Pointe Woods", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Hamtramck", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Harbor Beach", "countryCode": "US", "stateCode": "MI"}, {"name": "Harbor Springs", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Harrisville", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Hartford", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Hastings", "countryCode": "US", "stateCode": "MI"}, {"name": "Hazel Park", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Highland Park", "countryCode": "US", "stateCode": "MI"}, {"name": "Hillsdale", "countryCode": "US", "stateCode": "MI"}, {"name": "Hillsdale County", "countryCode": "US", "stateCode": "MI"}, {"name": "Holland", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Houghton", "countryCode": "US", "stateCode": "MI"}, {"name": "Houghton County", "countryCode": "US", "stateCode": "MI"}, {"name": "Houghton Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Howard City", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Hubbard Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Hudsonville", "countryCode": "US", "stateCode": "MI"}, {"name": "Huntington Woods", "countryCode": "US", "stateCode": "MI"}, {"name": "Huron County", "countryCode": "US", "stateCode": "MI"}, {"name": "Imlay City", "countryCode": "US", "stateCode": "MI"}, {"name": "Indian River", "countryCode": "US", "stateCode": "MI"}, {"name": "Ingham County", "countryCode": "US", "stateCode": "MI"}, {"name": "Inkster", "countryCode": "US", "stateCode": "MI"}, {"name": "Ionia", "countryCode": "US", "stateCode": "MI"}, {"name": "Ionia County", "countryCode": "US", "stateCode": "MI"}, {"name": "Iosco County", "countryCode": "US", "stateCode": "MI"}, {"name": "Iron County", "countryCode": "US", "stateCode": "MI"}, {"name": "Iron Mountain", "countryCode": "US", "stateCode": "MI"}, {"name": "Iron River", "countryCode": "US", "stateCode": "MI"}, {"name": "Ironwood", "countryCode": "US", "stateCode": "MI"}, {"name": "Isabella County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Ithaca", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Jonesville", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON> <PERSON><PERSON> Air Force Base", "countryCode": "US", "stateCode": "MI"}, {"name": "Kalamazoo", "countryCode": "US", "stateCode": "MI"}, {"name": "Kalamazoo County", "countryCode": "US", "stateCode": "MI"}, {"name": "Kalkaska", "countryCode": "US", "stateCode": "MI"}, {"name": "Kalkaska County", "countryCode": "US", "stateCode": "MI"}, {"name": "Keego Harbor", "countryCode": "US", "stateCode": "MI"}, {"name": "Kent City", "countryCode": "US", "stateCode": "MI"}, {"name": "Kent County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Keweenaw County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Kingsford", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "L'Anse", "countryCode": "US", "stateCode": "MI"}, {"name": "Laingsburg", "countryCode": "US", "stateCode": "MI"}, {"name": "Lake City", "countryCode": "US", "stateCode": "MI"}, {"name": "Lake County", "countryCode": "US", "stateCode": "MI"}, {"name": "Lake Fenton", "countryCode": "US", "stateCode": "MI"}, {"name": "Lake Isabella", "countryCode": "US", "stateCode": "MI"}, {"name": "Lake Michigan Beach", "countryCode": "US", "stateCode": "MI"}, {"name": "Lake Odessa", "countryCode": "US", "stateCode": "MI"}, {"name": "Lake Orion", "countryCode": "US", "stateCode": "MI"}, {"name": "Lakeview", "countryCode": "US", "stateCode": "MI"}, {"name": "Lakewood Club", "countryCode": "US", "stateCode": "MI"}, {"name": "Lambertville", "countryCode": "US", "stateCode": "MI"}, {"name": "Lansing", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Lapeer County", "countryCode": "US", "stateCode": "MI"}, {"name": "Lathrup Village", "countryCode": "US", "stateCode": "MI"}, {"name": "Laurium", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Leelanau County", "countryCode": "US", "stateCode": "MI"}, {"name": "Leland", "countryCode": "US", "stateCode": "MI"}, {"name": "Lenawee County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Level Park-Oak Park", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Lexington", "countryCode": "US", "stateCode": "MI"}, {"name": "Lincoln Park", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Litchfield", "countryCode": "US", "stateCode": "MI"}, {"name": "Livingston County", "countryCode": "US", "stateCode": "MI"}, {"name": "Livonia", "countryCode": "US", "stateCode": "MI"}, {"name": "Lowell", "countryCode": "US", "stateCode": "MI"}, {"name": "Luce County", "countryCode": "US", "stateCode": "MI"}, {"name": "Ludington", "countryCode": "US", "stateCode": "MI"}, {"name": "Luna Pier", "countryCode": "US", "stateCode": "MI"}, {"name": "Mackinac County", "countryCode": "US", "stateCode": "MI"}, {"name": "Macomb County", "countryCode": "US", "stateCode": "MI"}, {"name": "Madison Heights", "countryCode": "US", "stateCode": "MI"}, {"name": "Mancelona", "countryCode": "US", "stateCode": "MI"}, {"name": "Manchester", "countryCode": "US", "stateCode": "MI"}, {"name": "Manistee", "countryCode": "US", "stateCode": "MI"}, {"name": "Manistee County", "countryCode": "US", "stateCode": "MI"}, {"name": "Manistique", "countryCode": "US", "stateCode": "MI"}, {"name": "Manitou Beach-Devils Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Manton", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Marine City", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Marquette", "countryCode": "US", "stateCode": "MI"}, {"name": "Marquette County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Marysville", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Mason County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Mecosta County", "countryCode": "US", "stateCode": "MI"}, {"name": "Melvindale", "countryCode": "US", "stateCode": "MI"}, {"name": "Memphis", "countryCode": "US", "stateCode": "MI"}, {"name": "Menominee", "countryCode": "US", "stateCode": "MI"}, {"name": "Menominee County", "countryCode": "US", "stateCode": "MI"}, {"name": "Michigan Center", "countryCode": "US", "stateCode": "MI"}, {"name": "Middleville", "countryCode": "US", "stateCode": "MI"}, {"name": "Midland", "countryCode": "US", "stateCode": "MI"}, {"name": "Midland County", "countryCode": "US", "stateCode": "MI"}, {"name": "Milan", "countryCode": "US", "stateCode": "MI"}, {"name": "Milford", "countryCode": "US", "stateCode": "MI"}, {"name": "Millington", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Missaukee County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Monroe County", "countryCode": "US", "stateCode": "MI"}, {"name": "Montague", "countryCode": "US", "stateCode": "MI"}, {"name": "Montcalm County", "countryCode": "US", "stateCode": "MI"}, {"name": "Montmorency County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Mount Clemens", "countryCode": "US", "stateCode": "MI"}, {"name": "Mount Morris", "countryCode": "US", "stateCode": "MI"}, {"name": "Mount Pleasant", "countryCode": "US", "stateCode": "MI"}, {"name": "Munising", "countryCode": "US", "stateCode": "MI"}, {"name": "Muskegon", "countryCode": "US", "stateCode": "MI"}, {"name": "Muskegon County", "countryCode": "US", "stateCode": "MI"}, {"name": "Muskegon Heights", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Nashville", "countryCode": "US", "stateCode": "MI"}, {"name": "Negaunee", "countryCode": "US", "stateCode": "MI"}, {"name": "New Baltimore", "countryCode": "US", "stateCode": "MI"}, {"name": "New Buffalo", "countryCode": "US", "stateCode": "MI"}, {"name": "New Haven", "countryCode": "US", "stateCode": "MI"}, {"name": "Newaygo", "countryCode": "US", "stateCode": "MI"}, {"name": "Newaygo County", "countryCode": "US", "stateCode": "MI"}, {"name": "Newberry", "countryCode": "US", "stateCode": "MI"}, {"name": "Niles", "countryCode": "US", "stateCode": "MI"}, {"name": "North Branch", "countryCode": "US", "stateCode": "MI"}, {"name": "North Muskegon", "countryCode": "US", "stateCode": "MI"}, {"name": "Northview", "countryCode": "US", "stateCode": "MI"}, {"name": "Northville", "countryCode": "US", "stateCode": "MI"}, {"name": "Norton Shores", "countryCode": "US", "stateCode": "MI"}, {"name": "Norway", "countryCode": "US", "stateCode": "MI"}, {"name": "Novi", "countryCode": "US", "stateCode": "MI"}, {"name": "Oak Park", "countryCode": "US", "stateCode": "MI"}, {"name": "Oakland County", "countryCode": "US", "stateCode": "MI"}, {"name": "Oceana County", "countryCode": "US", "stateCode": "MI"}, {"name": "Ogemaw County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Ontonagon", "countryCode": "US", "stateCode": "MI"}, {"name": "Ontonagon County", "countryCode": "US", "stateCode": "MI"}, {"name": "Orchard Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Ortonville", "countryCode": "US", "stateCode": "MI"}, {"name": "Osceola County", "countryCode": "US", "stateCode": "MI"}, {"name": "Oscoda County", "countryCode": "US", "stateCode": "MI"}, {"name": "Otsego", "countryCode": "US", "stateCode": "MI"}, {"name": "Otsego County", "countryCode": "US", "stateCode": "MI"}, {"name": "Ottawa County", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Owosso", "countryCode": "US", "stateCode": "MI"}, {"name": "Oxford", "countryCode": "US", "stateCode": "MI"}, {"name": "Parchment", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>w", "countryCode": "US", "stateCode": "MI"}, {"name": "Paw Paw Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Pearl Beach", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Petersburg", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>eon", "countryCode": "US", "stateCode": "MI"}, {"name": "Pinckney", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Pleasant Ridge", "countryCode": "US", "stateCode": "MI"}, {"name": "Plymouth", "countryCode": "US", "stateCode": "MI"}, {"name": "Pontiac", "countryCode": "US", "stateCode": "MI"}, {"name": "Port Huron", "countryCode": "US", "stateCode": "MI"}, {"name": "Portage", "countryCode": "US", "stateCode": "MI"}, {"name": "Portland", "countryCode": "US", "stateCode": "MI"}, {"name": "Potterville", "countryCode": "US", "stateCode": "MI"}, {"name": "Presque Isle County", "countryCode": "US", "stateCode": "MI"}, {"name": "Prudenville", "countryCode": "US", "stateCode": "MI"}, {"name": "Quincy", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Rapid City", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Reading", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Reed City", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Richmond", "countryCode": "US", "stateCode": "MI"}, {"name": "River Rouge", "countryCode": "US", "stateCode": "MI"}, {"name": "Riverview", "countryCode": "US", "stateCode": "MI"}, {"name": "Rochester", "countryCode": "US", "stateCode": "MI"}, {"name": "Rochester Hills", "countryCode": "US", "stateCode": "MI"}, {"name": "Rockford", "countryCode": "US", "stateCode": "MI"}, {"name": "Rockwood", "countryCode": "US", "stateCode": "MI"}, {"name": "Rogers City", "countryCode": "US", "stateCode": "MI"}, {"name": "Romeo", "countryCode": "US", "stateCode": "MI"}, {"name": "Romulus", "countryCode": "US", "stateCode": "MI"}, {"name": "Roosevelt Park", "countryCode": "US", "stateCode": "MI"}, {"name": "Roscommon", "countryCode": "US", "stateCode": "MI"}, {"name": "Roscommon County", "countryCode": "US", "stateCode": "MI"}, {"name": "Roseville", "countryCode": "US", "stateCode": "MI"}, {"name": "Royal Oak", "countryCode": "US", "stateCode": "MI"}, {"name": "Saginaw", "countryCode": "US", "stateCode": "MI"}, {"name": "Saginaw County", "countryCode": "US", "stateCode": "MI"}, {"name": "Saginaw Township North", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Saint Clair", "countryCode": "US", "stateCode": "MI"}, {"name": "Saint Clair County", "countryCode": "US", "stateCode": "MI"}, {"name": "Saint Clair Shores", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Saint Johns", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Saint Joseph County", "countryCode": "US", "stateCode": "MI"}, {"name": "Saint Louis", "countryCode": "US", "stateCode": "MI"}, {"name": "Saline", "countryCode": "US", "stateCode": "MI"}, {"name": "Sand Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Sandusky", "countryCode": "US", "stateCode": "MI"}, {"name": "Sanilac County", "countryCode": "US", "stateCode": "MI"}, {"name": "Saranac", "countryCode": "US", "stateCode": "MI"}, {"name": "Sault Ste. Marie", "countryCode": "US", "stateCode": "MI"}, {"name": "Schoolcraft", "countryCode": "US", "stateCode": "MI"}, {"name": "Schoolcraft County", "countryCode": "US", "stateCode": "MI"}, {"name": "Scottville", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Shiawassee County", "countryCode": "US", "stateCode": "MI"}, {"name": "Shields", "countryCode": "US", "stateCode": "MI"}, {"name": "Shorewood-Tower Hills-Harbert", "countryCode": "US", "stateCode": "MI"}, {"name": "Skidway Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "South Gull Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "South Haven", "countryCode": "US", "stateCode": "MI"}, {"name": "South Lyon", "countryCode": "US", "stateCode": "MI"}, {"name": "South Monroe", "countryCode": "US", "stateCode": "MI"}, {"name": "South Rockwood", "countryCode": "US", "stateCode": "MI"}, {"name": "Southfield", "countryCode": "US", "stateCode": "MI"}, {"name": "Southgate", "countryCode": "US", "stateCode": "MI"}, {"name": "Sparta", "countryCode": "US", "stateCode": "MI"}, {"name": "Spring Arbor", "countryCode": "US", "stateCode": "MI"}, {"name": "Spring Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Springfield", "countryCode": "US", "stateCode": "MI"}, {"name": "Stambaugh, Iron River", "countryCode": "US", "stateCode": "MI"}, {"name": "Standish", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Sterling Heights", "countryCode": "US", "stateCode": "MI"}, {"name": "Stevensville", "countryCode": "US", "stateCode": "MI"}, {"name": "Stockbridge", "countryCode": "US", "stateCode": "MI"}, {"name": "Stony Point", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Swartz Creek", "countryCode": "US", "stateCode": "MI"}, {"name": "Sylvan Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Tawas City", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Tecumseh", "countryCode": "US", "stateCode": "MI"}, {"name": "Temperance", "countryCode": "US", "stateCode": "MI"}, {"name": "Three Oaks", "countryCode": "US", "stateCode": "MI"}, {"name": "Three Rivers", "countryCode": "US", "stateCode": "MI"}, {"name": "Traverse City", "countryCode": "US", "stateCode": "MI"}, {"name": "Trenton", "countryCode": "US", "stateCode": "MI"}, {"name": "Trowbridge Park", "countryCode": "US", "stateCode": "MI"}, {"name": "Troy", "countryCode": "US", "stateCode": "MI"}, {"name": "Tuscola County", "countryCode": "US", "stateCode": "MI"}, {"name": "Twin Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Union City", "countryCode": "US", "stateCode": "MI"}, {"name": "Utica", "countryCode": "US", "stateCode": "MI"}, {"name": "Van Buren County", "countryCode": "US", "stateCode": "MI"}, {"name": "Vandercook Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Vicksburg", "countryCode": "US", "stateCode": "MI"}, {"name": "W<PERSON>ust<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Wakefield", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Walled Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Washtenaw County", "countryCode": "US", "stateCode": "MI"}, {"name": "Waterford", "countryCode": "US", "stateCode": "MI"}, {"name": "Watervliet", "countryCode": "US", "stateCode": "MI"}, {"name": "Waverly", "countryCode": "US", "stateCode": "MI"}, {"name": "Wayland", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Wayne County", "countryCode": "US", "stateCode": "MI"}, {"name": "Webberville", "countryCode": "US", "stateCode": "MI"}, {"name": "West Bloomfield Township", "countryCode": "US", "stateCode": "MI"}, {"name": "West Branch", "countryCode": "US", "stateCode": "MI"}, {"name": "West Ishpeming", "countryCode": "US", "stateCode": "MI"}, {"name": "West Monroe", "countryCode": "US", "stateCode": "MI"}, {"name": "Westland", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Wexford County", "countryCode": "US", "stateCode": "MI"}, {"name": "White Cloud", "countryCode": "US", "stateCode": "MI"}, {"name": "White Pigeon", "countryCode": "US", "stateCode": "MI"}, {"name": "Whitehall", "countryCode": "US", "stateCode": "MI"}, {"name": "Whitmore Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MI"}, {"name": "Wixom", "countryCode": "US", "stateCode": "MI"}, {"name": "Wolf Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Wolverine Lake", "countryCode": "US", "stateCode": "MI"}, {"name": "Woodhaven", "countryCode": "US", "stateCode": "MI"}, {"name": "Woodland Beach", "countryCode": "US", "stateCode": "MI"}, {"name": "Wyandotte", "countryCode": "US", "stateCode": "MI"}, {"name": "Wyoming", "countryCode": "US", "stateCode": "MI"}, {"name": "Yale", "countryCode": "US", "stateCode": "MI"}, {"name": "Ypsilanti", "countryCode": "US", "stateCode": "MI"}, {"name": "Zeeland", "countryCode": "US", "stateCode": "MI"}, {"name": "Zilwaukee", "countryCode": "US", "stateCode": "MI"}]