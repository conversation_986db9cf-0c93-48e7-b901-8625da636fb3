[{"name": "Abilene", "countryCode": "US", "stateCode": "KS"}, {"name": "Allen County", "countryCode": "US", "stateCode": "KS"}, {"name": "Alma", "countryCode": "US", "stateCode": "KS"}, {"name": "Altamont", "countryCode": "US", "stateCode": "KS"}, {"name": "Anderson County", "countryCode": "US", "stateCode": "KS"}, {"name": "Andover", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Arkansas City", "countryCode": "US", "stateCode": "KS"}, {"name": "Arma", "countryCode": "US", "stateCode": "KS"}, {"name": "Ashland", "countryCode": "US", "stateCode": "KS"}, {"name": "Atchison", "countryCode": "US", "stateCode": "KS"}, {"name": "Atchison County", "countryCode": "US", "stateCode": "KS"}, {"name": "Atwood", "countryCode": "US", "stateCode": "KS"}, {"name": "Auburn", "countryCode": "US", "stateCode": "KS"}, {"name": "Augusta", "countryCode": "US", "stateCode": "KS"}, {"name": "Baldwin City", "countryCode": "US", "stateCode": "KS"}, {"name": "Barber County", "countryCode": "US", "stateCode": "KS"}, {"name": "Barton County", "countryCode": "US", "stateCode": "KS"}, {"name": "Basehor", "countryCode": "US", "stateCode": "KS"}, {"name": "Baxter Springs", "countryCode": "US", "stateCode": "KS"}, {"name": "Bellaire", "countryCode": "US", "stateCode": "KS"}, {"name": "Belle Plaine", "countryCode": "US", "stateCode": "KS"}, {"name": "Belleville", "countryCode": "US", "stateCode": "KS"}, {"name": "Beloit", "countryCode": "US", "stateCode": "KS"}, {"name": "Bonner Springs", "countryCode": "US", "stateCode": "KS"}, {"name": "Bourbon County", "countryCode": "US", "stateCode": "KS"}, {"name": "Brown County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Burlington", "countryCode": "US", "stateCode": "KS"}, {"name": "Butler County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Carbondale", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Chase County", "countryCode": "US", "stateCode": "KS"}, {"name": "Chautauqua County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Cherokee County", "countryCode": "US", "stateCode": "KS"}, {"name": "Cherryvale", "countryCode": "US", "stateCode": "KS"}, {"name": "Chetopa", "countryCode": "US", "stateCode": "KS"}, {"name": "Cheyenne County", "countryCode": "US", "stateCode": "KS"}, {"name": "Cimarron", "countryCode": "US", "stateCode": "KS"}, {"name": "Clark County", "countryCode": "US", "stateCode": "KS"}, {"name": "Clay Center", "countryCode": "US", "stateCode": "KS"}, {"name": "Clay County", "countryCode": "US", "stateCode": "KS"}, {"name": "Clearwater", "countryCode": "US", "stateCode": "KS"}, {"name": "Cloud County", "countryCode": "US", "stateCode": "KS"}, {"name": "Coffey County", "countryCode": "US", "stateCode": "KS"}, {"name": "Coffeyville", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Coldwater", "countryCode": "US", "stateCode": "KS"}, {"name": "Columbus", "countryCode": "US", "stateCode": "KS"}, {"name": "Colwich", "countryCode": "US", "stateCode": "KS"}, {"name": "Comanche County", "countryCode": "US", "stateCode": "KS"}, {"name": "Concordia", "countryCode": "US", "stateCode": "KS"}, {"name": "Conway Springs", "countryCode": "US", "stateCode": "KS"}, {"name": "Cottonwood Falls", "countryCode": "US", "stateCode": "KS"}, {"name": "Council Grove", "countryCode": "US", "stateCode": "KS"}, {"name": "Cowley County", "countryCode": "US", "stateCode": "KS"}, {"name": "Crawford County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Decatur County", "countryCode": "US", "stateCode": "KS"}, {"name": "Derby", "countryCode": "US", "stateCode": "KS"}, {"name": "Dickinson County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Dodge City", "countryCode": "US", "stateCode": "KS"}, {"name": "Doniphan County", "countryCode": "US", "stateCode": "KS"}, {"name": "Douglas County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Edgerton", "countryCode": "US", "stateCode": "KS"}, {"name": "Edwards County", "countryCode": "US", "stateCode": "KS"}, {"name": "Edwardsville", "countryCode": "US", "stateCode": "KS"}, {"name": "El Dorado", "countryCode": "US", "stateCode": "KS"}, {"name": "Elk County", "countryCode": "US", "stateCode": "KS"}, {"name": "Elkhart", "countryCode": "US", "stateCode": "KS"}, {"name": "Ellin<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Ellis County", "countryCode": "US", "stateCode": "KS"}, {"name": "Ellsworth", "countryCode": "US", "stateCode": "KS"}, {"name": "Ellsworth County", "countryCode": "US", "stateCode": "KS"}, {"name": "Elwood", "countryCode": "US", "stateCode": "KS"}, {"name": "Emporia", "countryCode": "US", "stateCode": "KS"}, {"name": "Erie", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Eureka", "countryCode": "US", "stateCode": "KS"}, {"name": "Fairway", "countryCode": "US", "stateCode": "KS"}, {"name": "Finney County", "countryCode": "US", "stateCode": "KS"}, {"name": "Ford County", "countryCode": "US", "stateCode": "KS"}, {"name": "Fort Riley North", "countryCode": "US", "stateCode": "KS"}, {"name": "Fort Scott", "countryCode": "US", "stateCode": "KS"}, {"name": "Franklin County", "countryCode": "US", "stateCode": "KS"}, {"name": "Fredonia", "countryCode": "US", "stateCode": "KS"}, {"name": "Frontenac", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Garden City", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Geary County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Goodland", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Gove County", "countryCode": "US", "stateCode": "KS"}, {"name": "Graham County", "countryCode": "US", "stateCode": "KS"}, {"name": "Grandview Plaza", "countryCode": "US", "stateCode": "KS"}, {"name": "Grant County", "countryCode": "US", "stateCode": "KS"}, {"name": "Gray County", "countryCode": "US", "stateCode": "KS"}, {"name": "Great Bend", "countryCode": "US", "stateCode": "KS"}, {"name": "Greeley County", "countryCode": "US", "stateCode": "KS"}, {"name": "Greensburg", "countryCode": "US", "stateCode": "KS"}, {"name": "Greenwood County", "countryCode": "US", "stateCode": "KS"}, {"name": "Halstead", "countryCode": "US", "stateCode": "KS"}, {"name": "Hamilton County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Harper County", "countryCode": "US", "stateCode": "KS"}, {"name": "Harvey County", "countryCode": "US", "stateCode": "KS"}, {"name": "Haskell County", "countryCode": "US", "stateCode": "KS"}, {"name": "Haven", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Haysville", "countryCode": "US", "stateCode": "KS"}, {"name": "Herington", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Highland", "countryCode": "US", "stateCode": "KS"}, {"name": "Hill City", "countryCode": "US", "stateCode": "KS"}, {"name": "Hillsboro", "countryCode": "US", "stateCode": "KS"}, {"name": "Hodgeman County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Humboldt", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Independence", "countryCode": "US", "stateCode": "KS"}, {"name": "Inman", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Jackson County", "countryCode": "US", "stateCode": "KS"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "KS"}, {"name": "Jetmore", "countryCode": "US", "stateCode": "KS"}, {"name": "Jewell County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Johnson County", "countryCode": "US", "stateCode": "KS"}, {"name": "Junction City", "countryCode": "US", "stateCode": "KS"}, {"name": "Kansas City", "countryCode": "US", "stateCode": "KS"}, {"name": "Kearny County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Kingman", "countryCode": "US", "stateCode": "KS"}, {"name": "Kingman County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Kiowa", "countryCode": "US", "stateCode": "KS"}, {"name": "Kiowa County", "countryCode": "US", "stateCode": "KS"}, {"name": "La Crosse", "countryCode": "US", "stateCode": "KS"}, {"name": "La Cygne", "countryCode": "US", "stateCode": "KS"}, {"name": "Labette County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Lane County", "countryCode": "US", "stateCode": "KS"}, {"name": "Lansing", "countryCode": "US", "stateCode": "KS"}, {"name": "Larned", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Leavenworth", "countryCode": "US", "stateCode": "KS"}, {"name": "Leavenworth County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Lenexa", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Liberal", "countryCode": "US", "stateCode": "KS"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "KS"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "KS"}, {"name": "Lindsborg", "countryCode": "US", "stateCode": "KS"}, {"name": "Linn County", "countryCode": "US", "stateCode": "KS"}, {"name": "Logan County", "countryCode": "US", "stateCode": "KS"}, {"name": "Louisburg", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Lyon County", "countryCode": "US", "stateCode": "KS"}, {"name": "Lyons", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Manhattan", "countryCode": "US", "stateCode": "KS"}, {"name": "Mankato", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Marion County", "countryCode": "US", "stateCode": "KS"}, {"name": "Marshall County", "countryCode": "US", "stateCode": "KS"}, {"name": "Marysville", "countryCode": "US", "stateCode": "KS"}, {"name": "McConnell AFB", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "McPherson County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Meade County", "countryCode": "US", "stateCode": "KS"}, {"name": "Medicine Lodge", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Miami County", "countryCode": "US", "stateCode": "KS"}, {"name": "Minneapolis", "countryCode": "US", "stateCode": "KS"}, {"name": "Mission", "countryCode": "US", "stateCode": "KS"}, {"name": "Mission Hills", "countryCode": "US", "stateCode": "KS"}, {"name": "Mitchell County", "countryCode": "US", "stateCode": "KS"}, {"name": "Montgomery County", "countryCode": "US", "stateCode": "KS"}, {"name": "Morris County", "countryCode": "US", "stateCode": "KS"}, {"name": "Morton County", "countryCode": "US", "stateCode": "KS"}, {"name": "Mound City", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Nemaha County", "countryCode": "US", "stateCode": "KS"}, {"name": "Neodesha", "countryCode": "US", "stateCode": "KS"}, {"name": "Neosho County", "countryCode": "US", "stateCode": "KS"}, {"name": "Ness City", "countryCode": "US", "stateCode": "KS"}, {"name": "Ness County", "countryCode": "US", "stateCode": "KS"}, {"name": "New Century, KS", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "North Newton", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Norton County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>ley", "countryCode": "US", "stateCode": "KS"}, {"name": "Oberlin", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Osage City", "countryCode": "US", "stateCode": "KS"}, {"name": "Osage County", "countryCode": "US", "stateCode": "KS"}, {"name": "Osawatomie", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Osborne County", "countryCode": "US", "stateCode": "KS"}, {"name": "Oskaloosa", "countryCode": "US", "stateCode": "KS"}, {"name": "Oswego", "countryCode": "US", "stateCode": "KS"}, {"name": "Ottawa", "countryCode": "US", "stateCode": "KS"}, {"name": "Ottawa County", "countryCode": "US", "stateCode": "KS"}, {"name": "Overbrook", "countryCode": "US", "stateCode": "KS"}, {"name": "Overland Park", "countryCode": "US", "stateCode": "KS"}, {"name": "Oxford", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Park City", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Pawnee County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Phillips County", "countryCode": "US", "stateCode": "KS"}, {"name": "Phillipsburg", "countryCode": "US", "stateCode": "KS"}, {"name": "Pittsburg", "countryCode": "US", "stateCode": "KS"}, {"name": "Plains", "countryCode": "US", "stateCode": "KS"}, {"name": "Plainville", "countryCode": "US", "stateCode": "KS"}, {"name": "Pleasanton", "countryCode": "US", "stateCode": "KS"}, {"name": "Pottawatomie County", "countryCode": "US", "stateCode": "KS"}, {"name": "Prairie Village", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Pratt County", "countryCode": "US", "stateCode": "KS"}, {"name": "Rawlins County", "countryCode": "US", "stateCode": "KS"}, {"name": "Reno County", "countryCode": "US", "stateCode": "KS"}, {"name": "Republic County", "countryCode": "US", "stateCode": "KS"}, {"name": "Rice County", "countryCode": "US", "stateCode": "KS"}, {"name": "Riley County", "countryCode": "US", "stateCode": "KS"}, {"name": "Roeland Park", "countryCode": "US", "stateCode": "KS"}, {"name": "Rooks County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Rossville", "countryCode": "US", "stateCode": "KS"}, {"name": "Rush County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Russell County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Saint Marys", "countryCode": "US", "stateCode": "KS"}, {"name": "Salina", "countryCode": "US", "stateCode": "KS"}, {"name": "Saline County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Scott City", "countryCode": "US", "stateCode": "KS"}, {"name": "Scott County", "countryCode": "US", "stateCode": "KS"}, {"name": "Sedan", "countryCode": "US", "stateCode": "KS"}, {"name": "Sedgwick", "countryCode": "US", "stateCode": "KS"}, {"name": "Sedgwick County", "countryCode": "US", "stateCode": "KS"}, {"name": "Seneca", "countryCode": "US", "stateCode": "KS"}, {"name": "Seward County", "countryCode": "US", "stateCode": "KS"}, {"name": "Sharon Springs", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Shawnee County", "countryCode": "US", "stateCode": "KS"}, {"name": "Sheridan County", "countryCode": "US", "stateCode": "KS"}, {"name": "Sherman County", "countryCode": "US", "stateCode": "KS"}, {"name": "Silver Lake", "countryCode": "US", "stateCode": "KS"}, {"name": "Smith Center", "countryCode": "US", "stateCode": "KS"}, {"name": "Smith County", "countryCode": "US", "stateCode": "KS"}, {"name": "Solomon", "countryCode": "US", "stateCode": "KS"}, {"name": "South Hutchinson", "countryCode": "US", "stateCode": "KS"}, {"name": "Spring Hill", "countryCode": "US", "stateCode": "KS"}, {"name": "Stafford County", "countryCode": "US", "stateCode": "KS"}, {"name": "Stanton County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Stevens County", "countryCode": "US", "stateCode": "KS"}, {"name": "Stockton", "countryCode": "US", "stateCode": "KS"}, {"name": "Sublette", "countryCode": "US", "stateCode": "KS"}, {"name": "Sumner County", "countryCode": "US", "stateCode": "KS"}, {"name": "Syracuse", "countryCode": "US", "stateCode": "KS"}, {"name": "Thomas County", "countryCode": "US", "stateCode": "KS"}, {"name": "Tonganoxie", "countryCode": "US", "stateCode": "KS"}, {"name": "Topeka", "countryCode": "US", "stateCode": "KS"}, {"name": "Towanda", "countryCode": "US", "stateCode": "KS"}, {"name": "Trego County", "countryCode": "US", "stateCode": "KS"}, {"name": "Tribune", "countryCode": "US", "stateCode": "KS"}, {"name": "Troy", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Valley Center", "countryCode": "US", "stateCode": "KS"}, {"name": "Valley Falls", "countryCode": "US", "stateCode": "KS"}, {"name": "Victoria", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Wabaunsee County", "countryCode": "US", "stateCode": "KS"}, {"name": "Wallace County", "countryCode": "US", "stateCode": "KS"}, {"name": "Wamego", "countryCode": "US", "stateCode": "KS"}, {"name": "Washington", "countryCode": "US", "stateCode": "KS"}, {"name": "Washington County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Wellington", "countryCode": "US", "stateCode": "KS"}, {"name": "Wellsville", "countryCode": "US", "stateCode": "KS"}, {"name": "Westmoreland", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Wichita", "countryCode": "US", "stateCode": "KS"}, {"name": "Wichita County", "countryCode": "US", "stateCode": "KS"}, {"name": "Wilson County", "countryCode": "US", "stateCode": "KS"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "KS"}, {"name": "Woodson County", "countryCode": "US", "stateCode": "KS"}, {"name": "Wyandotte County", "countryCode": "US", "stateCode": "KS"}, {"name": "Yates Center", "countryCode": "US", "stateCode": "KS"}]