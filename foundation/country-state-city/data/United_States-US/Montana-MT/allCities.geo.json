[{"name": "Absarokee", "countryCode": "US", "stateCode": "MT", "latitude": "45.52050000", "longitude": "-109.44294000"}, {"name": "Anaconda", "countryCode": "US", "stateCode": "MT", "latitude": "46.12854000", "longitude": "-112.94226000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.36695000", "longitude": "-104.28466000"}, {"name": "Beaverhead County", "countryCode": "US", "stateCode": "MT", "latitude": "45.13273000", "longitude": "-112.89889000"}, {"name": "Belgrade", "countryCode": "US", "stateCode": "MT", "latitude": "45.77604000", "longitude": "-111.17690000"}, {"name": "Big Horn County", "countryCode": "US", "stateCode": "MT", "latitude": "45.42346000", "longitude": "-107.48970000"}, {"name": "Big Sky", "countryCode": "US", "stateCode": "MT", "latitude": "45.28465000", "longitude": "-111.36829000"}, {"name": "Big Timber", "countryCode": "US", "stateCode": "MT", "latitude": "45.83494000", "longitude": "-109.95546000"}, {"name": "Bigfork", "countryCode": "US", "stateCode": "MT", "latitude": "48.06329000", "longitude": "-114.07261000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "45.78329000", "longitude": "-108.50069000"}, {"name": "Blaine County", "countryCode": "US", "stateCode": "MT", "latitude": "48.43276000", "longitude": "-108.95866000"}, {"name": "Bonner-West Riverside", "countryCode": "US", "stateCode": "MT", "latitude": "46.87669000", "longitude": "-113.88678000"}, {"name": "Boulder", "countryCode": "US", "stateCode": "MT", "latitude": "46.23659000", "longitude": "-112.12083000"}, {"name": "Bozeman", "countryCode": "US", "stateCode": "MT", "latitude": "45.67965000", "longitude": "-111.03856000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "45.44387000", "longitude": "-105.41133000"}, {"name": "Broadwater County", "countryCode": "US", "stateCode": "MT", "latitude": "46.33199000", "longitude": "-111.49547000"}, {"name": "Browning", "countryCode": "US", "stateCode": "MT", "latitude": "48.55692000", "longitude": "-113.01342000"}, {"name": "Butte", "countryCode": "US", "stateCode": "MT", "latitude": "46.00382000", "longitude": "-112.53474000"}, {"name": "Butte-Silver Bow (Balance)", "countryCode": "US", "stateCode": "MT", "latitude": "45.90194000", "longitude": "-112.65708000"}, {"name": "Carbon County", "countryCode": "US", "stateCode": "MT", "latitude": "45.22737000", "longitude": "-109.02832000"}, {"name": "Carter County", "countryCode": "US", "stateCode": "MT", "latitude": "45.51677000", "longitude": "-104.53616000"}, {"name": "Cascade County", "countryCode": "US", "stateCode": "MT", "latitude": "47.30802000", "longitude": "-111.34715000"}, {"name": "Chester", "countryCode": "US", "stateCode": "MT", "latitude": "48.51054000", "longitude": "-110.96747000"}, {"name": "Chinook", "countryCode": "US", "stateCode": "MT", "latitude": "48.59000000", "longitude": "-109.23128000"}, {"name": "Choteau", "countryCode": "US", "stateCode": "MT", "latitude": "47.81245000", "longitude": "-112.18363000"}, {"name": "Chouteau County", "countryCode": "US", "stateCode": "MT", "latitude": "47.88056000", "longitude": "-110.43520000"}, {"name": "Circle", "countryCode": "US", "stateCode": "MT", "latitude": "47.41667000", "longitude": "-105.59222000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.46521000", "longitude": "-111.98638000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.76909000", "longitude": "-113.71260000"}, {"name": "Col<PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "45.88416000", "longitude": "-106.62364000"}, {"name": "Columbia Falls", "countryCode": "US", "stateCode": "MT", "latitude": "48.37246000", "longitude": "-114.********"}, {"name": "Columbus", "countryCode": "US", "stateCode": "MT", "latitude": "45.********", "longitude": "-109.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "48.********", "longitude": "-111.********"}, {"name": "Crow Agency", "countryCode": "US", "stateCode": "MT", "latitude": "45.********", "longitude": "-107.********"}, {"name": "Custer County", "countryCode": "US", "stateCode": "MT", "latitude": "46.********", "longitude": "-105.********"}, {"name": "Cut Bank", "countryCode": "US", "stateCode": "MT", "latitude": "48.********", "longitude": "-112.********"}, {"name": "Daniels County", "countryCode": "US", "stateCode": "MT", "latitude": "48.********", "longitude": "-105.********"}, {"name": "Dawson County", "countryCode": "US", "stateCode": "MT", "latitude": "47.********", "longitude": "-104.********"}, {"name": "Deer Lodge", "countryCode": "US", "stateCode": "MT", "latitude": "46.********", "longitude": "-112.********"}, {"name": "Deer Lodge County", "countryCode": "US", "stateCode": "MT", "latitude": "46.********", "longitude": "-113.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "45.********", "longitude": "-112.********"}, {"name": "East Helena", "countryCode": "US", "stateCode": "MT", "latitude": "46.********", "longitude": "-111.********"}, {"name": "East Missoula", "countryCode": "US", "stateCode": "MT", "latitude": "46.********", "longitude": "-113.********"}, {"name": "Ekalaka", "countryCode": "US", "stateCode": "MT", "latitude": "45.88889000", "longitude": "-104.55273000"}, {"name": "Eureka", "countryCode": "US", "stateCode": "MT", "latitude": "48.87996000", "longitude": "-115.05350000"}, {"name": "Evergreen", "countryCode": "US", "stateCode": "MT", "latitude": "48.22579000", "longitude": "-114.27624000"}, {"name": "Fallon County", "countryCode": "US", "stateCode": "MT", "latitude": "46.33402000", "longitude": "-104.41742000"}, {"name": "Fergus County", "countryCode": "US", "stateCode": "MT", "latitude": "47.26357000", "longitude": "-109.22433000"}, {"name": "Flathead County", "countryCode": "US", "stateCode": "MT", "latitude": "48.29516000", "longitude": "-114.04981000"}, {"name": "<PERSON>sy<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.********", "longitude": "-106.67781000"}, {"name": "Fort Belknap Agency", "countryCode": "US", "stateCode": "MT", "latitude": "48.48250000", "longitude": "-108.76544000"}, {"name": "Fort Benton", "countryCode": "US", "stateCode": "MT", "latitude": "47.81830000", "longitude": "-110.66744000"}, {"name": "Four Corners", "countryCode": "US", "stateCode": "MT", "latitude": "45.62965000", "longitude": "-111.18606000"}, {"name": "Frenchtown", "countryCode": "US", "stateCode": "MT", "latitude": "47.01492000", "longitude": "-114.22984000"}, {"name": "Gallatin County", "countryCode": "US", "stateCode": "MT", "latitude": "45.54049000", "longitude": "-111.17035000"}, {"name": "Garfield County", "countryCode": "US", "stateCode": "MT", "latitude": "47.27770000", "longitude": "-106.99283000"}, {"name": "Glacier County", "countryCode": "US", "stateCode": "MT", "latitude": "48.70508000", "longitude": "-112.99475000"}, {"name": "Glasgow", "countryCode": "US", "stateCode": "MT", "latitude": "48.19696000", "longitude": "-106.63671000"}, {"name": "Glendive", "countryCode": "US", "stateCode": "MT", "latitude": "47.10529000", "longitude": "-104.71246000"}, {"name": "Golden Valley County", "countryCode": "US", "stateCode": "MT", "latitude": "46.38126000", "longitude": "-109.17494000"}, {"name": "Granite County", "countryCode": "US", "stateCode": "MT", "latitude": "46.40444000", "longitude": "-113.44026000"}, {"name": "Great Falls", "countryCode": "US", "stateCode": "MT", "latitude": "47.50024000", "longitude": "-111.30081000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.24687000", "longitude": "-114.16037000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "45.73248000", "longitude": "-107.61203000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.43551000", "longitude": "-109.83435000"}, {"name": "Havre", "countryCode": "US", "stateCode": "MT", "latitude": "48.55000000", "longitude": "-109.68409000"}, {"name": "Helena", "countryCode": "US", "stateCode": "MT", "latitude": "46.59271000", "longitude": "-112.03611000"}, {"name": "Helena Valley Northeast", "countryCode": "US", "stateCode": "MT", "latitude": "46.69882000", "longitude": "-111.95207000"}, {"name": "Helena Valley Northwest", "countryCode": "US", "stateCode": "MT", "latitude": "46.72894000", "longitude": "-112.06275000"}, {"name": "Helena Valley Southeast", "countryCode": "US", "stateCode": "MT", "latitude": "46.61527000", "longitude": "-111.92156000"}, {"name": "Helena Valley West Central", "countryCode": "US", "stateCode": "MT", "latitude": "46.66291000", "longitude": "-112.06044000"}, {"name": "Helena West Side", "countryCode": "US", "stateCode": "MT", "latitude": "46.59672000", "longitude": "-112.11304000"}, {"name": "Hill County", "countryCode": "US", "stateCode": "MT", "latitude": "48.62823000", "longitude": "-110.11131000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.29277000", "longitude": "-107.23423000"}, {"name": "Jefferson County", "countryCode": "US", "stateCode": "MT", "latitude": "46.14849000", "longitude": "-112.09374000"}, {"name": "Jordan", "countryCode": "US", "stateCode": "MT", "latitude": "47.32083000", "longitude": "-106.91007000"}, {"name": "Judith Basin County", "countryCode": "US", "stateCode": "MT", "latitude": "47.04546000", "longitude": "-110.26607000"}, {"name": "Kalispell", "countryCode": "US", "stateCode": "MT", "latitude": "48.19579000", "longitude": "-114.31291000"}, {"name": "Lake County", "countryCode": "US", "stateCode": "MT", "latitude": "47.64594000", "longitude": "-114.08935000"}, {"name": "Lakeside", "countryCode": "US", "stateCode": "MT", "latitude": "48.01939000", "longitude": "-114.22457000"}, {"name": "<PERSON><PERSON> Deer", "countryCode": "US", "stateCode": "MT", "latitude": "45.62305000", "longitude": "-106.66670000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "45.66912000", "longitude": "-108.77153000"}, {"name": "Lewis and Clark County", "countryCode": "US", "stateCode": "MT", "latitude": "47.12234000", "longitude": "-112.39035000"}, {"name": "Lewistown", "countryCode": "US", "stateCode": "MT", "latitude": "47.06247000", "longitude": "-109.42824000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "48.38829000", "longitude": "-115.55600000"}, {"name": "Liberty County", "countryCode": "US", "stateCode": "MT", "latitude": "48.56169000", "longitude": "-111.02461000"}, {"name": "Lincoln", "countryCode": "US", "stateCode": "MT", "latitude": "46.95494000", "longitude": "-112.68171000"}, {"name": "Lincoln County", "countryCode": "US", "stateCode": "MT", "latitude": "48.54253000", "longitude": "-115.40519000"}, {"name": "Livingston", "countryCode": "US", "stateCode": "MT", "latitude": "45.66244000", "longitude": "-110.56104000"}, {"name": "Lockwood", "countryCode": "US", "stateCode": "MT", "latitude": "45.81912000", "longitude": "-108.41486000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.75881000", "longitude": "-114.08094000"}, {"name": "Madison County", "countryCode": "US", "stateCode": "MT", "latitude": "45.30074000", "longitude": "-111.92033000"}, {"name": "Malmstrom Air Force Base", "countryCode": "US", "stateCode": "MT", "latitude": "47.50549000", "longitude": "-111.18302000"}, {"name": "Malta", "countryCode": "US", "stateCode": "MT", "latitude": "48.35972000", "longitude": "-107.87428000"}, {"name": "Manhattan", "countryCode": "US", "stateCode": "MT", "latitude": "45.85660000", "longitude": "-111.33246000"}, {"name": "McCone County", "countryCode": "US", "stateCode": "MT", "latitude": "47.64523000", "longitude": "-105.79534000"}, {"name": "Meagher County", "countryCode": "US", "stateCode": "MT", "latitude": "46.59819000", "longitude": "-110.88564000"}, {"name": "Miles City", "countryCode": "US", "stateCode": "MT", "latitude": "46.40834000", "longitude": "-105.84056000"}, {"name": "Mineral County", "countryCode": "US", "stateCode": "MT", "latitude": "47.14732000", "longitude": "-114.99850000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.87215000", "longitude": "-113.99400000"}, {"name": "Missoula County", "countryCode": "US", "stateCode": "MT", "latitude": "47.03649000", "longitude": "-113.92371000"}, {"name": "Montana City", "countryCode": "US", "stateCode": "MT", "latitude": "46.53771000", "longitude": "-111.93277000"}, {"name": "Musselshell County", "countryCode": "US", "stateCode": "MT", "latitude": "46.49655000", "longitude": "-108.39771000"}, {"name": "North Browning", "countryCode": "US", "stateCode": "MT", "latitude": "48.57025000", "longitude": "-113.00953000"}, {"name": "Orchard Homes", "countryCode": "US", "stateCode": "MT", "latitude": "46.86326000", "longitude": "-114.04844000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "47.60021000", "longitude": "-114.11900000"}, {"name": "Park County", "countryCode": "US", "stateCode": "MT", "latitude": "45.48834000", "longitude": "-110.52632000"}, {"name": "Petroleum County", "countryCode": "US", "stateCode": "MT", "latitude": "47.11751000", "longitude": "-108.25012000"}, {"name": "Philipsburg", "countryCode": "US", "stateCode": "MT", "latitude": "46.33215000", "longitude": "-113.29423000"}, {"name": "Phillips County", "countryCode": "US", "stateCode": "MT", "latitude": "48.25909000", "longitude": "-107.91329000"}, {"name": "Plains", "countryCode": "US", "stateCode": "MT", "latitude": "47.46021000", "longitude": "-114.88291000"}, {"name": "Plentywood", "countryCode": "US", "stateCode": "MT", "latitude": "48.77475000", "longitude": "-104.56246000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "47.69355000", "longitude": "-114.16317000"}, {"name": "Pondera County", "countryCode": "US", "stateCode": "MT", "latitude": "48.22798000", "longitude": "-112.22639000"}, {"name": "Powder River County", "countryCode": "US", "stateCode": "MT", "latitude": "45.39501000", "longitude": "-105.63010000"}, {"name": "Powell County", "countryCode": "US", "stateCode": "MT", "latitude": "46.85663000", "longitude": "-112.93620000"}, {"name": "Prairie County", "countryCode": "US", "stateCode": "MT", "latitude": "46.86049000", "longitude": "-105.37794000"}, {"name": "Ravalli County", "countryCode": "US", "stateCode": "MT", "latitude": "46.08170000", "longitude": "-114.12069000"}, {"name": "Red Lodge", "countryCode": "US", "stateCode": "MT", "latitude": "45.18578000", "longitude": "-109.24682000"}, {"name": "Richland County", "countryCode": "US", "stateCode": "MT", "latitude": "47.78792000", "longitude": "-104.56134000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "47.52882000", "longitude": "-114.10150000"}, {"name": "Roosevelt County", "countryCode": "US", "stateCode": "MT", "latitude": "48.29455000", "longitude": "-105.01652000"}, {"name": "Rosebud County", "countryCode": "US", "stateCode": "MT", "latitude": "46.22974000", "longitude": "-106.73082000"}, {"name": "Roundup", "countryCode": "US", "stateCode": "MT", "latitude": "46.44524000", "longitude": "-108.54180000"}, {"name": "Ryegate", "countryCode": "US", "stateCode": "MT", "latitude": "46.29718000", "longitude": "-109.25879000"}, {"name": "Sanders County", "countryCode": "US", "stateCode": "MT", "latitude": "47.67483000", "longitude": "-115.13329000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "48.79252000", "longitude": "-105.42083000"}, {"name": "Seeley Lake", "countryCode": "US", "stateCode": "MT", "latitude": "47.17938000", "longitude": "-113.48452000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "48.50526000", "longitude": "-111.85697000"}, {"name": "Sheridan County", "countryCode": "US", "stateCode": "MT", "latitude": "48.72120000", "longitude": "-104.50468000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "47.71668000", "longitude": "-104.15633000"}, {"name": "Silver Bow County", "countryCode": "US", "stateCode": "MT", "latitude": "45.90236000", "longitude": "-112.65672000"}, {"name": "Somers", "countryCode": "US", "stateCode": "MT", "latitude": "48.08023000", "longitude": "-114.22151000"}, {"name": "South Browning", "countryCode": "US", "stateCode": "MT", "latitude": "48.54608000", "longitude": "-113.01425000"}, {"name": "Stanford", "countryCode": "US", "stateCode": "MT", "latitude": "47.15358000", "longitude": "-110.21826000"}, {"name": "Stevensville", "countryCode": "US", "stateCode": "MT", "latitude": "46.50992000", "longitude": "-114.09316000"}, {"name": "Stillwater County", "countryCode": "US", "stateCode": "MT", "latitude": "45.66944000", "longitude": "-109.39477000"}, {"name": "Sun Prairie", "countryCode": "US", "stateCode": "MT", "latitude": "47.53690000", "longitude": "-111.48136000"}, {"name": "Superior", "countryCode": "US", "stateCode": "MT", "latitude": "47.19159000", "longitude": "-114.89180000"}, {"name": "Sweet Grass County", "countryCode": "US", "stateCode": "MT", "latitude": "45.81373000", "longitude": "-109.94105000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.79306000", "longitude": "-105.31221000"}, {"name": "Teton County", "countryCode": "US", "stateCode": "MT", "latitude": "47.83729000", "longitude": "-112.24080000"}, {"name": "Thompson Falls", "countryCode": "US", "stateCode": "MT", "latitude": "47.59489000", "longitude": "-115.33834000"}, {"name": "Three Forks", "countryCode": "US", "stateCode": "MT", "latitude": "45.89243000", "longitude": "-111.55219000"}, {"name": "Toole County", "countryCode": "US", "stateCode": "MT", "latitude": "48.65530000", "longitude": "-111.69570000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.31910000", "longitude": "-111.52080000"}, {"name": "Treasure County", "countryCode": "US", "stateCode": "MT", "latitude": "46.21147000", "longitude": "-107.27170000"}, {"name": "Valley County", "countryCode": "US", "stateCode": "MT", "latitude": "48.36531000", "longitude": "-106.66752000"}, {"name": "Virginia City", "countryCode": "US", "stateCode": "MT", "latitude": "45.29381000", "longitude": "-111.94609000"}, {"name": "Warm Springs", "countryCode": "US", "stateCode": "MT", "latitude": "46.18131000", "longitude": "-112.78476000"}, {"name": "West Glendive", "countryCode": "US", "stateCode": "MT", "latitude": "47.11085000", "longitude": "-104.74968000"}, {"name": "West Yellowstone", "countryCode": "US", "stateCode": "MT", "latitude": "44.66215000", "longitude": "-111.10411000"}, {"name": "Wheatland County", "countryCode": "US", "stateCode": "MT", "latitude": "46.46634000", "longitude": "-109.84440000"}, {"name": "White Sulphur Springs", "countryCode": "US", "stateCode": "MT", "latitude": "46.54828000", "longitude": "-110.90216000"}, {"name": "Whitefish", "countryCode": "US", "stateCode": "MT", "latitude": "48.41108000", "longitude": "-114.33763000"}, {"name": "Whitehall", "countryCode": "US", "stateCode": "MT", "latitude": "45.********", "longitude": "-112.09749000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "46.98501000", "longitude": "-104.18827000"}, {"name": "Wibaux County", "countryCode": "US", "stateCode": "MT", "latitude": "46.96535000", "longitude": "-104.24897000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "MT", "latitude": "47.00276000", "longitude": "-108.35207000"}, {"name": "Wolf Point", "countryCode": "US", "stateCode": "MT", "latitude": "48.09057000", "longitude": "-105.64056000"}, {"name": "Yellowstone County", "countryCode": "US", "stateCode": "MT", "latitude": "45.93725000", "longitude": "-108.27435000"}]