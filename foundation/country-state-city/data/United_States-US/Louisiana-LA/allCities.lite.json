[{"name": "Abbeville", "countryCode": "US", "stateCode": "LA"}, {"name": "Abita Springs", "countryCode": "US", "stateCode": "LA"}, {"name": "Acadia Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Albany", "countryCode": "US", "stateCode": "LA"}, {"name": "Alexandria", "countryCode": "US", "stateCode": "LA"}, {"name": "Allen Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Amelia", "countryCode": "US", "stateCode": "LA"}, {"name": "Amite", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Arcadia", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Ascension Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Assumption Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Avondale", "countryCode": "US", "stateCode": "LA"}, {"name": "Avoyelles Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Banks Springs", "countryCode": "US", "stateCode": "LA"}, {"name": "Barataria", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Bastrop", "countryCode": "US", "stateCode": "LA"}, {"name": "Baton Rouge", "countryCode": "US", "stateCode": "LA"}, {"name": "Bawcomville", "countryCode": "US", "stateCode": "LA"}, {"name": "Bayou Cane", "countryCode": "US", "stateCode": "LA"}, {"name": "Bayou Gauche", "countryCode": "US", "stateCode": "LA"}, {"name": "Bayou Vista", "countryCode": "US", "stateCode": "LA"}, {"name": "Beauregard Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Bernice", "countryCode": "US", "stateCode": "LA"}, {"name": "Berwick", "countryCode": "US", "stateCode": "LA"}, {"name": "Bienville Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Bogalusa", "countryCode": "US", "stateCode": "LA"}, {"name": "Bossier City", "countryCode": "US", "stateCode": "LA"}, {"name": "Bossier Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Bourg", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Breaux Bridge", "countryCode": "US", "stateCode": "LA"}, {"name": "Bridge City", "countryCode": "US", "stateCode": "LA"}, {"name": "Broussard", "countryCode": "US", "stateCode": "LA"}, {"name": "Brownsfield", "countryCode": "US", "stateCode": "LA"}, {"name": "Brownsville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Bunkie", "countryCode": "US", "stateCode": "LA"}, {"name": "Caddo Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Calcasieu Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Caldwell Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Cameron Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Carencro", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Carville", "countryCode": "US", "stateCode": "LA"}, {"name": "Catahoula", "countryCode": "US", "stateCode": "LA"}, {"name": "Catahoula Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Cecilia", "countryCode": "US", "stateCode": "LA"}, {"name": "Central", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Cha<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Church Point", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Claiborne Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Colfax", "countryCode": "US", "stateCode": "LA"}, {"name": "Columbia", "countryCode": "US", "stateCode": "LA"}, {"name": "Concordia Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Convent", "countryCode": "US", "stateCode": "LA"}, {"name": "Cottonport", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>ushatta", "countryCode": "US", "stateCode": "LA"}, {"name": "Covington", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Cut Off", "countryCode": "US", "stateCode": "LA"}, {"name": "De Soto Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Delcambre", "countryCode": "US", "stateCode": "LA"}, {"name": "Delhi", "countryCode": "US", "stateCode": "LA"}, {"name": "Denham Springs", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Donaldsonville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "East Baton Rouge Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "East Carroll Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "East Feliciana Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Eden Isle", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Elmwood", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Erath", "countryCode": "US", "stateCode": "LA"}, {"name": "Erwinville", "countryCode": "US", "stateCode": "LA"}, {"name": "E<PERSON>lle", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Evangeline <PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Farmerville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Fort Polk North", "countryCode": "US", "stateCode": "LA"}, {"name": "Fort Polk South", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Franklin Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "French Settlement", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Gardere", "countryCode": "US", "stateCode": "LA"}, {"name": "Garyville", "countryCode": "US", "stateCode": "LA"}, {"name": "Glenmora", "countryCode": "US", "stateCode": "LA"}, {"name": "Golden Meadow", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Grambling", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Grand Isle", "countryCode": "US", "stateCode": "LA"}, {"name": "Grand Point", "countryCode": "US", "stateCode": "LA"}, {"name": "Grant Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Greensburg", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Hahnville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Harrisonburg", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Haynesville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Iberia Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Iberville Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Independence", "countryCode": "US", "stateCode": "LA"}, {"name": "Inniswold", "countryCode": "US", "stateCode": "LA"}, {"name": "Iota", "countryCode": "US", "stateCode": "LA"}, {"name": "Iowa", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Jackson Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Jefferson <PERSON> Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Jefferson Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Jonesboro", "countryCode": "US", "stateCode": "LA"}, {"name": "Jonesville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Kinder", "countryCode": "US", "stateCode": "LA"}, {"name": "Krotz Springs", "countryCode": "US", "stateCode": "LA"}, {"name": "La Salle Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Labadieville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Lafayette", "countryCode": "US", "stateCode": "LA"}, {"name": "Lafayette Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Lafourche Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Lake Arthur", "countryCode": "US", "stateCode": "LA"}, {"name": "Lake Charles", "countryCode": "US", "stateCode": "LA"}, {"name": "Lake Providence", "countryCode": "US", "stateCode": "LA"}, {"name": "Lakeshore", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Lecompte", "countryCode": "US", "stateCode": "LA"}, {"name": "Leesville", "countryCode": "US", "stateCode": "LA"}, {"name": "Leonville", "countryCode": "US", "stateCode": "LA"}, {"name": "Lincoln Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Livingston", "countryCode": "US", "stateCode": "LA"}, {"name": "Livingston Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Livonia", "countryCode": "US", "stateCode": "LA"}, {"name": "Lockport", "countryCode": "US", "stateCode": "LA"}, {"name": "Lockport Heights", "countryCode": "US", "stateCode": "LA"}, {"name": "Logansport", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Madison Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Mandeville", "countryCode": "US", "stateCode": "LA"}, {"name": "Mansfield", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Many", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Marksville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Merrydale", "countryCode": "US", "stateCode": "LA"}, {"name": "Merryville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Metairie Terrace", "countryCode": "US", "stateCode": "LA"}, {"name": "Midway", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Minden", "countryCode": "US", "stateCode": "LA"}, {"name": "Minorca", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Montz", "countryCode": "US", "stateCode": "LA"}, {"name": "Morehouse Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Morgan City", "countryCode": "US", "stateCode": "LA"}, {"name": "Moss Bluff", "countryCode": "US", "stateCode": "LA"}, {"name": "Napoleonville", "countryCode": "US", "stateCode": "LA"}, {"name": "Natalbany", "countryCode": "US", "stateCode": "LA"}, {"name": "Natchitoches", "countryCode": "US", "stateCode": "LA"}, {"name": "Natchitoches Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "New Iberia", "countryCode": "US", "stateCode": "LA"}, {"name": "New Llano", "countryCode": "US", "stateCode": "LA"}, {"name": "New Orleans", "countryCode": "US", "stateCode": "LA"}, {"name": "New Roads", "countryCode": "US", "stateCode": "LA"}, {"name": "New Sarpy", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Norco", "countryCode": "US", "stateCode": "LA"}, {"name": "North Vacherie", "countryCode": "US", "stateCode": "LA"}, {"name": "Oak Grove", "countryCode": "US", "stateCode": "LA"}, {"name": "Oak Hills Place", "countryCode": "US", "stateCode": "LA"}, {"name": "Oakdale", "countryCode": "US", "stateCode": "LA"}, {"name": "Oberlin", "countryCode": "US", "stateCode": "LA"}, {"name": "Old Jefferson", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Opelousas", "countryCode": "US", "stateCode": "LA"}, {"name": "Orleans Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Ossun", "countryCode": "US", "stateCode": "LA"}, {"name": "Ouachita Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Paul<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Pearl River", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Pine Prairie", "countryCode": "US", "stateCode": "LA"}, {"name": "Pineville", "countryCode": "US", "stateCode": "LA"}, {"name": "Plaquemine", "countryCode": "US", "stateCode": "LA"}, {"name": "Plaquemines Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Pointe Coupee Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Ponchatoula", "countryCode": "US", "stateCode": "LA"}, {"name": "Port Allen", "countryCode": "US", "stateCode": "LA"}, {"name": "Port Barre", "countryCode": "US", "stateCode": "LA"}, {"name": "Port Sulphur", "countryCode": "US", "stateCode": "LA"}, {"name": "Poydras", "countryCode": "US", "stateCode": "LA"}, {"name": "Prairieville", "countryCode": "US", "stateCode": "LA"}, {"name": "Presquille", "countryCode": "US", "stateCode": "LA"}, {"name": "Prien", "countryCode": "US", "stateCode": "LA"}, {"name": "Raceland", "countryCode": "US", "stateCode": "LA"}, {"name": "Rapides Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Rayville", "countryCode": "US", "stateCode": "LA"}, {"name": "Red Chute", "countryCode": "US", "stateCode": "LA"}, {"name": "Red River Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Reserve", "countryCode": "US", "stateCode": "LA"}, {"name": "Richland Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Ringgold", "countryCode": "US", "stateCode": "LA"}, {"name": "River Ridge", "countryCode": "US", "stateCode": "LA"}, {"name": "Roseland", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>ust<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Sabine Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint Bernard Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint Charles Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint Helena Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint James Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint <PERSON> the Baptist Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint Landry Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint Martin Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint Mary Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint Rose", "countryCode": "US", "stateCode": "LA"}, {"name": "Saint Tammany Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Shenandoah", "countryCode": "US", "stateCode": "LA"}, {"name": "Shreveport", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Simmesport", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "South Vacherie", "countryCode": "US", "stateCode": "LA"}, {"name": "Springhill", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Stonewall", "countryCode": "US", "stateCode": "LA"}, {"name": "Sulphur", "countryCode": "US", "stateCode": "LA"}, {"name": "Sunset", "countryCode": "US", "stateCode": "LA"}, {"name": "Supreme", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>z", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Tangipahoa Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Tensas Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Terrebonne Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Terrytown", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Union Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Urania", "countryCode": "US", "stateCode": "LA"}, {"name": "Vermilion Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Vernon Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Vienna Bend", "countryCode": "US", "stateCode": "LA"}, {"name": "Village Saint George", "countryCode": "US", "stateCode": "LA"}, {"name": "Ville Platte", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Violet", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Washington Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Webster Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Welsh", "countryCode": "US", "stateCode": "LA"}, {"name": "West Baton Rouge Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "West Carroll Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "West Feliciana Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "West Ferriday", "countryCode": "US", "stateCode": "LA"}, {"name": "West Monroe", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Westminster", "countryCode": "US", "stateCode": "LA"}, {"name": "Westwego", "countryCode": "US", "stateCode": "LA"}, {"name": "White Castle", "countryCode": "US", "stateCode": "LA"}, {"name": "Winn Parish", "countryCode": "US", "stateCode": "LA"}, {"name": "Winnfield", "countryCode": "US", "stateCode": "LA"}, {"name": "Winnsboro", "countryCode": "US", "stateCode": "LA"}, {"name": "Woodmere", "countryCode": "US", "stateCode": "LA"}, {"name": "Woodworth", "countryCode": "US", "stateCode": "LA"}, {"name": "Youngsville", "countryCode": "US", "stateCode": "LA"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA"}, {"name": "Zwolle", "countryCode": "US", "stateCode": "LA"}]