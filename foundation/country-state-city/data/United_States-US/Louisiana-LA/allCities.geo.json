[{"name": "Abbeville", "countryCode": "US", "stateCode": "LA", "latitude": "29.97465000", "longitude": "-92.13429000"}, {"name": "Abita Springs", "countryCode": "US", "stateCode": "LA", "latitude": "30.47864000", "longitude": "-90.04008000"}, {"name": "Acadia Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.29053000", "longitude": "-92.41198000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.35380000", "longitude": "-91.26539000"}, {"name": "Albany", "countryCode": "US", "stateCode": "LA", "latitude": "30.50436000", "longitude": "-90.58231000"}, {"name": "Alexandria", "countryCode": "US", "stateCode": "LA", "latitude": "31.31129000", "longitude": "-92.44514000"}, {"name": "Allen Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.65287000", "longitude": "-92.82788000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.95215000", "longitude": "-90.29647000"}, {"name": "Amelia", "countryCode": "US", "stateCode": "LA", "latitude": "29.66632000", "longitude": "-91.10204000"}, {"name": "Amite", "countryCode": "US", "stateCode": "LA", "latitude": "30.72657000", "longitude": "-90.50898000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.95437000", "longitude": "-90.00535000"}, {"name": "Arcadia", "countryCode": "US", "stateCode": "LA", "latitude": "32.54904000", "longitude": "-92.92016000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.39770000", "longitude": "-91.93151000"}, {"name": "Ascension Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.20354000", "longitude": "-90.91129000"}, {"name": "Assumption Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.90077000", "longitude": "-91.06259000"}, {"name": "Avondale", "countryCode": "US", "stateCode": "LA", "latitude": "29.91298000", "longitude": "-90.********"}, {"name": "Avoyelles Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.********", "longitude": "-92.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.********", "longitude": "-91.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.********", "longitude": "-91.********"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "31.********", "longitude": "-92.********"}, {"name": "Banks Springs", "countryCode": "US", "stateCode": "LA", "latitude": "32.********", "longitude": "-92.********"}, {"name": "Barataria", "countryCode": "US", "stateCode": "LA", "latitude": "29.********", "longitude": "-90.********"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.********", "longitude": "-92.********"}, {"name": "Bastrop", "countryCode": "US", "stateCode": "LA", "latitude": "32.********", "longitude": "-91.********"}, {"name": "Baton Rouge", "countryCode": "US", "stateCode": "LA", "latitude": "30.********", "longitude": "-91.********"}, {"name": "Bawcomville", "countryCode": "US", "stateCode": "LA", "latitude": "32.********", "longitude": "-92.********"}, {"name": "Bayou Cane", "countryCode": "US", "stateCode": "LA", "latitude": "29.********", "longitude": "-90.********"}, {"name": "Bayou Gauche", "countryCode": "US", "stateCode": "LA", "latitude": "29.********", "longitude": "-90.********"}, {"name": "Bayou Vista", "countryCode": "US", "stateCode": "LA", "latitude": "29.68965000", "longitude": "-91.27094000"}, {"name": "Beauregard Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.64847000", "longitude": "-93.34334000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.85493000", "longitude": "-89.99063000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.05048000", "longitude": "-91.04149000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.69487000", "longitude": "-93.74185000"}, {"name": "Bernice", "countryCode": "US", "stateCode": "LA", "latitude": "32.82209000", "longitude": "-92.65793000"}, {"name": "Berwick", "countryCode": "US", "stateCode": "LA", "latitude": "29.69465000", "longitude": "-91.21899000"}, {"name": "Bienville Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.34722000", "longitude": "-93.05595000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.58098000", "longitude": "-93.89268000"}, {"name": "Bogalusa", "countryCode": "US", "stateCode": "LA", "latitude": "30.79102000", "longitude": "-89.84869000"}, {"name": "Bossier City", "countryCode": "US", "stateCode": "LA", "latitude": "32.51599000", "longitude": "-93.73212000"}, {"name": "Bossier Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.67899000", "longitude": "-93.60500000"}, {"name": "Bourg", "countryCode": "US", "stateCode": "LA", "latitude": "29.55355000", "longitude": "-90.60231000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.90243000", "longitude": "-90.38814000"}, {"name": "Breaux Bridge", "countryCode": "US", "stateCode": "LA", "latitude": "30.27353000", "longitude": "-91.89928000"}, {"name": "Bridge City", "countryCode": "US", "stateCode": "LA", "latitude": "29.93326000", "longitude": "-90.17007000"}, {"name": "Broussard", "countryCode": "US", "stateCode": "LA", "latitude": "30.14715000", "longitude": "-91.96123000"}, {"name": "Brownsfield", "countryCode": "US", "stateCode": "LA", "latitude": "30.54658000", "longitude": "-91.12066000"}, {"name": "Brownsville", "countryCode": "US", "stateCode": "LA", "latitude": "32.48709000", "longitude": "-92.15430000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.39436000", "longitude": "-91.25372000"}, {"name": "Bunkie", "countryCode": "US", "stateCode": "LA", "latitude": "30.95325000", "longitude": "-92.18263000"}, {"name": "Caddo Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.58017000", "longitude": "-93.88235000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.08742000", "longitude": "-91.90540000"}, {"name": "Calcasieu Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.22922000", "longitude": "-93.35795000"}, {"name": "Caldwell Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.09227000", "longitude": "-92.11661000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.79772000", "longitude": "-93.32515000"}, {"name": "Cameron Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.84687000", "longitude": "-93.19890000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "31.89350000", "longitude": "-93.11822000"}, {"name": "Carencro", "countryCode": "US", "stateCode": "LA", "latitude": "30.31714000", "longitude": "-92.04901000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.16882000", "longitude": "-93.37599000"}, {"name": "Carville", "countryCode": "US", "stateCode": "LA", "latitude": "30.21742000", "longitude": "-91.09621000"}, {"name": "Catahoula", "countryCode": "US", "stateCode": "LA", "latitude": "30.21464000", "longitude": "-91.70900000"}, {"name": "Catahoula Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.66617000", "longitude": "-91.84707000"}, {"name": "Cecilia", "countryCode": "US", "stateCode": "LA", "latitude": "30.33714000", "longitude": "-91.85317000"}, {"name": "Central", "countryCode": "US", "stateCode": "LA", "latitude": "30.55435000", "longitude": "-91.03677000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.88354000", "longitude": "-90.79732000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.94296000", "longitude": "-89.96537000"}, {"name": "Cha<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.88159000", "longitude": "-91.52511000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.43855000", "longitude": "-90.59537000"}, {"name": "Church Point", "countryCode": "US", "stateCode": "LA", "latitude": "30.40298000", "longitude": "-92.21513000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.51598000", "longitude": "-92.19180000"}, {"name": "Claiborne Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.82269000", "longitude": "-92.99573000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.02655000", "longitude": "-92.13903000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.86574000", "longitude": "-91.01566000"}, {"name": "Colfax", "countryCode": "US", "stateCode": "LA", "latitude": "31.51906000", "longitude": "-92.70682000"}, {"name": "Columbia", "countryCode": "US", "stateCode": "LA", "latitude": "32.10516000", "longitude": "-92.07791000"}, {"name": "Concordia Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.44584000", "longitude": "-91.64006000"}, {"name": "Convent", "countryCode": "US", "stateCode": "LA", "latitude": "30.02076000", "longitude": "-90.82982000"}, {"name": "Cottonport", "countryCode": "US", "stateCode": "LA", "latitude": "30.98408000", "longitude": "-92.05346000"}, {"name": "<PERSON>ushatta", "countryCode": "US", "stateCode": "LA", "latitude": "32.01488000", "longitude": "-93.34212000"}, {"name": "Covington", "countryCode": "US", "stateCode": "LA", "latitude": "30.47549000", "longitude": "-90.10042000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.21409000", "longitude": "-92.37458000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.96903000", "longitude": "-93.45073000"}, {"name": "Cut Off", "countryCode": "US", "stateCode": "LA", "latitude": "29.54272000", "longitude": "-90.33814000"}, {"name": "De Soto Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.05545000", "longitude": "-93.73728000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.45048000", "longitude": "-93.43322000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.84631000", "longitude": "-93.28905000"}, {"name": "Delcambre", "countryCode": "US", "stateCode": "LA", "latitude": "29.94826000", "longitude": "-91.98873000"}, {"name": "Delhi", "countryCode": "US", "stateCode": "LA", "latitude": "32.45764000", "longitude": "-91.49317000"}, {"name": "Denham Springs", "countryCode": "US", "stateCode": "LA", "latitude": "30.48740000", "longitude": "-90.95753000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.82382000", "longitude": "-90.47508000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.94322000", "longitude": "-90.35345000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "31.35740000", "longitude": "-92.16541000"}, {"name": "Donaldsonville", "countryCode": "US", "stateCode": "LA", "latitude": "30.10114000", "longitude": "-90.99412000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.38883000", "longitude": "-90.71398000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.23576000", "longitude": "-92.18540000"}, {"name": "East Baton Rouge Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.53824000", "longitude": "-91.09562000"}, {"name": "East Carroll Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.73255000", "longitude": "-91.23507000"}, {"name": "East Feliciana Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.84507000", "longitude": "-91.04554000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.55626000", "longitude": "-93.56712000"}, {"name": "Eden Isle", "countryCode": "US", "stateCode": "LA", "latitude": "30.22853000", "longitude": "-89.79867000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.04326000", "longitude": "-90.56009000"}, {"name": "Elmwood", "countryCode": "US", "stateCode": "LA", "latitude": "29.95659000", "longitude": "-90.18980000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.48131000", "longitude": "-92.69570000"}, {"name": "Erath", "countryCode": "US", "stateCode": "LA", "latitude": "29.95826000", "longitude": "-92.03596000"}, {"name": "Erwinville", "countryCode": "US", "stateCode": "LA", "latitude": "30.53102000", "longitude": "-91.40789000"}, {"name": "E<PERSON>lle", "countryCode": "US", "stateCode": "LA", "latitude": "29.84576000", "longitude": "-90.10674000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.49437000", "longitude": "-92.41763000"}, {"name": "Evangeline <PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.72894000", "longitude": "-92.40590000"}, {"name": "Farmerville", "countryCode": "US", "stateCode": "LA", "latitude": "32.77347000", "longitude": "-92.40570000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "31.63017000", "longitude": "-91.55456000"}, {"name": "Fort Polk North", "countryCode": "US", "stateCode": "LA", "latitude": "31.10302000", "longitude": "-93.17913000"}, {"name": "Fort Polk South", "countryCode": "US", "stateCode": "LA", "latitude": "31.05110000", "longitude": "-93.21578000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.79604000", "longitude": "-91.50150000"}, {"name": "Franklin Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.13322000", "longitude": "-91.67377000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.84731000", "longitude": "-90.15527000"}, {"name": "French Settlement", "countryCode": "US", "stateCode": "LA", "latitude": "30.29599000", "longitude": "-90.79630000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.44216000", "longitude": "-90.29925000"}, {"name": "Gardere", "countryCode": "US", "stateCode": "LA", "latitude": "30.34575000", "longitude": "-91.14011000"}, {"name": "Garyville", "countryCode": "US", "stateCode": "LA", "latitude": "30.05604000", "longitude": "-90.61926000"}, {"name": "Glenmora", "countryCode": "US", "stateCode": "LA", "latitude": "30.97658000", "longitude": "-92.58514000"}, {"name": "Golden Meadow", "countryCode": "US", "stateCode": "LA", "latitude": "29.37911000", "longitude": "-90.26008000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.23853000", "longitude": "-90.92010000"}, {"name": "Grambling", "countryCode": "US", "stateCode": "LA", "latitude": "32.52765000", "longitude": "-92.71404000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.04742000", "longitude": "-90.68981000"}, {"name": "Grand Isle", "countryCode": "US", "stateCode": "LA", "latitude": "29.23662000", "longitude": "-89.98729000"}, {"name": "Grand Point", "countryCode": "US", "stateCode": "LA", "latitude": "30.06131000", "longitude": "-90.75343000"}, {"name": "Grant Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.59970000", "longitude": "-92.55952000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.69771000", "longitude": "-90.78648000"}, {"name": "Greensburg", "countryCode": "US", "stateCode": "LA", "latitude": "30.83074000", "longitude": "-90.67176000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.44293000", "longitude": "-93.97296000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.91465000", "longitude": "-90.05396000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.02604000", "longitude": "-92.50847000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.99605000", "longitude": "-93.34210000"}, {"name": "Hahnville", "countryCode": "US", "stateCode": "LA", "latitude": "29.97659000", "longitude": "-90.40897000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.50463000", "longitude": "-90.46293000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.94048000", "longitude": "-90.20313000"}, {"name": "Harrisonburg", "countryCode": "US", "stateCode": "LA", "latitude": "31.77211000", "longitude": "-91.82152000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.90354000", "longitude": "-90.07729000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.53265000", "longitude": "-93.50406000"}, {"name": "Haynesville", "countryCode": "US", "stateCode": "LA", "latitude": "32.96208000", "longitude": "-93.14016000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.31325000", "longitude": "-91.79039000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.79192000", "longitude": "-93.05503000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.59577000", "longitude": "-90.71953000"}, {"name": "Iberia Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.51353000", "longitude": "-91.83964000"}, {"name": "Iberville Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.25850000", "longitude": "-91.34936000"}, {"name": "Independence", "countryCode": "US", "stateCode": "LA", "latitude": "30.63551000", "longitude": "-90.50335000"}, {"name": "Inniswold", "countryCode": "US", "stateCode": "LA", "latitude": "30.40491000", "longitude": "-91.08344000"}, {"name": "Iota", "countryCode": "US", "stateCode": "LA", "latitude": "30.33131000", "longitude": "-92.49569000"}, {"name": "Iowa", "countryCode": "US", "stateCode": "LA", "latitude": "30.23687000", "longitude": "-93.01376000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.83740000", "longitude": "-91.21761000"}, {"name": "Jackson Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.30203000", "longitude": "-92.55774000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.73604000", "longitude": "-90.12674000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.91104000", "longitude": "-91.66345000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.96604000", "longitude": "-90.15313000"}, {"name": "Jefferson <PERSON> Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.26772000", "longitude": "-92.81412000"}, {"name": "Jefferson Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.68097000", "longitude": "-90.09798000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "31.68323000", "longitude": "-92.13374000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.22243000", "longitude": "-92.65708000"}, {"name": "Jonesboro", "countryCode": "US", "stateCode": "LA", "latitude": "32.24127000", "longitude": "-92.71599000"}, {"name": "Jonesville", "countryCode": "US", "stateCode": "LA", "latitude": "31.62656000", "longitude": "-91.81818000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.99798000", "longitude": "-92.28485000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.99409000", "longitude": "-90.24174000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.93824000", "longitude": "-90.50898000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.35881000", "longitude": "-90.58620000"}, {"name": "Kinder", "countryCode": "US", "stateCode": "LA", "latitude": "30.48548000", "longitude": "-92.85070000"}, {"name": "Krotz Springs", "countryCode": "US", "stateCode": "LA", "latitude": "30.53686000", "longitude": "-91.75289000"}, {"name": "La Salle Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.67673000", "longitude": "-92.16044000"}, {"name": "Labadieville", "countryCode": "US", "stateCode": "LA", "latitude": "29.83743000", "longitude": "-90.95621000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.31353000", "longitude": "-89.94313000"}, {"name": "Lafayette", "countryCode": "US", "stateCode": "LA", "latitude": "30.22409000", "longitude": "-92.01984000"}, {"name": "Lafayette Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.20677000", "longitude": "-92.06388000"}, {"name": "Lafourche Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.50033000", "longitude": "-90.40259000"}, {"name": "Lake Arthur", "countryCode": "US", "stateCode": "LA", "latitude": "30.08076000", "longitude": "-92.67153000"}, {"name": "Lake Charles", "countryCode": "US", "stateCode": "LA", "latitude": "30.21309000", "longitude": "-93.20440000"}, {"name": "Lake Providence", "countryCode": "US", "stateCode": "LA", "latitude": "32.80499000", "longitude": "-91.17098000"}, {"name": "Lakeshore", "countryCode": "US", "stateCode": "LA", "latitude": "32.53514000", "longitude": "-92.02958000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.06698000", "longitude": "-90.48147000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.57244000", "longitude": "-90.38175000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.51853000", "longitude": "-92.18485000"}, {"name": "Lecompte", "countryCode": "US", "stateCode": "LA", "latitude": "31.09463000", "longitude": "-92.40041000"}, {"name": "Leesville", "countryCode": "US", "stateCode": "LA", "latitude": "31.14352000", "longitude": "-93.26100000"}, {"name": "Leonville", "countryCode": "US", "stateCode": "LA", "latitude": "30.47047000", "longitude": "-91.97845000"}, {"name": "Lincoln Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.60164000", "longitude": "-92.66482000"}, {"name": "Livingston", "countryCode": "US", "stateCode": "LA", "latitude": "30.50213000", "longitude": "-90.74787000"}, {"name": "Livingston Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.44014000", "longitude": "-90.72791000"}, {"name": "Livonia", "countryCode": "US", "stateCode": "LA", "latitude": "30.55908000", "longitude": "-91.55594000"}, {"name": "Lockport", "countryCode": "US", "stateCode": "LA", "latitude": "29.64605000", "longitude": "-90.53925000"}, {"name": "Lockport Heights", "countryCode": "US", "stateCode": "LA", "latitude": "29.65049000", "longitude": "-90.54647000"}, {"name": "Logansport", "countryCode": "US", "stateCode": "LA", "latitude": "31.97545000", "longitude": "-93.99797000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.93215000", "longitude": "-90.36647000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.04048000", "longitude": "-90.69898000"}, {"name": "Madison Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.36440000", "longitude": "-91.24258000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.63381000", "longitude": "-92.41930000"}, {"name": "Mandeville", "countryCode": "US", "stateCode": "LA", "latitude": "30.35825000", "longitude": "-90.06563000"}, {"name": "Mansfield", "countryCode": "US", "stateCode": "LA", "latitude": "32.03766000", "longitude": "-93.70018000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "31.05797000", "longitude": "-92.04901000"}, {"name": "Many", "countryCode": "US", "stateCode": "LA", "latitude": "31.56878000", "longitude": "-93.48406000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.49130000", "longitude": "-91.51955000"}, {"name": "Marksville", "countryCode": "US", "stateCode": "LA", "latitude": "31.12797000", "longitude": "-92.06624000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.89937000", "longitude": "-90.10035000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.68632000", "longitude": "-90.54675000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.10854000", "longitude": "-92.12457000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.69297000", "longitude": "-91.74400000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.93017000", "longitude": "-89.91623000"}, {"name": "Merrydale", "countryCode": "US", "stateCode": "LA", "latitude": "30.50130000", "longitude": "-91.10844000"}, {"name": "Merryville", "countryCode": "US", "stateCode": "LA", "latitude": "30.75437000", "longitude": "-93.54045000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.98409000", "longitude": "-90.15285000"}, {"name": "Metairie Terrace", "countryCode": "US", "stateCode": "LA", "latitude": "29.97854000", "longitude": "-90.16396000"}, {"name": "Midway", "countryCode": "US", "stateCode": "LA", "latitude": "31.69212000", "longitude": "-92.15236000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.10381000", "longitude": "-92.07651000"}, {"name": "Minden", "countryCode": "US", "stateCode": "LA", "latitude": "32.61543000", "longitude": "-93.28684000"}, {"name": "Minorca", "countryCode": "US", "stateCode": "LA", "latitude": "31.57933000", "longitude": "-91.48179000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.50931000", "longitude": "-92.11930000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.47439000", "longitude": "-90.55703000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.48908000", "longitude": "-91.04872000"}, {"name": "Montz", "countryCode": "US", "stateCode": "LA", "latitude": "30.00687000", "longitude": "-90.46869000"}, {"name": "Morehouse Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.82022000", "longitude": "-91.80180000"}, {"name": "Morgan City", "countryCode": "US", "stateCode": "LA", "latitude": "29.69937000", "longitude": "-91.20677000"}, {"name": "Moss Bluff", "countryCode": "US", "stateCode": "LA", "latitude": "30.30270000", "longitude": "-93.19071000"}, {"name": "Napoleonville", "countryCode": "US", "stateCode": "LA", "latitude": "29.94048000", "longitude": "-91.02482000"}, {"name": "Natalbany", "countryCode": "US", "stateCode": "LA", "latitude": "30.54622000", "longitude": "-90.48619000"}, {"name": "Natchitoches", "countryCode": "US", "stateCode": "LA", "latitude": "31.76072000", "longitude": "-93.08627000"}, {"name": "Natchitoches Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.72354000", "longitude": "-93.09624000"}, {"name": "New Iberia", "countryCode": "US", "stateCode": "LA", "latitude": "30.00354000", "longitude": "-91.81873000"}, {"name": "New Llano", "countryCode": "US", "stateCode": "LA", "latitude": "31.11491000", "longitude": "-93.27155000"}, {"name": "New Orleans", "countryCode": "US", "stateCode": "LA", "latitude": "29.95465000", "longitude": "-90.07507000"}, {"name": "New Roads", "countryCode": "US", "stateCode": "LA", "latitude": "30.70157000", "longitude": "-91.43622000"}, {"name": "New Sarpy", "countryCode": "US", "stateCode": "LA", "latitude": "29.97817000", "longitude": "-90.38963000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.07265000", "longitude": "-91.24095000"}, {"name": "Norco", "countryCode": "US", "stateCode": "LA", "latitude": "29.99909000", "longitude": "-90.41230000"}, {"name": "North Vacherie", "countryCode": "US", "stateCode": "LA", "latitude": "29.99687000", "longitude": "-90.70565000"}, {"name": "Oak Grove", "countryCode": "US", "stateCode": "LA", "latitude": "32.86096000", "longitude": "-91.38845000"}, {"name": "Oak Hills Place", "countryCode": "US", "stateCode": "LA", "latitude": "30.35992000", "longitude": "-91.08760000"}, {"name": "Oakdale", "countryCode": "US", "stateCode": "LA", "latitude": "30.81603000", "longitude": "-92.66042000"}, {"name": "Oberlin", "countryCode": "US", "stateCode": "LA", "latitude": "30.62020000", "longitude": "-92.76265000"}, {"name": "Old Jefferson", "countryCode": "US", "stateCode": "LA", "latitude": "30.38269000", "longitude": "-91.01705000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "31.90294000", "longitude": "-92.24319000"}, {"name": "Opelousas", "countryCode": "US", "stateCode": "LA", "latitude": "30.53353000", "longitude": "-92.08151000"}, {"name": "Orleans Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.06864000", "longitude": "-89.92813000"}, {"name": "Ossun", "countryCode": "US", "stateCode": "LA", "latitude": "30.27603000", "longitude": "-92.11235000"}, {"name": "Ouachita Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.47831000", "longitude": "-92.15487000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.87965000", "longitude": "-90.43397000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.69326000", "longitude": "-91.30205000"}, {"name": "Paul<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.02631000", "longitude": "-90.71315000"}, {"name": "Pearl River", "countryCode": "US", "stateCode": "LA", "latitude": "30.37603000", "longitude": "-89.74840000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.96520000", "longitude": "-91.20316000"}, {"name": "Pine Prairie", "countryCode": "US", "stateCode": "LA", "latitude": "30.78381000", "longitude": "-92.42541000"}, {"name": "Pineville", "countryCode": "US", "stateCode": "LA", "latitude": "31.32240000", "longitude": "-92.43430000"}, {"name": "Plaquemine", "countryCode": "US", "stateCode": "LA", "latitude": "30.29005000", "longitude": "-91.23497000"}, {"name": "Plaquemines Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.54421000", "longitude": "-89.82047000"}, {"name": "Pointe Coupee Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.70940000", "longitude": "-91.60079000"}, {"name": "Ponchatoula", "countryCode": "US", "stateCode": "LA", "latitude": "30.43880000", "longitude": "-90.44148000"}, {"name": "Port Allen", "countryCode": "US", "stateCode": "LA", "latitude": "30.45214000", "longitude": "-91.21011000"}, {"name": "Port Barre", "countryCode": "US", "stateCode": "LA", "latitude": "30.56020000", "longitude": "-91.95401000"}, {"name": "Port Sulphur", "countryCode": "US", "stateCode": "LA", "latitude": "29.48049000", "longitude": "-89.69395000"}, {"name": "Poydras", "countryCode": "US", "stateCode": "LA", "latitude": "29.86937000", "longitude": "-89.88895000"}, {"name": "Prairieville", "countryCode": "US", "stateCode": "LA", "latitude": "30.30297000", "longitude": "-90.97205000"}, {"name": "Presquille", "countryCode": "US", "stateCode": "LA", "latitude": "29.56383000", "longitude": "-90.64620000"}, {"name": "Prien", "countryCode": "US", "stateCode": "LA", "latitude": "30.18187000", "longitude": "-93.27377000"}, {"name": "Raceland", "countryCode": "US", "stateCode": "LA", "latitude": "29.72743000", "longitude": "-90.59898000"}, {"name": "Rapides Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.19862000", "longitude": "-92.53320000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.23493000", "longitude": "-92.26846000"}, {"name": "Rayville", "countryCode": "US", "stateCode": "LA", "latitude": "32.47736000", "longitude": "-91.75485000"}, {"name": "Red Chute", "countryCode": "US", "stateCode": "LA", "latitude": "32.55598000", "longitude": "-93.61323000"}, {"name": "Red River Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.09315000", "longitude": "-93.33988000"}, {"name": "Reserve", "countryCode": "US", "stateCode": "LA", "latitude": "30.05381000", "longitude": "-90.55175000"}, {"name": "Richland Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.41779000", "longitude": "-91.76349000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.44876000", "longitude": "-92.08486000"}, {"name": "Ringgold", "countryCode": "US", "stateCode": "LA", "latitude": "32.32849000", "longitude": "-93.27989000"}, {"name": "River Ridge", "countryCode": "US", "stateCode": "LA", "latitude": "29.96020000", "longitude": "-90.21563000"}, {"name": "Roseland", "countryCode": "US", "stateCode": "LA", "latitude": "30.76491000", "longitude": "-90.51176000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.91991000", "longitude": "-93.28239000"}, {"name": "<PERSON>ust<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.52321000", "longitude": "-92.63793000"}, {"name": "Sabine Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.56401000", "longitude": "-93.55470000"}, {"name": "Saint Bernard Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.87399000", "longitude": "-89.82422000"}, {"name": "Saint Charles Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.90553000", "longitude": "-90.35822000"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.77990000", "longitude": "-91.37650000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.25770000", "longitude": "-91.09927000"}, {"name": "Saint Helena Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.82198000", "longitude": "-90.71032000"}, {"name": "Saint James Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.02630000", "longitude": "-90.79632000"}, {"name": "Saint <PERSON> the Baptist Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.12646000", "longitude": "-90.47088000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "31.91849000", "longitude": "-91.23345000"}, {"name": "Saint Landry Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.59885000", "longitude": "-92.00586000"}, {"name": "Saint Martin Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.12907000", "longitude": "-91.60830000"}, {"name": "Saint <PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.12520000", "longitude": "-91.83345000"}, {"name": "Saint Mary Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.63462000", "longitude": "-91.47293000"}, {"name": "Saint Rose", "countryCode": "US", "stateCode": "LA", "latitude": "29.94687000", "longitude": "-90.32313000"}, {"name": "Saint Tammany Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.40875000", "longitude": "-89.95393000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.74215000", "longitude": "-90.81037000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.23576000", "longitude": "-92.09457000"}, {"name": "Shenandoah", "countryCode": "US", "stateCode": "LA", "latitude": "30.40130000", "longitude": "-91.00094000"}, {"name": "Shreveport", "countryCode": "US", "stateCode": "LA", "latitude": "32.52515000", "longitude": "-93.75018000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.53932000", "longitude": "-93.29628000"}, {"name": "Simmesport", "countryCode": "US", "stateCode": "LA", "latitude": "30.98352000", "longitude": "-91.80012000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.27519000", "longitude": "-89.78117000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.18436000", "longitude": "-90.85926000"}, {"name": "South Vacherie", "countryCode": "US", "stateCode": "LA", "latitude": "29.92743000", "longitude": "-90.69981000"}, {"name": "Springhill", "countryCode": "US", "stateCode": "LA", "latitude": "33.00597000", "longitude": "-93.46684000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.69625000", "longitude": "-92.08597000"}, {"name": "Stonewall", "countryCode": "US", "stateCode": "LA", "latitude": "32.28183000", "longitude": "-93.82407000"}, {"name": "Sulphur", "countryCode": "US", "stateCode": "LA", "latitude": "30.23659000", "longitude": "-93.37738000"}, {"name": "Sunset", "countryCode": "US", "stateCode": "LA", "latitude": "30.41131000", "longitude": "-92.06845000"}, {"name": "Supreme", "countryCode": "US", "stateCode": "LA", "latitude": "29.85937000", "longitude": "-90.98121000"}, {"name": "<PERSON><PERSON>z", "countryCode": "US", "stateCode": "LA", "latitude": "32.56875000", "longitude": "-91.98513000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.40848000", "longitude": "-91.18678000"}, {"name": "Tangipahoa Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.62665000", "longitude": "-90.40568000"}, {"name": "Tensas Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.00166000", "longitude": "-91.34007000"}, {"name": "Terrebonne Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.33744000", "longitude": "-90.83764000"}, {"name": "Terrytown", "countryCode": "US", "stateCode": "LA", "latitude": "29.91021000", "longitude": "-90.03257000"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.79576000", "longitude": "-90.82287000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.87743000", "longitude": "-90.03202000"}, {"name": "Union Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.83190000", "longitude": "-92.37482000"}, {"name": "Urania", "countryCode": "US", "stateCode": "LA", "latitude": "31.86378000", "longitude": "-92.29597000"}, {"name": "Vermilion Parish", "countryCode": "US", "stateCode": "LA", "latitude": "29.80939000", "longitude": "-92.30428000"}, {"name": "Vernon Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.10829000", "longitude": "-93.18415000"}, {"name": "<PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "31.56544000", "longitude": "-91.42595000"}, {"name": "Vienna Bend", "countryCode": "US", "stateCode": "LA", "latitude": "31.73239000", "longitude": "-93.04100000"}, {"name": "Village Saint George", "countryCode": "US", "stateCode": "LA", "latitude": "30.36214000", "longitude": "-91.06733000"}, {"name": "Ville Platte", "countryCode": "US", "stateCode": "LA", "latitude": "30.68797000", "longitude": "-92.27152000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.19076000", "longitude": "-93.58127000"}, {"name": "Violet", "countryCode": "US", "stateCode": "LA", "latitude": "29.89576000", "longitude": "-89.89784000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "32.87153000", "longitude": "-93.98740000"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "29.91854000", "longitude": "-90.21091000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.48797000", "longitude": "-90.86149000"}, {"name": "Washington Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.85334000", "longitude": "-90.04052000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.57574000", "longitude": "-90.95316000"}, {"name": "Webster Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.71345000", "longitude": "-93.33498000"}, {"name": "Welsh", "countryCode": "US", "stateCode": "LA", "latitude": "30.23604000", "longitude": "-92.82265000"}, {"name": "West Baton Rouge Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.46341000", "longitude": "-91.31275000"}, {"name": "West Carroll Parish", "countryCode": "US", "stateCode": "LA", "latitude": "32.78856000", "longitude": "-91.45674000"}, {"name": "West Feliciana Parish", "countryCode": "US", "stateCode": "LA", "latitude": "30.87977000", "longitude": "-91.42003000"}, {"name": "West Ferriday", "countryCode": "US", "stateCode": "LA", "latitude": "31.64044000", "longitude": "-91.57318000"}, {"name": "West Monroe", "countryCode": "US", "stateCode": "LA", "latitude": "32.51848000", "longitude": "-92.14764000"}, {"name": "<PERSON><PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.24215000", "longitude": "-93.25071000"}, {"name": "Westminster", "countryCode": "US", "stateCode": "LA", "latitude": "30.41380000", "longitude": "-91.08760000"}, {"name": "Westwego", "countryCode": "US", "stateCode": "LA", "latitude": "29.90604000", "longitude": "-90.14230000"}, {"name": "White Castle", "countryCode": "US", "stateCode": "LA", "latitude": "30.16992000", "longitude": "-91.14705000"}, {"name": "Winn Parish", "countryCode": "US", "stateCode": "LA", "latitude": "31.94425000", "longitude": "-92.63677000"}, {"name": "Winnfield", "countryCode": "US", "stateCode": "LA", "latitude": "31.92558000", "longitude": "-92.64131000"}, {"name": "Winnsboro", "countryCode": "US", "stateCode": "LA", "latitude": "32.16321000", "longitude": "-91.72068000"}, {"name": "Woodmere", "countryCode": "US", "stateCode": "LA", "latitude": "29.85798000", "longitude": "-90.08035000"}, {"name": "Woodworth", "countryCode": "US", "stateCode": "LA", "latitude": "31.14658000", "longitude": "-92.49736000"}, {"name": "Youngsville", "countryCode": "US", "stateCode": "LA", "latitude": "30.09965000", "longitude": "-91.99012000"}, {"name": "<PERSON>", "countryCode": "US", "stateCode": "LA", "latitude": "30.64852000", "longitude": "-91.15650000"}, {"name": "Zwolle", "countryCode": "US", "stateCode": "LA", "latitude": "31.63156000", "longitude": "-93.64407000"}]