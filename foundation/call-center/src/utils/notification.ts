export async function registerNotification() {
  if ('Notification' in window) {
    if (Notification.permission === 'granted') {
      console.log('Notification permission already granted.');
    } else if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        console.log('Notification permission granted.');
      }
    }
  }
}

export function sendNotification(title: string, body: string) {
  if (Notification.permission === 'granted') {
    const notification = new Notification(title, { body: body, icon: 'https://cdn.moego.pet/w3/go/v2/logo.jpg' });
    notification.onclick = () => {
      window.focus();
    };
  } else {
    console.warn('Notification permission not granted.');
  }
}
