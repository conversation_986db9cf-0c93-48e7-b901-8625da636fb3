import { toast } from '@moego/ui';
import { TwilioError } from '@twilio/voice-sdk/es5/twilio/errors';

enum CallCenterErrorCode {
  SIDInvalid = 20001,
  PermissionDenied = 20003,
  PhoneNumInvalid = 20005,
  TokenDenied = 20006,
  TokenExpire = 20104,
  TokenInvalid = 20007,
  TokenInvalid2 = 20101,
  ErrorPhoneFormat = 21211,
  phoneCanNotCall = 21614,
  ConnectionTimeout = 31003,
  ConnectionError = 31005,
  JWTTokenExpired = 31205,
  MicrophoneInvalid = 31208,
}

export const resolveError = (error: TwilioError, updateToken: (manually?: boolean) => void, destroy: () => void) => {
  switch (error.code) {
    case CallCenterErrorCode.TokenDenied:
    case CallCenterErrorCode.TokenExpire:
    case CallCenterErrorCode.JWTTokenExpired:
      updateToken(true);
      break;
    case CallCenterErrorCode.ConnectionTimeout:
      toast({
        type: 'error',
        title: 'Connection timeout, please try again latter',
      });
      break;
    case CallCenterErrorCode.PhoneNumInvalid:
      toast({
        type: 'error',
        title: 'Invalid calling phone number',
      });
      break;
    case CallCenterErrorCode.SIDInvalid:
    case CallCenterErrorCode.TokenInvalid:
    case CallCenterErrorCode.TokenInvalid2:
      toast({
        type: 'error',
        title: 'Token of calling invalid, please refresh the page if you need to call',
      });
      destroy();
      break;
    case CallCenterErrorCode.phoneCanNotCall:
      toast({
        type: 'error',
        title: 'The phone number is not available for voice calls',
      });
      break;
    case CallCenterErrorCode.ErrorPhoneFormat:
      toast({
        type: 'error',
        title: 'Invalid format of calling phone number',
      });
      break;
    case CallCenterErrorCode.PermissionDenied:
      // permission denied, do nothing, allow register no permission token
      break;
    case CallCenterErrorCode.MicrophoneInvalid:
      toast({
        type: 'error',
        title: 'Please allow your browser`s microphone permission',
      });
      break;
    default:
      // toast({
      //   type: 'error',
      //   title: 'An error occurred while establishing communication, please try again latter',
      // });
      destroy();
      break;
  }
};
