export enum CallCenterStatus {
  //   未注册
  unRegistered,
  //   已注册
  registered,
  //   device与worker 某一个ready
  ready,
  //   device与worker 均注册好了
  allReady,
  //   呼叫中/被呼叫中
  calling,
  //   通讯中
  connecting,
  //   错误状态
  error,
}

export type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Keys extends keyof T
  ? { [K in Keys]-?: T[K] } & Omit<T, Keys>
  : never;

interface CallModelUuid {
  type: 'Outing' | 'Incoming';
  customerId?: string;
  clientId?: string;
  businessId?: string;
}

// 两者中至少有一个是必填的
export type CallModel = RequireAtLeastOne<CallModelUuid, 'customerId' | 'clientId'>;

// Id: -2 表示Twilio 错误 报漏于Call center Help, -1 表示全局提醒报错，其余为Call id
export enum CallErrorIdEnum {
  TwilioError = -2,
  GlobalError = -1,
}

export interface ListenerType {
  onCalling: (info: CallModel) => void;
  onIncoming: (info: CallModel) => void;
  onCancel: (id: number | string) => void;
  onReject: (id: number | string) => void;
  onConnected: (id: number | string) => void;
  onConnectEnd: (id: number | string) => void;
  onMuteChanged: (id: number | string, isMuted: boolean) => void;
  onStatusChanged: (status: CallCenterStatus) => void;
  onWarning?: (id: number | string, code: CallCenterWarningCode, data: Record<string, string>) => void;
  onError?: (id: CallErrorIdEnum | number | string, error: Error) => void;
}

export enum Direction {
  'Outing' = 'Outing',
  'Incoming' = 'Incoming',
}

export enum CallCenterInfoCode {
  TokenUpdated = 'CCI-10001',
  tokenWillExpire = 'CCI-10002',
  CallIncoming = 'CCI-10003',
}

export const CallCenterInfoCodeMessage: Record<CallCenterInfoCode, string> = {
  [CallCenterInfoCode.TokenUpdated]: CallCenterInfoCode.TokenUpdated + ': Token updated',
  [CallCenterInfoCode.tokenWillExpire]: CallCenterInfoCode.tokenWillExpire + ': Token will expire soon',
  [CallCenterInfoCode.CallIncoming]: CallCenterInfoCode.CallIncoming + ': Incoming call',
};

export enum CallCenterWarningCode {
  QualityWarning = 'CCW-10001',
}

export const CallCenterWarningCodeMessage: Record<CallCenterWarningCode, string> = {
  [CallCenterWarningCode.QualityWarning]: CallCenterWarningCode.QualityWarning + ': Poor call quality',
};

export enum CallCenterErrorCode {
  // 10001 - 19999: Call center error
  InternalError = 'CCE-10001',
  UpdateTokenWhenCalling = 'CCE-10002',
  MicrophonePermissionDenied = 'CCE-10003',
  PermissionDenied = 'CCE-10004',
  InvalidPhoneNumber = 'CCE-10005',
  // 20001 - 29999: Device error

  DeviceNotRegistered = 'CCE-20001',
  // 30001 - 39999: call error
  CallExists = 'CCE-30001',
  CallMissed = 'CCE-30002',
  CalloutError = 'CCE-30003',
  IsBusy = 'CCE-30004',
  // 40001 - 49999: action error
  CanNotSendDTMF = 'CCE-40001',
}

export const CallCenterErrorCodeMessage: Record<CallCenterErrorCode, string> = {
  [CallCenterErrorCode.InternalError]:
    CallCenterErrorCode.InternalError + ': Recovering from internal error. Please wait a few minutes and try again.',
  [CallCenterErrorCode.CallMissed]:
    CallCenterErrorCode.CallMissed + ': Current call has ended, please refresh the page and dial again if needed.',
  [CallCenterErrorCode.CanNotSendDTMF]:
    CallCenterErrorCode.CanNotSendDTMF + ': Failed to send signal. Please try typing the number again.',
  [CallCenterErrorCode.MicrophonePermissionDenied]:
    CallCenterErrorCode.MicrophonePermissionDenied + ': Please enable microphone permissions in your browser.',
  [CallCenterErrorCode.PermissionDenied]:
    CallCenterErrorCode.PermissionDenied + ': You have denied the permission to make calls.',
  [CallCenterErrorCode.InvalidPhoneNumber]:
    CallCenterErrorCode.InvalidPhoneNumber + ': Invalid phone number, please check and try again.',
  [CallCenterErrorCode.CallExists]:
    CallCenterErrorCode.CallExists + ': This call already exists. Please do not dial again.',
  [CallCenterErrorCode.DeviceNotRegistered]:
    CallCenterErrorCode.DeviceNotRegistered + ': Call center is not registered yet.',
  [CallCenterErrorCode.IsBusy]:
    CallCenterErrorCode.IsBusy +
    ': Your colleague is calling this client. Please wait till they finish before dial again.',
  [CallCenterErrorCode.UpdateTokenWhenCalling]:
    CallCenterErrorCode.UpdateTokenWhenCalling + ': Call in progress. Cannot update status.',
  [CallCenterErrorCode.CalloutError]: CallCenterErrorCode.CalloutError + ': Call out error',
};
