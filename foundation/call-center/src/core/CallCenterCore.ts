/* eslint-disable max-lines */
import { EventListener } from '@moego/tools';
import { Call, Device } from '@twilio/voice-sdk';
import { TwilioError } from '@twilio/voice-sdk/es5/twilio/errors';
import TaskRouter, { Reservation } from 'twilio-taskrouter';
import { getToken, getWorkerToken } from '../api';
import {
  CallCenterErrorCode,
  CallCenterErrorCodeMessage,
  CallCenterInfoCode,
  CallCenterInfoCodeMessage,
  CallCenterStatus,
  CallCenterWarningCode,
  CallCenterWarningCodeMessage,
  CallErrorIdEnum,
  Direction,
  ListenerType,
  RequireAtLeastOne,
} from '../constant/callCenterConst';
import { audioCheck } from '../utils/audioCheck';
import { registerNotification, sendNotification } from '../utils/notification';
import { resolveError } from '../utils/resolveError';
declare global {
  interface Window {
    callCenterUuid: string | undefined | null;
  }
}

export interface CallCenterLogSystemParams {
  error?: Error;
  extra?: Record<string, any>;
  info?: string;
}

interface CallPhoneUuidModel {
  customerId?: number;
  clientId?: number;
  businessId: number;
  companyId: number;
  staffId: number;
}

export type CallPhoneModel = RequireAtLeastOne<CallPhoneUuidModel, 'customerId' | 'clientId'>;
export class CallCenterCore {
  /**
   * 通话队列
   */
  private callPools: {
    allIds: (number | string)[];
    byId: Record<number | string, Call | null>;
  } = { allIds: [], byId: {} };
  // 当前token
  private token: string = '';
  // 是否为外呼
  private outCalling: boolean = false;
  // 上一个链接中的开始时间
  private connectStartTime: Date | null = null;
  // device
  private device: Device | undefined = undefined;
  // 当前task router任务
  private reservation: Reservation | undefined = undefined;
  // 当前worker
  private worker: TaskRouter.Worker | undefined = undefined;
  // 监听器
  private listener: EventListener<ListenerType>;
  // 是否注册worker
  private isUseWorker: boolean = false;
  // 当前状态
  private _status: CallCenterStatus = CallCenterStatus.unRegistered;

  get status(): CallCenterStatus {
    return this._status;
  }

  set status(newStatus: CallCenterStatus) {
    if (this._status !== newStatus) {
      this._status = newStatus;
      this.listener.emit('onStatusChanged', newStatus);
    }
  }

  // 日志回调，让包外层处理日志上报，注入上报回调即可
  private logSystemCallBack?: (callBackParam: CallCenterLogSystemParams) => void;
  // token 刷新锁定时间 目前服务端刷新24h 本地记录一个token 1小时锁内不刷新
  private tokenRefreshBlockMs: number = 60 * 60 * 1000;
  // 上一个device token注册时间
  private tokenRegisterTime: number = 0;

  /**
   * 初始化
   * @param isUseWorker  是否需要注册 worker
   */
  constructor(isUseWorker: boolean = false, tokenRefreshBlockMs: number = 60 * 60 * 1000) {
    this.callPools = {
      allIds: [],
      byId: {},
    };
    this.isUseWorker = isUseWorker;
    this.tokenRefreshBlockMs = tokenRefreshBlockMs;
    this.listener = new EventListener<ListenerType>();
  }

  registerLogSystem(callBack: (callBackParam: CallCenterLogSystemParams) => void) {
    this.logSystemCallBack = callBack;
  }
  /**
   * token 是否过期
   * @returns
   */
  isTokenExpired = () => {
    if (this.tokenRegisterTime === 0) {
      return true;
    }
    const now = new Date().getTime();
    return now - this.tokenRegisterTime > this.tokenRefreshBlockMs;
  };

  /**
   * 更新token
   */
  updateToken = async (manually?: boolean) => {
    if (this.status === CallCenterStatus.error) {
      await this.register();
      return;
    }
    if (this.status === CallCenterStatus.calling || this.status === CallCenterStatus.connecting) {
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.UpdateTokenWhenCalling],
        extra: { token: this.token, code: CallCenterErrorCode.UpdateTokenWhenCalling },
      });
      if (manually) {
        this.listener.emit(
          'onError',
          -1,
          new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.UpdateTokenWhenCalling]),
        );
      }
      return;
    }
    if (!this.isTokenExpired() && !manually) {
      return;
    }

    const token = await getToken();
    this.logSystemCallBack?.({
      info: CallCenterInfoCodeMessage[CallCenterInfoCode.TokenUpdated],
      extra: { oldToken: this.token, newToken: token, code: CallCenterInfoCode.TokenUpdated },
    });
    this.tokenRegisterTime = new Date().getTime();
    if (this.token === token) {
      return;
    }
    this.token = token;
    // 后端根据当前角色 分配worker Sid 并创建token
    if (token === '') {
      if (this.device) {
        this.device.destroy();
      }
      this.status = CallCenterStatus.unRegistered;
    } else {
      if (this.device) {
        this.device.updateToken(token);
        if (this.device.state === Device.State.Registered) {
          this.status = CallCenterStatus.allReady;
        } else if (this.device.state === Device.State.Destroyed) {
          this.status = CallCenterStatus.unRegistered;
        } else {
          this.status = CallCenterStatus.registered;
          this.device.register();
        }
      } else {
        await this.registerDevice(token);
      }
    }
  };
  private updateWorkerToken = async () => {
    // 后端根据当前角色 分配worker Sid 并创建token
    if (this.worker) {
      const token = await getWorkerToken();
      this.worker.updateToken(token);
    }
  };
  /**
   * 获取status
   * @returns status
   */
  getStatus = (): CallCenterStatus => {
    if (this.status === CallCenterStatus.allReady) {
      if (this.device?.state === 'registered') {
        return CallCenterStatus.allReady;
      } else {
        this.status = CallCenterStatus.unRegistered;
        return CallCenterStatus.unRegistered;
      }
    }
    return this.status;
  };
  /**
   * 获取呼叫方向
   * @returns 是呼叫还是接听
   */
  getDirection = (): Direction => {
    return this.outCalling ? Direction.Outing : Direction.Incoming;
  };
  /**
   * 获取通话开始时间
   * @returns start time
   */
  getStartTime = (): Date | null => {
    return this.connectStartTime;
  };
  /**
   * 设置 call
   * @param call Call对象
   */
  setCall = (uuid: string, call: Call) => {
    call.on('accept', async () => {
      this.status = CallCenterStatus.connecting;
      this.connectStartTime = new Date();
      this.listener.emit('onConnected', uuid);
      this.outCalling = false;
      const able = await audioCheck();
      if (!able) {
        this.mute(uuid);
        this.listener.emit(
          'onError',
          -1,
          new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied]),
        );
      }
    });
    call.on('cancel', () => {
      this.connectStartTime = null;
      if (this.callPools.allIds.length === 1) {
        this.status = CallCenterStatus.allReady;
      }
      this.outCalling = false;
      this.listener.emit('onCancel', uuid);
      this.callPools?.byId[uuid]?.removeAllListeners?.();
      this.removeCall(uuid);
    });
    call.on('reject', () => {
      this.connectStartTime = null;
      if (this.callPools.allIds.length === 1) {
        this.status = CallCenterStatus.allReady;
      }
      this.listener.emit('onReject', uuid);
      this.callPools?.byId[uuid]?.removeAllListeners?.();
      this.removeCall(uuid);
    });
    call.on('disconnect', () => {
      // this.destroyLastDevice();
      this.connectStartTime = null;
      this.outCalling = false;
      this.status = this.callPools.allIds.length > 1 ? CallCenterStatus.calling : CallCenterStatus.allReady;

      this.listener.emit('onConnectEnd', uuid);
      this.callPools?.byId[uuid]?.removeAllListeners?.();
      this.removeCall(uuid);
    });
    call.on('mute', (isMuted) => {
      this.listener.emit('onMuteChanged', uuid, isMuted);
    });
    call.on('warning', (warningName, warningData) => {
      /* warningData is the following
          {
            // Stat name
            "name": "mos",
    
            // Array of mos values in the past 5 samples that triggered the warning
            "values": [2, 2, 2, 2, 2],
    
            // Array of samples collected that triggered the warning.
            // See sample object format here https://www.twilio.com/docs/voice/sdks/javascript/twiliocall#sample-event
            "samples": [...],
    
            // The threshold configuration.
            // In this example, low-mos warning will be raised if the value is below 3
            "threshold": {
              "name": "min",
              "value": 3
            }
          }
         */
      this.listener.emit('onWarning', uuid, CallCenterWarningCode.QualityWarning, warningData);
      this.logSystemCallBack?.({
        info: CallCenterWarningCodeMessage[CallCenterWarningCode.QualityWarning],
        extra: { token: this.token, uuid, warningName, warningData, ...warningData },
      });
    });
    if (!this.callPools.allIds.includes(uuid)) {
      this.callPools.allIds.push(uuid);
    }
    this.callPools.byId[uuid] = call;
  };
  /**
   * remove call
   * @param uuid client uuid | call uuid
   */
  removeCall = (uuid: number | string) => {
    this.callPools.allIds = this.callPools.allIds.filter((item) => item !== uuid);
    delete this.callPools.byId[uuid];
  };
  /**
   * 注册
   */
  register = async () => {
    // 注册之前先清理内容
    this.destroy();
    registerNotification();

    const noPermission = await this.registerDevice();
    if (this.isUseWorker) {
      await this.registerWorker();
    }
    if (!noPermission) {
      this.status = CallCenterStatus.registered;
      return true;
    }
    return false;
  };
  /**
   * 注册 device ,  return true => 没权限
   */
  registerDevice = async (incomingToken?: string): Promise<boolean> => {
    if (incomingToken && !this.isTokenExpired()) {
      return true;
    }
    const token = incomingToken ?? (await getToken());
    this.tokenRegisterTime = new Date().getTime();
    if (token === '') {
      return true;
    }
    this.token = token;
    // 注册device
    const device = new Device(token, {
      closeProtection: true,
      // 30s 前发出过期前警告
      tokenRefreshMs: 30000,
      allowIncomingWhileBusy: true,
      // TODO Channon 可配置我们铃声
      // sounds: {
      //   incoming: 'http://mysite.com/incoming.mp3',
      //   outgoing: 'http://mysite.com/outgoing.mp3',
      // },
    });
    device.on('registered', () => {
      if (this.status === CallCenterStatus.connecting) {
        return;
      }
      this.status =
        this.isUseWorker && this.status === CallCenterStatus.registered
          ? CallCenterStatus.ready
          : CallCenterStatus.allReady;
    });
    device.on('error', (error: TwilioError) => {
      this.logSystemCallBack?.({
        error: new Error(error.message),
        extra: {
          token: this.token,
          ...error,
        },
      });
      resolveError(error, this.updateToken, this.destroy);
      this.listener.emit('onError', CallErrorIdEnum.TwilioError, new Error(error.message));
      this.status = CallCenterStatus.error;
    });
    device.on('tokenWillExpire', () => {
      this.logSystemCallBack?.({
        info: CallCenterInfoCodeMessage[CallCenterInfoCode.tokenWillExpire],
        extra: { token: this.token, code: CallCenterInfoCode.tokenWillExpire },
      });
      this.updateToken(true);
    });
    device.on('incoming', (call: Call) => {
      // device 直接收到语音呼叫
      if (this.callPools.allIds.length <= 0) {
        this.status = CallCenterStatus.calling;
      }
      if (document.visibilityState !== 'visible' || !document.hasFocus()) {
        sendNotification('Moego call incoming', `Some one is calling you`);
      }
      const parameters = new URLSearchParams(call.parameters.Params);

      const uuid = parameters.get('customerId')
        ? `customer-${parameters.get('customerId')}`
        : `client-${parameters.get('clientId')}`;
      this.logSystemCallBack?.({
        info: CallCenterInfoCodeMessage[CallCenterInfoCode.CallIncoming],
        extra: {
          token: this.token,
          uuid,
          code: CallCenterInfoCode.CallIncoming,
          businessId: parameters.get('businessId'),
        },
      });
      this.listener.emit('onIncoming', {
        type: 'Incoming',
        customerId: parameters.get('customerId') ?? undefined,
        clientId: parameters.get('clientId') ?? undefined,
        businessId: parameters.get('businessId') ?? undefined,
      });

      this.setCall(uuid, call);
    });
    device.register();
    this.device = device;
    return false;
  };
  /**
   * 注册worker
   */
  registerWorker = async () => {
    // 注册worker
    const token = await getWorkerToken();
    const worker = new TaskRouter.Worker(token);
    worker.on('ready', (readyAlice) => {
      console.log(`Worker ${readyAlice.sid} is now ready for work`);
      this.status = this.status === CallCenterStatus.registered ? CallCenterStatus.ready : CallCenterStatus.allReady;
    });
    worker.on('tokenExpired', () => {
      this.updateWorkerToken();
    });
    worker.on('reservationCreated', (reservation) => {
      if (this.status === CallCenterStatus.connecting || this.status === CallCenterStatus.calling) {
        // this.reservation?.redirect()
        this.reservation?.reject();
      }
      if (this.callPools.allIds.length <= 0) {
        this.status = CallCenterStatus.calling;
      }
      // 收到Task call 请求
      if (document.visibilityState !== 'visible' || !document.hasFocus()) {
        sendNotification('Moego call incoming', `Some one is calling you`);
      }
      this.listener.emit('onIncoming', {
        type: 'Incoming',
        customerId: reservation.task.attributes.customerId,
        clientId: reservation.task.attributes.clientId || '',
      });
      reservation.on('accepted', async (acceptedReservation) => {
        console.log(`Reservation ${acceptedReservation.sid} was accepted.`);
        const call = await this.device?.connect({
          params: {
            to: reservation.task.attributes.from,
          },
        });

        const uuid = reservation.task.attributes.customerId
          ? `customer-${reservation.task.attributes.customerId}`
          : `client-${reservation.task.attributes.clientId}`;
        call && this.setCall(uuid, call);
      });
      this.reservation = reservation;
    });
    this.worker = worker;
  };

  /**
   * 呼叫
   * @contact 呼叫参数
   */
  callPhone = async (callPhoneModel: CallPhoneModel) => {
    if (this.status === CallCenterStatus.error) {
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.CalloutError],
        extra: { token: this.token, ...callPhoneModel, code: CallCenterErrorCode.CalloutError },
      });
      this.listener.emit(
        'onError',
        CallErrorIdEnum.GlobalError,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.InternalError]),
      );
      return;
    }
    const able = await audioCheck();
    if (!able) {
      this.listener.emit(
        'onError',
        -1,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied]),
      );
      return;
    }
    const uuid = callPhoneModel.customerId
      ? `customer-${callPhoneModel.customerId}`
      : `client-${callPhoneModel.clientId}`;
    if (this.callPools?.allIds?.includes?.(uuid)) {
      this.listener.emit('onError', uuid, new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.CallExists]));
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.CallExists],
        extra: { token: this.token, ...callPhoneModel, code: CallCenterErrorCode.CallExists },
      });
      return;
    } else {
      this.callPools.allIds.push(uuid);
      this.callPools.byId[uuid] = null;
    }
    if (this.status === CallCenterStatus.unRegistered) {
      this.listener.emit(
        'onError',
        CallErrorIdEnum.GlobalError,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.DeviceNotRegistered]),
      );
      return;
    }
    if (this.status === CallCenterStatus.calling && this.outCalling === true) {
      this.listener.emit(
        'onError',
        CallErrorIdEnum.GlobalError,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.IsBusy]),
      );
      return;
    }
    if (this.status === CallCenterStatus.connecting) {
      this.listener.emit(
        'onError',
        CallErrorIdEnum.GlobalError,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.IsBusy]),
      );
      return;
    }
    if (this.device?.isBusy) {
      this.listener.emit(
        'onError',
        CallErrorIdEnum.GlobalError,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.IsBusy]),
      );
      return;
    }

    this.status = CallCenterStatus.calling;
    this.outCalling = true;
    // const tempDevice = new Device(this.token);
    const params: {
      customerId?: string;
      clientId?: string;
      companyId?: string;
      businessId?: string;
    } = {
      companyId: callPhoneModel.companyId,
      businessId: callPhoneModel.businessId,
      staffId: callPhoneModel.staffId,
    } as any;
    if (callPhoneModel.customerId) {
      params['customerId'] = callPhoneModel.customerId + '';
    }
    if (callPhoneModel.clientId) {
      params['clientId'] = callPhoneModel.clientId + '';
    }
    try {
      const call = await this.device?.connect({
        params,
      });
      call && this.setCall(uuid, call);
      this.listener.emit('onCalling', {
        type: 'Outing',
        customerId: callPhoneModel.customerId ? callPhoneModel.customerId + '' : undefined,
        clientId: callPhoneModel.clientId ? callPhoneModel.clientId + '' : undefined,
      });
    } catch (e) {
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.CalloutError],
        extra: { token: this.token, ...callPhoneModel, code: CallCenterErrorCode.CalloutError },
      });
    }
  };

  /**
   *  发送DTMF
   * @param uuid  `customer-${customerId}` | `client-${clientId}`
   * @param digit  按键
   */
  sendDTMF = async (uuid: string, digit: string) => {
    if (this.callPools?.allIds.includes(uuid)) {
      try {
        this.callPools.byId[uuid]?.sendDigits(digit);
      } catch (e) {
        this.listener.emit('onError', uuid, new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.CanNotSendDTMF]));
        this.logSystemCallBack?.({
          error: new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.CanNotSendDTMF]),
          extra: { token: this.token, digit, uuid, code: CallCenterErrorCode.CanNotSendDTMF, function: 'sendDTMF' },
        });
      }
    } else {
      this.listener.emit('onError', uuid, new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.CallMissed]));
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.CallMissed],
        extra: { token: this.token, uuid, code: CallCenterErrorCode.CallMissed, function: 'sendDTMF' },
      });
    }
  };

  /**
   * 添加事件监听
   * @param type 事件类型
   * @param listener 事件
   */
  addListener = (type: keyof ListenerType, listener: (...param: any[]) => void) => {
    this.listener.addListener(type, listener);
  };
  /**
   * 移除事件监听
   * @param type 事件类型
   * @param listener 事件
   */
  removeListener = (type: keyof ListenerType, listener: (...param: any[]) => void) => {
    this.listener.removeListener(type, listener);
  };
  /**
   * 清空某种类型的事件监听
   * @param type 事件类型
   */
  clearListener = (type?: keyof ListenerType) => {
    this.listener.clearListener(type);
  };
  /**
   * 接听
   * @uuid `customer-${customerId}` | `client-${clientId}`
   */
  answer = async (uuid: string) => {
    if (this.status === CallCenterStatus.unRegistered) {
      this.listener.emit(
        'onError',
        uuid,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.DeviceNotRegistered]),
      );
      return;
    }
    if (
      this.status === CallCenterStatus.connecting ||
      (this.status === CallCenterStatus.calling && this.outCalling === true)
    ) {
      this.listener.emit('onError', uuid, new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.IsBusy]));
      return;
    }
    const able = await audioCheck();
    if (!able) {
      this.listener.emit(
        'onError',
        -1,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied]),
      );
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied],
        extra: { token: this.token, uuid, code: CallCenterErrorCode.MicrophonePermissionDenied },
      });
      return;
    }
    if (this.reservation) {
      this.reservation.accept();
    } else if (this.callPools?.allIds.includes(uuid)) {
      this.callPools?.byId[uuid]?.accept?.();
    } else {
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.CallMissed],
        extra: { token: this.token, uuid, code: CallCenterErrorCode.CallMissed },
      });
      this.listener.emit('onCancel', uuid);
    }
  };
  /**
   * 忽略接听
   * @uuid `customer-${customerId}` | `client-${clientId}`
   */
  ignore = (uuid: string) => {
    if (this.callPools?.allIds.includes(uuid)) {
      this.callPools?.byId[uuid]?.ignore?.();
      this.connectStartTime = null;
      if (this.callPools.allIds.length === 1) {
        this.status = CallCenterStatus.allReady;
      }
      this.listener.emit('onCancel', uuid);
      this.callPools?.byId[uuid]?.removeAllListeners?.();
      this.removeCall(uuid);
    } else {
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.CallMissed],
        extra: { token: this.token, uuid, code: CallCenterErrorCode.CallMissed, function: 'ignore' },
      });
      this.listener.emit('onCancel', uuid);
    }
  };
  /**
   * 挂断或者拒绝任务
   * @uuid `customer-${customerId}` | `client-${clientId}`
   */
  hangUp = (uuid: string) => {
    if (this.status === CallCenterStatus.unRegistered) {
      this.listener.emit(
        'onError',
        uuid,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.DeviceNotRegistered]),
      );
      return;
    }
    if (this.callPools?.allIds.includes(uuid)) {
      if (this.status === CallCenterStatus.connecting) {
        this.callPools?.byId[uuid]?.disconnect?.();
      } else if (this.status === CallCenterStatus.calling) {
        if (this.callPools?.byId[uuid]?.direction === Call.CallDirection.Incoming) {
          this.callPools?.byId[uuid].reject();
        } else {
          this.callPools?.byId[uuid]?.disconnect?.();
        }
      }
    } else {
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.CallMissed],
        extra: { token: this.token, uuid, code: CallCenterErrorCode.CallMissed, function: 'hangUp' },
      });
      this.listener.emit('onCancel', uuid);
    }
    if (this.reservation) {
      if (this.status === CallCenterStatus.calling) {
        this.reservation.reject();
        this.reservation = undefined;
      }
    }
  };
  /**
   * 转接电话
   * @param sid 转接到的worker sid
   * @param url 链接
   */
  redirect = (sid: string, url: string) => {
    if (this.status === CallCenterStatus.unRegistered) {
      this.listener.emit(
        'onError',
        CallErrorIdEnum.GlobalError,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.DeviceNotRegistered]),
      );
      return;
    }
    if (this.reservation) {
      this.reservation?.redirect(sid, url);
    }
  };
  /**
   * 闭麦
   * @uuid `customer-${customerId}` | `client-${clientId}`
   */
  mute = (uuid: string) => {
    if (this.callPools?.allIds.includes(uuid)) {
      this.callPools.byId[uuid]?.mute?.(true);
    } else {
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.CallMissed],
        extra: { token: this.token, uuid, code: CallCenterErrorCode.CallMissed, function: 'mute' },
      });
      this.listener.emit('onCancel', uuid);
    }
  };
  /**
   * 开麦
   * @uuid `customer-${customerId}` | `client-${clientId}`
   */
  notMute = async (uuid: string) => {
    const able = await audioCheck();
    if (!able) {
      this.listener.emit(
        'onError',
        -1,
        new Error(CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied]),
      );
      return;
    }
    if (this.callPools?.allIds.includes(uuid)) {
      this.callPools.byId[uuid]?.mute?.(false);
    } else {
      this.logSystemCallBack?.({
        info: CallCenterErrorCodeMessage[CallCenterErrorCode.CallMissed],
        extra: { token: this.token, uuid, code: CallCenterErrorCode.CallMissed, function: 'notMute' },
      });
      this.listener.emit('onCancel', uuid);
    }
  };
  /**
   * 销毁
   */
  destroy = () => {
    if (this.device) {
      this.device.removeAllListeners();
      this.device.destroy();
      this.device = undefined;
    }
    if (this.callPools.allIds.length > 0) {
      for (const callId of this.callPools.allIds) {
        this.callPools.byId[callId]?.disconnect?.();
        delete this.callPools.byId[callId];
      }
      this.callPools.allIds = [];
    }
    if (this.worker) {
      this.worker.disconnect();
      this.worker = undefined;
    }
    this.status = CallCenterStatus.unRegistered;
  };
}
