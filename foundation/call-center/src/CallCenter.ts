import { CallCenterStatus, Direction, ListenerType } from './constant/callCenterConst';
import { CallCenterCore, CallCenterLogSystemParams, CallPhoneModel } from './core/CallCenterCore';

declare global {
  interface Window {
    callCenterCore: CallCenterCore | undefined | null;
  }
}

/**
 * 注册call center
 * @param isUseWorker 是否需要注册Worker
 * @param tokenRefreshBlockMs token刷新锁死时间 默认1小时内不刷新
 */
async function register(isUseWorker: boolean = false, tokenRefreshBlockMs: number = 1000 * 60 * 60) {
  if (!window.callCenterCore) {
    window.callCenterCore = new CallCenterCore(isUseWorker, tokenRefreshBlockMs);
  }
  if (window.callCenterCore.getStatus() === CallCenterStatus.unRegistered) {
    await window.callCenterCore.register();
  } else {
    window.callCenterCore.updateToken(true);
  }
}
/**
 *  重新加载设备
 */
async function reloadDevice() {
  if (!window.callCenterCore) {
    return;
  }
  await window.callCenterCore.register();
}
/**
 * @returns 是否可以进行下一步操作
 */
async function ready() {
  if (getStatus() === CallCenterStatus.unRegistered) {
    const hasPermission = await getCallCenterCore().register();
    return hasPermission;
  }
  return true;
}
/**
 * 获取call center 实体
 */
function getCallCenterCore() {
  if (!window.callCenterCore) {
    throw Error('call center core has not register yet');
  }
  return window.callCenterCore;
}
/**
 * 注入日志系统
 */
async function registerLogSystem(callback: (param: CallCenterLogSystemParams) => void) {
  const able = await ready();
  if (able) {
    getCallCenterCore().registerLogSystem(callback);
  }
}
/**
 * 主动更新token
 */
async function updateToken(manually: boolean = false) {
  await getCallCenterCore().updateToken(manually);
}
/**
 * 呼叫
 * @param client CallPhoneModel 呼叫客户实体
 */
async function call(client: CallPhoneModel) {
  const able = await ready();
  if (able) {
    getCallCenterCore().callPhone(client);
  }
}
/**
 * 接听
 * @uuid `customer-${customerId}` | `client-${clientId}`
 */
async function answer(uuid: string) {
  const able = await ready();
  if (able) {
    getCallCenterCore().answer(uuid);
  }
}
/**
 * 获取状态
 * @returns call center status  @CallCenterStatus
 */
function getStatus(): CallCenterStatus {
  return getCallCenterCore().getStatus();
}
/**
 * 获取通话方向
 * @returns Direction
 */
function getDirection(): Direction {
  return getCallCenterCore().getDirection();
}
/**
 * 获取电话链接开始时间
 * @returns call start time @Date
 */
function getCallStartTime(): Date | null {
  return getCallCenterCore().getStartTime();
}
/**
 * 挂断
 * @uuid `customer-${customerId}` | `client-${clientId}`
 */
async function ignore(uuid: string) {
  const able = await ready();
  if (able) {
    getCallCenterCore().ignore(uuid);
  }
}
/**
 * 挂断
 * @uuid `customer-${customerId}` | `client-${clientId}`
 */
async function hangUp(uuid: string) {
  const able = await ready();
  if (able) {
    getCallCenterCore().hangUp(uuid);
  }
}
/**
 * 闭麦
 * @uuid `customer-${customerId}` | `client-${clientId}`
 */
async function mute(uuid: string) {
  const able = await ready();
  if (able) {
    getCallCenterCore().mute(uuid);
  }
}
/**
 * 发送DTMF
 * @param uuid  `customer-${customerId}` | `client-${clientId}`
 * @param digit  DTMF digit to send
 */
async function sendDTMF(uuid: string, digit: string) {
  const able = await ready();
  if (able) {
    getCallCenterCore().sendDTMF(uuid, digit);
  }
}
/**
 * 开麦
 * @uuid `customer-${customerId}` | `client-${clientId}`
 */
async function notMute(uuid: string) {
  const able = await ready();
  if (able) {
    getCallCenterCore().notMute(uuid);
  }
}
/**
 * 转接
 * @param sid 跳转的worker的sid
 * @param url A valid TwiML URI that is executed on the Caller's leg upon redirecting
 */
async function redirect(sid: string, url: string) {
  const able = await ready();
  if (able) {
    getCallCenterCore().redirect(sid, url);
  }
}
/**
 * 销毁
 */
function destroy() {
  getCallCenterCore().destroy();
  delete window.callCenterCore;
}

/**
 * 添加事件监听
 * @param type 事件类型
 * @param listener 事件
 */
function addListener(type: keyof ListenerType, listener: (...param: any[]) => void) {
  getCallCenterCore().addListener(type, listener);
}
/**
 * 移除事件监听
 * @param type 事件类型
 * @param listener 事件
 */
function removeListener(type: keyof ListenerType, listener: (...param: any[]) => void) {
  getCallCenterCore().removeListener(type, listener);
}
/**
 * 清空某种类型的事件监听
 * @param type 事件类型
 */
function clearListener(type: keyof ListenerType) {
  getCallCenterCore().clearListener(type);
}

export const CallCenter = {
  register,
  reloadDevice,
  getCallCenterCore,
  registerLogSystem,
  ready,
  call,
  answer,
  hangUp,
  ignore,
  getStatus,
  getCallStartTime,
  redirect,
  sendDTMF,
  mute,
  notMute,
  destroy,
  addListener,
  removeListener,
  clearListener,
  updateToken,
  getDirection,
};
