import { GetCallingDetailParams, GetCallingDetailResult } from '@moego/api-web/moego/api/engagement/v1/calling_api';

import { CallingSource, Pet, StaffPermission } from '@moego/api-web/moego/models/engagement/v1/voice_models';
import { useState } from 'react';
import { CallModel, RequireAtLeastOne } from '../constant/callCenterConst';
import { CallingClient } from './client';

export async function getToken(): Promise<string> {
  const data = await CallingClient.getVoipToken({ callingSource: CallingSource.WEB });
  if (
    !data.permissions?.includes?.(StaffPermission.CALLING) &&
    !data.permissions?.includes?.(StaffPermission.ANSWERING)
  ) {
    return '';
  }
  const token = data.token ?? '';
  return token;
}
// export async function getToken(id: string) {
//   return id;
// }
export async function getWorkerToken() {
  return '';
}
interface ClientAndInfoOriginModel {
  callUuid?: string;
  clientUuid?: number;
  customerId?: number;
  avatar: string;
  name: string;
  companyUuid?: number;
  companyName?: string;
  businessUuid?: number;
  businessName?: string;
  isRecording?: boolean;
  petInfos: {
    petId: number;
    petName: string;
    petType: string;
  }[];
}

export type ClientAndInfoModel = RequireAtLeastOne<ClientAndInfoOriginModel, 'callUuid' | 'clientUuid'>;

export const useQueryClientAndPetInfo = (call: CallModel) => {
  const [loading, setLoading] = useState(false);
  const [clientAndPetInfo, setClientAndPetInfo] = useState<ClientAndInfoModel>();
  const run = async () => {
    setLoading(true);
    const param: GetCallingDetailParams = {};
    if (call.customerId) {
      param.customerId = call.customerId;
    } else {
      param.clientId = call.clientId;
    }

    try {
      const res: GetCallingDetailResult = await CallingClient.getCallingDetail(param);
      setClientAndPetInfo({
        callUuid: res.customer.avatarPath,
        clientUuid: res.clientId ? +res.clientId : undefined,
        customerId: res.customer.id ? +res.customer.id : undefined,
        avatar: res.customer.avatarPath,
        name: res?.customer?.firstName ? res.customer.firstName + ' ' + res.customer.lastName : res.clientName,
        companyName: res.companyName,
        companyUuid: res.companyId ? +res.companyId : undefined,
        businessUuid: res.businessId ? +res.businessId : undefined,
        businessName: res.businessName,
        isRecording: res.isRecording ?? false,
        petInfos: res.pets.map((pet: Pet) => {
          return {
            petId: +pet.id,
            petName: pet.name,
            petType: pet.breed,
          };
        }),
      });
    } finally {
      setLoading(false);
    }
  };
  return {
    run,
    loading,
    data: clientAndPetInfo,
  };
};
