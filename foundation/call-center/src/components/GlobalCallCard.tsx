import {
  Avatar,
  cn,
  I<PERSON><PERSON><PERSON>on,
  MajorCallHangUpOutlined,
  MajorLocationOutlined,
  MajorMessageOutlined,
  MajorMicMuteOutlined,
  MajorMicOutlined,
  MajorPawsOutlined,
  MinorCallInboundOutlined,
  toast,
  Typography,
} from '@moego/ui';
import { useMemoizedFn } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';
import { ClientAndInfoModel, useQueryClientAndPetInfo } from '../api';
import { CallCenter } from '../CallCenter';
import { CallCenterWarningCode, CallModel } from '../constant/callCenterConst';
import { getTimeDifference } from '../utils/time';
import { GlobalCallMiniCardModel } from './GlobalCallView';
import { KeyboardIcon } from './Icons';

export interface CreateCallCardProps {
  call: CallModel;
  onCreateClient: () => void;
  onClientClick: ({
    companyUuid,
    businessUuid,
    customerId,
  }: {
    companyUuid?: number;
    businessUuid?: number;
    customerId?: number;
  }) => void;
  onPetClick: ({
    companyUuid,
    businessUuid,
    customerId,
    petId,
  }: {
    companyUuid?: number;
    businessUuid?: number;
    customerId?: number;
    petId?: number;
  }) => void;
  onAcceptAction?: (callModel: GlobalCallMiniCardModel) => void;
  onLinkedClick?: ({
    companyUuid,
    businessUuid,
    customerId,
  }: {
    companyUuid?: number;
    businessUuid?: number;
    customerId?: number;
  }) => void;
  onclose?: () => void;
}

export const GlobalCallCard = (props: CreateCallCardProps) => {
  const { call } = props;
  const uuid = call.customerId ? `customer-${call.customerId}` : `client-${call.clientId}`;
  const [isEndStatus, setIsEndStatus] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [subMessage, setSubMessage] = useState<string>(call.type === 'Outing' ? 'Calling' : 'Incoming call');
  const [loading, setLoading] = useState(false);
  const [isMute, setIsMute] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [answerMode, setAnswerMode] = useState(call.type === 'Outing' ? false : true);
  const timeRef = useRef<ReturnType<typeof setTimeout>>();
  const [connectTime, setConnectTime] = useState<Date | null>(null);
  const currentConnectTime = useRef<Date | null>();
  currentConnectTime.current = connectTime;
  const dataRef = useRef<ClientAndInfoModel | null>();
  const { data, loading: clientLoading, run } = useQueryClientAndPetInfo(call);
  dataRef.current = data;
  useEffect(() => {
    run();
    const onConnectedAction = (connectUuid: string) => {
      if (uuid !== connectUuid) {
        return;
      }
      setLoading(false);
      setAnswerMode(false);
      setIsConnected(true);
      setConnectTime(CallCenter.getCallStartTime() || new Date());
      timeRef.current = setInterval(() => {
        const subMessage = getTimeDifference(new Date(), currentConnectTime.current!);
        if (subMessage === '29:00') {
          toast({
            type: 'neutral',
            title: 'Call duration limit will reached in 1 minute',
          });
        }
        setSubMessage(subMessage);
        props.onAcceptAction?.({
          avatar: dataRef.current?.avatar,
          name: dataRef.current?.name,
          duration: subMessage,
        });
      }, 1000);
    };
    const onConnectEndAction = (connectUuid: string) => {
      if (uuid !== connectUuid) {
        return;
      }
      if (timeRef.current) {
        clearInterval(timeRef.current);
      }
      setLoading(false);
      setSubMessage('Call ended');
      setIsEndStatus(true);
      setTimeout(() => {
        closeAnimation();
      }, 1000);
    };
    const onConnectCancel = (connectUuid: string) => {
      if (uuid !== connectUuid) {
        return;
      }
      setLoading(false);
      setSubMessage('Canceled');
      setIsEndStatus(true);
      setTimeout(() => {
        closeAnimation();
      }, 1000);
    };
    const onReject = (connectUuid: string) => {
      if (uuid !== connectUuid) {
        return;
      }
      setLoading(false);
      setSubMessage('Rejected');
      setIsEndStatus(true);
      setTimeout(() => {
        closeAnimation();
      }, 1000);
    };
    const onMuteChanged = (connectUuid: string, isMuted: boolean) => {
      if (uuid !== connectUuid) {
        return;
      }
      setLoading(false);
      setIsMute(isMuted);
    };
    const onWarning = (connectUuid: string, code: CallCenterWarningCode, data: Record<string, string>) => {
      if (uuid !== connectUuid) {
        return;
      }
      if (code === CallCenterWarningCode.QualityWarning) {
        console.log(data);
      }
      // 预留后续处理低通话质量告知
    };
    const onError = (connectUuid: string, error: Error) => {
      if (uuid !== connectUuid) {
        return;
      }
      setLoading(false);
      toast({
        type: 'error',
        title: error.message,
      });
    };
    CallCenter.addListener('onReject', onReject);
    CallCenter.addListener('onCancel', onConnectCancel);
    CallCenter.addListener('onConnected', onConnectedAction);
    CallCenter.addListener('onConnectEnd', onConnectEndAction);
    CallCenter.addListener('onMuteChanged', onMuteChanged);
    CallCenter.addListener('onWarning', onWarning);
    CallCenter.addListener('onError', onError);
    return () => {
      if (timeRef.current) {
        clearInterval(timeRef.current);
      }
      setIsEndStatus(false);
      CallCenter.removeListener('onReject', onReject);
      CallCenter.removeListener('onCancel', onConnectCancel);
      CallCenter.removeListener('onConnected', onConnectedAction);
      CallCenter.removeListener('onConnectEnd', onConnectEndAction);
      CallCenter.removeListener('onMuteChanged', onMuteChanged);
      CallCenter.removeListener('onWarning', onWarning);
      CallCenter.removeListener('onError', onError);
    };
  }, []);
  const customerAction = useMemoizedFn(() => {
    if (clientLoading) {
      return;
    }
    if (data?.customerId) {
      props.onClientClick({
        companyUuid: data?.companyUuid,
        businessUuid: call?.businessId ? +call.businessId : data?.businessUuid,
        customerId: data.customerId,
      });
    } else {
      props.onCreateClient();
    }
  });
  const viewPetDetail = useMemoizedFn(() => {
    if (clientLoading) {
      return;
    }
    if (data?.customerId && data?.petInfos.length && data?.petInfos.length > 0) {
      props?.onPetClick?.({
        companyUuid: data?.companyUuid,
        businessUuid: call?.businessId ? +call.businessId : data?.businessUuid,
        customerId: data.customerId,
        petId: data?.petInfos.length > 1 ? undefined : data.petInfos[0].petId,
      });
    }
  });
  const onLinkAction = useMemoizedFn(() => {
    if (clientLoading) {
      return;
    }
    props?.onLinkedClick?.({
      companyUuid: data?.companyUuid,
      businessUuid: call?.businessId ? +call.businessId : data?.businessUuid,
      customerId: data?.customerId,
    });
  });
  const switchMute = useMemoizedFn(() => {
    setLoading(true);
    isMute ? CallCenter.notMute(uuid) : CallCenter.mute(uuid);
  });
  const switchKeyboardVisible = useMemoizedFn(() => {
    setKeyboardVisible((val) => !val);
  });

  const onKeyboardDown = useMemoizedFn((digit: string) => {
    CallCenter.sendDTMF(uuid, digit);
  });

  const dismissAction = useMemoizedFn(() => {
    CallCenter.ignore(uuid);
    setLoading(true);
  });
  const answerAction = useMemoizedFn(() => {
    CallCenter.answer(uuid);
    setLoading(true);
  });
  const closeAnimation = useMemoizedFn(() => {
    let fallbackTimeout: ReturnType<typeof setTimeout> | null = null;
    const closeAction = () => {
      props?.onclose?.();
      fallbackTimeout && clearTimeout(fallbackTimeout);
    };
    const element = document.getElementById(`call-center-dom-${uuid}`);
    if (element) {
      element.classList?.remove('animate__backInRight');
      element.classList?.add('animate__backOutRight');
      element.addEventListener('animationend', closeAction);
      fallbackTimeout = setTimeout(() => {
        closeAction();
      }, 1000);
    } else {
      closeAction();
    }
  });
  return (
    <div
      id={`call-center-dom-${uuid}`}
      className={cn(
        'animate__animated animate__backInRight moe-duration-400 moe-overflow-hidden moe-rounded-4 moe-bg-neutral-dark moe-shadow moe-transition-all',
        'moe-pointer-events-auto moe-mr-5 moe-mt-3 moe-w-[292px]',
      )}
    >
      <div className="moe-w-full moe-overflow-hidden moe-bg-[#232323] moe-p-2 moe-transition-all">
        <div className="moe-flex moe-w-full moe-items-center moe-justify-between moe-overflow-hidden">
          <div
            onClick={customerAction}
            className="moe-group moe-flex moe-cursor-pointer moe-items-center moe-gap-1 moe-overflow-hidden moe-rounded-2 moe-px-2 moe-py-1 hover:moe-bg-scrim"
          >
            <Avatar.Client
              name={data?.name ?? ''}
              src={data?.avatar ?? ''}
              showLoading={clientLoading}
              color="#fff"
              className="moe-h-[32px] moe-w-[32px] moe-shrink-0"
            />
            <div className="moe-overflow-hidden moe-text-ellipsis moe-whitespace-nowrap moe-text-base moe-font-bold moe-text-[#b4b4b4]">
              {data?.name}
            </div>
            <div className="moe-shrink-0 moe-text-s moe-font-bold moe-text-white moe-underline moe-opacity-0 group-hover:moe-opacity-100">
              {data?.customerId ? 'View' : 'Add client'}
            </div>
          </div>
          {!answerMode && data?.customerId && (
            <IconButton
              onPress={onLinkAction}
              className="moe-right-2 moe-shrink-0 moe-bg-[#232323] moe-text-white enabled:hover:moe-bg-scrim"
              icon={<MajorMessageOutlined />}
            ></IconButton>
          )}
        </div>
        {!!data?.businessUuid && (
          <div className="moe-group moe-mt-2 moe-flex moe-w-full moe-items-center moe-overflow-hidden moe-rounded-2 moe-px-2">
            <MajorLocationOutlined color="#b4b4b4" className="moe-ml-[7px] moe-mr-3 moe-h-5 moe-w-5" />
            <div className="moe-flex-1 moe-overflow-hidden">
              <div>
                <Typography.Text
                  variant="caption"
                  className="moe-overflow-hidden moe-text-ellipsis moe-whitespace-nowrap moe-text-white"
                >
                  {data.businessName}
                </Typography.Text>
                <Typography.Text
                  variant="caption"
                  className="moe-overflow-hidden moe-text-ellipsis moe-whitespace-nowrap moe-text-disabled"
                >
                  {data.companyName}
                </Typography.Text>
              </div>
            </div>
          </div>
        )}
        <div
          className="moe-group moe-mt-2 moe-flex moe-w-full moe-cursor-pointer moe-items-center moe-overflow-hidden moe-rounded-2 moe-p-2 hover:moe-bg-scrim"
          onClick={viewPetDetail}
        >
          <MajorPawsOutlined color="#b4b4b4" className="moe-ml-[7px] moe-mr-3 moe-h-5 moe-w-5" />
          {data?.petInfos?.length && data?.petInfos?.length > 0 ? (
            <div className="moe-flex-1 moe-overflow-hidden">
              {data?.petInfos?.length > 1 ? (
                <Typography.Text variant="caption" className="moe-text-white">
                  {data?.petInfos?.length + ' pets'}
                </Typography.Text>
              ) : (
                <div>
                  <Typography.Text
                    variant="caption"
                    className="moe-overflow-hidden moe-text-ellipsis moe-whitespace-nowrap moe-text-white"
                  >
                    {data.petInfos[0].petName}
                  </Typography.Text>
                  <Typography.Text
                    variant="caption"
                    className="moe-overflow-hidden moe-text-ellipsis moe-whitespace-nowrap moe-text-disabled"
                  >
                    {data.petInfos[0].petType}
                  </Typography.Text>
                </div>
              )}
            </div>
          ) : (
            <Typography.Text variant="caption" className="moe-flex-1 moe-text-white">
              No pet info
            </Typography.Text>
          )}
          {data?.petInfos?.length && data?.petInfos?.length > 0 && (
            <div className="moe-min-w-fit moe-shrink-0 moe-text-s moe-font-bold moe-text-white moe-underline moe-opacity-0 group-hover:moe-opacity-100">
              View
            </div>
          )}
        </div>
      </div>
      <div className="moe-flex moe-w-full moe-flex-row moe-items-center moe-overflow-hidden moe-px-3 moe-py-2">
        {isConnected && !!data?.isRecording && (
          <div className="moe-flex moe-h-6 moe-w-fit moe-shrink-0 moe-flex-row moe-items-center moe-gap-1 moe-rounded-2 moe-border moe-border-information moe-bg-information-subtle moe-px-2 moe-text-xs moe-text-information">
            REC:{subMessage}
            <div className="moe-relative moe-h-[6px] moe-w-[6px] moe-rounded-full moe-bg-red-600" />
          </div>
        )}
        <div
          className={cn(
            'moe-flex-1 moe-text-s moe-font-bold moe-text-information',
            isConnected && 'moe-pl-2',
            isEndStatus && 'moe-text-danger',
          )}
        >
          {(!data?.isRecording || !isConnected) && subMessage}
        </div>
        {answerMode ? (
          <div className="moe-flex moe-shrink-0 moe-items-center moe-justify-between">
            <Typography.Link className="moe-mr-2 moe-text-white" onClick={dismissAction}>
              Dismiss
            </Typography.Link>
            <IconButton
              className="moe-h-10 moe-w-[60px] moe-shrink-0 moe-px-4 moe-py-[10px]"
              variant="primary"
              color="success"
              disabled={loading}
              onPress={answerAction}
              icon={<MinorCallInboundOutlined />}
            ></IconButton>
          </div>
        ) : (
          <div className="moe-flex moe-shrink-0 moe-items-center moe-justify-between">
            <IconButton
              onPressEnd={switchKeyboardVisible}
              disabled={loading}
              variant="primary"
              className="moe-bg-[rgba(32, 32, 32, 0.40)] moe-mr-2 moe-h-10 moe-w-10 moe-shrink-0 moe-border moe-border-focus-dark moe-text-white enabled:hover:moe-bg-scrim"
              icon={<KeyboardIcon />}
            />
            {isConnected && (
              <IconButton
                onPressEnd={switchMute}
                disabled={loading}
                variant="primary"
                className="moe-bg-[rgba(32, 32, 32, 0.40)] moe-mr-2 moe-h-10 moe-w-10 moe-shrink-0 moe-border moe-border-focus-dark moe-text-white enabled:hover:moe-bg-scrim"
                icon={isMute ? <MajorMicOutlined /> : <MajorMicMuteOutlined />}
              />
            )}
            <IconButton
              color="danger"
              disabled={loading}
              variant="primary"
              onPress={() => {
                CallCenter.hangUp(uuid);
              }}
              className="moe-h-10 moe-w-[60px] moe-shrink-0 moe-px-4 moe-py-[10px]"
              icon={<MajorCallHangUpOutlined />}
            />
          </div>
        )}
      </div>

      <div
        className={cn(
          'moe-w-full moe-overflow-hidden moe-px-[18px] moe-pb-[12px] moe-pt-[17px] moe-transition-all moe-duration-500 moe-ease-in-out',
          keyboardVisible ? 'moe-h-[270px]' : 'moe-h-0 moe-py-0',
        )}
      >
        {['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#'].map((keyword) => {
          return (
            <div
              key={keyword}
              className="moe-border-neutral-dark moe-inline-block moe-h-[60px] moe-w-[33.3333%] moe-items-center moe-justify-center moe-text-white"
            >
              <div
                className="moe-mx-auto moe-flex moe-h-[60px] moe-w-[60px] moe-cursor-pointer moe-flex-row moe-items-center moe-justify-center moe-rounded-full moe-text-center moe-text-xl moe-font-bold moe-text-disabled hover:moe-bg-scrim hover:moe-text-white"
                onClick={() => onKeyboardDown(keyword)}
              >
                {keyword}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
