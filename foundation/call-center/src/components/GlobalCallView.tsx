import { Avatar, cn, Condition, MajorCallOutlined, MajorChevronRightOutlined, toast } from '@moego/ui';
import React, { useEffect, useRef, useState } from 'react';
import { CallCenter } from '../CallCenter';
import { CallErrorIdEnum, CallModel } from '../constant/callCenterConst';
import { GlobalCallCard } from './GlobalCallCard';
/**
 * get call id
 * @param call CallModel
 * @returns call id
 */
const getCallId = (call: CallModel) => (call.customerId ? `customer-${call.customerId}` : `client-${call.clientId}`);

export interface GlobalCallViewProps {
  onCreateClient: () => void;
  onClientClick: ({
    companyUuid,
    businessUuid,
    customerId,
  }: {
    companyUuid?: number;
    businessUuid?: number;
    customerId?: number;
  }) => void;
  onPetClick: ({
    companyUuid,
    businessUuid,
    customerId,
    petId,
  }: {
    companyUuid?: number;
    businessUuid?: number;
    customerId?: number;
    petId?: number;
  }) => void;
  onLinkedClick?: ({
    companyUuid,
    businessUuid,
    customerId,
  }: {
    companyUuid?: number;
    businessUuid?: number;
    customerId?: number;
  }) => void;
}

export interface GlobalCallMiniCardModel {
  avatar?: string;
  name?: string;
  duration?: string;
}
const GlobalCallView = (props: GlobalCallViewProps) => {
  const { onCreateClient, onClientClick, onPetClick, onLinkedClick } = props;
  const scrollRef = useRef<HTMLDivElement>(null);
  const [calls, setCalls] = useState<CallModel[]>([]);
  const [show, setShow] = useState(true);
  const [callingModel, setCallingModel] = useState<GlobalCallMiniCardModel | null>();

  useEffect(() => {
    const incomingAction = (param: CallModel) => {
      setShow(true);
      setTimeout(() => {
        setCalls((val) => [...val, param]);
      }, 300);
    };
    const callAction = (param: CallModel) => {
      setShow(true);
      setTimeout(() => {
        setCalls((val) => [...val, param]);
      }, 300);
    };
    const onError = (connectUuid: number | string, error: Error) => {
      if (CallErrorIdEnum.GlobalError === connectUuid) {
        toast({
          type: 'error',
          title: error.message,
        });
      }
    };
    CallCenter.addListener('onCalling', callAction);
    CallCenter.addListener('onIncoming', incomingAction);
    CallCenter.addListener('onError', onError);
    return () => {
      CallCenter.removeListener('onCalling', callAction);
      CallCenter.removeListener('onIncoming', incomingAction);
      CallCenter.removeListener('onError', onError);
    };
  }, []);

  const onAcceptAction = (CallModel: GlobalCallMiniCardModel) => {
    setCallingModel(CallModel);
  };

  const onCloseAction = (uuid: string) => {
    setCalls((val) => {
      const result = val.filter((call) => getCallId(call) !== uuid);
      setCallingModel(null);
      setShow(true);
      return result;
    });
  };

  return (
    <>
      <Condition if={calls.length > 0}>
        <div className="animate__animated animate__backInRight moe-fixed moe-right-5 moe-top-[68px] moe-z-[2147483005] moe-font-manrope">
          <div
            className={cn(
              'moe-duration-800 moe-relative moe-rounded-4 moe-bg-[#232323] moe-px-4 moe-py-3 moe-transition-all',
              'moe-flex moe-flex-row moe-items-center moe-gap-4 moe-overflow-hidden',
              'moe-h-[42px] moe-w-[49px]',
              {
                'moe-h-[56px] moe-w-[287px]': !show && !!callingModel,
                'moe-h-[42px] moe-w-[158px]': !show && !callingModel,
                'moe-h-[77px]': !show && !!callingModel && calls.length > 1,
              },
            )}
          >
            <MajorChevronRightOutlined
              className={cn(
                'moe-duration-800 moe-relative moe-h-6 moe-w-6 moe-shrink-0 moe-cursor-pointer moe-bg-[#232323] moe-text-white moe-transition-all moe-ease-linear',
                { 'moe-rotate-180': !show },
              )}
              onClick={() => setShow((val) => !val)}
            />
            <Condition if={!show}>
              <div className="moe-relative moe-h-auto moe-w-auto">
                <Condition if={!!callingModel}>
                  <div className="animate__animated animate__fadeInRight moe-flex moe-h-8 moe-max-w-[224px] moe-flex-row moe-items-center">
                    <Avatar.Client
                      name={callingModel?.name ?? ''}
                      src={callingModel?.avatar ?? ''}
                      color="#fff"
                      className="moe-h-[32px] moe-w-[32px] moe-shrink-0"
                    />
                    <div className="moe-ml-2 moe-flex-1 moe-overflow-hidden moe-text-ellipsis moe-whitespace-nowrap moe-text-s moe-text-[#b4b4b4]">
                      {callingModel?.name}
                    </div>
                    <MajorCallOutlined className="moe-ml-2 moe-h-6 moe-w-6 moe-shrink-0" color="#07AB4C" />
                    <div className="moe-ml-1 moe-shrink-0 moe-text-s moe-text-success">{callingModel?.duration}</div>
                  </div>
                </Condition>
                <Condition if={calls.length - (callingModel ? 1 : 0) > 0}>
                  <div
                    className={cn(
                      'animate__animated animate__fadeInRight moe-h-6 moe-w-fit moe-rounded-2 moe-bg-warning-bold moe-px-2 moe-py-1 moe-text-xs',
                      { 'moe-mt-2': !!callingModel },
                    )}
                  >
                    {calls.length - (callingModel ? 1 : 0)} calls waiting
                  </div>
                </Condition>
              </div>
            </Condition>
          </div>
        </div>
      </Condition>
      <div
        ref={scrollRef}
        className={cn(
          'moe-duration-600 moe-pointer-events-none moe-fixed moe-right-0 moe-top-[110px] moe-z-[2147483004] moe-pl-4 moe-transition-all moe-ease-linear',
          'moe-overflow-auto moe-pb-4 moe-font-manrope',
          { 'moe-translate-x-[320px]': !show },
        )}
        style={{
          height: 'calc(100% - 110px)',
          ...(calls.length > 4
            ? {
                background:
                  'linear-gradient(to right,rgba(255, 255, 255, 0),rgba(255, 255, 255, 0.7) 10%, rgba(255, 255, 255, 0.7))',
                maskImage:
                  'linear-gradient(to bottom,rgba(0, 0, 0, 0), rgba(0, 0, 0, 1) 16px, rgba(0, 0, 0, 1) 98%, rgba(0, 0, 0, 0) )',
                backdropFilter: 'blur(1px)',
              }
            : {}),
        }}
      >
        {calls.map((call) => {
          return (
            <GlobalCallCard
              key={getCallId(call)}
              call={call}
              onclose={() => onCloseAction(getCallId(call))}
              onAcceptAction={onAcceptAction}
              onCreateClient={onCreateClient}
              onClientClick={onClientClick}
              onPetClick={onPetClick}
              onLinkedClick={onLinkedClick}
            />
          );
        })}
      </div>
    </>
  );
};
GlobalCallView.displayName = 'GlobalCallView';

export { GlobalCallView };
