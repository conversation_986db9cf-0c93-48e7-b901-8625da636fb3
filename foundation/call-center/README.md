# Call center kit

## enterprise 开发流程

1. 进入`moego-enterprise-web-v1`项目, 执行 `pnpm link {path to moego-business-web/foundation/call-center}` 执行后双方建立联系,即可开发
2. 开发完毕后,先推送`moego-business-web` publish发版,然后进入`moego-enterprise-web-v1`安装最新依赖即可
3. 启动监听`pnpm run dev-watch`

## Boarding_Desktop开发流程

local环境是基于react 18,所以需要yalc工具辅助开发.

1. 安装yalc工具 `pnpm i yalc -g`
2. 进入call-center-kit安装包 `cd foundation/call-center`并执行 `yalc publish`
3. 进入业务仓库,执行`yalc add @moego/call-center --link`
4. 安装依赖`cd .yalc/@moego/call-center`并执行`pnpm install -P`,
5. 在call-center仓库启动监听`pnpm run dev-watch`
   **_注意请使用webpack开发,如果使用vite每次修改之后需要重启server并且加上--force清除构建缓存_**

## 注意事项

1. 由于是直接导出ts文件,所以请务必在入口index.ts里导出业务想使用的模块.而不是 import xx from '@moego/call-center/src/xxx'.否则会导致context等Symbol模块异常
2. 从`@moego/ui`引入的时候,也从index导入,不要写文件路径.比如dropdown

## 使用说明

1. 首先在应用入口注入 `CallCenter.register({ accountId: *** });` 开启全局监听
2. 使用拨打电话的api 直接 `CallCenter.call({...})` 进行拨打
