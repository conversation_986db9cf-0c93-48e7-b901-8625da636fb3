import { EDITOR_CLASS_CONTAINER } from './const';
import { calculateAttachmentSize, handleFileUpload } from './RichEditorPlugins/upload.utils';
import { checkButtonInContentIsValid } from './RichEditorPlugins/utils';
import { RichTextEditor, RichTextEditorRef } from './RichTextEditor';
export type { Editor } from 'tinymce';
export type * from './RichEditorPlugins/types';
export { addInlineStyleForContent } from './RichEditorPlugins/utils';
export {
  calculateAttachmentSize,
  checkButtonInContentIsValid,
  EDITOR_CLASS_CONTAINER,
  handleFileUpload,
  RichTextEditor,
  type RichTextEditorRef,
};
