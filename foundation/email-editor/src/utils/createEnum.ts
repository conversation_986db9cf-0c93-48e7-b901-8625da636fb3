/*
 * @since 2020-08-19 16:30:18
 * <AUTHOR> <<EMAIL>>
 */

import {
  GetUnionLength,
  GreaterThan,
  IsObject,
  IsString,
  JudgeBasicConst,
  JudgeObject,
  TuplifyUnion,
  UnionKeyValueToObject,
} from './advanced';

type ID = string | number;

type ExactKeyValue<V extends ID, D, JudgeD = JudgeBasicConst<Exclude<D[keyof D], undefined>>, FD = D> = FD extends (
  ...any: any[]
) => any
  ? Record<V, D>
  : JudgeD extends true
    ? UnionKeyValueToObject<V, D>
    : Record<V, D>;

type ExactEnumType<
  V extends ID,
  D,
  IsStringD = IsString<D>,
  IsObjectD = IsObject<D>,
  IsBigKey = GreaterThan<GetUnionLength<V>, 20>,
> = IsStringD extends true
  ? Record<V, D>
  : IsBigKey extends true
    ? Record<V, D>
    : IsObjectD extends true
      ? ExactKeyValue<V, D>
      : Record<V, D>;

export type EnumOptions<K extends string, V extends ID, D, JudgeD = JudgeObject<D>> = (Judge<PERSON> extends true
  ? Readonly<UnionKeyValueToObject<K, V>>
  : Readonly<Record<K, V>>) & {
  readonly mapLabels: ExactEnumType<V, D>;
  readonly mapKeys: JudgeD extends true ? UnionKeyValueToObject<V, K> : Record<V, K>;
  readonly values: JudgeD extends true ? TuplifyUnion<V> : V[];
  readonly keys: JudgeD extends true ? TuplifyUnion<K> : K[];
};

export type EnumKeys<T extends EnumOptions<any, any, any, boolean>> =
  T extends EnumOptions<infer K, infer _V, infer _D, unknown> ? K : never;
export type EnumLabels<T extends EnumOptions<any, any, any, boolean>> =
  T extends EnumOptions<infer _K, infer _V, infer D, unknown> ? D : never;
export type EnumValues<T extends EnumOptions<any, any, any, boolean>> =
  T extends EnumOptions<infer _K, infer V, infer _D, unknown> ? V : never;

/**
 * enum 工具函数
 *
 * 通常情况下, 我们不关注后端返回的 enum 类型的具体数值是多少, 比如 staff 的 employeeCategory,
 * 谁代表了 employer, 谁代表了 employee, 我们需要关注的是这个 enum 都有哪些有意义的值, 因此我们
 * 在声明 enum 类型的时候, 值都指定为 number, 但是禁止代码中 magic number 的存在, 而应该用此处
 * 定义好的 key 来进行匹配操作.
 *
 * 同时此函数提供了一些辅助结构来对 key 和 value 进行遍历及取值, 参考 {@see EnumOptions}.
 *
 * 如果需要完善类型提示和严格类型检查, createEnum 入参需要加上 `as const`.
 *
 * @example 严格模式
 * ```ts
 * const StaffCategory = createEnum({ Employee: [0, 'employee'], Employer: [1, 'employer'] } as const)
 * ```
 *
 * @example
 * ```ts
 * const StaffCategory = createEnum({ Employee: [0, 'employee'], Employer: [1, 'employer'] })
 *
 * if (staff.employCategory === StaffCategory.Employee) {
 *   // do something for employee
 * }
 * ```
 *
 * @param schema
 */
export function createEnum<K extends string, V, D>(schema: Record<K, readonly [V, D]>): EnumOptions<K, V & ID, D> {
  return createStrictEnum(schema as Record<K, readonly [V & ID, D]>);
}

export function createStrictEnum<K extends string, V extends ID, D>(
  schema: Record<K, readonly [V, D]>,
): EnumOptions<K, V, D> {
  const output = {
    mapLabels: {},
    mapKeys: {},
    values: [],
    keys: [],
  } as any;
  let isNum = false;
  for (const key in schema) {
    if (Object.prototype.hasOwnProperty.call(schema, key)) {
      const [value, label] = schema[key];
      isNum = typeof value === 'number';
      output[key] = value as any;
      output.mapLabels[value] = label;
      output.mapKeys[value] = key;
      output.values.push(value);
      output.keys.push(key);
    }
  }
  isNum && (output.values as number[]).sort((a, b) => a - b);
  return output;
}

export function nextItem(value: number, holder: EnumOptions<any, any, any>) {
  const index = holder.values.indexOf(value);
  return holder.values[index === -1 || index === holder.values.length - 1 ? 0 : index + 1];
}

export class BoolOptions<T> {
  constructor(
    readonly True: T,
    readonly False: T,
    readonly NotSet: T,
  ) {}

  toggle(value: T) {
    return value === this.True ? this.False : this.True;
  }

  truly(value: T) {
    return this.True === value;
  }

  falsy(value: T) {
    return this.False === value;
  }

  get(value: boolean) {
    return value ? this.True : this.False;
  }
}

export const LegacyBool = new BoolOptions(1, 2, 0);

export const NormalBool = new BoolOptions(1, 0, 0);
