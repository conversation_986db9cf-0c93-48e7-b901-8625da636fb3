// Union to tuple
export type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;
export type LastOf<T> = UnionToIntersection<T extends any ? () => T : never> extends () => infer R ? R : never;

export type Push<T extends any[], V> = [...T, V];
export type Unshift<T extends any[], V> = [V, ...T];

export type TuplifyUnion<T, L = LastOf<T>, N = [T] extends [never] ? true : false> = true extends N
  ? []
  : Push<TuplifyUnion<Exclude<T, L>>, L>;
export type UnionifyTuple<T> = T extends any[] ? T[number] : never;

/**
 * 合并两个对象key value，若key重复，则保留F类型的value
 */
export type Merge<F, S> = {
  [P in keyof F | keyof S]: P extends keyof S ? S[P] : P extends keyof F ? F[P] : never;
};

/**
 * 联合类型的key 联合类型的value合并到一个对象中
 * @example UnionKeyValueToObject<0 | 1 | 2, '00' | '11' | '22'> --> { 0: '00', 1: '11', 2: '22' }
 */
export type UnionKeyValueToObject<V, D, VL = LastOf<V>, DL = LastOf<D>, ED = Exclude<D, DL>> = [V] extends [never]
  ? object
  : Merge<
      Record<VL extends keyof any ? VL : never, DL>,
      UnionKeyValueToObject<Exclude<V, VL>, ED extends [never] ? D : ED>
    >;

export type IsString<T> = string extends T ? true : false;

export type IsObject<T> = T extends object ? true : false;

export type GetUnionLength<T, C extends any[] = [], K = LastOf<T>> = [T] extends [never]
  ? C['length']
  : GetUnionLength<Exclude<T, K>, [K, ...C]>;

export type Length<T extends any[]> = T['length'];

export type IsAny<T> = 0 extends 1 & T ? true : false;

type _IncDigit = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0];
type Digit = _IncDigit[number];

type _Inc<T extends string> = T extends `${infer F}${Digit}`
  ? T extends `${F}${infer L extends Digit}`
    ? `${L extends 9 ? _Inc<F> : F}${_IncDigit[L]}`
    : never
  : 1;

type Increment<T extends number> = number extends T
  ? number
  : `${T}` extends `${string}${'.' | '+' | '-' | 'e'}${string}`
    ? number
    : _Inc<`${T}`> extends `${infer N extends number}`
      ? N
      : never;

/**
 * RangeToUnion<1, 5> => 1 | 2 | 3 | 4 | 5
 */
export type RangeToUnion<A extends number, B extends number, Result extends number = A> = A extends B
  ? Result | A
  : RangeToUnion<Increment<A>, B, Result | A>;

export type ArrayWithLength<T extends number, U extends any[] = []> = U['length'] extends T
  ? U
  : ArrayWithLength<T, [true, ...U]>;

/**
 * @example GreaterThan<5, 3> => true
 */
export type GreaterThan<T extends number, U extends number> =
  ArrayWithLength<U> extends [...ArrayWithLength<T>, ...any] ? false : true;

export type JudgeBasicConst<T> = string extends T
  ? false
  : number extends T
    ? false
    : boolean extends T
      ? false
      : null extends T
        ? false
        : undefined extends T
          ? false
          : true;

export type JudgeObject<T> = T extends object ? JudgeBasicConst<Exclude<T[keyof T], undefined>> : JudgeBasicConst<T>;

export type Identity<T> = T;
