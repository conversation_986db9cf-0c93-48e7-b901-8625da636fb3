import { nunitoFont } from './font';
import { EditorInitOptions } from './RichEditorPlugins/types';
export const EDITOR_COMMAND_INVALID_BUTTON_DIALOG = 'mceInvalidButtonDialog';
export const EDITOR_COMMAND_OPEN_BUTTON_DIALOG = 'mceOpenButtonDialog';
export const EDITOR_COMMAND_OPEN_HIGHLIGHT_SECTION_DIALOG = 'mceOpenHighlightSectionDialog';
// it is the tinymce native command
export const EDITOR_COMMAND_OPEN_NATIVE_LINK_DIALOG = 'mceLink';
export const EDITOR_COMMAND_REMOVE_BUTTON = 'mceRemoveButton';
export const EDITOR_COMMAND_REMOVE_HIGHLIGHT_SECTION = 'mceRemoveHighlightSection';

// css class
export const EDITOR_CLASS_WRAPPER = 'moe-editor-wrapper';
export const EDITOR_CLASS_WRAPPER_INNER = 'moe-editor-wrapper__inner';
export const EDITOR_CLASS_CONTAINER = 'moe-editor-container';

export const DATE_FORMAT_EXCHANGE = 'YYYY-MM-DD';

export const IS_DEV = () => window.location.href.includes('debugging');

export const HtmlPlaceholder = {
  AppDownloadLink: '{AppDownloadLink}',
};

export const RICHTEXT_API_KEY = '73vjddjx07e14rnaejwbx8ejfne90aqk7l18pxvxgn21t0bd';

export const RICHTEXT_INIT_CONFIG: EditorInitOptions = {
  branding: false,
  menubar: false,
  toolbar: [
    {
      name: 'history',
      items: ['undo', 'redo'],
    },
    {
      name: 'styles',
      items: ['styles'],
    },
    {
      name: 'formatting',
      items: ['bold', 'italic'],
    },
    {
      name: 'alignment',
      items: ['alignleft', 'aligncenter', 'alignright', 'alignjustify'],
    },
    {
      name: 'indentation',
      items: ['outdent', 'indent'],
    },
    {
      name: 'list',
      items: ['bullist', 'numlist'],
    },
    {
      name: 'others',
      items: ['emoticons', 'link', 'fullscreen'],
    },
  ],
  plugins: 'wordcount image link paste emoticons lists fullscreen',
  contextmenu: 'copy paste link',
  paste_auto_cleanup_on_paste: true,
  paste_remove_styles: true,
  paste_remove_styles_if_webkit: true,
  paste_strip_class_attributes: true,
  paste_data_images: true,
  paste_preprocess: function (_, args) {
    // paste_remove_styles 没有生效，手动处理
    // TODO<vision,p2> 后续看看是否有更好的处理方式
    args.content = args.content.replace(/style="[^"]*text-indent:[^;]+;?[^"]*"/g, '');
  },
  autoresize: true,
  min_height: 200,
  max_height: 300,
  content_style: `
  ${nunitoFont}
  body {
    font-family: Nunito, system-ui ,-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }
  `,
};
