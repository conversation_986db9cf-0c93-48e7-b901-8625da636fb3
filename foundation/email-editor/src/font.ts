// 关于 font-face: https://www.zhangxinxu.com/wordpress/2017/03/css3-font-face-src-local/
import NunitoBlackItalic from '../assets/font/Nunito-BlackItalic.ttf';
import NunitoBold from '../assets/font/Nunito-Bold.ttf';
import NunitoItalic from '../assets/font/Nunito-Italic.ttf';
import NunitoLightItalic from '../assets/font/Nunito-LightItalic.ttf';
import NunitoSemiBold from '../assets/font/Nunito-SemiBold.ttf';
import NunitoSemiBoldItalic from '../assets/font/Nunito-SemiBoldItalic.ttf';
import ProximaNovaBold from '../assets/font/ProximaNova-Bold.otf';
import ProximaNovaRegular from '../assets/font/ProximaNova-Regular.otf';
import ProximaNovaSemiBold from '../assets/font/ProximaNova-SemiBold.otf';

export const nunitoFont = `
@font-face {
  font-family: 'Nunito';
  src: url('${NunitoSemiBold}');
  font-weight: 100 300;
}

@font-face {
  font-family: 'Nunito';
  src: url('${NunitoSemiBold}');
  font-weight: 400;
}

@font-face {
  font-family: 'Nunito';
  src: url('${NunitoSemiBold}');
  font-weight: 500 600;
}

@font-face {
  font-family: 'Nunito';
  src: url('${NunitoBold}');
  font-weight: 700 900;
}

@font-face {
  font-family: 'Nunito';
  src: url('${NunitoLightItalic}');
  font-style: italic;
  font-weight: 100 300;
}

@font-face {
  font-family: 'Nunito';
  src: url('${NunitoItalic}');
  font-style: italic;
  font-weight: 400;
}

@font-face {
  font-family: 'Nunito';
  src: url('${NunitoSemiBoldItalic}');
  font-style: italic;
  font-weight: 500 600;
}

@font-face {
  font-family: 'Nunito';
  src: url('${NunitoBlackItalic}');
  font-style: italic;
  font-weight: 700 900;
}


@font-face {
  font-family: 'ProximaNova';
  src: url('${ProximaNovaRegular}');
  font-weight: 400;
}

@font-face {
  font-family: 'ProximaNova';
  src: url('${ProximaNovaSemiBold}');
  font-weight: 500 600;
}

@font-face {
  font-family: 'ProximaNova';
  src: url('${ProximaNovaBold}');
  font-weight: 700 900;
}

`;
