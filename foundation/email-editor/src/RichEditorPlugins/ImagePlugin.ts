import chroma from 'chroma-js';
import { Editor, Ui } from 'tinymce';
import SvgIconEditNewSvg from '../../assets/svg/icon-edit-new.svg';
import SvgIconEditorImageEditSvg from '../../assets/svg/icon-editor-image-edit.svg';
import { EditorComponentApi, RichEditorInitHandler, RichEditorOptionsHandler, RichEditorPlugin } from './types';
import { handleImageUpload } from './upload.utils';
import { createButtonTooltip, getCurrentNode, isDefinedInPlugins, isDefinedInToolbar, svgTransformer } from './utils';

interface ImageOptions {
  id: string;
  alt: string;
  backgroundColor: string;
  borderRadius: string;
  src: string;
  size: {
    width: string;
    height: string;
  };
}

const templateImage = (options: ImageOptions) => `
<img
  class="moego-editor-image"
  style="
    object-fit: cover;
    background-color: ${options.backgroundColor};
    border-radius: ${options.borderRadius};
    overflow: hidden;
  "
  alt="${options.alt}"
  src="${options.src}"
  id="${options.id}"
  width="${String(options.size.width)}"
  height="${String(options.size.height)}"
/>
`;

export const ImageInitOptionsHandler: RichEditorOptionsHandler = (options, getApi: () => EditorComponentApi) => {
  const hasImagePlugin = isDefinedInPlugins(options.plugins, 'image');
  const hasImageToolbar = isDefinedInToolbar(options.toolbar, 'image');
  if (!hasImagePlugin || !hasImageToolbar) {
    return options;
  }
  options.image_description = false;
  options.file_picker_types = 'image';
  options.file_picker_callback = async (cb) => {
    const res = await handleImageUpload({
      getApi,
      ownerId: options.ownerId,
    });
    if (res) {
      cb(res.url, { title: res.name });
    }
  };
  return options;
};

let uid = 0;
const buildDialogOptions = (editor: Editor) => {
  let hasImageBlock = false;
  const options: Partial<ImageOptions> = {};
  const currentImage = getCurrentNode<HTMLImageElement>(editor, 'img');
  if (currentImage) {
    hasImageBlock = true;
    const backgroundColor = editor.dom.getStyle(currentImage, 'background-color', true) || '';

    try {
      options.alt = currentImage.getAttribute('alt') || '';
      options.id = currentImage.id;
      options.src = currentImage.dataset.mceSrc || currentImage.src;
      options.borderRadius = editor.dom.getStyle(currentImage, 'border-radius', true) || '';
      options.size = {
        width: String(currentImage.width),
        height: String(currentImage.height),
      };
      options.id = currentImage.id;
      options.backgroundColor = chroma(backgroundColor).hex().toUpperCase();
    } catch (e) {
      // the color maybe invalid
      console.error(e);
    }
  }

  const id = (options.id = options.id || `image-${uid++}`);

  // edit image border-radius or background-color
  const dialogOptions: Ui.Dialog.DialogSpec<Ui.Dialog.DialogData> = {
    title: `Edit image attributes`,
    body: {
      type: 'panel',
      items: [
        {
          type: 'input',
          name: 'src',
          label: 'Image URL',
        },
        {
          type: 'input',
          name: 'alt',
          label: 'Image alt',
        },
        {
          type: 'sizeinput',
          name: 'size',
        },
        {
          type: 'input',
          name: 'borderRadius',
          label: 'Border radius',
        },
        {
          type: 'colorinput',
          name: 'backgroundColor',
          label: 'Background color',
        },
      ],
    },
    onSubmit: function (dialog) {
      const _options = dialog.getData() as ImageOptions;
      const options = {
        ..._options,
        id,
      };
      if (hasImageBlock && currentImage) {
        editor.dom.setStyle(currentImage, 'background-color', options.backgroundColor);
        editor.dom.setAttrib(currentImage, 'src', options.src);
        editor.dom.setAttrib(currentImage, 'id', id);
        editor.dom.setAttrib(currentImage, 'alt', options.alt);
        editor.dom.setStyle(currentImage, 'border-radius', options.borderRadius);
        editor.dom.setAttrib(currentImage, 'width', options.size.width.toString());
        editor.dom.setAttrib(currentImage, 'height', options.size.height.toString());
      } else {
        const html = templateImage(options);
        editor.insertContent(html);
      }
      dialog.close();
    },
    buttons: [
      {
        text: 'Close',
        type: 'cancel',
      },
      {
        text: 'Save',
        type: 'submit',
        primary: true,
        enabled: true,
      },
    ],
  };

  const defaultData: Partial<ImageOptions> = {
    backgroundColor: options.backgroundColor || '',
    alt: options.alt || '',
    src: options.src || '',
    borderRadius: options.borderRadius || '0',
    size: {
      width: options.size?.width || '0',
      height: options.size?.height || '0',
    },
    id,
  };

  return {
    dialogOptions,
    defaultData,
  };
};

const showDialog = (editor: Editor) => {
  const res = buildDialogOptions(editor);
  const dialogApi = editor.windowManager.open(res.dialogOptions);
  dialogApi.setData(res.defaultData);
};

export const ImageInitHandler: RichEditorInitHandler = (editor, options, getApi) => {
  const hasImagePlugin = isDefinedInPlugins(options.plugins, 'image');
  const hasImageToolbar = isDefinedInToolbar(options.toolbar, 'image');
  if (!hasImagePlugin || !hasImageToolbar) {
    return;
  }
  editor.ui.registry.addIcon('moegoimageedit', svgTransformer(SvgIconEditorImageEditSvg));
  editor.ui.registry.addIcon('moegoedit', svgTransformer(SvgIconEditNewSvg));

  editor.ui.registry.addButton('imagelink', {
    icon: 'moegoimageedit',
    onAction: async () => {
      const node = getCurrentNode(editor, 'img');
      if (node) {
        const res = await handleImageUpload({
          getApi,
          ownerId: options?.ownerId,
        });
        if (res) {
          editor.dom.setAttrib(node, 'src', res.url);
          editor.dom.setStyle(node, 'object-fit', 'cover');
        }
      }
    },
  });

  editor.ui.registry.addButton('imageedit', {
    icon: 'moegoedit',
    onAction: () => editor.execCommand('openImageEditDialog'),
  });

  editor.addCommand('openImageEditDialog', () => {
    showDialog(editor);
  });

  createButtonTooltip(editor, 'Insert/edit image');
};

export const ImagePlugin: RichEditorPlugin = {
  options: ImageInitOptionsHandler,
  init: ImageInitHandler,
};
