import SparkMD5 from 'spark-md5';

import { toast } from '@moego/ui';
import axios from 'axios';
import dayjs from 'dayjs';
import { appendQuery } from 'monofile-utilities/lib/query-string';
import { BWebFileClient, EnterpriseFileClient } from '../api/client';
import { EditorComponentApi } from './types';
import { md5HexToBase64 } from './utils';

export const calculateAttachmentSize = (size: number) => {
  if (size < 1024) {
    return `${size}B`;
  }
  if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)}KB`;
  }
  return `${(size / 1024 / 1024).toFixed(2)}MB`;
};

export const setTinyMCENativeButtonDisabledStatus = () => {
  const uploadButton = document.querySelector('button[title="Source"].tox-browse-url');
  const saveButton = document.querySelector('button[title="Save"].tox-button');

  return {
    toggle: (disabled: boolean) => {
      if (disabled) {
        uploadButton?.setAttribute('disabled', 'true');
        saveButton?.setAttribute('disabled', 'true');
      } else {
        uploadButton?.removeAttribute('disabled');
        saveButton?.removeAttribute('disabled');
      }
    },
  };
};

export const handleFileUpload = async ({
  getApi,
  file,
  acceptFileType,
  maxSize,
  ownerId,
}: {
  getApi?: () => EditorComponentApi;
  file?: File;
  acceptFileType?: string;
  maxSize?: number;
  ownerId?: string;
}) => {
  return new Promise<UploadFileInfo | false>((resolve) => {
    const handleUpload = async () => {
      if (file) {
        const res = await uploadFile(file, getApi, maxSize, ownerId);
        resolve(res);
        return;
      } else {
        const input = document.createElement('input');
        input.setAttribute('type', 'file');
        if (acceptFileType) {
          input.setAttribute('accept', acceptFileType);
        }

        const { toggle } = setTinyMCENativeButtonDisabledStatus();

        input.addEventListener('change', async (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            toggle(true);
            const res = await uploadFile(file, getApi, maxSize, ownerId);
            resolve(res);
            toggle(false);
          } else {
            resolve(false);
          }
        });

        input.addEventListener('cancel', () => {
          resolve(false);
        });

        input.click();
      }
    };
    handleUpload();
  });
};

export const handleImageUpload = async ({
  getApi,
  file,
  ownerId,
}: {
  getApi?: () => EditorComponentApi;
  file?: File;
  ownerId?: string;
}) => {
  return await handleFileUpload({
    getApi,
    file,
    acceptFileType: 'image/*',
    ownerId: ownerId,
  });
};

const bWebUploadHandler = (
  file: File,
  onProgress: { (): void; (arg0: number): void },
  onSuccess: (url: string) => void,
  onError: (reason: any) => void,
  ownerId?: string,
) => {
  const upload = async () => {
    const reader = new FileReader();

    reader.onload = async (event) => {
      const arrayBuffer = event.target?.result;
      if (arrayBuffer instanceof ArrayBuffer) {
        const spark = new SparkMD5.ArrayBuffer();
        spark.append(arrayBuffer);
        const hashHex = spark.end();
        const base64Md5 = md5HexToBase64(hashHex);
        try {
          const res = await BWebFileClient.getUploadPresignedUrl({
            fileName: file.name || `${dayjs().format('YYYYMMDDHHmmss')}.jpg`,
            ownerType: 'account',
            ownerId: ownerId || '',
            usage: 'USED_FOR_EMAIL_TEMPLATE_IMAGE',
            md5: base64Md5,
            fileSizeByte: `${file.size}`,
            metadata: {},
          });
          console.log(res.metadata);
          await axios.put(res.presignedUrl, arrayBuffer, {
            headers: {
              'content-md5': base64Md5,
              'content-type': res.metadata?.['Content-Type'],
              'x-amz-acl': 'public-read',
            },
            onUploadProgress: (progressEvent) => {
              if (!progressEvent.total) return;
              const percentCompleted = Math.round(progressEvent.loaded / progressEvent.total);
              onProgress(percentCompleted);
            },
          });
          const {
            file: { accessUrl, fileName },
          } = await BWebFileClient.flushFile({
            fileId: res.fileId,
          });
          onProgress(1);
          onSuccess(appendQuery(accessUrl, { name: fileName }));
        } catch (e) {
          onError(e);
        }
      }
    };
    reader.readAsArrayBuffer(file);
  };
  upload();
};

const enterpriseUploadHandler = (
  file: File,
  onProgress: { (): void; (arg0: number): void },
  onSuccess: (url: string) => void,
  onError: (reason: any) => void,
  ownerId?: string,
) => {
  const upload = async () => {
    const reader = new FileReader();

    reader.onload = async (event) => {
      const arrayBuffer = event.target?.result;
      if (arrayBuffer instanceof ArrayBuffer) {
        const spark = new SparkMD5.ArrayBuffer();
        spark.append(arrayBuffer);
        const hashHex = spark.end();
        const base64Md5 = md5HexToBase64(hashHex);
        try {
          const res = await EnterpriseFileClient.getUploadPresignedURL({
            fileName: file.name || `${dayjs().format('YYYYMMDDHHmmss')}.jpg`,
            ownerType: 'account',
            ownerId: ownerId || '',
            usage: 'USED_FOR_EMAIL_TEMPLATE_IMAGE',
            md5: base64Md5,
            fileSizeByte: `${file.size}`,
          });
          axios
            .put(res.presignedUrl, arrayBuffer, {
              headers: {
                'content-md5': base64Md5,
                'content-type': res.contentType,
                'x-amz-acl': 'public-read',
              },
              onUploadProgress: (progressEvent) => {
                if (!progressEvent.total) return;
                const percentCompleted = Math.round(progressEvent.loaded / progressEvent.total);
                onProgress(percentCompleted);
              },
            })
            .then(() => {
              onProgress(1);
              onSuccess(appendQuery(res.accessUrl, { name: file.name }));
            })
            .catch(onError);
        } catch (e) {
          console.error(e);
          onError(e);
        }
      }
    };
    reader.readAsArrayBuffer(file);
  };
  upload();
};

const defaultUploadHandler = document.location.hostname.includes('enterprise.')
  ? enterpriseUploadHandler
  : bWebUploadHandler;

export interface UploadFileInfo {
  url: string;
  name: string;
  type: string;
  size: number;
}

const uploadFile = (file: File, getApi?: () => EditorComponentApi, maxFileSize?: number, ownerId?: string) => {
  return new Promise<UploadFileInfo | false>((resolve) => {
    if (maxFileSize && file.size > maxFileSize) {
      return toast({
        type: 'error',
        title: `File size is too large, please upload a file smaller than ${calculateAttachmentSize(maxFileSize)}`,
      });
    }
    const api = getApi?.();
    api?.handleLoading(true);
    defaultUploadHandler(
      file,
      () => {},
      (url: string) => {
        resolve({
          url,
          name: file.name,
          type: file.type,
          size: file.size,
        });
        api?.handleLoading(false);
      },
      (reason: Error) => {
        console.error(reason);
        toast({
          type: 'error',
          title: `Upload failed please try again later`,
        });
        api?.handleLoading(false);
        resolve(false);
      },
      ownerId,
    );
  });
};
