import SvgIconEditorVariableSvg from '../../assets/svg/icon-editor-variable.svg';
import { RichEditorInitHandler, RichEditorPlugin, RichEditorPluginInfo } from './types';
import { createButtonTooltip, isDefinedInToolbar, svgTransformer } from './utils';

const PluginInfo = RichEditorPluginInfo.mapLabels[RichEditorPluginInfo.AddVariable];

export const VariableInitPlugin: RichEditorInitHandler = (editor, options) => {
  isDefinedInToolbar(options.toolbar, PluginInfo.name, () => {
    editor.ui.registry.addIcon(PluginInfo.name, svgTransformer(SvgIconEditorVariableSvg));
    editor.ui.registry.addMenuButton(PluginInfo.name, {
      tooltip: PluginInfo.tooltip,
      icon: PluginInfo.name,
      onSetup() {
        createButtonTooltip(editor, PluginInfo.tooltip, (elm) => {
          elm.style.width = '34px';
        });
        return () => {};
      },
      fetch: async (callback) => {
        callback(options?.getVariables?.(editor) ?? []);
      },
    });
  });
};

export const VariablePlugin: RichEditorPlugin = {
  init: VariableInitPlugin,
};
