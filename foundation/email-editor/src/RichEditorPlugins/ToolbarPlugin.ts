// for extends origin toolbar
// for change origin toolbar button icon
import SvgIconEditorEmojiSvg from '../../assets/svg/icon-editor-emoji.svg';
import SvgIconEditorImageSvg from '../../assets/svg/icon-editor-image.svg';
import SvgIconEditorLinkSvg from '../../assets/svg/icon-editor-link.svg';
import SvgIconEditorTextFormatSvg from '../../assets/svg/icon-editor-text-format.svg';
import { RichEditorInitHandler, RichEditorPlugin, RichEditorPluginInfo } from './types';
import { createButtonTooltip, isDefinedInToolbar, svgTransformer } from './utils';

const alignmentList = ['align-left', 'align-center', 'align-right', 'align-justify'];

const PluginInfo = RichEditorPluginInfo.mapLabels[RichEditorPluginInfo.TextFormat];
const AlignPluginInfo = RichEditorPluginInfo.mapLabels[RichEditorPluginInfo.Alignment];

const ToolbarInitPlugin: RichEditorInitHandler = (editor, options) => {
  editor.ui.registry.addSplitButton(AlignPluginInfo.name, {
    icon: 'align-left',
    tooltip: AlignPluginInfo.tooltip,
    onAction: (_) => {
      editor.execCommand('mceToggleFormat', false, 'alignleft');
    },
    onItemAction(_, value) {
      const formatter = value.replace('-', '');
      editor.execCommand('mceToggleFormat', false, formatter);
    },
    fetch(callback) {
      const items = alignmentList.map(
        (item) =>
          ({
            type: 'choiceitem',
            icon: item,
            value: item,
          }) as const,
      );
      callback(items);
    },
  });

  isDefinedInToolbar(options.toolbar, PluginInfo.name, () => {
    editor.ui.registry.addIcon(PluginInfo.name, svgTransformer(SvgIconEditorTextFormatSvg));

    editor.ui.registry.addGroupToolbarButton(PluginInfo.name, {
      icon: PluginInfo.name,
      tooltip: PluginInfo.tooltip,
      items:
        'bold italic underline strikethrough alignment | fontsize forecolor backcolor | numlist bullist outdent indent blockquote',
      onSetup() {
        createButtonTooltip(editor, PluginInfo.tooltip);
        // the following is native toolbar button
        createButtonTooltip(editor, 'Emojis');
        createButtonTooltip(editor, 'Insert/edit link');
        return () => {};
      },
    });
  });

  editor.ui.registry.addIcon('image', svgTransformer(SvgIconEditorImageSvg));
  editor.ui.registry.addIcon('emoji', svgTransformer(SvgIconEditorEmojiSvg));
  editor.ui.registry.addIcon('link', svgTransformer(SvgIconEditorLinkSvg));
  editor.once('init', () => {
    const elms = document.querySelectorAll(`div[role="toolbar"]`);
    elms.forEach((elm) => {
      editor.dom.setAttrib(elm, 'title', '');
    });
  });
};
export const ToolbarPlugin: RichEditorPlugin = {
  init: ToolbarInitPlugin,
};
