import { toast } from '@moego/ui';
import { TinyMCE } from 'tinymce';
import SvgIconAnnounceSvg from '../../assets/svg/icon-announce.svg';
import { EDITOR_CLASS_WRAPPER_INNER } from '../const';
import { RichEditorInitHandler, RichEditorPlugin, RichEditorPluginInfo } from './types';
import { createButtonTooltip, isDefinedInToolbar, svgTransformer } from './utils';

declare const tinymce: TinyMCE;

const PluginInfo = RichEditorPluginInfo.mapLabels[RichEditorPluginInfo.ImportHTML];

const ImportInitPlugin: RichEditorInitHandler = (editor, options) => {
  isDefinedInToolbar(options.toolbar, PluginInfo.name, () => {
    editor.ui.registry.addIcon(PluginInfo.name, svgTransformer(SvgIconAnnounceSvg));
    editor.ui.registry.addButton(PluginInfo.name, {
      tooltip: PluginInfo.tooltip,
      icon: PluginInfo.name,
      onAction: () => {
        const input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.setAttribute('accept', 'text/html');

        input.addEventListener('change', (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = function (event: ProgressEvent<FileReader>) {
              const fileContent = event.target?.result;
              if (fileContent) {
                const parsedHTML = tinymce.html.DomParser().parse(fileContent as string);
                let nextNode = parsedHTML.walk();
                while (nextNode) {
                  if (
                    nextNode.attributes?.find(
                      (item) => item.name === 'class' && item.value === EDITOR_CLASS_WRAPPER_INNER,
                    )
                  ) {
                    const children = nextNode.children();
                    let result = '';
                    for (const item of children) {
                      const serializedHTML = tinymce.html.Serializer().serialize(item);
                      result += serializedHTML;
                    }
                    editor.setContent(result);
                    toast({
                      type: 'success',
                      title: 'Imported HTML successfully',
                    });
                    return;
                  }
                  nextNode = nextNode.walk();
                }
                // do nothing if the imported HTML is not exported from editor
                toast({
                  type: 'error',
                  title: 'Invalid HTML template, please try again',
                });
              }
            };
            reader.readAsText(file);
          }
        });

        input.click();
      },
      onSetup() {
        createButtonTooltip(editor, PluginInfo.tooltip);
        return () => {};
      },
    });
  });
};

export const ImportPlugin: RichEditorPlugin = {
  init: ImportInitPlugin,
};
