import { createHttpClient } from '@moego/api-web/createHttpClient';
import { FileService as BWebFileService } from '@moego/api-web/moego/api/file/v2/file_api';
import { FileService as EnterpriseFileService } from '@moego/api-web/moego/enterprise/file/v1/file_api';
import { HttpClient } from '@moego/http-client';

const http = new HttpClient();
http.setOptions({
  restfulBaseUrl: '',
});

export const EnterpriseFileClient = createHttpClient<EnterpriseFileService>('moego.enterprise.file.v1.FileService')(
  http.rpc,
);
export const BWebFileClient = createHttpClient<BWebFileService>('moego.api.file.v2.FileService')(http.rpc);
