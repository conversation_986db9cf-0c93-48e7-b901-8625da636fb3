import { cn, Condition, Spin } from '@moego/ui';
import { Editor } from '@tinymce/tinymce-react';
import { InitOptions } from '@tinymce/tinymce-react/lib/cjs/main/ts/components/Editor';
import { useBoolean, useControllableValue, useMemoizedFn, useUnmount } from 'ahooks';
import React, { forwardRef, useImperativeHandle, useMemo, useRef } from 'react';
import { Editor as TinyMCE } from 'tinymce';
import '../assets/css/RichTextEditor.css';
import { EDITOR_COMMAND_INVALID_BUTTON_DIALOG, RICHTEXT_API_KEY, RICHTEXT_INIT_CONFIG } from './const';
import { PluginEntry } from './RichEditorPlugins/PluginEntry';
import { EditorComponentApi, EditorInitOptions } from './RichEditorPlugins/types';
import { handleImageUpload, UploadFileInfo } from './RichEditorPlugins/upload.utils';

const plugins: string[] = [];

interface RichTextEditorProps {
  options?: EditorInitOptions;
  defaultValue?: string;
  value?: string;
  onChange?: (content: string) => void;
  handleUploadAttachment?: (file?: File) => Promise<void>;
  onBlur?: (content: string) => void;
  onFocus?: () => void;
  onDirty?: () => void;
  hasError?: boolean;
  onInit?: (editor: TinyMCE) => void;
  setContentAfterInit?: boolean;
  className?: string;
  showErrorStyle?: boolean;
}

export interface RichTextEditorRef {
  openInvalidButtonDialog: () => void;
}

export const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>((props, ref) => {
  const editorRef = useRef<TinyMCE | null>(null);
  const { options, showErrorStyle, className } = props;
  const [loading, setLoadingAction] = useBoolean(true);
  const [setContentAfterInit, setContentAfterInitAction] = useBoolean(
    !!props.setContentAfterInit && props.value === '',
  );
  const init: EditorInitOptions = useMemo(
    () => ({
      ...RICHTEXT_INIT_CONFIG,
      ...(options || {}),
    }),
    [],
  );

  const openInvalidButtonDialog = () => {
    editorRef.current?.execCommand(EDITOR_COMMAND_INVALID_BUTTON_DIALOG);
  };

  useImperativeHandle(ref, () => ({
    openInvalidButtonDialog,
  }));

  const pluginEntry = useMemo(() => {
    return new PluginEntry();
  }, []);

  // const handleGetIntakeFormList = useGetIntakeFormList();
  // const handleGetAgreementList = useGetAgreementList();
  // const handleGetOnlineBookingURL = useGetOnlineBookingURL();
  // const handleGetMembershipList = useGetMembershipList();
  // heavily modified
  const initOptions = useMemoizedFn(() => {
    const api: EditorComponentApi = {
      // getIntakeFormList: handleGetIntakeFormList,
      // getAgreementList: handleGetAgreementList,
      // getOnlineBookingURL: handleGetOnlineBookingURL,
      // getMembershipList: handleGetMembershipList,
      handleLoading: (showLoading: boolean) => {
        if (showLoading) {
          setLoadingAction.setTrue();
        } else {
          setLoadingAction.setFalse();
        }
      },
      handleUploadAttachment: props.handleUploadAttachment ?? (() => Promise.resolve()),
      // extend design, not use now
      getDefaultData: (id: string) => {
        console.log('getDefaultData', id);

        return null;
      },
    };
    return pluginEntry.resolveInitOptions(init, api);
  });

  const [content, setContent] = useControllableValue(props);

  const handleContentChange = useMemoizedFn((content: string) => {
    setContent(content);
  });

  const handleInit = useMemoizedFn((_, editor: TinyMCE) => {
    editorRef.current = editor;
    setLoadingAction.setFalse();
    props.onInit?.(editor);
  });

  const handleDirty = useMemoizedFn((_, editor: TinyMCE) => {
    // if the editor is not focused, we don't want to trigger onDirty
    if (setContentAfterInit && !editor.hasFocus()) {
      editor.undoManager.clear();
      editor.setDirty(false);
    } else {
      props.onDirty?.();
    }
    setContentAfterInitAction.setFalse();
  });

  useUnmount(() => {
    pluginEntry.destory();
  });

  const handleDrop = useMemoizedFn(async (e: DragEvent, ownerId?: string) => {
    e.stopPropagation();
    e.preventDefault();
    e.stopImmediatePropagation();

    const files = [...(e.dataTransfer?.files || [])];
    if (files.length === 0) {
      return;
    }

    const imageList = files.filter((file) => file.type.includes('image/'));
    const fileList = files.filter((file) => !file.type.includes('image/'));

    if (imageList.length > 0) {
      setLoadingAction.setTrue();
      const fileResList = await Promise.all(
        imageList.map((file) =>
          handleImageUpload({
            file,
            ownerId,
          }),
        ),
      );

      (fileResList.filter((res) => !!res) as UploadFileInfo[]).forEach((res) => {
        editorRef.current?.insertContent(`<img src="${res.url}" style="max-width: 100%; height: auto;" />`);
      });
      setLoadingAction.setFalse();
    }

    if (fileList.length > 1) {
      // Alert..warn('You can only upload one file at a time.');
    } else if (fileList.length === 1) {
      props.handleUploadAttachment?.(fileList[0]);
    }
  });

  return (
    <Spin isLoading={loading}>
      <div
        className={cn('moego-rich-editor', className)}
        style={
          showErrorStyle
            ? {
                border: '1px solid #f3413b',
              }
            : {}
        }
      >
        <Editor
          init={initOptions() as InitOptions}
          value={content}
          onEditorChange={handleContentChange}
          apiKey={RICHTEXT_API_KEY}
          plugins={plugins}
          onInit={handleInit}
          onBlur={() => props.onBlur?.(content)}
          onDirty={handleDirty}
          onFocus={props.onFocus}
          onDrop={(e) => handleDrop(e, props.options?.ownerId)}
        />
      </div>
      <Condition if={!!props.hasError}>
        <span className="!moe-mt-[4px] !moe-text-[12px] !moe-text-[#D0021B]">The email content can not be blank.</span>
      </Condition>
    </Spin>
  );
});
