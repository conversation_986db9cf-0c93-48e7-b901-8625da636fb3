import { Meta, Stories, Controls, Primary } from '@storybook/blocks';
import * as AutoSelectAllDropdown from './AutoSelectAllDropdown.stories';

# AutoSelectAllDropdown

`AutoSelectAllDropdown` is based on the select component from @moego/ui and provides a "Select All" feature, allowing users to select or deselect all options at once (only for multi-select scenarios).

Differences from @moego/ui's select all:

- A preset multi-select style used in the project is provided, but custom styles are still supported.
- The value has been changed from only containing the list of selected items to the following object. When isSelectedAll is needed in the project, it no longer requires defining and maintaining a variable by developer.

```ts
interface ValueObject<T extends OptionValue> {
  isSelectedAll: boolean;
  list: T[];
}
```

- Add isSelectedAll as a parameter to renderMultipleValues.

## Usage

```jsx

export const AutoSelectAllDropdownExample = () => {
  const [value, setValue] = React.useState<{ list: string[]; isSelectedAll: boolean }>({
    list: [],
    isSelectedAll: false,
  });
  const handleChange = (value: { list: string[]; isSelectedAll: boolean }) => {
    setValue(value);
  };
  return (
    <AutoSelectAllDropdown
      className="moe-w-[300px]"
      value={value}
      onChange={handleChange}
      options={[
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
        { label: 'Option 3', value: 'option3' },
      ]}
      allLabel="Select All Options"
    />
  );
};
```

## Props

Except for the following input parameters, the rest of the input parameters are consistent with Select.

tips:

- Please make sure to pass in the `value` and `onChange` parameters, otherwise the "Select All" feature will not work.
- You can use the select-all style of the Select component itself, but changed from only containing the list of selected items to the following object, please refer to the "With Original Select All" module.

```ts
interface ValueObject<T extends OptionValue> {
  isSelectedAll: boolean;
  list: T[];
}
```

<Meta of={AutoSelectAllDropdown} />
<Primary />
<Controls />
<Stories />
