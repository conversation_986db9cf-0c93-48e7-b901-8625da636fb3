import { Option, OptionValue } from '@moego/ui';
import { Meta, StoryFn } from '@storybook/react';
import React, { useState } from 'react';
import { AutoSelectAllDropdown } from './AutoSelectAllDropdown';

export default {
  title: 'Foundation/Components/AutoSelectAllDropdown',
  component: AutoSelectAllDropdown,
  args: {
    value: undefined,
    onChange: () => {},
    defaultValue: { list: [], isSelectedAll: false },
    className: '',
    allLabel: 'Select All',
    handleMultipleMode: undefined,
    options: [],
  },
} as Meta;

// 模拟选项数据
const mockOptions: Option<OptionValue>[] = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
  { label: 'Option 3', value: 'option3' },
];

const Template: StoryFn<typeof AutoSelectAllDropdown> = (args) => {
  const [value, setValue] = useState(args.value);

  const handleChange = (newValue: { isSelectedAll: boolean; list: OptionValue[] }) => {
    setValue(newValue);
  };

  return <AutoSelectAllDropdown {...args} value={value} onChange={handleChange} />;
};

export const WithDefaultValue = Template.bind({});
WithDefaultValue.args = {
  options: mockOptions,
  defaultValue: { list: ['option1'], isSelectedAll: false },
};

export const WithInitialSelection = Template.bind({});
WithInitialSelection.args = {
  options: mockOptions,
  value: {
    isSelectedAll: false,
    list: ['option1', 'option2'],
  },
  allLabel: 'Select All',
};

export const WithSelectAll = Template.bind({});
WithSelectAll.args = {
  options: mockOptions,
  value: {
    isSelectedAll: true,
    list: ['option1', 'option2', 'option3'],
  },
  allLabel: 'Select All',
};

export const WithOriginalSelectAll = Template.bind({});

WithOriginalSelectAll.args = {
  options: mockOptions,
  showSelectAll: true,
  allLabel: 'Select All Options',
  classNames: { selectAll: 'moe-py-none' },
  footer: null,
};

export const WithCustomRenderMultiple = Template.bind({});

const getLabel = (values: Option<OptionValue>[], isSelectedAll: boolean) => {
  if (isSelectedAll) {
    return '全部已选中';
  }
  if (values.length === 0) {
    return undefined;
  }
  return `目前已选中：${values.map((v) => v.label).join(', ')}`;
};

WithCustomRenderMultiple.args = {
  options: mockOptions,
  value: {
    isSelectedAll: false,
    list: ['option1', 'option2'],
  },
  handleMultipleMode: () => 'value',
  renderMultipleValues: (values, isSelectedAll) => <div>{getLabel(values, isSelectedAll)}</div>,
};
