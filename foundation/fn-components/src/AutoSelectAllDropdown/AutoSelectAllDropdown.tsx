import {
  cn,
  isUndefined,
  Option,
  OptionValue,
  LegacySelect as Select,
  SelectInstance,
  LegacySelectProps as SelectProps,
  Switch,
  SwitchProps,
} from '@moego/ui';
import { ActionMeta, MultiValue } from '@moego/ui/dist/esm/components/LegacySelect/ReactSelect/types';
import { useMemoizedFn } from 'ahooks';
import React, { forwardRef, memo, ReactNode, Ref, useEffect, useMemo } from 'react';
import { isDev } from './utils';

interface ValueObject<T extends OptionValue> {
  isSelectedAll: boolean;
  list: T[];
}

export interface AutoSelectAllDropdownProps<T extends OptionValue>
  extends Omit<SelectProps<Option<T>, true>, 'value' | 'onChange' | 'renderMultipleValues' | 'defaultValue'> {
  value?: ValueObject<T>;
  onChange?: (value: ValueObject<T>, option: MultiValue<Option<T>>, action: ActionMeta<Option<T>>) => void;
  defaultValue?: ValueObject<T>;
  allLabel?: ReactNode;
  options: Option<T>[];
  renderMultipleValues?: (values: Option<T>[], isSelectedAll: boolean) => React.ReactNode;
  handleMultipleMode?: (isSelectAll: boolean) => 'value' | 'count' | 'tag';
  switchProps?: Omit<SwitchProps, 'isSelected'>;
}

export const AutoSelectAllDropdown = memo(
  forwardRef(function AutoSelectAllDropdown<T extends OptionValue>(
    props: AutoSelectAllDropdownProps<T>,
    ref?: Ref<SelectInstance<Option<T>, true>> | null,
  ) {
    const {
      value,
      onChange,
      allLabel = 'Select All',
      options,
      defaultValue,
      footer: propsFooter,
      renderMultipleValues: propsRenderMultipleValues,
      handleMultipleMode: propsHandleMultipleMode,
      switchProps,
      ...restProps
    } = props;

    const { onChange: switchOnChange, className: switchClassName, ...restSwitchProps } = switchProps || {};

    const isSelectedAll = value?.isSelectedAll ?? false;

    const handleSelectAllChange = useMemoizedFn(() => {
      const newIsSelectedAll = !isSelectedAll;
      const newList = newIsSelectedAll ? options.map((item) => item.value) : [];
      onChange?.({ list: newList, isSelectedAll: newIsSelectedAll }, options, {} as ActionMeta<Option<T>>);
      switchOnChange?.(newIsSelectedAll);
    });

    const handleChange: (
      value: T[] | undefined,
      options: MultiValue<Option<T>>,
      action: ActionMeta<Option<T>>,
    ) => void = useMemoizedFn((value, ...rest) => {
      onChange?.({ list: value || [], isSelectedAll: value?.length === options.length }, ...rest);
    });

    const handleRenderMultipleValues = useMemoizedFn((values) => {
      if (!isUndefined(propsRenderMultipleValues)) {
        return propsRenderMultipleValues(values, isSelectedAll);
      }
      return <div>{allLabel}</div>;
    });

    const handleMultipleMode = useMemo(() => {
      if (!isUndefined(propsHandleMultipleMode)) {
        return propsHandleMultipleMode(isSelectedAll);
      }
      return isSelectedAll ? 'value' : 'tag';
    }, [isSelectedAll]);

    // 初始化时和渲染时的脏检查
    useEffect(() => {
      if (!isDev) return;
      const optionValues = options.map((option) => option.value);
      const valueList = value?.list || [];
      const defaultValueList = defaultValue?.list || [];

      // 检查 value 是否在 options 中
      const invalidValues = valueList.filter((v) => !optionValues.includes(v));
      if (invalidValues.length > 0) {
        console.warn(`Invalid values found: ${invalidValues.join(', ')}`);
      }

      // 检查 defaultValue 是否在 options 中
      const invalidDefaultValues = defaultValueList.filter((v) => !optionValues.includes(v));
      if (invalidDefaultValues.length > 0) {
        console.warn(`Invalid defaultValues found: ${invalidDefaultValues.join(', ')}`);
      }

      // 检查 isSelectedAll 和 valueList 的实际匹配情况
      const valueListLength = valueList.length;
      const optionsLength = options.length;

      if (isSelectedAll && valueListLength !== optionsLength) {
        console.warn(
          `isSelectedAll is true but valueList length (${valueListLength}) does not match options length (${optionsLength})`,
        );
      } else if (!isSelectedAll && valueListLength === optionsLength) {
        console.warn(
          `isSelectedAll is false but valueList length (${valueListLength}) matches options length (${optionsLength})`,
        );
      }
    }, [value, defaultValue, options, isSelectedAll]);

    const handleFooter = useMemo(() => {
      if (!isUndefined(propsFooter)) {
        return propsFooter;
      }
      return (
        <Switch
          isSelected={isSelectedAll}
          onChange={handleSelectAllChange}
          className={cn('moe-p-[6px]', switchClassName)}
          {...restSwitchProps}
        >
          {allLabel}
        </Switch>
      );
    }, [isSelectedAll, allLabel]);

    return (
      <Select<Option<T>, true>
        {...restProps}
        ref={ref}
        isMultiple
        defaultValue={defaultValue?.list || []}
        value={value?.list}
        onChange={handleChange}
        options={options}
        multipleMode={handleMultipleMode}
        renderMultipleValues={handleRenderMultipleValues}
        footer={handleFooter}
      />
    );
  }),
) as <T extends OptionValue>(
  props: AutoSelectAllDropdownProps<T> & { ref?: Ref<SelectInstance<Option<T>, true>> },
) => React.ReactElement;
