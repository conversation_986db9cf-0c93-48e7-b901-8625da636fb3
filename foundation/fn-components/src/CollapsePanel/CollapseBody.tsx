import { cn } from '@moego/ui';
import React, { memo, useEffect, useRef, useState } from 'react';

export interface CollapseBodyProps {
  className?: string;
  isOpen?: boolean;
  offsetHeight?: number;
}

export const CollapseBody = memo<React.PropsWithChildren<CollapseBodyProps>>(function FilterTogglePanel(props) {
  const { className, children, isOpen, offsetHeight } = props;
  const ref = useRef<HTMLElement>(null);
  const [height, setHeight] = useState(0);

  useEffect(() => {
    const ele = ref.current;
    if (ele) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.contentBoxSize) {
            const contentBoxSize: ResizeObserverSize = Array.isArray(entry.contentBoxSize)
              ? entry.contentBoxSize[0]
              : entry.contentBoxSize;
            setHeight(contentBoxSize.blockSize);
          } else {
            setHeight(entry.contentRect.height);
          }
        }
      });
      resizeObserver.observe(ele);
      return () => {
        resizeObserver.unobserve(ele);
      };
    }
  }, []);

  return (
    <div
      style={{ height: isOpen && height !== 0 ? height + (offsetHeight ?? 0) : 0 }}
      className={cn('moe-overflow-hidden moe-transition-all', className)}
    >
      <section ref={ref} aria-label="toggle body">
        {children}
      </section>
    </div>
  );
});
