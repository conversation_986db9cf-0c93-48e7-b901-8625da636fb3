import { cn, MajorChevronDownOutlined, Text } from '@moego/ui';
import { useControllableValue } from 'ahooks';
import { isBoolean } from 'lodash-es';
import React, { memo, useCallback } from 'react';
import { CollapseBody } from './CollapseBody';

export interface CollapsePanelProps {
  className?: string;
  bodyClassName?: string;
  titleClassName?: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  isExpanded?: boolean;
  onExpandedChange?: (v: boolean) => void;
  renderIcon?: (isExpanded: boolean) => React.ReactNode;
}

export const CollapsePanel = memo<React.PropsWithChildren<CollapsePanelProps>>(function CollapsePanel(props) {
  const { title, description, className, bodyClassName, titleClassName, children, renderIcon } = props;
  const [isExpanded, setIsExpanded] = useControllableValue(props, {
    defaultValue: false,
    valuePropName: 'isExpanded',
    trigger: 'onExpandedChange',
  });
  const toggle = useCallback((v?: any) => setIsExpanded((pre) => (isBoolean(v) ? v : !pre)), []);

  return (
    <div className={cn(className)}>
      <div
        className={cn('moe-flex moe-cursor-pointer moe-items-center moe-justify-between', titleClassName)}
        onClick={toggle}
      >
        <div className="moe-flex moe-flex-col moe-gap-y-xxs">
          {typeof title === 'string' ? <Text variant="regular-short">{title}</Text> : title}
          {description}
        </div>
        {renderIcon?.(isExpanded) ?? (
          <MajorChevronDownOutlined rotate={isExpanded ? 180 : 0} className="moe-text-icon-primary" />
        )}
      </div>
      <CollapseBody isOpen={isExpanded}>
        <div className={bodyClassName}>{children}</div>
      </CollapseBody>
    </div>
  );
});
