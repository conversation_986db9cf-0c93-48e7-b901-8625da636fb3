import { ElementNode, LexicalNode } from 'lexical';

export class Div<PERSON><PERSON>N<PERSON> extends ElementNode {
  static getType(): string {
    return 'div';
  }

  static clone(node: DivElementNode): DivElementNode {
    return new DivElementNode(node.__key);
  }

  createDOM(): HTMLElement {
    const dom = document.createElement('div');
    return dom;
  }

  updateDOM(): boolean {
    // Returning false tells Lexical that this node does not need its
    return false;
  }
}

export function $createCustomDivNode(): DivElementNode {
  return new DivElementNode();
}

export function $isDivElementNode(node: LexicalNode | null | undefined): node is DivElementNode {
  return node instanceof DivElementNode;
}
