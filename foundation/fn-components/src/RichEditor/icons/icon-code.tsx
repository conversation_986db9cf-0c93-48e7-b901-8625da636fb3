import React, { forwardRef, memo } from 'react';

const Render = () => {
  return (
    <svg width="23" height="19" viewBox="0 0 23 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6 4.5L1 9.68008L6 14.5"
        stroke="#2A2D34"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17 4.5L22 9.68008L17 14.5"
        stroke="#2A2D34"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M11.9536 1.5L9.94531 17.5" stroke="#2A2D34" strokeWidth="1.5" strokeLinecap="round" />
    </svg>
  );
};

export const SvgIconCode = memo(forwardRef(Render));
