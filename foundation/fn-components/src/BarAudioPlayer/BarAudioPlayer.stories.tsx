import React from 'react';
import { BarAudioPlayer, BarAudioPlayerProps } from './BarAudioPlayer';

export default {
  title: 'Foundation/Components/BarAudioPlayer',
};

export const BarAudioPlayerStory = (args: BarAudioPlayerProps) => {
  return (
    <div className="moe-relative moe-h-full moe-w-full">
      <BarAudioPlayer
        {...args}
        url="https://api.twilio.com/2010-04-01/Accounts/AC426f9d098219020db0785ee68fd472f7/Recordings/REa604b34d0e422dd30bfdabd21f183784"
      />
      <BarAudioPlayer
        className="moe-mt-2"
        disabled
        url="https://dcgll7qxxap6x.cloudfront.net/u/0/2024/8/98eb5758-84f9-4a5f-8ead-fe3c1baba0f1.wav"
      />
    </div>
  );
};
BarAudioPlayerStory.args = {
  className: '',
  url: '',
};
BarAudioPlayerStory.argTypes = {};
