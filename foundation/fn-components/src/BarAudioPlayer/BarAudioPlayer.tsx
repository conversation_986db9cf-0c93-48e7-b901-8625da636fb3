import { cn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Minor<PERSON><PERSON><PERSON><PERSON>illed, MinorPlayFilled, Spin, Typography } from '@moego/ui';
import { Howl } from 'howler';
import React, { useEffect, useRef, useState } from 'react';
import { formatTime } from '../utils/formatTime';

export interface BarAudioPlayerProps {
  url: string;
  className?: string;
  disabled?: boolean;
  format?: string[];
}
const BarAudioPlayer = ({ url, className, disabled, format = ['wav', 'mp3'] }: BarAudioPlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(true);

  const soundRef = useRef<Howl | null>(null);
  const progressBarRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (disabled) {
      return;
    }
    soundRef.current = new Howl({
      src: [url],
      format,
      html5: true,
      onplay: () => {
        if (soundRef.current) {
          requestAnimationFrame(updateProgress);
        }
      },
      onload: () => {
        if (soundRef.current) {
          setLoading(false);
          setDuration(soundRef.current.duration());
        }
      },
      onend: () => {
        setIsPlaying(false);
      },
    });
    soundRef.current.load();

    return () => {
      soundRef.current && soundRef.current.unload();
    };
  }, [url]);

  const playPause = () => {
    if (disabled) {
      return;
    }
    if (isPlaying) {
      soundRef.current && soundRef.current.pause();
    } else {
      soundRef.current && soundRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const updateProgress = () => {
    if (disabled) {
      return;
    }
    if (!soundRef.current) {
      return;
    }
    const current = soundRef.current.seek();
    setCurrentTime(current);
    setProgress((current / soundRef.current.duration()) * 100);

    if (soundRef.current.playing()) {
      requestAnimationFrame(updateProgress);
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (disabled) {
      return;
    }
    if (progressBarRef.current && soundRef.current) {
      const rect = progressBarRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const newProgress = clickX / rect.width;
      const newTime = newProgress * soundRef.current.duration();

      soundRef.current.seek(newTime); // 跳转到点击位置的时间点
      setCurrentTime(newTime); // 更新当前时间
      setProgress(newProgress * 100); // 更新进度条
    }
  };

  return (
    <div
      className={cn(
        'moe-flex moe-h-12 moe-w-[432px] moe-items-center moe-justify-between moe-gap-0 moe-overflow-hidden moe-rounded-2 moe-bg-neutral-sunken-light moe-pl-2 moe-pr-4',
        className,
      )}
    >
      {loading && !disabled ? (
        <Spin isLoading={true} className="moe-min-w-[32px]" />
      ) : (
        <IconButton
          size="m"
          className="moe-min-w-[32px] moe-bg-inherit disabled:moe-bg-inherit"
          isDisabled={disabled}
          onPress={playPause}
          icon={isPlaying ? <MinorPauseFilled /> : <MinorPlayFilled />}
        />
      )}

      <Typography.Text
        variant="small"
        className={cn(
          'moe-min-w-[48px] moe-break-keep moe-pr-[8px] moe-lining-nums moe-text-primary',
          disabled && 'moe-text-disabled',
        )}
      >
        {formatTime(currentTime)}
      </Typography.Text>
      <div
        className="moe-relative moe-h-[8px] moe-w-full moe-cursor-pointer moe-rounded-full moe-bg-neutral-sunken-1"
        ref={progressBarRef}
        onClick={handleProgressClick}
      >
        <div
          className="moe-h-full moe-rounded-full moe-bg-neutral-dark"
          style={{
            width: `${progress}%`,
          }}
        ></div>
      </div>
      <Typography.Text
        variant="small"
        className={cn(
          'moe-min-w-[56px] moe-break-keep moe-pl-[8px] moe-text-right moe-lining-nums moe-text-primary',
          disabled && 'moe-text-disabled',
        )}
      >
        -{formatTime(duration - currentTime, true)}
      </Typography.Text>
    </div>
  );
};

BarAudioPlayer.displayName = 'BarAudioPlayer';

export { BarAudioPlayer };
