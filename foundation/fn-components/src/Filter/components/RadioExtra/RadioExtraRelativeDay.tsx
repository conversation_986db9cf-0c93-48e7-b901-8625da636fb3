import { cn, Input, Text } from '@moego/ui';
import { useControllableValue } from 'ahooks';
import React, { memo } from 'react';

export interface RadioExtraRelativeDayProps {
  className?: string;
  value?: number;
  onChange?: (v: number) => void;
}

export const RadioExtraRelativeDay = memo<RadioExtraRelativeDayProps>(function RadioExtraRelativeDay(props) {
  const { className } = props;
  const [value, setValue] = useControllableValue(props);

  return (
    <div className={cn('moe-flex moe-items-center moe-pl-[28px] moe-text-sm', className)}>
      <Input.Number
        minValue={0}
        maxValue={99}
        step={1}
        classNames={{
          inputWrapper: 'moe-w-[140px]',
          inputBox: 'moe-min-w-0',
        }}
        value={value ?? 0}
        onChange={(value) => setValue(typeof value === 'number' ? value : undefined)}
      />
      <Text variant="regular-short" className="moe-ml-xs">
        {value > 1 ? 'days' : 'day'} ago
      </Text>
    </div>
  );
});
