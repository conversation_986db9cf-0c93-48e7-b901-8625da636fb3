import { cn, Radio } from '@moego/ui';
import React, { memo } from 'react';
import { DateValue } from '../Date/DateFilterSelector.types';
import { RadioWithExtraDispatch } from './RadioWithExtraDispatch';

export interface RadioWithExtraProps extends Record<string, any> {
  className?: string;
  label: React.ReactNode;
  value: string | number;
  onChange?: (v: RadioWithExtraProps['value']) => void;
  // extra child common props
  showExtra: boolean;
  componentType?: number;
  extraComponentValue?: DateValue;
  onExtraComponentValueChange?: (v: Partial<DateValue>) => void;
}

export const RadioWithExtra = memo<React.PropsWithChildren<RadioWithExtraProps>>(function RadioWithExtra(props) {
  const {
    children,
    showExtra,
    className,
    componentType,
    value,
    label,
    extraComponentValue,
    onChange,
    onExtraComponentValueChange,
    ...otherProps
  } = props;
  return (
    <div className={cn('moe-flex moe-flex-col moe-gap-y-xs', className)}>
      <Radio value={value} onClick={() => onChange?.(value)}>
        {label}
      </Radio>
      {showExtra
        ? children || (
            <RadioWithExtraDispatch
              componentType={componentType!}
              value={extraComponentValue}
              onChange={onExtraComponentValueChange}
              {...otherProps}
            />
          )
        : null}
    </div>
  );
});
