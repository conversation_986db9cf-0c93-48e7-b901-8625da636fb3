import { FilterComponentType } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { useMemoizedFn } from 'ahooks';
import { Dayjs } from 'dayjs';
import { isNil } from 'lodash-es';
import React, { memo } from 'react';
import { renderCountableNounPlurals } from '../api';
import { DateValue } from '../Date/DateFilterSelector.types';
import { RadioExtraDate } from './RadioExtraDate';
import { RadioExtraRelativeDay } from './RadioExtraRelativeDay';

interface ExtraComponentConfig {
  component: React.ComponentType<Record<string, any>>;
  valueKey: keyof DateValue;
  previewComponent: React.ComponentType<any>;
}

export const PresetExtraComponents: Record<number, ExtraComponentConfig> = {
  [FilterComponentType.DATE]: {
    component: RadioExtraDate,
    valueKey: 'date',
    previewComponent: ({
      prefix,
      value,
      formatDate,
    }: {
      prefix: string;
      value: Dayjs | null;
      formatDate: (v?: Dayjs) => string;
    }) => {
      if (!value || !formatDate) {
        return null;
      }
      const txt = `${prefix} ${formatDate(value)}`;
      return <span title={txt}>{txt}</span>;
    },
  },
  [FilterComponentType.RELATIVE_DATE]: {
    component: RadioExtraRelativeDay,
    valueKey: 'relativeNum',
    previewComponent: ({ prefix, value }) => {
      const txt = `${prefix} ${renderCountableNounPlurals(isNil(value) ? 0 : value, 'day')}`;
      return <span title={txt}>{txt}</span>;
    },
  },
};

export interface RadioWithExtraDispatchProps extends Record<string, unknown> {
  componentType: number;
  value?: DateValue;
  onChange?: (v: Partial<DateValue>) => void;
}

export const RadioWithExtraDispatch = memo<RadioWithExtraDispatchProps>(function RadioWithExtraDispatch(props) {
  const { componentType, value, onChange, ...others } = props;
  const target = PresetExtraComponents[componentType];
  const onExtraChange = useMemoizedFn((value: any) => {
    if (target?.valueKey) {
      onChange?.({ [target.valueKey]: value });
    }
  });
  if (!target) {
    return <div>Unimplemented radio extra component: {componentType}</div>;
  }
  const { component, valueKey } = target;
  const extraValue = value?.[valueKey];
  return React.createElement(component, { ...others, value: extraValue, onChange: onExtraChange });
});
