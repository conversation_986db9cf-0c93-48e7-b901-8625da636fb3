import { DatePicker } from '@moego/ui';
import { useControllableValue } from 'ahooks';
import { Dayjs } from 'dayjs';
import React, { memo } from 'react';

export interface RadioExtraDateProps {
  value?: Dayjs | null;
  onChange?: (v: Dayjs | undefined | null) => void;
  formatDate?: (v: Dayjs | undefined) => string;
}

export const RadioExtraDate = memo<RadioExtraDateProps>(function RadioExtraDate(props) {
  const [value, setValue] = useControllableValue(props);

  return (
    <DatePicker
      className="moe-pl-[28px]"
      value={value}
      format={props.formatDate}
      placeholder="Select Date"
      onChange={setValue}
    />
  );
});
