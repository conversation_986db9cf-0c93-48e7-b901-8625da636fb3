import { FilterComponentType } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { registerOperatorComponent } from '../api';
import {
  CheckboxSelector,
  CheckboxSelectorDisplay,
  CheckboxSelectorProps,
  useCheckBoxPreviewValue,
} from './CheckboxFilter';

export { CheckboxSelectorDisplay };

export const CheckboxFilter = registerOperatorComponent({
  component: CheckboxSelector,
  displayComponent: CheckboxSelectorDisplay,
  componentType: FilterComponentType.CHECKBOX,
  usePreviewValue: useCheckBoxPreviewValue,
  checkValueValidation: (props: CheckboxSelectorProps<any>) => {
    const { value, options = [] } = props;
    const validateKeys = new Set(options.map((i) => i.key));
    return { validate: value!.every((key) => validateKeys.has(key)) };
  },
});
