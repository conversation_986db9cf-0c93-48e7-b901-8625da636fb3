import { Checkbox, CheckboxGroup, OptionValue } from '@moego/ui';
import { CheckboxGroupSlotsClasses } from '@moego/ui/dist/esm/components/Checkbox/CheckboxGroup.style';
import React, { memo } from 'react';
import { useFormatOptionValues } from '../../hooks/useFormatOptionValues';
import { useOptionValues } from '../../hooks/useOptionValues';
import { DisplayComponentCommonProps, OperatorComponentValue, Optional } from '../api';
import { DropdownSelector } from '../DropdownSelector';

export type OptionItem<T extends Exclude<OptionValue, null>> = {
  key: T;
  label: string;
};

interface CheckBoxCommonProps<T extends Exclude<OptionValue, null>> {
  options: OptionItem<T>[];
}

export interface CheckboxSelectorProps<T extends Exclude<OptionValue, null>>
  extends CheckBoxCommonProps<T>,
    OperatorComponentValue<Optional<T[]>> {
  classNames?: CheckboxGroupSlotsClasses;
}

export const CheckboxSelector = <T extends Exclude<OptionValue, null>>(props: CheckboxSelectorProps<T>) => {
  const { value, onChange, options: optionsList, classNames } = props;
  const options = optionsList || [];

  return (
    <CheckboxGroup
      value={value || []}
      onChange={(nextValues) => onChange?.(!nextValues.length ? undefined : nextValues)}
      classNames={{ wrapper: 'moe-gap-y-[20px]', ...classNames }}
    >
      {options.map((option) => {
        const { key, label } = option;
        return (
          <Checkbox key={key} value={key ?? undefined}>
            {label}
          </Checkbox>
        );
      })}
    </CheckboxGroup>
  );
};

export interface CheckboxSelectorDisplayProps<T extends Exclude<OptionValue, null>>
  extends DisplayComponentCommonProps,
    CheckBoxCommonProps<T> {}

export const useCheckBoxPreviewValue = <T extends Exclude<OptionValue, null>>(
  props: CheckboxSelectorDisplayProps<T>,
) => {
  const optionValues = useOptionValues(props);
  const valueNode = useFormatOptionValues(optionValues);
  return valueNode;
};

export const CheckboxSelectorDisplay = memo(function CheckboxSelectorDisplay<T extends Exclude<OptionValue, null>>(
  props: CheckboxSelectorDisplayProps<T>,
) {
  const { title, onClear, ...checkboxSelectorProps } = props;
  const valueNode = useCheckBoxPreviewValue(props);

  return (
    <DropdownSelector title={title} value={valueNode} onClear={onClear}>
      <CheckboxSelector {...checkboxSelectorProps} />
    </DropdownSelector>
  );
}) as <T extends Exclude<OptionValue, null>>(props: CheckboxSelectorDisplayProps<T>) => JSX.Element;
