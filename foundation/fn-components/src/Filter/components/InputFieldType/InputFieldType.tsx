import { FieldType } from '@moego/api-web/moego/models/reporting/v2/field_model';
import { Input } from '@moego/ui';
import React, { memo } from 'react';
import { MoneyInputField } from './MoneyInputField';

const fieldTypeRecords: Record<number, React.ComponentType<any>> = {
  [FieldType.MONEY]: MoneyInputField,
};

export interface InputFieldTypeProps extends Record<string, any> {
  inputType?: FieldType;
  value: any;
  onChange?: (v: any) => void;
}

export const InputFieldType = memo<InputFieldTypeProps>(function InputFieldType(props) {
  const { inputType = FieldType.TEXT, ...otherInputProps } = props;
  const target = fieldTypeRecords[inputType];

  if (!target) {
    return <Input.Text {...otherInputProps} value={otherInputProps.value ?? ''} />;
  }

  return React.createElement(target, otherInputProps);
});
