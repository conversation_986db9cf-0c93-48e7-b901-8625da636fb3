import { FilterComponentType } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { isDayjs } from 'dayjs';
import { isNil } from 'lodash-es';
import { registerOperatorComponent } from '../api';
import {
  DateFilterSelector,
  DateFilterSelectorDisplay,
  DateFilterSelectorProps,
  useDateFilterPreviewValue,
} from './DateFilterSelector';

export { DateFilterSelectorDisplay };

export const DateFilter = registerOperatorComponent({
  component: DateFilterSelector,
  displayComponent: DateFilterSelectorDisplay,
  componentType: FilterComponentType.DATE,
  usePreviewValue: useDateFilterPreviewValue,
  checkValueValidation: (props: DateFilterSelectorProps) => {
    const { options = [] } = props;
    const { key, date, relativeNum } = props.value!;
    const { componentType } = options.find((i) => !!i.options?.find((j) => j.key === key))!;
    const inValidateDate = componentType === FilterComponentType.DATE && !isDayjs(date);
    const invalidateNum = componentType === FilterComponentType.RELATIVE_DATE && isNil(relativeNum);
    if (isNil(key) || inValidateDate || invalidateNum) {
      return { validate: false };
    }
    return { validate: true };
  },
});
