import { Heading, RadioGroup } from '@moego/ui';
import { CheckboxGroupSlotsClasses } from '@moego/ui/dist/esm/components/Checkbox/CheckboxGroup.style';
import { useMemoizedFn } from 'ahooks';
import { Dayjs } from 'dayjs';
import React, { memo, useMemo } from 'react';
import { DisplayComponentCommonProps, OperatorComponentValue, Optional, PlainOptionItem } from '../api';
import { DropdownSelector } from '../DropdownSelector';
import { RadioWithExtra } from '../RadioExtra/RadioWithExtra';
import { PresetExtraComponents } from '../RadioExtra/RadioWithExtraDispatch';
import { DateValue } from './DateFilterSelector.types';

type DateFilterSelectorValue = Optional<DateValue>;

interface DateFilterOptionItem extends PlainOptionItem {
  componentType: number;
  options?: Optional<PlainOptionItem[]>;
}

interface SelectFilterCommonProps {
  options: DateFilterOptionItem[];
  formatDate?: (v: Dayjs) => string;
}

export interface DateFilterSelectorProps
  extends OperatorComponentValue<DateFilterSelectorValue>,
    SelectFilterCommonProps {
  classNames?: CheckboxGroupSlotsClasses;
}

export const DateFilterSelector = (props: DateFilterSelectorProps) => {
  const { value: rawValue, onChange, options: optionsList, formatDate } = props;
  const groups = useMemo<DateFilterOptionItem[]>(() => (!!optionsList?.length ? optionsList : []), [optionsList]);
  const value = useMemo(
    () => rawValue || ({ key: undefined, date: null, relativeNum: 0 } as DateValue),
    [rawValue, groups],
  );
  const { key: selectedValue } = value;
  const onChangePartial = useMemoizedFn((obj: Partial<DateValue>) =>
    onChange({
      ...value,
      ...obj,
    }),
  );

  return (
    <RadioGroup value={selectedValue || ''}>
      {groups.map((item) => {
        const { key, label, componentType, options } = item;
        return (
          <div key={key} className="moe-flex moe-flex-col moe-gap-y-xs">
            <Heading size="6" className="moe-mb-xs">
              {label}
            </Heading>
            {(options || []).map((item) => {
              const { key: radioKey, label } = item;
              const isSelectedRadio = selectedValue === radioKey;
              return (
                <RadioWithExtra
                  showExtra={isSelectedRadio}
                  key={radioKey}
                  label={label}
                  value={radioKey}
                  onChange={(v) => {
                    const isClearValue = v === selectedValue;
                    if (isClearValue) {
                      onChange(undefined);
                      return;
                    }
                    onChangePartial({ key: v });
                  }}
                  componentType={componentType}
                  extraComponentValue={value}
                  onExtraComponentValueChange={onChangePartial}
                  formatDate={formatDate}
                />
              );
            })}
          </div>
        );
      })}
    </RadioGroup>
  );
};

export interface SelectFilterSelectorDisplayProps
  extends DisplayComponentCommonProps<DateFilterSelectorValue>,
    SelectFilterCommonProps {}

export const useDateFilterPreviewValue = (props: SelectFilterSelectorDisplayProps) => {
  const { value: rawValue, options, formatDate } = props;
  return useMemo(() => {
    const optionList = options || [];
    const groupOptions = optionList
      .map((i) => i.options)
      .flat()
      .filter((i) => !!i);
    if (rawValue && groupOptions.length) {
      const value = rawValue || ({} as DateValue);
      const { key } = value;
      const group = optionList.find((i) => !!(i.options && i.options.find((j) => j.key === key)));
      const target = groupOptions.find((i) => i.key === key);
      if (group && target) {
        const { componentType } = group;
        const preset = PresetExtraComponents[componentType];
        const { label } = target;
        if (preset) {
          const { previewComponent, valueKey } = preset;
          return React.createElement(previewComponent, { prefix: label, value: value[valueKey], formatDate });
        }
      }
    }
    return null;
  }, [rawValue, options, formatDate]);
};

export const DateFilterSelectorDisplay = memo<SelectFilterSelectorDisplayProps>(
  function CheckboxSelectorDisplay(props) {
    const { title, onClear, ...otherProps } = props;
    const valueNode = useDateFilterPreviewValue(props);

    return (
      <DropdownSelector title={title} value={valueNode} onClear={onClear}>
        <DateFilterSelector {...otherProps} />
      </DropdownSelector>
    );
  },
);
