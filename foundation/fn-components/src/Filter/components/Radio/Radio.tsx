import { FilterComponentType } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { registerOperatorComponent } from '../api';
import { RadioSelector, RadioSelectorDisplay, RadioSelectorProps, useRadioPreviewValue } from './RadioDisplay';

export { RadioSelectorDisplay };

export const RadioFilter = registerOperatorComponent({
  componentType: FilterComponentType.RADIO,
  component: RadioSelector,
  displayComponent: RadioSelectorDisplay,
  usePreviewValue: useRadioPreviewValue,
  checkValueValidation: (_: RadioSelectorProps) => ({ validate: true }),
});
