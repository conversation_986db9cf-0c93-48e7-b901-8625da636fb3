import { Radio, RadioGroup } from '@moego/ui';
import { RadioGroupSlotsClasses } from '@moego/ui/dist/esm/components/Radio/RadioGroup.style';
import React, { memo } from 'react';
import { useFormatOptionValues } from '../../hooks/useFormatOptionValues';
import { useOptionValues } from '../../hooks/useOptionValues';
import { DisplayComponentCommonProps, OperatorComponentValue, Optional, OptionItem } from '../api';
import { DropdownSelector } from '../DropdownSelector';

type RadioSelectorValue = Optional<string | number>;

interface RadioCommonProps {
  options: OptionItem[];
}

export interface RadioSelectorProps extends OperatorComponentValue<RadioSelectorValue>, RadioCommonProps {
  classNames?: RadioGroupSlotsClasses;
}

export const RadioSelector = (props: RadioSelectorProps) => {
  const { value, options: optionsList, onChange, classNames } = props;
  const options = optionsList || [];
  const selectedValue = value ?? '';

  return (
    <RadioGroup value={selectedValue} classNames={{ wrapper: 'moe-gap-y-[20px]', ...classNames }}>
      {options.map((option) => {
        const { key, label } = option;
        return (
          <Radio
            key={key}
            value={key}
            onClick={() => {
              onChange(selectedValue === key ? undefined : key);
            }}
          >
            {label}
          </Radio>
        );
      })}
    </RadioGroup>
  );
};

export interface RadioSelectorDisplayProps extends DisplayComponentCommonProps<RadioSelectorValue>, RadioCommonProps {}

export const useRadioPreviewValue = (props: RadioSelectorDisplayProps) => {
  const optionValues = useOptionValues(props);
  const valueNode = useFormatOptionValues(optionValues);
  return valueNode;
};

export const RadioSelectorDisplay = memo<RadioSelectorDisplayProps>(function InputDisplay(props) {
  const { title, onClear, ...radioSelectorProps } = props;
  const valueNode = useRadioPreviewValue(props);
  return (
    <DropdownSelector title={title} value={valueNode} onClear={onClear}>
      <RadioSelector {...radioSelectorProps} />
    </DropdownSelector>
  );
});
