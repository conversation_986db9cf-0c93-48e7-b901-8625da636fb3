import { FilterComponentType } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { registerOperatorComponent } from '../api';
import {
  DateFilterSelectorDisplay,
  RelativeDate,
  checkValueValidation,
  useDateFilterPreviewValue,
} from './RelativeDateFilter';

export const RelativeDateFilter = registerOperatorComponent({
  component: RelativeDate,
  displayComponent: DateFilterSelectorDisplay,
  componentType: FilterComponentType.RELATIVE_DATE,
  usePreviewValue: useDateFilterPreviewValue,
  checkValueValidation,
});
