import { FilterComponentType } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { Heading, RadioGroup } from '@moego/ui';
import { CheckboxGroupSlotsClasses } from '@moego/ui/dist/esm/components/Checkbox/CheckboxGroup.style';
import { useMemoizedFn } from 'ahooks';
import { Dayjs, isDayjs } from 'dayjs';
import { isNil } from 'lodash-es';
import React, { useMemo } from 'react';
import {
  DisplayComponentCommonProps,
  FilterValidation,
  GroupOptionItem,
  OperatorComponentValue,
  Optional,
} from '../api';
import { DateValue } from '../Date/DateFilterSelector.types';
import { DropdownSelector } from '../DropdownSelector';
import { RadioWithExtra } from '../RadioExtra/RadioWithExtra';
import { PresetExtraComponents } from '../RadioExtra/RadioWithExtraDispatch';

export type OptionalValue = Optional<DateValue>;

export const checkValueValidation = (props: RelativeDateInteractiveProps): FilterValidation => {
  const { value, groupOptions } = props;
  if (!value || isNil(value.key)) return { validate: false };
  const { key, date, relativeNum } = value;
  const { componentType } = groupOptions.find((i) => !!i.options.find((j) => j.key === key))!;
  const inValidateDate = componentType === FilterComponentType.DATE && !isDayjs(date);
  const invalidateNum = componentType === FilterComponentType.RELATIVE_DATE && isNil(relativeNum);
  if (inValidateDate || invalidateNum) {
    return { validate: false };
  }
  return {
    validate: true,
  };
};

/** shared with display component props */
export interface RelativeDateCommonProps {
  groupOptions: GroupOptionItem[];
  formatDate?: (v: Dayjs) => string;
}

export interface RelativeDateInteractiveProps extends OperatorComponentValue<OptionalValue>, RelativeDateCommonProps {
  classNames?: CheckboxGroupSlotsClasses;
}

export interface RelativeDateFilterDisplayProps
  extends DisplayComponentCommonProps<OptionalValue>,
    RelativeDateCommonProps {}

export const RelativeDate = (props: RelativeDateInteractiveProps) => {
  const { value: rawValue, onChange, formatDate, groupOptions: groups = [] } = props;
  const value = useMemo(() => rawValue || ({ key: undefined, date: null, relativeNum: 0 } as DateValue), [rawValue]);
  const { key: selectedValue } = value;
  const onChangePartial = useMemoizedFn((obj: Partial<DateValue>) => {
    // if clear date, then clear component's value
    if ('date' in obj && obj.date === null) {
      onChange(undefined);
      return;
    }
    onChange({
      ...value,
      ...obj,
    });
  });

  return (
    <RadioGroup value={selectedValue || ''}>
      {groups.map((item) => {
        const { groupName, options, componentType } = item;
        return (
          <div key={groupName} className="moe-flex moe-flex-col moe-gap-y-xs">
            <Heading size="6" className="moe-mb-xs">
              {groupName}
            </Heading>
            {(options || []).map((item) => {
              const { key: radioKey, label } = item;
              const isSelectedRadio = selectedValue === radioKey;
              return (
                <RadioWithExtra
                  showExtra={isSelectedRadio}
                  key={radioKey}
                  label={label}
                  value={radioKey}
                  onChange={(v) => {
                    const isClearValue = v === selectedValue;
                    if (isClearValue) {
                      onChange(undefined);
                      return;
                    }
                    onChangePartial({ key: v });
                  }}
                  componentType={componentType}
                  extraComponentValue={value}
                  onExtraComponentValueChange={onChangePartial}
                  formatDate={formatDate}
                />
              );
            })}
          </div>
        );
      })}
    </RadioGroup>
  );
};

export const useDateFilterPreviewValue = (props: RelativeDateFilterDisplayProps) => {
  const { value: rawValue, groupOptions: groupOptionList = [], formatDate } = props;
  return useMemo(() => {
    const groupOptions = groupOptionList
      .map((i) => i.options)
      .flat()
      .filter((i) => !!i);
    if (rawValue && groupOptions.length) {
      const value = rawValue || ({} as DateValue);
      const { key } = value;
      const group = groupOptionList.find((i) => !!(i.options && i.options.find((j) => j.key === key)));
      const target = groupOptions.find((i) => i.key === key);
      if (group && target) {
        const { componentType } = group;
        const preset = PresetExtraComponents[componentType];
        const { label } = target;
        if (preset) {
          const { previewComponent, valueKey } = preset;
          return React.createElement(previewComponent, { prefix: label, value: value[valueKey], formatDate });
        }
      }
    }
    return null;
  }, [rawValue, groupOptionList, formatDate]);
};

export const DateFilterSelectorDisplay = function (props: RelativeDateFilterDisplayProps) {
  const { title, onClear, ...otherProps } = props;
  const valueNode = useDateFilterPreviewValue(props);

  return (
    <DropdownSelector title={title} value={valueNode} onClear={onClear}>
      <RelativeDate {...otherProps} />
    </DropdownSelector>
  );
};
