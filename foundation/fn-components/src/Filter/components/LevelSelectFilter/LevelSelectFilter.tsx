import { FilterComponentType } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { registerOperatorComponent } from '../api';
import { LevelSelector, LevelSelectorDisplay, LevelSelectorProps, useLevelSelectorPreview } from './LevelSelector';

/** e.g pet type with two selectors. should not conflict with {@link FilterComponentType} */
export const FilterLevelSelectComponentType = 390;

export const LevelSelectFilter = registerOperatorComponent({
  component: LevelSelector,
  displayComponent: LevelSelectorDisplay,
  componentType: FilterLevelSelectComponentType,
  usePreviewValue: useLevelSelectorPreview,
  checkValueValidation: ({ value }: LevelSelectorProps) => ({ validate: value!.length > 0 }),
});
