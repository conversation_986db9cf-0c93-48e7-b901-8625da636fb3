import { LegacySelect as Select } from '@moego/ui';
import { isNil } from 'lodash-es';
import React, { memo, useMemo } from 'react';
import { mapUIOptionFromItem } from '../../utils/mapFn';
import { DisplayComponentCommonProps, NestedPlainOptionItem, OperatorComponentValue, Optional } from '../api';
import { DropdownSelector } from '../DropdownSelector';

export type Value = (string | number)[];
export type OptionalValue = Optional<Value>;
export interface LevelSelectorCommonProps extends OperatorComponentValue<OptionalValue> {
  options: NestedPlainOptionItem[];
}
export interface LevelSelectorProps extends LevelSelectorCommonProps {}
export interface LevelSelectorDisplayProps
  extends LevelSelectorCommonProps,
    DisplayComponentCommonProps<OptionalValue> {}

export const LevelSelector = memo<LevelSelectorProps>(function PetTypeSelector(props) {
  const { options = [], value, onChange } = props;
  const [leadValue, ...tailValues] = value || [];
  const leadOptions = useMemo(() => options.map(mapUIOptionFromItem), [options]);
  const tailOptions = useMemo(
    () => options.find((i) => i.key === leadValue)?.children?.map(mapUIOptionFromItem) || [],
    [options, leadValue],
  );

  return (
    <div className="moe-flex moe-justify-between moe-gap-xs">
      <Select
        isClearable
        options={leadOptions}
        placeholder="Type"
        size="m"
        value={leadValue}
        onChange={(e) => onChange(e !== leadValue ? [e] : undefined)}
      />
      <Select
        options={tailOptions}
        isMultiple
        multipleMode="value"
        placeholder="Breed"
        className="moe-max-h-[296px] moe-overflow-y-auto"
        size="m"
        classNames={{
          formItemWrapper: 'moe-flex-1',
        }}
        isDisabled={isNil(leadValue)}
        value={tailValues}
        onChange={(e) => {
          onChange([leadValue, ...e]);
        }}
      />
    </div>
  );
});

export const LevelSelectorDisplay = memo<LevelSelectorDisplayProps>(function PetTypeSelector(props) {
  const { title, onClear, ...otherProps } = props;
  const valueNode = useLevelSelectorPreview(otherProps);

  return (
    <DropdownSelector title={title} value={valueNode} onClear={onClear}>
      <LevelSelector {...otherProps} />
    </DropdownSelector>
  );
});

export const useLevelSelectorPreview = (props: LevelSelectorProps) => {
  const { value, options = [] } = props;
  const [leadValue, ...tailValues] = value || [];
  const { label: leadLabel, children = [] } = options.find((i) => i.key === leadValue) || {};
  const tailLabels = tailValues
    .map((tailValue) => {
      const target = children.find((item) => item.key === tailValue);
      return target?.label;
    })
    .filter((label): label is string => !!label);
  return [leadLabel, tailLabels.length === 1 ? tailLabels[0] : tailLabels.length].filter((i) => !!i).join('/');
};
