import { FieldType } from '@moego/api-web/moego/models/reporting/v2/field_model';
import { RadioGroup } from '@moego/ui';
import { RadioGroupSlotsClasses } from '@moego/ui/dist/esm/components/Radio/RadioGroup.style';
import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { isNil } from 'lodash-es';
import React, { memo } from 'react';
import { useFormatOptionValues } from '../../hooks/useFormatOptionValues';
import { useOptionValues } from '../../hooks/useOptionValues';
import { DisplayComponentCommonProps, OperatorComponentValue, Optional, OptionItem } from '../api';
import { DropdownSelector } from '../DropdownSelector';
import { InputFieldType } from '../InputFieldType/InputFieldType';
import { RadioWithExtra } from '../RadioExtra/RadioWithExtra';

export type Value = { value: string | number; inputValue: string };

export type RadioInputSelectorValue = Optional<Value>;

interface RadioInputCommonProps {
  /** when select current radio, the extra input field type. e.g Money input style */
  inputType?: FieldType;
  inputTypePrefix?: React.ReactNode;
  options: OptionItem[];
  /** when user clear input value. we should destroy the component if there is no string value. this parameter determine how long to wait to check  */
  waitClearValue?: number;
}

export interface RadioInputProps extends OperatorComponentValue<RadioInputSelectorValue>, RadioInputCommonProps {
  classNames?: RadioGroupSlotsClasses;
}

export const RadioInputSelector = (props: RadioInputProps) => {
  const {
    value,
    options: optionsList,
    onChange,
    classNames,
    inputType = FieldType.TEXT,
    inputTypePrefix,
    waitClearValue = 2000,
  } = props;
  const options = optionsList || [];
  const { value: radioValue, inputValue } = value || {};
  const selectedValue = radioValue ?? '';
  const onChangePartial = useMemoizedFn((obj: Partial<Value>) => {
    onChange({ ...{ value: radioValue, inputValue: inputValue }, ...obj } as Value);
    // clear option values then clear this component's value
    if ('inputValue' in obj) {
      clearValue(obj.inputValue);
    }
  });
  /** if user stop input, and the value is empty. we should destroy the component after {@link waitClearValue/1000} seconds */
  const { run: clearValue } = useDebounceFn(
    (e: any) => {
      if (isNil(e)) {
        onChange(undefined);
      }
    },
    { wait: waitClearValue },
  );

  return (
    <RadioGroup value={selectedValue} classNames={{ wrapper: 'moe-gap-y-[20px]', ...classNames }}>
      {options.map((option) => {
        const { key, label } = option;
        const isSelectedRadio = selectedValue === key;
        return (
          <RadioWithExtra
            key={key}
            showExtra={isSelectedRadio}
            label={label}
            value={key}
            onChange={(nextKey) => {
              const isClearValue = radioValue === nextKey;
              if (isClearValue) {
                onChange(undefined);
                return;
              }
              onChangePartial({ value: nextKey });
            }}
          >
            <div className="moe-ml-[28px]">
              <InputFieldType
                inputType={inputType}
                prefix={inputTypePrefix}
                value={inputValue}
                onChange={(e) => onChangePartial({ inputValue: e })}
              />
            </div>
          </RadioWithExtra>
        );
      })}
    </RadioGroup>
  );
};

export interface RadioInputSelectorDisplayProps
  extends DisplayComponentCommonProps<RadioInputSelectorValue>,
    RadioInputCommonProps {}

export const useRadioInputPreviewValue = (props: RadioInputSelectorDisplayProps) => {
  const { value, options, inputType, inputTypePrefix } = props;
  const { value: radioValue, inputValue } = value || {};
  const optionValues = useOptionValues({ value: radioValue, options });
  const valueNode = useFormatOptionValues(optionValues);
  const moneyFmtTxt = isNil(inputValue) ? '' : `${inputTypePrefix}${inputValue}`;
  const inputNodes = inputType === FieldType.MONEY ? moneyFmtTxt : inputValue;

  if (!value || isNil(radioValue)) {
    return null;
  }

  return (
    <div className="moe-flex moe-items-center moe-gap-xxs">
      <div>{valueNode}</div>
      <div>{inputNodes}</div>
    </div>
  );
};

export const RadioInputSelectorDisplay = memo<RadioInputSelectorDisplayProps>(function InputDisplay(props) {
  const { title, onClear, ...radioSelectorProps } = props;
  const valueNode = useRadioInputPreviewValue(props);
  return (
    <DropdownSelector title={title} value={valueNode} onClear={onClear}>
      <RadioInputSelector {...radioSelectorProps} />
    </DropdownSelector>
  );
});
