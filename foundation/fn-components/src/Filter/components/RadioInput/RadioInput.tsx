import { FilterComponentType } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { isNil } from 'lodash-es';
import { registerOperatorComponent } from '../api';
import {
  RadioInputSelector,
  RadioInputSelectorDisplay,
  useRadioInputPreviewValue,
  Value,
} from './RadioInputFilterDisplay';

export const RadioInputFilter = registerOperatorComponent({
  componentType: FilterComponentType.RADIO_INPUT,
  component: RadioInputSelector,
  displayComponent: RadioInputSelectorDisplay,
  usePreviewValue: useRadioInputPreviewValue,
  checkValueValidation: ({ value: { value, inputValue } }: { value: Value }) => ({
    validate:
      !isNil(value) && !isNil(inputValue) && (typeof inputValue === 'string' ? inputValue.trim().length > 0 : true),
  }),
});
