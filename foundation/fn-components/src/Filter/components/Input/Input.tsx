import { FilterComponentType } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { Input } from '@moego/ui';
import { useDebounceFn } from 'ahooks';
import { isNil } from 'lodash-es';
import React, { memo } from 'react';
import { ComponentDisplayProps, OperatorComponentValue, Optional, registerOperatorComponent } from '../api';
import { DropdownSelector } from '../DropdownSelector';

export interface InputProps extends OperatorComponentValue<Optional<string>> {}
export interface InputDisplayProps extends OperatorComponentValue<Optional<string>>, ComponentDisplayProps {}

export const InputOperator = memo<InputProps>(function InputDisplay(props) {
  const { value, onChange } = props;
  const { run: waitForClear } = useDebounceFn(
    (e: string) => {
      if (!e) {
        onChange(undefined);
      }
    },
    { wait: 2000 },
  );

  return (
    <Input
      value={value || ''}
      onChange={(e) => {
        onChange(e);
        waitForClear(e);
      }}
    />
  );
});

export const InputDisplay = memo<InputDisplayProps>(function InputDisplay(props) {
  const { title, onClear, ...others } = props;
  const { value } = others;
  const len = value?.length || 0;
  const isAllEmpty = value?.trim().length === 0;

  return (
    <DropdownSelector
      title={title}
      value={isAllEmpty ? new Array(len).fill(1).map((_, index) => <span key={index}>&nbsp;</span>) : value}
      onClear={onClear}
    >
      <InputOperator {...others} />
    </DropdownSelector>
  );
});

export const InputFilter = registerOperatorComponent({
  component: InputOperator,
  displayComponent: InputDisplay,
  componentType: FilterComponentType.INPUT,
  usePreviewValue: (props: InputProps) => props.value || '',
  checkValueValidation: ({ value }) => ({ validate: !isNil(value) }),
});
