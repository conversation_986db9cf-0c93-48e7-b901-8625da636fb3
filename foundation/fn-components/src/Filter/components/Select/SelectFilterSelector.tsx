import { RadioGroup, LegacySelect as Select } from '@moego/ui';
import { CheckboxGroupSlotsClasses } from '@moego/ui/dist/esm/components/Checkbox/CheckboxGroup.style';
import { useMemoizedFn } from 'ahooks';
import React, { memo, useMemo } from 'react';
import { useFormatOptionValues } from '../../hooks/useFormatOptionValues';
import { useOptionValues } from '../../hooks/useOptionValues';
import { mapUIOptionFromItem } from '../../utils/mapFn';
import { DisplayComponentCommonProps, OperatorComponentValue, Optional, PlainOptionItem } from '../api';
import { DropdownSelector } from '../DropdownSelector';
import { RadioWithExtra } from '../RadioExtra/RadioWithExtra';
import { useSelectRatioOption } from '../useSelectRationOption';
import { Value } from './SelectFilter.types';

type SelectFilterSelectorValue = Optional<Value>;

interface SelectFilterCommonProps {
  options: PlainOptionItem[];
  /** select parent radio option */
  radioOptions?: PlainOptionItem[];
}

export interface SelectFilterSelectorProps
  extends OperatorComponentValue<SelectFilterSelectorValue>,
    SelectFilterCommonProps {
  classNames?: CheckboxGroupSlotsClasses;
}

export const SelectFilterSelector = (props: SelectFilterSelectorProps) => {
  const { value, onChange, options: selectOptions, radioOptions } = props;
  const options = useMemo(() => (selectOptions || []).map(mapUIOptionFromItem), [selectOptions]);
  const radioOptionList = useSelectRatioOption(radioOptions);
  const { key: radioValue, values: selectValues } = value || ({ key: options[0]?.value, values: [] } as Value);
  const onChangePartial = useMemoizedFn((obj: Partial<Value>) => {
    // clear option values then clear this component's value
    if ('values' in obj && obj.values!.length === 0) {
      onChange(undefined);
      return;
    }
    onChange({ ...{ key: radioValue, values: selectValues }, ...obj } as Value);
  });

  return (
    <RadioGroup value={radioValue}>
      {radioOptionList.map((item) => {
        const { key, label } = item;
        const isSelectedRadio = radioValue === key;
        return (
          <RadioWithExtra
            key={key}
            showExtra={isSelectedRadio}
            label={label}
            value={key}
            onChange={(nextKey) => {
              const isClearValue = radioValue === nextKey;
              if (isClearValue) {
                onChange(undefined);
                return;
              }
              onChangePartial({ key: nextKey });
            }}
          >
            <Select
              className="moe-ml-[28px] moe-min-w-[280px]"
              isMultiple
              options={options}
              value={selectValues}
              multipleMode="tag"
              onChange={(values) => onChangePartial({ values })}
            />
          </RadioWithExtra>
        );
      })}
    </RadioGroup>
  );
};

export interface SelectFilterSelectorDisplayProps
  extends DisplayComponentCommonProps<SelectFilterSelectorValue>,
    SelectFilterCommonProps {}

export const useSelectFilterPreviewValue = (props: SelectFilterSelectorDisplayProps) => {
  const { value, options } = props;
  const { values: selectValues } = value || ({ key: '', values: [] } as Value);
  const optionValues = useOptionValues({ value: selectValues, options });
  const valueNode = useFormatOptionValues(optionValues);
  return valueNode;
};

export const SelectFilterSelectorDisplay = memo<SelectFilterSelectorDisplayProps>(
  function CheckboxSelectorDisplay(props) {
    const { title, onClear, ...otherProps } = props;
    const valueNode = useSelectFilterPreviewValue(props);

    return (
      <DropdownSelector title={title} value={valueNode} onClear={onClear}>
        <SelectFilterSelector {...otherProps} />
      </DropdownSelector>
    );
  },
);
