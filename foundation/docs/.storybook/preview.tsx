import { ThemeProvider, Toaster } from '@moego/ui';
import type { Preview } from '@storybook/react';
import React from 'react';

import 'animate.css';
import './preview.css';

export default {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    options: {
      storySort: {
        order: [
          // foundation component
          'Foundation',
          ['Introduction', 'Changelog', 'Components'],
        ],
        method: 'alphabetical',
      },
    },
  },
  decorators: [
    (Story, context) => {
      return (
        <ThemeProvider theme={context.globals.moeTheme}>
          <Story />
          <Toaster />
        </ThemeProvider>
      );
    },
  ],
} satisfies Preview;
