#!/usr/bin/env bash

set -xeuo pipefail

# 本地配置

TAG=latest
IS_PRODUCTION=false

case "$BRANCH_NAME" in
  main)
    IS_PRODUCTION=true
  ;;
  feature-* | bugfix-*)
    TAG=$(echo "$BRANCH_NAME" | sed -E 's/^(feature|bugfix)-(.*)$/\2/')
  ;;
  *)
    echo "Skip publish npm on branch $BRANCH_NAME"
    exit 0
  ;;
esac

PNPM_BIN_DIR=~/bin
if [ ! -d "$PNPM_BIN_DIR" ]; then
  mkdir $PNPM_BIN_DIR
fi
corepack enable --install-directory $PNPM_BIN_DIR

npx npm-cli-login \
  -u "$NPM_PUBLISHER_USR" \
  -p "$NPM_PUBLISHER_PSW" \
  -e <EMAIL> \
  --config-path .npmrc \
  -r "https://nexus.devops.moego.pet/repository/npm-local/"

node -v
pnpm -v
pnpm install --frozen-lockfile

echo "Build Result"

if ! pnpm run build:packages; then
  echo "Build packages failed with some errors."
  exit 1
fi

RELEASE_ALL=false

# 主分支中通过 workflow_dispatch 由于无法判断哪些包变化了，所以强制发布所有包
if [ "$BRANCH_NAME" = "main" ] && [ "$GITHUB_EVENT_NAME" = "workflow_dispatch" ]; then
  RELEASE_ALL=true
fi

export TAG="$TAG"
export IS_PRODUCTION="$IS_PRODUCTION"
export BUILD_ID="$GITHUB_RUN_ID"
export BRANCH_NAME="$BRANCH_NAME"
export RELEASE_ALL="$RELEASE_ALL"

node ./ci/publish.cjs

# build storybook
pnpm run build:storybook
pnpm run build:foundation-storybook

# copy foundation docs to the sub directory
cp -rf ./foundation/docs/storybook-static ./storybook-static/fn


# build apps
pnpm run build:apps
[ ! -d "apps-static" ] && mkdir apps-static
cp -rf ./apps/web/build/client ./apps-static/web

