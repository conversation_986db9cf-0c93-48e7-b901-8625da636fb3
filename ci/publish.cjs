#!/usr/bin/env node
const { execSync } = require('node:child_process');
const fs = require('node:fs');
const path = require('node:path');
const semver = require('semver');
const { compareVersions } = require('compare-versions');

// 本地配置
const {
  IS_PRODUCTION = 'false',
  TAG = 'dev',
  BUILD_ID = '0',
  BRANCH_NAME = 'main',
  RELEASE_ALL = 'false',
} = process.env;

const isProduction = IS_PRODUCTION === 'true';

const safeJsonParse = (str) => {
  try {
    return JSON.parse(str);
  } catch (e) {
    console.error(e);
    return null;
  }
};

// 获取所有包的列表
const packages = fs
  .readdirSync('packages', { withFileTypes: true })
  .filter((dirent) => dirent.isDirectory())
  .map((dirent) => `packages/${dirent.name}`);
const foundations = fs
  .readdirSync('foundation', { withFileTypes: true })
  .filter((dirent) => dirent.isDirectory())
  .map((dirent) => `foundation/${dirent.name}`);

const workspaceRE = /^packages|^foundation/;

/**
 * @type {Map<string, import('@pnpm/types').ProjectManifest | null>}
 */
const pkgCache = new Map();

const getPkgPath = (pkgName) => path.resolve(__dirname, '..', pkgName, 'package.json');

/**
 * @type {(pkgName: string) => import('@pnpm/types').ProjectManifest | null}
 */
const getPkgJson = (pkgName) => {
  if (pkgCache.has(pkgName)) {
    return pkgCache.get(pkgName);
  }

  const pkgPath = getPkgPath(pkgName);
  if (fs.existsSync(pkgPath)) {
    try {
      const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'));
      return pkg;
    } catch (e) {}
  }
  return null;
};

const filterPrivate = (pkgName) => {
  const pkg = getPkgJson(pkgName);
  return !pkg?.private && !!pkg.name;
};

const allPackages = [...packages, ...foundations].filter(filterPrivate);

let updatePkgs;

if (RELEASE_ALL === 'true') {
  console.log('Force release all packages.');
  console.log();

  updatePkgs = allPackages;
} else {
  let origin;
  if (isProduction) {
    execSync('git fetch --tags');
    origin = execSync("git tag --sort=-v:refname | grep '^release-' | head -n 1", { stdio: 'pipe', encoding: 'utf-8' });
    origin = origin.trim() || 'HEAD~1';
  } else {
    execSync('git fetch origin main');
    origin = 'origin/main';
  }

  // 获取更改文件的包名
  const changePackages = new Set(
    execSync(`git diff --name-only ${origin}...HEAD`, {
      stdio: 'pipe',
      encoding: 'utf-8',
    })
      .split('\n')
      .filter((path) => {
        // 添加 workspace
        return workspaceRE.test(path);
      })
      .map((path) => {
        const res = path.match(/^((packages|foundation)\/(.*?))\//);
        return res?.[1] || '';
      })
      .filter((name) => {
        return !!name && allPackages.includes(name);
      }),
  );

  const restPackages = allPackages.filter((name) => !changePackages.has(name));

  // 依赖收集
  for (let i = 0; i < restPackages.length; ) {
    const pkgName = restPackages[i];
    const pkg = getPkgJson(pkgName);

    if (!pkg || !pkg.dependencies) {
      restPackages.splice(i, 1);
      continue;
    }

    if (Object.keys(pkg.dependencies).some((name) => changePackages.has(name))) {
      restPackages.splice(i, 1);
      changePackages.add(pkgName);
      i = 0;
      continue;
    }

    i++;
  }

  updatePkgs = [...changePackages];
}

if (updatePkgs.length) {
  console.log('Updated packages:', updatePkgs);
  console.log();

  const filterFlags = [];

  if (isProduction) {
    for (const pkgName of updatePkgs) {
      const pkg = getPkgJson(pkgName);

      // 始终查找最高的版本而不是使用 latest 标签，以防止有异常版本发布到了该标签上
      const npmVersions = safeJsonParse(
        execSync(`pnpm view ${pkg.name} versions --json`, { stdio: 'pipe', encoding: 'utf-8' }),
      );
      const npmVersion = npmVersions ? npmVersions.sort(compareVersions).pop() : '0.0.0';

      const currentMajor = parseInt(npmVersion.split('.')[0] || '0');
      const pkgMajor = parseInt(pkg.version?.split('.')[0] || '0');

      let newVersion;
      if (pkgMajor > currentMajor) {
        newVersion = `${pkgMajor}.0.0`;
      } else {
        newVersion = semver.inc(npmVersion || '0.0.0', 'minor') || '0.1.0';
        // 如果此时 package.json 指定了一个比将要发布的版本更大的版本，采用 package.json 指定的版本
        newVersion = pkg.version && compareVersions(pkg.version, newVersion) > 0 ? pkg.version : newVersion;
      }

      pkg.version = newVersion;
      fs.writeFileSync(getPkgPath(pkgName), JSON.stringify(pkg, undefined, 2) + '\n', 'utf-8');

      filterFlags.push(`-F "${pkg.name}"`);
      console.log(`Release ${pkgName}, target: ${pkg.name}@${pkg.version}`);
    }

    // 主分支时打一个 tag 作为下一次对比的锚点
    const releaseTag = `release-${Date.now()}`;

    execSync(`git tag ${releaseTag}`);
    execSync(`git push origin refs/tags/${releaseTag}`);
  } else {
    for (const pkgName of updatePkgs) {
      const pkg = getPkgJson(pkgName);
      pkg.version = `${pkg.version || '0.0.0'}-${TAG}.${BUILD_ID}`;
      fs.writeFileSync(getPkgPath(pkgName), JSON.stringify(pkg, null, 2) + '\n');

      filterFlags.push(`-F "${pkg.name}"`);
      console.log(`Release ${pkgName}, target: ${pkg.name}@${pkg.version}`);
    }
  }

  const publishCommand = `pnpm -r publish --tag "${TAG}" --publish-branch "${BRANCH_NAME}" ${filterFlags.join(' ')} --no-git-checks --registry "https://nexus.devops.moego.pet/repository/npm-local/"`;

  console.log();
  console.log(publishCommand);
  console.log();

  execSync(publishCommand, { stdio: 'inherit' });
} else {
  console.log();
  console.log('No package updated, skip release.');
  console.log();
}
