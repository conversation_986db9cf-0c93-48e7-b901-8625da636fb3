#!/usr/bin/env bash

export XDG_DATA_HOME="$PWD/.cache"
set -euo pipefail
rm -f "$XDG_DATA_HOME/lint.error"

if [ ! -d "$XDG_DATA_HOME" ]; then
  mkdir -p "$XDG_DATA_HOME"
fi

function echo_error() {
  echo -e "\n"
  echo -e "\033[31mERROR: $1\033[0m"
  touch "$XDG_DATA_HOME/lint.error"
}

function echo_info() {
  echo -e "\033[32mINFO: $1\033[0m"
}

function check_ts_ignore() {
  echo_info "Checking ts-ignore"
  if git --no-pager grep -n -i @ts''-ignore; then
    echo_error '@ts''-ignore is forbidden, please fix it or use @ts-expect-error instead.'
  fi
}

function check_default() {
  echo_info "Checking export default"
  if git grep 'export default' -- src | grep -v '.d.ts:'; then
    echo_error 'export default is forbidden, please use export { xxx } instead.'
  fi
}

function check_circular() {
  echo_info "Checking circular dependencies"
  if ! pnpm run check:circular; then
    echo_error 'Circular dependencies detected, please fix it.'
  fi
}

function check_prettier() {
  echo_info "Checking prettier"
  if ! npx prettier --check .; then
    echo_error 'Code is not formatted correctly, please fix it.'
  fi
}

function check_types() {
  echo_info "Checking types"
  if ! pnpm run check:types; then
    echo_error 'Ts check failed, please fix it.'
  fi
}

function check_commit_msg() {
  echo_info "Checking commit messages"
  if ! npx commitlint --from origin/main; then
    echo_error 'Commit message check failed, please fix it.'
  fi
}

if [[ $# -gt 0 ]]; then
  "check_$1"
else
  check_circular
  check_prettier
  check_types
  check_commit_msg
  check_ts_ignore
  check_default
fi

if [ -f "$XDG_DATA_HOME/lint.error" ]; then
  echo -e "\n"
  echo -e "\033[31mFound error(s) please fix.\033[0m"
  rm "$XDG_DATA_HOME/lint.error"
  exit 1
fi
