{"$schema": "https://moego.s3.us-west-2.amazonaws.com/ops/github-actions/ci-json-schema.json", "service_name": "moego-business-web-kit", "slack": ["#platform-fe-alert"], "language": {"type": "node", "version": "18"}, "install": {"commands": ["pnpm i"], "cache_dir": "node_modules"}, "lint": {"commands": ["pnpm i", "pnpm build:packages", "bash ci/lint.sh"]}, "test": {"commands": ["pnpm i", "pnpm run test"]}, "build": {"commands": ["pnpm i", "bash ci/build.sh"], "build_image": [{"dockerfile": "ci/Dockerfile", "context": "."}]}, "deploy": {"type": "service"}}