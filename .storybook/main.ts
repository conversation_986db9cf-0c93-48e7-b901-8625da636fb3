import type { StorybookConfig } from '@storybook/react-vite';
import { dirname, join } from 'path';
import remarkGfm from 'remark-gfm';

export default {
  stories: ['./stories/**/*.mdx', '../packages/*/src/**/*.stories.@(js|jsx|mjs|ts|tsx)', '../packages/**/*.mdx'],
  addons: [
    {
      name: '@storybook/addon-docs',
      options: {
        mdxPluginOptions: {
          mdxCompileOptions: {
            remarkPlugins: [remarkGfm],
          },
        },
      },
    },
    getAbsolutePath('@storybook/addon-onboarding'),
    getAbsolutePath('@storybook/addon-links'),
    getAbsolutePath('@storybook/addon-essentials'),
    getAbsolutePath('@storybook/addon-interactions'),
    getAbsolutePath('@storybook/addon-storysource'),
  ],
  framework: {
    name: getAbsolutePath('@storybook/react-vite'),
    options: {},
  },
  typescript: {
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      tsconfigPath: '../tsconfig.json',
      // Speeds up Storybook build time
      compilerOptions: {
        allowSyntheticDefaultImports: true,
        esModuleInterop: false,
      },
      // Makes union prop types like variant and size appear as select controls
      shouldExtractLiteralValuesFromEnum: true,
      // Makes string and boolean types that can be undefined appear as inputs and switches
      shouldRemoveUndefinedFromOptional: true,
      // Filter out third-party props from node_modules except some packages
      propFilter: (prop) => {
        return prop.parent
          ? !/node_modules\/\.pnpm\/(?!(@react-types|@radix-ui|@react-aria|react-aria))/.test(prop.parent.fileName)
          : true;
      },
      savePropValueAsString: true,
    },
  },
  staticDirs: ['../.storybook/public'],
  // managerHead: (head) => {
  //   return [head, '<link rel="icon" type="image/svg+xml" href="/logo.svg">'].join('\n');
  // },
} satisfies StorybookConfig;

function getAbsolutePath(value: string): any {
  return dirname(require.resolve(join(value, 'package.json')));
}
